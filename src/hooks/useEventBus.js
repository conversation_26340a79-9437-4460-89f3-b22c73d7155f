import mitt from 'mitt'
const emitter = mitt()

// 添加 once 方法
emitter.once = function (type, handler) {
  const wrappedHandler = (...args) => {
    this.off(type, wrappedHandler)
    handler(...args)
  }
  this.on(type, wrappedHandler)
  return this
}

// 重写 emit 方法，支持多参数传递
emitter.emit = function (type, ...args) {
  // 调用每个监听的回调函数，并把所有参数展开传递
  (this.all.get(type) || []).forEach(handler => handler(...args))
}

export { emitter }
