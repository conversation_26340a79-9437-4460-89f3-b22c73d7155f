import AgoraService from '@/core/ThinkService/AgoraService'
import { ref } from 'vue'
let globalRtcService = null
const rtcIsInitialized = ref(false)
export function useRtcService() {
  const initRtcService = () => {
    if (!globalRtcService) {
      globalRtcService = new AgoraService()
      globalRtcService.initServe()
      rtcIsInitialized.value = true
    }
    return globalRtcService
  }
  // 获取 RTC 服务
  const getRtcService = () => {
    return globalRtcService
  }
  const releaseRtcService = () => {
    if (globalRtcService) {
      globalRtcService.release()
      globalRtcService = null
      rtcIsInitialized.value = false
    }
  }

  return {
    initRtcService,
    getRtcService,
    rtcIsInitialized,
    releaseRtcService
  }
}
