import { ref, reactive } from 'vue'
import { classroomStudentBasicApi, classStudentListApi, webPreClassPrepare } from '@/api/classroom'
import { useRoute } from 'vue-router'
// import { getCoursewareInfo } from '@/api/h5courseware/index.js'
import { message } from 'ant-design-vue'
import { useClassData } from '@/stores/classroom'
export function useInitClass() {
  const isLoading = ref(true)
  const errorObject = reactive({
    isShow: false,
    message: '',
    showRefresh: true,
    subMessage: ''
  })
  const errorMsgTip = (message, showRefresh = true) => {
    // todo 这里加一下subMessage
    errorObject.isShow = true
    errorObject.message = message
    errorObject.showRefresh = showRefresh
  }
  const route = useRoute()
  const store = useClassData()
  const { planId, classId } = route.query
  const params = {
    planId: +planId,
    classId: +classId
  }
  const initClassData = async () => {
    try {
      console.log('initClassData', params)
      const res = await classroomStudentBasicApi(params)
      if (res.code !== 0) {
        message.error(`初始化课程数据失败,${res.msg}`)
        errorMsgTip(`初始化课程数据失败: ${res.msg},code:${res.code}`)
        throw new Error(`初始化课程数据失败: ${res.msg},code:${res.code}`)
      }
      window._requestBasicTime = new Date().getTime()

      const {
        configs = {},
        planInfo = {},
        stuInfo = {},
        counselorInfo = {},
        teacherInfo = {},
        nowTime,
        stuLiveInfo
      } = res.data
      store.changeTeacherInfo(teacherInfo)
      const { appId, appKey, ircRooms, stuIrcId, skinType } = configs
      const roomlist = Array.isArray(ircRooms) ? ircRooms : [ircRooms]
      const {
        userName: stuName,
        nickName,
        id: stuId,
        realName,
        goldNum,
        englishName,
        avatar: imgPath,
        gradeId
      } = stuInfo
      const { id: psId, id: password } = stuInfo
      // "mode":当前所在直播流   0辅导流， 1主讲流
      const { id: planId, stime, subjectIds, gradeIds, mode } = planInfo
      const { classId, teamId, courseId } = stuLiveInfo
      const { id: teacherId, avatar } = teacherInfo
      const timeOffset = nowTime * 1000 - +new Date()

      // irc配置参数
      const ircInitOptions = {
        appId: appId,
        appKey,
        psId: psId.toString(),
        password: password.toString(),
        nick: stuIrcId,
        planId,
        uid: stuId || teacherId,
        roomlist
      }

      // 聊天配置参数
      const chatOptions = {
        mode,
        // bizId,
        subjectIds,
        gradeIds,
        goldNum,
        realName,
        nickName,
        englishName,
        stuName,
        nick: stuIrcId,
        stuId,
        roomlist,
        planId,
        stime,
        psId,
        imgPath,
        classId,
        teamId,
        skinType,
        courseId
      }
      // 通用的参数
      const commonOption = {
        mode,
        stuName,
        stuId,
        planId,
        nickName,
        realName,
        englishName,
        // bizId,
        nowTime,
        stime,
        stuIRCId: stuIrcId,
        subjectIds,
        gradeIds,
        roomlist,
        goldNum,
        classId,
        teamId,
        skinType,
        psId,
        avatar,
        gradeId,
        timeOffset,
        counselorInfo,
        teacherInfo,
        configs,
        classType: 2
      }
      const baseData = {
        ...res.data,
        planId,
        classId,
        ircInitOptions,
        chatOptions,
        commonOption,
        // bizId,
        stuInfo
      }
      store.initBaseData(baseData)
      await initClassStudent(baseData)
      await initCoursewareInfo(baseData)
      isLoading.value = false

      // 成功时返回 baseData
      return baseData
    } catch (error) {
      message.error(`初始化课程数据失败,${JSON.stringify(error)}`)
      errorMsgTip(`初始化课程数据失败: ${JSON.stringify(error)}`)
      isLoading.value = false

      // 失败时抛出异常
      throw error
    }
  }

  /**
   * 获取课件信息
   */
  async function initCoursewareInfo(baseData) {
    const options = baseData.commonOption

    const planId = options.planId
    try {
      const res = await webPreClassPrepare({
        planId,
        classId: options.classId
      })
      if (res && res.code == 0) {
        console.log('hooks课件信息', res.data)
        store.initCoursewareData(res.data.coursewareInfo)
        store.initRedBagInfo(res.data.redBagInfo)
      } else {
        message.error(`获取课件信息失败,${res.msg}`)
        errorMsgTip(`获取课件信息失败: ${res.msg},code:${res.code}`)
        throw new Error(`获取课件信息失败: ${res.msg},code:${res.code}`)
      }
    } catch (error) {
      errorMsgTip(`获取课件信息失败: ${JSON.stringify(error)}`)
      throw error
    }
  }

  /**
   * 初始化学生列表
   */
  async function initClassStudent(baseData) {
    try {
      // @log-ignore
      const options = baseData.commonOption
      const res = await classStudentListApi({
        planId: options.planId,
        classId: options.classId
      })
      if (res && res.code == 0) {
        const studentList = res.data || []
        store.initStudentList(studentList)
      } else {
        message.error(`学生列表获取失败,${res.msg}`)
        errorMsgTip(`学生列表获取失败,${res.msg}`)
        throw new Error(`学生列表获取失败,${res.msg}`)
      }
    } catch (error) {
      message.error(`学生列表获取失败,${JSON.stringify(error)}`)
      errorMsgTip(`学生列表获取失败,${JSON.stringify(error)}`)
      throw error
    }
  }

  return {
    isLoading,
    errorObject,
    initClassData
  }
}
