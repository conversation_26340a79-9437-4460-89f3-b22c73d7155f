import { useClassData } from '@/stores/classroom'
import { message } from 'ant-design-vue'
import { RTCLog } from '@/utils/web-log/HWLogDefine'
const useRtcListener = service => {
  const store = useClassData()
  const teacherJoinChannel = data => {
    console.log('老师加入频道', data)
    store.changeTeacherInfo({ inClass: true })
  }
  const teacherLeaveChannel = data => {
    console.log('老师离开频道', data)
    store.changeTeacherInfo({ inClass: false })
  }
  const teacherVideoMute = mute => {
    console.log(`老师${mute ? '关闭' : '开启'}视频`)
    store.changeTeacherInfo({ videoMute: mute })
  }
  const teacherAudioMute = mute => {
    console.log(`老师${mute ? '关闭' : '开启'}音频`)
    store.changeTeacherInfo({ audioMute: mute })
  }
  const teacherVideoLeaveChannel = uid => {
    store.changeTeacherInfo({ videoMute: true, inClass: false })
    console.log('老师视频端离开频道', uid)
    // 离开频道销毁老师视频
    service.destroyTeacherVideo()
  }
  const teacherAudioVolume = volume => {
    store.changeTeacherInfo({ volume })
  }
  const localJoinChannel = () => {
    store.changeStudentInfo(0, { inClass: true, joinClassTime: Date.now() })
    console.log('本地加入频道成功')
  }
  const localAudioVolume = volume => {
    store.changeStudentInfo(0, { volume })
  }
  const remoteAudioVolume = (uid, volume) => {
    // store.changeStudentInfo(uid, { volume })
  }
  const remoteJoinChannel = async uid => {
    if (String(uid).length > 7) {
      // 学生id长度为7，大于7的不处理
      return
    }
    store.changeStudentInfo(uid, {
      inClass: true,
      joinClassTime: Date.now()
    })
    console.log(`远端学生加入频道, uid: ${uid}`)
  }
  const remoteLeaveChannel = uid => {
    store.changeStudentInfo(uid, {
      inClass: false,
      joinClassTime: 0
    })
    console.log(`远端学生离开频道, uid: ${uid}`)
  }
  const studentVideoMute = ({ uid, mute }) => {
    store.changeStudentInfo(uid, {
      videoMute: mute
    })
    console.log(`远端学生${mute ? '关闭' : '打开'}视频, uid: ${uid}`)
  }
  const studentAudioMute = ({ uid, mute }) => {
    store.changeStudentInfo(uid, {
      audioMute: mute
    })
    console.log(`远端学生${mute ? '关闭' : '打开'}音频, uid: ${uid}`)
  }

  const localNetworkQuality = quality => {
    //  *  - 0：质量未知。
    //  *  - 1：质量极好。
    //  *  - 2：用户主观感觉和极好差不多，但码率可能略低于极好。
    //  *  - 3：用户主观感受有瑕疵但不影响沟通。
    //  *  - 4：勉强能沟通但不顺畅。
    //  *  - 5：网络质量非常差，基本不能沟通。
    //  *  - 6: 网络连接断开，完全无法沟通。
    if (quality.downlinkNetworkQuality === 4) {
      // 网络质量差，记录日志
      RTCLog.error('勉强能沟通但不顺畅', {
        downlinkNetworkQuality: quality.downlinkNetworkQuality,
        uplinkNetworkQuality: quality.uplinkNetworkQuality
      })
    } else if (quality.downlinkNetworkQuality === 5) {
      // 网络质量非常差，记录日志
      RTCLog.error('网络质量非常差，基本不能沟通', {
        downlinkNetworkQuality: quality.downlinkNetworkQuality,
        uplinkNetworkQuality: quality.uplinkNetworkQuality
      })
    } else if (quality.downlinkNetworkQuality === 6) {
      // 网络连接断开，记录日志
      RTCLog.error('网络连接断开，完全无法沟通', {
        downlinkNetworkQuality: quality.downlinkNetworkQuality,
        uplinkNetworkQuality: quality.uplinkNetworkQuality
      })
    }
    store.updateNetworkQuality(quality.downlinkNetworkQuality)
  }

  const rtcError = err => {
    if (err.code == 'NO_CAMERA' || err.code == 'PERMISSION_DENIED') {
      if (err.type === 'video') {
        RTCLog.info('摄像头权限被拒绝', err)
      }
    }
    message.error(`${err.msg}(code:${err.code})`)
  }
  const videoError = () => {
    // 摄像头权限被拒绝
    store.changeStudentInfo(0, {
      videoMute: true
    })
  }
  const rtcEventMap = {
    videoError, // 加入频道时候判断当前videotrack，如果没有，就静音本地视频
    teacherJoinChannel, // 监听老师加入频道
    teacherLeaveChannel,
    teacherVideoMute, // 监听老师视频开关状态
    teacherAudioMute, // 监听老师音频静音状态
    teacherVideoLeaveChannel, // 监听老师视频端离开频道
    teacherAudioVolume, // 监听老师音量
    localJoinChannel, // 本地加入频道
    localAudioVolume, // 本地音量
    remoteAudioVolume, // 远端学生音量
    remoteJoinChannel, // 远端学生加入频道
    remoteLeaveChannel, // 远端学生离开频道
    studentVideoMute, // 远端学生开关视频
    studentAudioMute, // 远端学生开关音频
    localNetworkQuality, // 监听本地网络质量
    rtcError
  }
  for (const key in rtcEventMap) {
    service.on(key, rtcEventMap[key])
  }
  const removeRtcListener = () => {
    for (let key in rtcEventMap) {
      service.off(key, rtcEventMap[key])
    }
  }
  return removeRtcListener
}
export default useRtcListener
