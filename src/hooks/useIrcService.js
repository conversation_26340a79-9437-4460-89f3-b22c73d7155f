import SignalService from '@/core/ThinkService/SignalService'

import { ref } from 'vue'
let globalIrcService = null
const ircIsInitialized = ref(false)
export function useIrcService() {
  const initIrcService = ({ planId, businessId, userId, isAudition, ircOptions }) => {
    if (!globalIrcService) {
      globalIrcService = new SignalService({
        planId,
        businessId,
        userId,
        isAudition,
        ircOptions
      })
      ircIsInitialized.value = globalIrcService.canLogin
    }
    return globalIrcService
  }
  // 获取 RTC 服务
  const getIrcService = () => {
    return globalIrcService
  }
  const releaseIrcService = () => {
    if (globalIrcService) {
      globalIrcService.release()
      globalIrcService = null
      ircIsInitialized.value = false
    }
  }

  return {
    initIrcService,
    getIrcService,
    ircIsInitialized,
    releaseIrcService
  }
}
