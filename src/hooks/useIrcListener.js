import { emitter } from './useEventBus'
import { useClassData } from '@/stores/classroom'
import { useIRCMethods } from './useIRCMethods'
import { computed } from 'vue'
import { IRCLog } from '@/utils/web-log/HWLogDefine'

// import logger from 'utils/logger'
const messageCodecs = {
  TutorPrivateChat: 140
}
const interactionsList = [
  // 'take_picture', // 拍照上墙'
  'class_praise_list', // 课堂表扬榜
  'vote', // 投票
  // 'small_random_call', // 随机点名
  'openGift', // 开启礼物
  'nonpreset_fill_blank', // 非预设填空
  'student_rank', // 学生排名
  'interact', // 课件内单选、多选、判断题
  'fill_blank', // 课件内填空
  'distribute_coins', // 定向金币
  'classroom_praise', // 完课宝箱
  'schulte_table', // 舒尔特方格
  'redPacket', // 普通红包
  'red_packet_rain', // 红包雨
  'graffiti_board', // 涂鸦画板
  'speedyHand', // 抢答
  'class_examination', // 课中考试
  'classRest', // 课中休息
  'sendGiftBarrage' // 礼物弹幕
]
// const ircMap = {
//   all_audio_mute: '开关全员禁音',
//   all_onStage_closed: '全员上台',
//   allow_open_microphone: '是否允许学生开启麦克风',
//   forbid_mute_audio: '禁止关麦',
//   animation_emoji: '表情消息:在线',
//   auto_feedback: '课中自动反馈',
//   canvas_switch_courseware: '课件翻页',
//   canvas_switch_courseware_f: '课件翻页',
//   chat_msg_control: '仅老师消息可见',
//   class_examination: '课中考试',
//   class_praise_list: '课堂表扬榜',
//   classmode: '上课、下课（pc）',
//   classRest: '课间休息',
//   classRest_f: '课间休息',
//   classroom_praise: '课堂表扬:宝箱',
//   correct_picrure_t: '批改完成',
//   countDown: '倒计时',
//   countDown_f: '倒计时',
//   distribute_coins: '定向金币',
//   fill_blank: '填空题',
//   fill_blank_mark_correct: '填一填标记答案',
//   forbidden_student_emoji: '表情管控',
//   graffiti_board: '涂鸦画板',
//   graffiti_board_video: '老师上台私聊',
//   graffiti_board_watching: '教师查看涂鸦',
//   interact: '互动题',
//   level_chat_msg: '其他学生连对激励等级消息',
//   mode: '切流',
//   mult_video_mic: '多人上台',
//   nonpreset_fill_blank: '填一填',
//   openchat: '全员禁言',
//   openchat_f: '全员禁言',
//   openGift: '送礼物',
//   peer_chat_msg: '辅导私聊消息',
//   peer_mute_chat: '辅导禁言',
//   praise: '作业上墙点赞',
//   praise_list_point_like: '课堂表扬榜点赞',
//   private_mute_chat: '小班Chatbox禁言',
//   STUDENT_SPEAKING_MODE: '学生麦克风开启方式改变',
//   raise_hand: '举手',
//   random_call: '主讲随机点名',
//   random_video_mic: '主讲端随机点名后连麦指令',
//   red_packet_rain: '红包雨',
//   redPacket: '红包',
//   schulte_table: '舒尔特方格',
//   send_emoji: '表情消息:本地',
//   sendGiftBarrage: '礼物弹幕',
//   small_random_call: '随机点名',
//   speech_interact: '集体发言',
//   speedyHand: '抢答',
//   student_rank: '小班排行榜',
//   take_picture: '拍照上墙',
//   teacher_audio_mute: '禁音',
//   teacher_video_mic: '老师上台',
//   user_audio_mute: '开关麦克风',
//   user_video_mute: '开关摄像头',
//   video_bet_student: '学生互听',
//   video_mic: '视频连麦',
//   video_mic_f: '视频连麦',
//   vote: '投票',
//   vote_data: '投票统计',
//   vote_data_f: '投票统计',
//   vote_f: '投票',
//   teacher_video_mute: '主讲视频开关',
//   game_interact: '课件游戏互动',
//   Ambiance_Tools: '氛围工具',
//   forbid_video_sticker: '是否禁止学员贴纸',
//   change_collect_volume: '老师控制学生麦克风采集音量'
// }

/**
 * 监听信令消息
 */
const useIrcEvent = SignalService => {
  const {
    handleKickoutNotice,
    handleNetStatusChanged,
    handleClassMode,
    handleVideoBetStudent,
    handleEmoticon
    // handleFeedBackUpload
  } = useIRCMethods()
  const store = useClassData()
  const baseData = computed(() => store.baseData)
  const onRecvRoomDataUpdateNotice = options => {
    // IRCLog.info('ircMessageLog  onRecvRoomDataUpdateNotice', options)
    const { key, noticeContent, isHistory } = options
    // if (key !== 'canvas_switch_courseware') {
    //   // logger.send({
    //   //   tag: 'irc',
    //   //   level: 'info',
    //   //   content: {
    //   //     msg: `irc-kv消息-${ircMap[key]}`,
    //   //     key: key,
    //   //     noticeContent: noticeContent,
    //   //     isHistory: isHistory,
    //   //     interactType: 'kv消息'
    //   //   }
    //   // })
    // }

    // 收到上课指令时，校验当前课件是否与接口返回最新课件一致，不一致则弹窗让退出重进
    if (key === 'classmode') {
      handleClassMode(key, noticeContent)
      return
    }
    // 倒计时
    if (key === 'countDown') {
      emitter.emit('room.countDown', noticeContent)
      return
    }
    // 处理订正过程 拍照上墙 整个过程结束
    if (key === 'praise') {
      return emitter.emit('endCorrectPhotoWall', noticeContent, isHistory)
    }

    if (key === 'video_bet_student') {
      handleVideoBetStudent(noticeContent)
      return
    }
    // 全员上台
    if (key === 'all_onStage_closed') {
      store.updateAllOnStageStatus(false)
      return
    }
    // 老师视频开关历史消息
    if (key === 'teacher_video_mute') {
      emitter.emit('teacher_video_mute', noticeContent)
      return
    }
    // 接收到学生上下台以及授权指令时判断自己授权状态
    if (key === 'mult_video_mic') {
      emitter.emit('onStage', options)
      return
    }
    if (key === 'teacher_video_mic') {
      emitter.emit('onStage', options)
      return
    }
    if (key === 'graffiti_board_watching') {
      // 暂存消息，防止学生进入教室时涂鸦画板组件还没加载完成，导致监听不到该信令
      store.updateGraffitiBoardWatchingStutus(noticeContent)
      emitter.emit('graffiti_board_watching', noticeContent)
      return
    }
    // 处理私聊
    if (key === 'graffiti_board_video') {
      if (noticeContent?.userId != baseData.value.stuInfo.id) {
        // this.$emitter.$emit('teacherOnPrivateChat', noticeContent)
        store.changeTeacherInfo({
          privateChatStatus: noticeContent.pub
        })
      } else {
        noticeContent.teacherName = baseData.value.teacherInfo.name
        noticeContent.status = 1
        noticeContent.privateChat = true

        emitter.emit('onStage', options)
      }

      emitter.emit('changeAudioStatus', noticeContent)
      return
    }
    // 处理课件切换消息
    if (key === 'canvas_switch_courseware') {
      emitter.emit('canvas_switch_courseware', noticeContent)
      return
    }
    if (key === 'sendGiftBarrage') {
      emitter.emit('sendGiftBarrage', noticeContent)
      return
    }
    // 更新投票比例
    if (key === 'vote_data') {
      // 如果投票互动开启中，调用更新投票数据
      emitter.emit('getVoteStatistics', noticeContent)
      return
    }
    // 游戏课件需要全屏展示，因此不在互动层
    if (key === 'game_interact') {
      emitter.emit('gameInteract', noticeContent)
      return
    }
    if (key === 'take_picture') {
      // 移除单独处理，让它通过 interactionsList 处理
      emitter.emit('take_picture', options)
      return
    }
    if (key === 'small_random_call') {
      console.log('收到随机点名消息', options)
      emitter.emit('small_random_call', options)
      return
    }
    // 互动处理
    if (interactionsList.includes(key)) {
      if (key === 'interact' && noticeContent.jsString) {
        options.key = 'interact_in_courseware'
      }
      emitter.emit('interactions', options)
      return
    }

    // 学员表情管控
    if (key === 'forbidden_student_emoji') {
      emitter.emit('forbidden_student_emoji', noticeContent)
      return
    }

    if (key === 'forbid_video_sticker') {
      // 禁止学员开启形象
      emitter.emit('forbid_video_sticker', noticeContent)
      return
    }
    if (key === 'openchat') {
      // 分发给chatbox 全员禁言
      emitter.emit('openchat_chatbox', options)
      return
    }
    if (key === 'chat_msg_control') {
      // 分发给chatbox 仅老师消息可见
      emitter.emit('chat_msg_control_chatbox', options)
      return
    }
    if (key === 'private_mute_chat') {
      // 分发给chatbox Chatbox禁言
      emitter.emit('private_mute_chat_chatbox', options)
      return
    }

    if (key === 'STUDENT_SPEAKING_MODE') {
      // 学生讲话方式改变
      const { needToast, speakType, isOpenMic } = noticeContent
      emitter.emit('speakTypeChange', {
        needToast,
        msgSpeakType: speakType,
        isOpenMic
      })
      return
    }
    // 老师控制学生麦克风采集音量
    if (key === 'change_collect_volume') {
      const { students } = noticeContent
      emitter.emit('changeCollectVolume', students)
      return
    }
  }
  const onRecvRoomMessage = res => {
    console.log('ircMessageLog  onRecvRoomMessage', res)

    // 连对称号消息类型
    const { content, fromUserInfo } = res
    const { ircType, data } = content

    // 添加所有群聊消息的详细日志
    console.log('🔍 [DEBUG] 群聊消息详情:', {
      ircType,
      contentType: content?.type,
      hasContent: !!content,
      fromUser: fromUserInfo?.psId,
      messageContent: content
    })

    // 添加fill_blank_mark_correct专门的日志
    if (ircType === 'fill_blank_mark_correct') {
      console.log('🔍 [DEBUG] 收到fill_blank_mark_correct消息:', {
        ircType,
        content,
        fromUserInfo,
        data
      })
    }

    // logger.send({
    //   tag: 'irc',
    //   level: 'info',
    //   content: {
    //     msg: ircType ? `irc-群聊消息-${ircMap[ircType]}` : `irc-群聊消息-${content.type}`,
    //     key: ircType,
    //     type: content.type,
    //     content: content,
    //     interactType: 'irc群聊'
    //   }
    // })
    // 连对称号消息类型
    if (content && content.type == 142) {
      const data = {
        level: res.content.level
      }
      if (res.content.correctCount) {
        data['correctCount'] = res.content.correctCount
      }
      store.changeStudentInfo(res.content.userId, data)
      return
    }
    // 处理聊天消息
    if (content && (content.type == 130 || content.type == 139)) {
      emitter.emit('chatboxMsg', res)
    }
    // 处理本地表情消息
    if (content.ircType == 'send_emoji') {
      handleEmoticon(fromUserInfo.psId, {
        name: content.data.name,
        type: content.data.type
      })
    } else if (ircType == 'animation_emoji') {
      // 处理lottie表情消息
      handleEmoticon(fromUserInfo.psId, {
        type: data.type,
        name: data.resource.emojiName,
        ...data.resource
      })
    }
    // 处理举手消息
    if (ircType == 'raise_hand') {
      store.changeStudentInfo(fromUserInfo.psId, {
        raiseHandStatus: true
      })
    }
    // if (res.content && res.content.ircType === 'auto_feedback') {
    //   handleFeedBackUpload(res.content.data)
    //   return
    // }
    // 全员禁音
    if (ircType === 'all_audio_mute') {
      const status = data.pub
      console.log(11111, status)
      emitter.emit('all_audio_mute', !status)
    }
    // 氛围效果
    if (ircType === 'Ambiance_Tools') {
      if (store.baseData.commonOption.isAudition) {
        console.info('旁听生不触发氛围工具')
        return
      }
      const id = data.ambianceId
      emitter.emit('Ambiance_Tools', id)
    }
    // 学生游戏穿戴变更
    if (ircType === 'student_game_info_update') {
      store.changeStudentInfo(data.userId, {
        prop: data?.prop
      })
      return
    }
    if (ircType === 'open_mic_tip') {
      emitter.emit('open_mic_tip')
    }
  }
  const onRecvPeerMessage = res => {
    IRCLog.info('ircMessageLog  onRecvPeerMessage', res)
    const { content } = res
    const { type, ircType } = content

    // logger.send({
    //   tag: 'irc',
    //   level: 'info',
    //   content: {
    //     msg: ircType ? `irc-私聊消息-${ircMap[ircType]}` : `irc-私聊消息-${type}`,
    //     key: ircType,
    //     type: type,
    //     content: content,
    //     interactType: 'irc群聊'
    //   }
    // })
    // console.log(res, 'onRecvPeerMessage')
    // 辅导私聊
    if (type == messageCodecs.TutorPrivateChat) {
      // 辅导老师批改作业通知
      // 敏感词问题 消息题格式需兼容
      const resceiveMsg = content.parameter ? content.parameter : content.msg
      const correctPicrure = JSON.parse(resceiveMsg).correct_picrure_t
      correctPicrure && emitter.emit('chats.assignmentCheckedPush', correctPicrure)
      return
    }
    // 上传课件
    if (type === 'uploadCourseware') {
      // console.log('0901 小班课 打包上传本地课件')
      // 如果当前有课件正在上传则不进行再次上传
      return
    }
    // 老师控制学生摄像头开关
    if (ircType === 'user_video_mute') {
      emitter.emit('TeacherSwitchCamera', !content.data.pub)
      return
    }
    // 老师控制学生麦克风开关
    if (ircType === 'user_audio_mute') {
      const status = !content.data.pub
      emitter.emit('TeacherSwitchMicrophone', status)

      return
    }
    // 拍一拍提醒
    if (ircType === 'user_nudge_remind') {
      emitter.emit('userNudgeRemind', content.data)
      return
    }
  }
  const onNetStatusChanged = res => {
    IRCLog.info('ircMessageLog  onNetStatusChanged', res)
    // @log-ignore
    handleNetStatusChanged(res)
    // chatbox
    emitter.emit('chatboxNetStatusChanged', res)
  }
  const onKickoutNotice = res => {
    IRCLog.error('ircMessageLog  onKickoutNotice', res)
    handleKickoutNotice(res)
  }
  const onGetRoomHistoryMessageResponse = res => {
    IRCLog.info('ircMessageLog  onGetRoomHistoryMessageResponse', res)
    emitter.emit('chatHistoryMsg', res)
  }
  const onSendRoomMessageResponse = res => {
    IRCLog.info('ircMessageLog  onSendRoomMessageResponse', res)
    emitter.emit('chatGroupMsgRes', res)
  }
  const ircEventMap = {
    onRecvRoomDataUpdateNotice, // 监听kv消息
    onRecvRoomMessage, // 监听群聊消息
    onRecvPeerMessage, // 监听私聊消息
    onNetStatusChanged, // 监听IRC网络状态变化
    onKickoutNotice, // 监听被踢下线
    onGetRoomHistoryMessageResponse, // 监听获取历史消息
    onSendRoomMessageResponse // 群聊发送回调
  }
  const addIrcListener = () => {
    for (const key in ircEventMap) {
      SignalService.on(key, ircEventMap[key])
    }
  }
  const removeIrcListener = () => {
    for (const key in ircEventMap) {
      SignalService.off(key, ircEventMap[key])
    }
  }
  const addIrcHandle = () => {
    emitter.on('sendRaiseHand', () => {
      const opts = {
        roomList: store.baseData.commonOption.roomlist,
        content: {
          ircType: 'raise_hand'
        },
        chatMsgPriority: 1
      }
      SignalService.sendRoomMessage(opts)
    })
  }
  const removeIrcHandle = () => {
    emitter.off('sendRaiseHand')
  }
  return {
    addIrcListener,
    removeIrcListener,
    addIrcHandle,
    removeIrcHandle
  }
}
export default useIrcEvent
