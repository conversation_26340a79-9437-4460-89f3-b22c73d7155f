import { computed } from 'vue'
import { useClassData } from '@/stores/classroom'
import { emitter } from '@/hooks/useEventBus'
const sendLogger = msg => {
  console.log('useRemoteAudioStatus', msg)
}
export function useRemoteAudioStatus() {
  const store = useClassData()
  const remoteAudioStatus = computed(() => store.remoteAudioStatus)

  const remoteAudioStatusHandle = status => {
    store.updateRemoteAudioStatus(status)

    sendLogger(`收到语音管理状态变化, status: ${status}`)
  }
  const closeStudentAudioHandle = () => {
    if (!remoteAudioStatus.value) {
      sendLogger('进课默认静音其他人')
      // audioClose()
    } else {
      sendLogger('irc已先触发开启语音', 'warn')
    }
  }
  const listenRemoteAudioEvent = () => {
    // @log-ignore
    emitter.on('remoteAudioStatus', remoteAudioStatusHandle)
    // 本地加入房间后触发，默认静音其他人
    emitter.on('closeStudentAudio', closeStudentAudioHandle)
  }
  const removeRemoteAudioEvent = () => {
    emitter.off('remoteAudioStatus', remoteAudioStatusHandle)
    // 本地加入房间后触发，默认静音其他人
    emitter.off('closeStudentAudio', closeStudentAudioHandle)
  }

  return {
    listenRemoteAudioEvent,
    removeRemoteAudioEvent
  }
}
