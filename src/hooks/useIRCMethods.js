import { emitter } from './useEventBus'
import { useClassData } from '@/stores/classroom'
// import { checkCourseWareIdChange } from 'utils/courses'

/**
 * IRC网络状态上报信息映射
 */
export const ircNetStatis = {
  // 0-未知；1-网络不可用；2-服务器连接失败；3-服务器连接中；4-服务器连接成功；5-服务器断开连接
  0: 'Unkown',
  1: 'Unavailable',
  2: 'ServerFailed',
  3: 'Connecting',
  4: 'Connection successful',
  5: 'Disconnect'
}

export function useIRCMethods() {
  const store = useClassData()
  /**
   * 处理IRC网络状态变化
   */
  function handleNetStatusChanged(res) {
    // @log-ignore
    const status = res.netStatus
    store.updateIrcNetStatus(status)
    // const msg = ircNetStatis[status] || ''
    // 上报状态日志
    // sendLogger(msg)
  }
  /**
   * 处理被踢下线
   */
  function handleKickoutNotice(res) {
    if (res.code === 301) {
      emitter.emit('live-kickout', true)
      // sendLogger('账号被踢下线', { code: res.code }, 'error')
    }
  }

  function handleClassMode(key, noticeContent) {
    let content = noticeContent
    if (typeof noticeContent !== 'object') {
      content = {
        [key]: noticeContent
      }
    }
    content
    // 没有多语言
    // checkCourseWareIdChange(
    //   key,
    //   content,
    //   '课件异常，请退出后重新进入教室',
    //   '确认'
    // )
  }
  function handleVideoBetStudent(noticeContent) {
    emitter.emit('remoteAudioStatus', noticeContent.pub)
  }
  function handleEmoticon(stuId, params) {
    const setEmojiList = {
      emoticonName: params.name,
      emoticonType: params.type, // 默认本地图片表情
      lottieUrl: params.lottieUrl,
      emojiId: params.emojiId
    }
    store.changeStudentInfo(stuId, setEmojiList)
  }
  // // 反馈上传
  // const handleFeedBackUpload = async content => {
  //   const baseData = proxy.$store.state.smallClass.baseData
  //   if (!content.studentIds.includes(baseData.stuInfo.id)) return
  //   const res = await screenshot({
  //     width: window.screen.width,
  //     height: window.screen.height,
  //     feedback: true
  //   })
  //   const screenshotObj = await base64ToUrl(res, 'image/jpeg')
  //   console.log('截图', screenshotObj)
  //   const uploadUrl = screenshotObj.url
  //   sendLogger(
  //     '课中反馈上报截图',
  //     {
  //       uploadUrl
  //     },
  //     uploadUrl ? 'info' : 'error'
  //   )
  //   if (!uploadUrl) return
  //   feedbackUpdate({
  //     screenshot: uploadUrl,
  //     planId: baseData.commonOption.planId,
  //     feedbackId: content.feedbackId
  //   })
  //     .then(res => {
  //       if (res.code === 0) {
  //         // 发送成功
  //         sendLogger('课中反馈上报成功', {
  //           screenshot: uploadUrl,
  //           planId: baseData.commonOption.planId,
  //           feedbackId: content.feedbackId
  //         })
  //         uploadLogs()
  //       } else {
  //         // 发送失败
  //         console.error('反馈失败')
  //         sendLogger(
  //           '课中反馈上报失败',
  //           {
  //             screenshot: uploadUrl,
  //             planId: baseData.commonOption.planId,
  //             feedbackId: content.feedbackId
  //           },
  //           'error'
  //         )
  //       }
  //     })
  //     .catch(err => {
  //       console.error(err)
  //       sendLogger(
  //         '课中反馈上报失败',
  //         {
  //           screenshot: uploadUrl,
  //           planId: baseData.commonOption.planId,
  //           feedbackId: content.feedbackId
  //         },
  //         'error'
  //       )
  //     })
  //   uploadImages()
  // }
  // const uploadLogs = async () => {
  //   const { uid } = await getUserInfo()
  //   const logsUpload = new LogsUpload({
  //     userId: uid
  //   })
  //   logsUpload.upload()
  // }
  // const uploadImages = async () => {
  //   const { uid } = await getUserInfo()
  //   const imgUpload = new ImgUpload({
  //     userId: uid
  //   })
  //   imgUpload.upload()
  // }
  return {
    handleNetStatusChanged,
    handleKickoutNotice,
    handleClassMode,
    handleVideoBetStudent,
    handleEmoticon
    // handleFeedBackUpload
  }
}
