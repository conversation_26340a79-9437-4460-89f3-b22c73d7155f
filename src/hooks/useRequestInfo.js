import Cookies from 'js-cookie'
import { emitter } from './useEventBus'
import { useBaseData } from '@/stores/baseData'
let firstToken = ''

const useRequestInfo = () => {
  const getToken = () => {
    const currentToken = Cookies.get('_official_token') || ''

    // 如果 firstToken 为空字符串，说明是第一次设置
    if (firstToken === '') {
      firstToken = currentToken
    }
    // 如果当前 token 与第一次记录的 token 不同，触发事件
    else if (currentToken !== firstToken) {
      emitter.emit('globalLoginOrRefresh', true)
      // emitter.emit('toLogin', false)
    }
    return currentToken
  }
  const setFirstToken = val => {
    firstToken = val
  }
  const getSchoolCode = () => {
    const store = useBaseData()
    return store.schoolCode
  }
  const getTimezone = () => {
    const store = useBaseData()
    // 如果 取不到时区的值，那么 getTimezone 为空，应该接口会默认走 分校默认的时区逻辑吧
    // 但是新的时间格式会受这个影响， todo 待产品给出 分校和默认时区
    return Cookies.get('_official_timezone') || store.timezone || ''
  }
  const getLanguage = () => {
    const store = useBaseData()
    const languageMap = {
      uk: 'en-GB',
      sg: 'en',
      us: 'en-US',
      hk: 'zh-HK',
      fr: 'fr-FR',
      jp: 'jp-JP',
      cn: 'zh-CN',
      ca: 'en-CA',
      au: 'en-AU',
      my: 'ms-MY',
      bmus: 'en-US',
      bmsg: 'en',
      ae: 'en',
      am: 'en',
      tc: 'en',
      tmc: 'zh-CN'
    }
    const schoolShort = store.locale
    return languageMap[schoolShort]
  }

  return {
    getToken,
    getSchoolCode,
    getTimezone,
    getLanguage,
    setFirstToken
  }
}

export { useRequestInfo }
