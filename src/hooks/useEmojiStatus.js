import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { emitter } from './useEventBus'
export function useEmojiStatus() {
  const { t } = useI18n()
  let isEmojiActived = ref(false) // emoji开关状态，默认关闭
  let isEmojiClose = ref(false) // emoji是否被禁用，默认未禁用
  let emojiCount = ref(0) // emoji发送次数
  let isShowEmojiCloseTip = ref(false) // 是否展示emoji禁用弹窗
  let emojiCloseTipTimer = null // emoji禁用弹窗计时器
  const emojiCloseTipText = computed(() => {
    return t('classroom.smallClass.sendEmoji.forbiddenSendMessage')
  })

  const EMOJI_TIP_TIME = 3 // emoji禁用弹窗展示时间 单位秒
  const EMOJI_COUNT = 3 // emoji触发冷却的次数
  const EMOJI_COOLING_TIME = 30 // emoji冷却时间 单位秒

  let isEmojiCooling = ref(false) // emoji是否该触发冷却状态，默认未触发
  let emojiTimer = null // emoji冷却计时器
  let isShowEmojiCoolingTip = ref(false) // 是否展示emoji冷却弹窗
  let emojiCoolingTipTimer = null // emoji冷却弹窗计时器
  let emojiCoolingCountdownTimer = null // emoji冷却倒计时计时器
  let emojiCoolingCountdownNum = ref(30) // emoji冷却倒计时
  const emojiCoolingTipText = computed(() => {
    return t('classroom.smallClass.sendEmoji.sendTooFast', {
      num: emojiCoolingCountdownNum.value,
    })
  })

  onMounted(() => {
    // 接收emit
    emitter.on('forbidden_student_emoji', (data) => {
      if (data.pub) {
        openEmojiClose()
      } else {
        resetEmojiClose()
      }
      // 不管是禁用还是解禁都重置冷却状态
      resetEmojiCooling()
    })
    emitter.on('sendEmoji', () => {
      handleEmojiSended()
    })
  })
  onBeforeUnmount(() => {
    // 注销emit
    emitter.off('forbidden_student_emoji')
    emitter.off('sendEmoji')
  })
  // 点击emoji icon
  function handleEmojiSwitch() {
    // 是否关闭emoji
    if (isEmojiClose.value) {
      // 展示禁用弹窗,如果已经提示了则不再提示
      if (isShowEmojiCloseTip.value) return
      openEmojiCloseTip()
      return
    }
    // 是否触发冷却
    if (isEmojiCooling.value) {
      // 展示冷却弹窗,如果已经提示了则不再提示
      if (isShowEmojiCoolingTip.value) return
      openEmojiCoolingTip()
      return
    }
    if (isEmojiActived.value) {
      closeEmojiPane()
      return
    } else {
      openEmojiPane()
    }
  }

  // 发送表情之后校验是否触发冷却,并关闭关闭emoji面板
  function handleEmojiSended() {
    closeEmojiPane()
    emojiCount.value++
    if (emojiCount.value >= EMOJI_COUNT) {
      if (!isEmojiCooling.value) {
        openEmojiCooling()
      }
      return
    }
  }
  // 重置emoji冷却状态
  function resetEmojiCooling() {
    isEmojiCooling.value = false
    emojiCount.value = 0
    emojiCoolingCountdownNum.value = 30
    if (emojiCoolingCountdownTimer) clearInterval(emojiCoolingCountdownTimer)
    if (emojiTimer) clearTimeout(emojiTimer)
  }
  // 触发emoji冷却状态
  function openEmojiCooling() {
    isEmojiCooling.value = true
    emojiCoolingCountdownTimer = setInterval(() => {
      emojiCoolingCountdownNum.value--
    }, 1000)
    emojiTimer = setTimeout(() => {
      resetEmojiCooling()
    }, EMOJI_COOLING_TIME * 1000)
  }
  // 显示emoji冷却弹窗
  function openEmojiCoolingTip() {
    isShowEmojiCoolingTip.value = true
    emojiCoolingTipTimer = setTimeout(() => {
      closeEmojiCoolingTip()
    }, EMOJI_TIP_TIME * 1000)
  }
  // 关闭emoji冷却弹窗
  function closeEmojiCoolingTip() {
    isShowEmojiCoolingTip.value = false
    if (emojiCoolingTipTimer) clearTimeout(emojiCoolingTipTimer)
  }

  // 打开emoji禁用tip
  function openEmojiCloseTip() {
    isShowEmojiCloseTip.value = true
    emojiCloseTipTimer = setTimeout(() => {
      closeEmojiCloseTip()
    }, 3000)
  }
  // 关闭emoji禁用tip
  function closeEmojiCloseTip() {
    isShowEmojiCloseTip.value = false
    if (emojiCloseTipTimer) clearTimeout(emojiCloseTipTimer)
  }
  // 打开emoji禁用状态
  function openEmojiClose() {
    isEmojiClose.value = true
    closeEmojiPane()
  }
  // 重置emoji禁用状态
  function resetEmojiClose() {
    isEmojiClose.value = false
    closeEmojiCloseTip()
  }
  // 关闭表情面板
  function closeEmojiPane() {
    isEmojiActived.value = false
    emitter.emit('openEmojiPane', false)
  }
  // 打开表情面板
  function openEmojiPane() {
    isEmojiActived.value = true
    emitter.emit('openEmojiPane', true)
  }

  return {
    handleEmojiSwitch,
    isEmojiActived,
    isEmojiCooling,
    isShowEmojiCoolingTip,
    emojiCoolingTipText,
    isEmojiClose,
    isShowEmojiCloseTip,
    emojiCloseTipText,
  }
}
