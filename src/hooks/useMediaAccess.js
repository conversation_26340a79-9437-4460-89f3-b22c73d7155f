import { ref } from 'vue'
import { useRtcService } from '@/hooks/useRtcService'
export function useMediaAccess() {
  const cameraAccess = ref(true)
  const { getRtcService } = useRtcService()
  async function getCameraAccess() {
    const RtcService = getRtcService()
    console.info('调用getCameraAccess方法')
    const cameraAccessStatus = await RtcService.checkCameraPermission()
    console.info('调用getCameraAccess方法，cameraAccessStatus：', cameraAccessStatus)
    if (!cameraAccessStatus) {
      console.warn('摄像头没有授权')
      cameraAccess.value = false
    } else {
      cameraAccess.value = true
    }
  }
  const microphoneAccess = ref(false)
  async function getMicrophoneAccess() {
    const RtcService = getRtcService()
    const microphoneAccessStatus = await RtcService.checkMicPermission()
    if (!microphoneAccessStatus) {
      console.warn('麦克风没有授权')
      microphoneAccess.value = false
    } else {
      microphoneAccess.value = true
    }
  }

  return {
    cameraAccess,
    microphoneAccess,
    getCameraAccess,
    getMicrophoneAccess
  }
}
