import { onBeforeUnmount, onMounted, computed } from 'vue'
import { getValueByCloudConfigKey } from '@/utils/initConfig'
import { useClassData } from '@/stores/classroom'
import { emitter } from '@/hooks/useEventBus'
import { useRtcService } from '@/hooks/useRtcService'

export const useVolume = () => {
  const onstageVolume = Number(getValueByCloudConfigKey('onStageVolume', 'configValue')) || 100
  const downStageVolume = Number(getValueByCloudConfigKey('downStageVolume', 'configValue')) || 50
  const { getRtcService } = useRtcService()

  console.log('云控音量', onstageVolume, downStageVolume)
  const store = useClassData()
  // 降低其他同学音量
  const studentList = computed(() => store.studentlistNoSelf)
  function reduceOtherVolume() {
    console.info('降低其他同学音量')
    // 循环所有学生
    studentList.value.forEach(item => {
      adjustVolume(downStageVolume, item.userId)
      console.info('降低其他同学音量', downStageVolume, item.userId)
    })
  }

  // 调整音量
  function adjustVolume(volume, uid) {
    console.info('调整音量:', volume, uid)
    const RtcService = getRtcService()
    RtcService.setRemoteVoiceVolume(volume, uid)
  }
  function onLinkAdjustVolume({ v, uid }) {
    console.info('上下台调整学员声音音量:', v, uid)
    if (v === 'up') {
      adjustVolume(onstageVolume, uid)
      return
    }
    if (v === 'down') {
      adjustVolume(downStageVolume, uid)
      return
    }
  }

  onMounted(() => {
    // 上台或者私聊的时候会触发
    emitter.on('onlink_adjust_volume', onLinkAdjustVolume)
  })

  onBeforeUnmount(() => {
    emitter.off('onlink_adjust_volume', onLinkAdjustVolume)
  })
  return {
    reduceOtherVolume
  }
}
