import { useMediaAccess } from './useMediaAccess'
import { computed, ref } from 'vue'
import { useClassData } from '@/stores/classroom'
import { useRtcService } from '@/hooks/useRtcService'
import { emitter } from './useEventBus'
export function useCameraStatus() {
  const store = useClassData()
  const { getRtcService } = useRtcService()
  const cameraStatus = computed(() => !store.selfInfo.videoMute)
  const { cameraAccess, getCameraAccess } = useMediaAccess()
  const cameraOthersStatus = ref(false)
  async function handleOthersCameraSwitch(status) {
    cameraOthersStatus.value = status
    emitter.emit('focus_mode', status)
  }
  async function handleCameraSwitch(status) {
    const RtcService = getRtcService()
    if (status) {
      await getCameraAccess()
      if (!cameraAccess.value) {
        // 权限校验弹窗
        emitter.emit('showMediaSecurityAccess', 'camera')
        return
      }
    }

    store.changeStudentInfo(0, { videoMute: !status })

    RtcService.muteLocalVideo(!status)
  }
  const openCameraHandle = () => {
    handleCameraSwitch(true)
  }
  async function listenCameraEvent() {
    // 监听摄像头状态
    emitter.on('openCamera', openCameraHandle)
    emitter.on('TeacherSwitchCamera', handleTeacherCameraSwitch)
  }

  async function removeCameraEvent() {
    emitter.off('openCamera', openCameraHandle)
    emitter.off('TeacherSwitchCamera', handleTeacherCameraSwitch)
  }

  function handleTeacherCameraSwitch(status) {
    // 主讲老师关闭摄像头
    if (status) {
      // 展示是否允许老师打开摄像头弹窗
      emitter.emit('showCameraAllowConfirm')
    } else {
      handleCameraSwitch(false)
    }
  }

  return {
    handleCameraSwitch,
    handleTeacherCameraSwitch,
    removeCameraEvent,
    listenCameraEvent,
    cameraStatus,
    cameraOthersStatus,
    handleOthersCameraSwitch
  }
}
