import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { emitter } from '@/hooks/useEventBus'

export function useMicroPhoneTip() {
  const { t } = useI18n()
  const isShowSpeakGuide = ref(false)
  // 引导提示
  const isShowSpeakGuideHandle = async () => {
    const hasShow = localStorage.getItem('speakHasShow')
    if (!hasShow) {
      isShowSpeakGuide.value = true
    } else {
      console.info('已经展示过提示')
    }
  }
  const closeSpeakGuideHandle = async () => {
    if (isShowSpeakGuide.value) {
      console.info('关闭说话引导')
      isShowSpeakGuide.value = false
      localStorage.setItem('speakHasShow', true)
    }
  }

  // 当前显示tip类型
  const currentTipType = ref(0)
  // tip显示计时器
  const tipTimer = ref(null)
  const tipTypeMap = {
    SPEAKING: 1,
    SPEAKING_SHORT: 2,
    SPEAK_TYPE_CHANGE: 3
  }

  const clearTipTimer = () => {
    if (tipTimer.value) {
      clearTimeout(tipTimer.value)
      tipTimer.value = null
    }
  }
  const createTimer = (time = 3000) => {
    tipTimer.value = setTimeout(() => {
      currentTipType.value = 0
      tipTimer.value = null
    }, time)
  }
  const showSpeakTip = tipType => {
    clearTipTimer()
    currentTipType.value = tipType
    console.info('显示提示类型', tipType)
    if (tipType !== tipTypeMap.SPEAKING) {
      createTimer()
    }
  }

  // 说话时间太短判断
  const keyDownUpChangeTimer = ref(null)
  const keyEventChangeHandle = action => {
    if (action === 'down') {
      if (keyDownUpChangeTimer.value) {
        clearTimeout(keyDownUpChangeTimer.value)
        keyDownUpChangeTimer.value = null
      }
      // 200ms未触发抬起，清空计时器，显示正在说话提示
      keyDownUpChangeTimer.value = setTimeout(() => {
        keyDownUpChangeTimer.value = null
        // 显示正在说话提示
        showSpeakTip(tipTypeMap.SPEAKING)
      }, 200)
    } else if (action === 'up') {
      // 按下时间不足200ms，清空计时器，显示说话时间太短提示
      if (keyDownUpChangeTimer.value) {
        console.info('说明是按住空格键不足200ms,触发提示')
        clearTimeout(keyDownUpChangeTimer.value)
        keyDownUpChangeTimer.value = null
        // 显示说话时间太短提示
        showSpeakTip(tipTypeMap.SPEAKING_SHORT)
      } else {
        console.info('说明是按住空格键超过200ms抬起')
        if (currentTipType.value === tipTypeMap.SPEAKING) {
          // 抬起时，如果当前是正在说话提示，则关闭
          currentTipType.value = 0
          clearTipTimer()
        }
      }
    }
  }
  const showAudioMuteText = ref('')
  const speakTypeChangeTip = type => {
    console.info('收到老师改变麦克风方式提示', type)
    if (keyDownUpChangeTimer.value) {
      // 收到老师改变麦克风方式提示，清空按住空格键计时器
      clearTimeout(keyDownUpChangeTimer.value)
      keyDownUpChangeTimer.value = null
    }
    clearTipTimer()
    if (type == 1) {
      showAudioMuteText.value = t('liveroomKeepMuteModeToast')
    } else if (type == 2) {
      showAudioMuteText.value = t('liveroomKeepMicOnModeToast')
    } else if (type == 3) {
      showAudioMuteText.value = t('liveroomPushToTalkModeToast')
    } else if (type == 4) {
      showAudioMuteText.value = t('老师已将麦克风设置为自由发言模式')
    }
    showSpeakTip(tipTypeMap.SPEAK_TYPE_CHANGE)
  }

  onMounted(() => {
    emitter.once('showSpeakGuide', isShowSpeakGuideHandle)
    emitter.on('closeSpeakGuide', closeSpeakGuideHandle)
    emitter.on('keyDownUpChange', keyEventChangeHandle)
    emitter.on('all_audio_mute_status', speakTypeChangeTip)
  })
  onBeforeUnmount(() => {
    emitter.off('showSpeakGuide', isShowSpeakGuideHandle)
    emitter.off('closeSpeakGuide', closeSpeakGuideHandle)
    emitter.off('keyDownUpChange', keyEventChangeHandle)
    emitter.off('all_audio_mute_status', speakTypeChangeTip)

    clearTipTimer()
    if (keyDownUpChangeTimer.value) {
      clearTimeout(keyDownUpChangeTimer.value)
      keyDownUpChangeTimer.value = null
    }
  })
  return {
    isShowSpeakGuide,
    closeSpeakGuideHandle,
    showAudioMuteText,
    tipTypeMap,
    currentTipType
  }
}
