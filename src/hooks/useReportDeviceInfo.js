import { pushStudentDeviceInfo } from '@/api/classroom/index'

const pushStudentDeviceInfoHandle = (baseData, reportChannel) => {
  let workAreaSize = ''
  // 上传位置 enterroom quitroom
  let params = {
    planId: baseData.planId,
    // 屏幕分辨率
    resolution: workAreaSize,
    // 频道信息
    ircName: baseData?.configs?.stuIrcId,
    ircChannel: baseData?.configs?.ircServer?.ircLocation,
    // 上传位置 enterroom quitroom
    reportChannel: reportChannel,
    extra: ''
  }
  pushStudentDeviceInfo(params)
    .then(res => {
      if (res.code !== 0) {
        console.error('pushStudentDeviceInfo error', res)
      }
    })
    .catch(err => {
      console.error('pushStudentDeviceInfo error', err)
    })
}

export const enterroomPushStudentDeviceInfoHandle = baseData => {
  pushStudentDeviceInfoHandle(baseData, 'enterroom')
}

export const quitroomPushStudentDeviceInfoHandle = baseData => {
  pushStudentDeviceInfoHandle(baseData, 'quitroom')
}
