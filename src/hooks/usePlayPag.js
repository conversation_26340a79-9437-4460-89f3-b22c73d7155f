export function usePlayPag() {
  async function initPag(data) {
    if (!window.PAG) {
      window.PAG = await window.libpag.PAGInit()
    }
    const url = data.path ? new URL(`../assets/pagfiles/GrowthHandbook/${data.path}.pag`, import.meta.url).href : data.absolutePath
    const buffer = await fetch(url).then(response => response.arrayBuffer())
    const pagFile = await window.PAG.PAGFile.load(buffer)
    // 文字替换
    if (data.replaceTextArr) {
      data.replaceTextArr.forEach(async item => {
        const textData = await pagFile.getTextData(item.index)
        textData.text = item.text
        await pagFile.replaceText(item.index, textData)
      })
    }
    // 图片替换
    if (data.replaceImgArr) {
      data.replaceImgArr.forEach(async item => {
        const image = await new Promise(resolve => {
          const img = new Image()
          img.onload = () => resolve(img)
          img.src = item.url
        })
        if (!image) return
        const pagImage = await window.PAG.PAGImage.fromSource(image)
        pagFile.replaceImage(item.index, pagImage)
      })
    }
    return pagFile
  }
  let pagView = null
  /**
   *
   * @param {*} path 资源路径
   * @param {*} id canvas dom id
   * @param {*} repeatCount 是否循环播放
   * @param {*} replaceTextArr 文字替换
   */
  async function initAndPlayPag(data, callback) {
    const pagFile = await initPag(data)
    if (pagView) {
      await pagView.destroy()
      pagView = null
    }

    pagView = await window.PAG.PAGView.init(pagFile, data.domId)
    if (callback) {
      pagView.addListener('onAnimationEnd', () => {
        callback()
      })
    }
    data.repeatCount && pagView.setRepeatCount(0)
    pagView.play()
    return pagView
  }
  async function playMultiplePag(data, callback, callbackStart) {
    const pagFile = await initPag(data)
    let pagView = await window.PAG.PAGView.init(pagFile, data.domId)
    // Get audio data.
    const audioBytes = pagFile.audioBytes()
    let audioEl = null
    if (audioBytes && audioBytes.byteLength > 0) {
      audioEl = document.createElement('audio')
      audioEl.preload = 'auto'
      const blob = new Blob([audioBytes], { type: 'audio/mp3' })
      audioEl.src = URL.createObjectURL(blob)
    }
    pagView.addListener('onAnimationStart', () => {
      audioEl && audioEl.play()
      callbackStart && callbackStart()
    })
    pagView.addListener('onAnimationEnd', () => {
      if (audioEl) {
        audioEl.pause()
        audioEl = null
      }
      callback && callback()
    })
    pagView.addListener('onAnimationCancel', () => {
      if (audioEl) {
        audioEl.pause()
        audioEl = null
      }
    })

    data.repeatCount && pagView.setRepeatCount(0)
    await pagView.play()
    const destroyPagView = () => {
      if (audioEl) {
        audioEl.pause()
        audioEl = null
      }
      if (pagView) {
        pagView.destroy()
        pagView = null
      }
    }
    return destroyPagView
  }

  return {
    initAndPlayPag,
    playMultiplePag
  }
}
