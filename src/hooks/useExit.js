import { getCurrentInstance } from 'vue'
import { Modal } from 'ant-design-vue' // 导入 Modal
import logger from '@/utils/logger'
import { px2vh } from '@/utils/util'
import { useRouter } from 'vue-router'
import { useClassData } from '@/stores/classroom'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'

// import { setPlanId } from 'plugins/sensors'
// import { route_href } from 'utils/routeUtil'

export function useExit(planInfo) {
  const router = useRouter() // 在这里调用，而不是在回调函数中

  const { proxy } = getCurrentInstance()
  // console.log('@@@@proxy', proxy)
  const curBackUrl = window.location.href.split('backUrl=')[1]

  let queryObject = {}
  const path = curBackUrl.split('?')[0] || '/home'
  if (curBackUrl) {
    // 拿到当前 URL 的查询部分
    let rawQuery = curBackUrl.split('?')[1]

    // 再次解码整个查询字符串
    rawQuery = decodeURIComponent(rawQuery)

    // 手动将解码后的查询字符串拆解成对象
    rawQuery.split('&').forEach(param => {
      const [key, value] = param.split('=')
      queryObject[key] = value
    })

    console.log(queryObject)
  }

  /**
  @疑问  lessonType = typeMap[query['lessonType']] || '' 这个lessonType我临时修改了const lessonType = ''
  @答 lessonType 是一个枚举值： 正式课、回放、旁听、临时
  @解答人 未知
  @问题创建人 白海禄
  @创建时间 2025-05-19 14:56:07
  */

  // const lessonType = typeMap[query['lessonType']] || ''

  const typeMap = {
    FORMAL: '正式课',
    PLAYBACK: '回放',
    AUDITION: '旁听课',
    TEMPORARY: '临时课'
  }
  const lessonType = typeMap.FORMAL

  /**处理返回事件*/
  const handleExit = classOverInfo => {
    console.log(classOverInfo, 'classOverInfo--------')
    localStorage.setItem('smallClassTestCoverage', JSON.stringify(window.__coverage__))
    Modal.confirm({
      class: 'modal-simple apu-header exit-header',
      content: proxy.$t('classroom.modules.header.backConfirm.content'),
      okText: proxy.$t('common.yes'),
      cancelText: proxy.$t('common.no'),
      width: px2vh(416),
      centered: true,
      zIndex: 500000000,
      onOk: () => {
        console.log('点击了确定按钮')
        const nowTimestamp = Math.floor(new Date().getTime() / 1000)

        /**
        @疑问 神策集成，这里需要修改
        @答 未确认
        @解答人 未知
        @问题创建人 白海禄
        @创建时间 2025-05-19 14:59:14
        @是否解决 未解决
        */
        // classLiveSensor.sendExitOrRefreshSensor('click_exit_btn', {
        //   timeOffset: proxy.$store?.state?.common?.timeOffset,
        //   planInfo,
        //   lessonType: lessonType,
        //   interactionStatus: proxy.$store.state.smallClass.interactionStatus,
        //   classTypeName: '小班'
        // })

        const store = useClassData()

        classLiveSensor.osta_exit_classroom(
          store.baseData.commonOption.timeOffset,
          planInfo?.endStampTime
        )

        /**
        @疑问  setPlanId({}) 这个函数好像是设置shence全局变量的的
        @答 未确认
        @解答人 未知
        @问题创建人 白海禄
        @创建时间 2025-05-19 14:14:46
        @是否解决 未解决
        */
        // setPlanId({})

        logger.send({
          tag: 'student.Quit',
          content: {
            msg: '学生退出课堂'
          }
        })
        console.log('router', router)
        router.push({
          path: path,
          query: {
            ...queryObject
          }
        })
      }
    })
  }
  return {
    handleExit
  }
}
