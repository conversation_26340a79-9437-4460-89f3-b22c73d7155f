import { useRequestInfo } from '@/hooks/useRequestInfo'
import { ref, reactive, onMounted } from 'vue';
import { getNpsStatus } from '@/api/home/<USER>';
const { getLanguage } = useRequestInfo()

export function useNps(route, router) {
  // 响应式数据
  const showNpsModal = ref(false); // 控制弹窗显示
  const planId = ref('');          // 当前 Plan ID
  const classId = ref('');         // 当前 Class ID
  const tagList = reactive({});    // 标签列表

  // 处理 NPS 状态逻辑
  const handleNpsStatus = async () => {
    const { nps = 0, ...query } = route.query; // 解构路由参数
    console.log('NPS 参数:', route, nps, query);

    if (+nps) {
      // 替换路由，去掉 nps 参数
      router.replace({
        path: route.path,
        query,
      });

      try {
        const language = getLanguage(); // 获取语言信息
        const { data } = await getNpsStatus({
          planId: +query.planId,
          classId: +query.classId,
          languageType: language,
        });

        if (data && data.pcStatus) {
          planId.value = query.planId; // 设置 Plan ID
          classId.value = query.classId; // 设置 Class ID
          // todo 改一下啊看看能不能通
          showNpsModal.value = data.pcStatus === 1; // 判断是否显示弹窗
          Object.assign(tagList, data.tagList || {}); // 设置 Tag 列表
        }
      } catch (error) {
        console.error('获取 NPS 状态失败:', error);
      }
    }
  };

  // 挂载时调用处理逻辑
  onMounted(handleNpsStatus);

  // 返回响应式数据和方法，以 Hook 形式暴露
  return {
    showNpsModal,
    planId,
    classId,
    tagList,
    handleNpsStatus,
  };
}
