import { ref, onBeforeUnmount } from 'vue';
import { useRouter } from 'vue-router';
// import logger from '../utils/logger';
import { getUrlParam } from '@/utils/util'

export function useBindEvent() {
  const router = useRouter();

  // 响应式状态
  const isSendLogger = ref(false);
  const showExamIframe = ref(true);
  const examUrl = ref('');
  const backUrl = ref('');
  const sendMessageDone = ref(false);

  // 处理消息事件
  const bindMessage = (e) => {
    let parseData = e.data || {};
    console.log('看下收到了什么呢', e)
    console.log('message', parseData);

    // 处理字符串类型的消息
    if (typeof parseData === 'string') {
      try {
        parseData = JSON.parse(parseData)
      } catch (error) {
        console.error('JSON 解析错误:', error);
        parseData = e.data;
      }
    }

    const { action, data } = parseData;

    // 退出作业事件
    if (action === 'jy-exit') {
      sendLoggers(
        'h5message',
        `退出作业事件:actionType: ${action}, parseData: ${JSON.stringify(parseData)}`
      );
      // todo 这里是修改的，h5相关的回退都是 回退到上一页看下有问题不
      router.back()

      // if (isBeforeClassTest()) {
      //   router.push(backUrl.value);
      // }
    }

    // 提交作业事件
    if (action === 'jy-submit') {
      sendLoggers(
        'h5message',
        `提交作业事件:actionType: ${action}, parseData: ${JSON.stringify(
          parseData
        )}, sendMessageDone: ${sendMessageDone.value}`
      );
      if (!sendMessageDone.value) {
        submitHomeworkEvent(data?.state || 1, data?.type || 0);
      }
    }

    // 请求超时事件
    if (action === 'jy-overtime') {
      sendLoggers(
        'h5message',
        `请求超时事件:actionType: ${action}, parseData: ${JSON.stringify(parseData)}`
      );
      // todo 这里是修改的，h5相关的回退都是 回退到上一页看下有问题不
      router.back()
      // if (isBeforeClassTest()) {
      //   router.push(backUrl.value);
      // }
      // 这里需要替换为你的消息提示组件
      // useMessage().warning('Network error (or your account is signed in on another device)');
    }

    // 退出h5页面事件
    if (action === 'jy-close') {
      sendLoggers(
        'h5message',
        `退出h5页面事件:actionType: ${action}, parseData: ${JSON.stringify(parseData)}`
      );
      // todo 这里是修改的，h5相关的回退都是 回退到上一页看下有问题不
      // router.back()

      console.log('看下退出', backUrl.value)
      // todo 这里是修改的，h5相关的回退都是 回退到上一页看下有问题不
      router.back()
      // if (isBeforeClassTest()) {
      //   router.push(backUrl.value);
      // }
    }

    // 提交作业接口响应时长
    if (action === 'hwApiResponse') {
      const homeworkId = getUrlParam('homeworkId');
      const classId = getUrlParam('classId');
      const homeworkSubmit = {
        homeworkId,
        homeworkClassId: classId,
        homeworkApiPath: data?.path,
        homeworkResponseTime: data?.responseTime,
        homeworkStatus: data?.status
      };
      sendLoggers(
        'h5message',
        `提交作业接口响应时长:actionType: ${action}, parseData: ${JSON.stringify(
          parseData
        )},homeworkSubmit:${JSON.stringify(homeworkSubmit)}`
      );
    }

    // 上传图片上传时长
    if (action === 'hwUploadImg') {
      const homeworkId = getUrlParam('homeworkId');
      const classId = getUrlParam('classId');
      const homeworkUpload = {
        homeworkId,
        homeworkClassId: classId,
        homeworkUploadDuration: data?.uploadDuration,
        homeworkType: data?.uploadType,
        homeworkUploadStatus: data?.uploadStatus
      };
      sendLoggers(
        'h5message',
        `上传图片上传时长:actionType: ${action}, parseData: ${JSON.stringify(
          parseData
        )},homeworkUpload:${JSON.stringify(homeworkUpload)}`
      );
    }

    // 课中报告 监听h5交卷事件，通知主讲端
    if (action === 'completedExamSuccess') {
      sendSubmitExamToTeacher();
      sendLoggers(
        'h5message',
        `监听h5交卷事件:actionType: ${action}, parseData: ${JSON.stringify(parseData)}`
      );
    }

    // 课中报告 监听h5关闭事件
    if (action === 'dismissView' || action === 'hwHomeWorkRepeat') {
      showExamIframe.value = false;
      examUrl.value = ''; // 关闭iframe后清空url
      sendLoggers(
        'h5message',
        `监听h5关闭事件:actionType: ${action}, parseData: ${JSON.stringify(parseData)}`
      );
    }
  };

  // 绑定事件监听
  const bindEvent = () => {
    window.addEventListener('message', bindMessage, false);
  };

  // 发送日志
  const sendLoggers = (tag, msg) => {
    tag
    msg
    isSendLogger.value = true;
    // logger.send({
    //   tag,
    //   level: 'info',
    //   content: {
    //     msg
    //   }
    // });
  };

  // 判断是否是课前考试
  const isBeforeClassTest = () => {
    return !!router;
  };

  // 提交作业事件 (需要根据实际业务实现)
  const submitHomeworkEvent = (state, type) => {
    // 实现你的提交作业逻辑
    console.log('提交作业', { state, type });
    // ...
    sendMessageDone.value = true;
  };

  // 发送交卷事件给老师端 (需要根据实际业务实现)
  const sendSubmitExamToTeacher = () => {
    // 实现你的通知老师端逻辑
    console.log('通知老师端考试已提交');
    // ...
  };

  // 组件卸载时移除事件监听
  onBeforeUnmount(() => {
    window.removeEventListener('message', bindMessage);
  });

  // 暴露给组件使用的属性和方法
  return {
    isSendLogger,
    showExamIframe,
    examUrl,
    backUrl,
    sendMessageDone,
    bindEvent,
    bindMessage,
    sendLoggers,
    isBeforeClassTest,
    submitHomeworkEvent,
    sendSubmitExamToTeacher
  };
}
