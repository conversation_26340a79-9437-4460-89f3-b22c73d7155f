import './assets/main.css'
import './assets/antd-reset.scss'
import { i18n } from './locale/index.js'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import sensorsPlugin from '@/utils/sensorTrack/sensorsPlugin.js'
import { setDefaultApiDomain } from './utils/domainConfig'
import {
  Row,
  Col,
  Modal,
  message as Message,
  Checkbox,
  Button,
  Dropdown,
  Menu,
  Avatar,
  Switch,
  Radio,
  Progress,
  Carousel,
  Select,
  Input,
  Form,
  Upload,
  Tooltip,
  Steps,
  Rate
} from 'ant-design-vue'

const app = createApp(App)

// 注册组件
app
  .use(Row)
  .use(Col)
  .use(Modal)
  .use(Checkbox)
  .use(Button)
  .use(Dropdown)
  .use(Menu)
  .use(Avatar)
  .use(Switch)
  .use(Radio)
  .use(Progress)
  .use(Carousel)
  .use(Select)
  .use(Input)
  .use(Form)
  .use(Upload)
  .use(Tooltip)
  .use(Steps)
  .use(Rate)

// 全局挂载 Message 和 Modal
app.config.globalProperties.$message = Message
app.config.globalProperties.$Modal = Modal

app.use(createPinia())
app.use(router)

// 使用神策分析插件（可传入自定义配置参数）
app.use(sensorsPlugin, {
  // 可选项：覆盖默认配置
  // serverUrl: '自定义服务器URL',
  // isTrackSinglePage: true,
  // pageProps: { 自定义页面属性 }
})

setDefaultApiDomain()
app.use(i18n)

app.mount('#app')
