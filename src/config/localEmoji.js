/*
 * 表情符号映射关系配置
 * localImageName: 本地图片表情名称
 */
const localEmojiList = []
const emoticonConfig = [
  'smile',
  'XD',
  'LOL',
  'party',
  'the_rock',
  'thinking',
  'quiet',
  'pleading',
  'sad',
  'shame',
  'scream',
  'cry',
  'yeah',
  'like',
  'hands_up',
  'clap',
  'A',
  'B',
  'true',
  'false',
  'surprise',
  'heart',
  'unicorn',
  'ghost',
]
emoticonConfig.forEach((emoji) => {
  localEmojiList.push({
    name: `${`[${emoji}]`}`,
    type: 1,
  })
})
// 本地表情配置
const localEmojiConfig = {
  emojiPackageName: '', // 表情包名称
  emojiPackageId: 0, // 表情包id
  isAnimation: false, //是否是动态图
  isOver: '', //是否过期
  picture: '../assets/icon_emoji_native.png', //封面图
  overShow: '', //过期状态是否展示
  isLocal: true,
  content: [...localEmojiList],
}

export { localEmojiConfig }
