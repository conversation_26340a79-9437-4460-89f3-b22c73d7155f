// Ant Design Vue样式重写
button,
input,
label,
select {
  font-family: 'Helvetica', 'PingFang SC', 'sans-serif', 'Arial', 'Verdana', 'Microsoft YaHei';
}

// Modal样式重写
.ant-modal-mask {
  background-color: rgba(0, 0, 0, 0.3);
}

// 简洁的模态框
.modal-simple {
  // 不带"啊噗"的头部
  &.no-apu-header .ant-modal-body {
    padding: 35px 16px 0 16px;
    background-size: 100%;
  }

  &.no-apu-header .ant-modal-footer {
    padding: 32px 16px 16px 16px;

    .ant-btn {
      width: 147px;
      height: 48px;
      border-radius: 24px;
      -webkit-border-radius: 24px;
      -moz-border-radius: 24px;
      -ms-border-radius: 24px;
      -o-border-radius: 24px;
    }

    button + button {
      margin: 0;
    }

    div {
      display: flex;
      justify-content: space-between;

      button {
        font-size: 16px;
      }
    }
  }

  // 带有"阿噗"的图片头
  &.apu-header .ant-modal-body {
    padding-top: 160px;
    background: url('@/assets/images/bg-4.png') no-repeat top center;
    background-size: 100%;
  }

  &.exit-header .ant-modal-body {
    padding-top: 30.7%;
  }

  // 白色关闭按钮
  &.close-color-white {
    .ant-modal-close {
      color: rgba(255, 255, 255, 0.8);

      &:hover {
        color: rgba(255, 255, 255, 10);
      }
    }
  }

  .ant-modal-header {
    border-color: #f4f6fa;
    border-bottom: none;
  }

  .ant-modal-title {
    text-align: center;
    font-size: 18px;
    color: #172b4d;
  }

  .ant-modal-content {
    overflow: hidden;
    border-radius: 20px !important;
    padding: 0;
  }

  .ant-modal-body {
    padding: 0;
  }

  .ant-modal-footer,
  .ant-modal-confirm-btns {
    border-top: none;
    text-align: center;
    padding: 30px 20px;

    .ant-btn {
      min-width: 160px;
      height: 44px;
      border-radius: 22px;

      &:not(.ant-btn-primary) {
        border: none;
        color: #ffaa0a;
        background-color: #fff3dc;
        box-shadow: none;

        &:hover {
          background-color: #fff3dc;
        }

        &:active {
          background-color: #fff3dc;
        }
      }
    }

    button + button {
      margin-left: 20px;
    }
  }

  &.ant-modal-confirm {
    &.hide-cancel-btn {
      .ant-modal-confirm-btns .ant-btn:first-child {
        display: none;
      }
    }

    .ant-modal-confirm-btns {
      float: none;
      margin: 0;
    }

    .ant-modal-confirm-body {
      > .anticon {
        display: none;
      }

      .ant-modal-confirm-content {
        padding: 0 50px;
        color: #172b4d;
        font-size: 18px;
        text-align: center;
        width: 100%;
      }
    }
  }

  &.modal-changePassword {
    .ant-modal-confirm-body {
      padding-top: 30px;
    }

    .ant-modal-confirm-content {
      line-height: 1.3;
      padding: 0 16px !important;
    }
  }

  &.center-modal {
    .ant-modal-confirm-body .ant-modal-confirm-title {
      text-align: center;
    }
  }
}
.ant-modal-content {
  border-radius: 20px !important;
}
@media (max-width: 350px) {
  .modal-simple.no-apu-header .ant-modal-footer .ant-btn {
    width: 116px;
  }
}

// 默认按钮样式重写
.ant-btn {
  line-height: normal;
}
.ant-btn > span {
  display: inline-block;
  font-weight: 600 !important;
}

.ant-btn.ant-btn-default {
  border: none;
  color: #ffaa0a;
  background-color: #fff3dc;
  box-shadow: none;
  &:hover {
    // background-color: #fff3dc;
    color: #ffaa0a;
  }

  &:active {
    // background-color: #fff3dc;
    color: #ffaa0a;
  }
}

.ant-btn.ant-btn-primary {
  background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
  border: none;
}

// Dropdown样式重写
.ant-dropdown-menu {
  border-radius: 6px;
  padding: 6px;
}

.ant-dropdown-menu-item,
.ant-dropdown-menu-submenu-title {
  text-align: center;
}

.ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu-title:hover {
  background-color: #fff6e6;
  border-radius: 5px;

  > a {
    color: #ffaa0a;
  }
}

// Switch样式重写
.ant-switch-checked {
  background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
}

.ant-switch:not(.ant-switch-checked) {
  background-color: rgba(0, 0, 0, 0.05);
}

// Message消息提示样式
.ant-message {
  top: 30%;

  .ant-message-notice .ant-message-notice-content {
    padding: 20px;
    max-width: 380px;
    font-size: 14px;
    font-weight: 400;
    line-height: 19px;
    color: #fff;
    background: rgba($color: #0f192a, $alpha: 0.95);
    border-radius: 10px;
    word-break: break-word;

    .ant-message-custom-content.ant-message-info i {
      display: none;
    }

    .ant-message-loading {
      .anticon {
        color: rgba(255, 170, 10, 1);
      }
    }
  }
}

// From样式重写
.ant-form-item-control {
  line-height: normal;
}

.ant-form-item {
  margin-bottom: 20px;
}

.ant-input {
  height: 40px;
  background-color: #f4f6fa;
  border: none;
  color: #172b4d;
  font-weight: 500;
  padding: 5px 12px;
  border-radius: 6px;

  &:focus {
    border: 0;
    box-shadow: none;
  }

  &::placeholder {
    color: #dee2e7;
  }

  &[disabled] {
    background-color: #f4f6fa;
    color: #dee2e7;
  }
}

.ant-form-explain {
  font-size: 12px;
  margin: 5px 0 0 10px;
}

.has-error {
  position: relative;

  .ant-input,
  .ant-input:hover {
    background-color: #f4f6fa !important;
  }

  .ant-input:focus {
    box-shadow: none;
    border: none;
  }
}

// Select样式重写
.ant-select {
  .ant-select-selection {
    background-color: #f4f6fa;
    border: none;
  }

  .ant-select-selection--single {
    height: 40px;
  }

  .ant-select-selection__rendered {
    line-height: 40px;
  }

  .ant-select-selection__placeholder {
    color: #dee2e7;
  }

  &.ant-select-focused .ant-select-selection,
  .ant-select-selection:focus,
  .ant-select-selection:active {
    box-shadow: none;
    border: none;
  }
}

// Select Dropdown样式重写
.ant-select-dropdown {
  border-radius: 6px;

  .ant-select-dropdown-menu {
    padding: 15px;
  }

  .ant-select-dropdown-menu-item {
    line-height: 24px;
    border-radius: 6px;
    margin-bottom: 5px;
    color: #a2aab8;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ant-select-dropdown-menu-item-selected,
  .ant-select-dropdown-menu-item-active:not(.ant-select-dropdown-menu-item-disabled) {
    background-color: #fff6e6;
    color: #ffaa0a;

    &:hover {
      background-color: #fff6e6;
    }
  }

  .ant-select-dropdown-menu-item-active:hover:not(.ant-select-dropdown-menu-item-selected) {
    background-color: #fff;
    color: #172b4d;
  }
}

// Checkbox样式重写
.skin-white-checkbox {
  .ant-checkbox-inner {
    border-color: #fff;
    background: rgba(255, 255, 255, 0.2);
  }

  .ant-checkbox-checked .ant-checkbox-inner {
    background: #fff;
  }

  .ant-checkbox-checked .ant-checkbox-inner::after {
    border-color: #ffaa0a;
  }

  .ant-checkbox-wrapper:hover .ant-checkbox-inner,
  .ant-checkbox:hover .ant-checkbox-inner,
  .ant-checkbox-input:focus + .ant-checkbox-inner {
    border-color: #fff;
  }
}

.ant-checkbox-inner {
  width: 14px;
  height: 14px;
  border-radius: 8px;
  background: #f4f6fa;
}

.ant-checkbox-checked::after {
  border: none;
}

.ant-checkbox-inner::after {
  left: 20%;
  width: 5px;
  height: 8px;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
  border: none;
}

// Notification样式重置
.ant-notification {
  color: rgba(23, 43, 77, 1);

  .ant-notification-notice {
    margin-bottom: 20px;
    padding: 14px 40px 14px 16px;
    background: #fff;
    border-radius: 12px;
  }

  .ant-notification-notice-close {
    top: 50%;
    right: 16px;
    margin-top: -14px;
    color: #fff;
    font-size: 20px;
  }
}

// Notification离场动画重置
.ant-notification-fade-leave.ant-notification-fade-leave-active {
  -webkit-animation-name: NotificationFadeOutNew;
  animation-name: NotificationFadeOutNew;
  -webkit-animation-play-state: running;
  animation-play-state: running;
}

@-webkit-keyframes NotificationFadeOutNew {
  0% {
    right: 0;
    opacity: 1;
  }

  100% {
    right: -384px;
    opacity: 0;
  }
}

@keyframes NotificationFadeOutNew {
  0% {
    right: 0;
    opacity: 1;
  }

  100% {
    right: -384px;
    opacity: 0;
  }
}

.blackPop {
  .ant-popover-inner {
    background: rgba(15, 25, 42, 0.95) !important;
    border-radius: 35px !important;
  }

  .ant-popover-arrow {
    width: 0.35rem;
    height: 0.11rem;
    transform: translateX(-50%) rotate(0) !important;
    border: none !important;
    bottom: 0 !important;
    background: url('@/assets/images/arrow.png') no-repeat;
    background-size: 100% 100%;
    -webkit-transform: translateX(-50%) rotate(0) !important;
    -moz-transform: translateX(-50%) rotate(0) !important;
    -ms-transform: translateX(-50%) rotate(0) !important;
    -o-transform: translateX(-50%) rotate(0) !important;
  }
}

.customSwitch {
  .ant-switch::after {
    content: 'off';
    width: 0.23rem;
    height: 0.23rem;
    top: -0.04rem;
    margin-left: -0.02rem;
  }

  .ant-switch-checked::after {
    content: 'on';
    margin-left: 0.02rem;
    color: #ff850a;
  }

  .ant-switch {
    width: 0.4rem;
    height: 0.16rem;
  }
}
