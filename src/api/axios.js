import axios from 'axios'
import localStorageUtil from '@/utils/localStorageUtil'

const url = import.meta.env.VITE_APP_URL_ONE
const instance = axios.create({
  baseURL: url,
  timeout: 10000,
})

instance.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    return config
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error)
  },
)

instance.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    return response.data
  },
  (error) => {
    // 对响应错误做点什么
    return Promise.reject(error)
  },
)
const request = {
  post: (url, params, options = {}) => {
    const { headers, baseURL } = options
    if (baseURL) {
      instance.defaults.baseURL = baseURL
    }
    const finalHeaders = {
      schoolcode: '8601',
      'x-token': localStorageUtil.getItem('unifiedAccessToken') ?? '',
      ...headers,
    }
    return instance.post(url, params, { headers: finalHeaders })
  },
}
export default request
