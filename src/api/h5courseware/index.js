import { baseOverseaRequest } from '../overseaBase'
/**
 * 获取内容云课件详情
 * @param {Number} planId 讲次ID
 */
export const getCoursewareInfo = async (planId) => {
  let path = '/classroom-hub/classroom/courseware'
  console.log('课件getCoursewareInfo', planId)

  const res = await baseOverseaRequest(path, { planId })
  return res
}
export const getQuestionStatus = async (params) => {
  let path = '/classroom-hub/question/student/status'
  const res = await baseOverseaRequest(path, params)
  return res
}
export const submitUserAnswer = async (params) => {
  let path = '/classroom-hub/question/student/submit'
  const res = await baseOverseaRequest(path, params)
  console.log(res, 'submitUserAnswer')
  return res
}
export const getPrestrainList = async () => {
  const path = `/classroom-hub/timetable/student/prestrainList`
  const res = await baseOverseaRequest(path, {})
  return res
}

export const interactReport = async (params) => {
  const path = '/classroom-hub/classroom/student/interact/partakereport'
  return await baseOverseaRequest(path, params)
}
