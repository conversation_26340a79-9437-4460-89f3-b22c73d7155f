import { baseOverseaRequest } from '../overseaBase'

/**
 * 游戏互动异常恢复
 * @param {*} params
 */
export const getGameStatus = async (params) => {
  let path = '/classroom-hub/question/student/game/status'
  const res = await baseOverseaRequest(path, params)
  return res
}

export const submitGameResult = async (params) => {
  let path = '/classroom-hub/question/student/game/submit'
  const res = await baseOverseaRequest(path, params)
  return res
}

export const submitPics = async (params) => {
  let path = '/classroom-hub/question/student/game/submitAsync'
  const res = await baseOverseaRequest(path, params)
  return res
}
/**
 * 上报数据
 * @param {*} type
 * @param {*} params
 */
export const interactReport = async (type, params) => {
  let path
  if (type === 'open') {
    path = '/classroom-hub/classroom/student/interact/partakereport'
  } else {
    path = '/classroom-hub/question/student/game/close'
  }
  return await baseOverseaRequest(path, params)
}
