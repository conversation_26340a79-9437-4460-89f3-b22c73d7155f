import { baseOverseaRequest } from '../overseaBase'

/**
 * 计算投票率= 投票人数/ 接到信令人数
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function interactVoteReport(params) {
  const path = '/classroom-hub/classroom/student/interact/partakereport'
  return baseOverseaRequest(path, params)
}

/**
 * 学生提交投票接口
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function voteCommit(params) {
  const path = `/classroom-hub/vote/student/commit`
  return baseOverseaRequest(path, params)
}

/**
 * 获取投票统计结果
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function getVoteStatistics(params) {
  const path = `/classroom-hub/vote/student/getStatistics`
  return baseOverseaRequest(path, params)
}

/**
 * 状态恢复
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function recoverVoteStatus(params) {
  const path = `/classroom-hub/vote/student/status`
  return baseOverseaRequest(path, params)
}

/**
 * 学生没有作答自动提交
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function unSubmmit(params) {
  const path = `/classroom-hub/vote/student/noAnswerCommit`
  return baseOverseaRequest(path, params)
}

/**
 * 非预置填空题查询学生作答结果
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function answerCheck(params) {
  const path = `/api/vote/oneanswer/student/check`
  return baseOverseaRequest(path, params)
}

/**
 * 非预置填空题查询学生作答结果
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function submitOneAnswer(params) {
  const path = `/api/vote/oneanswer/student/commit`
  return baseOverseaRequest(path, params)
}
