import { baseOverseaRequest } from '../overseaBase'

/**
 * 获取礼物配置信息
 */
export function queryGiftsListApi(context, params) {
  const path = `/classroom-hub/gift/v2/detail`
  return baseOverseaRequest(context, path, params)
}

/**
 * 送礼物
 */
export function sendGiftApi(context, params) {
  const path = `/classroom-hub/gift/student/v2/send`
  return baseOverseaRequest(context, path, params)
}

/**
 * 状态恢复
 */
export function recoverGifteStatusApi(context, params) {
  const path = `/classroom-hub/gift/student/status`
  return baseOverseaRequest(context, path, params)
}
