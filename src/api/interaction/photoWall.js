import { baseOverseaRequest } from '../overseaBase'

/**
 * 提交学员照片
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function submitUserPhoto(params) {
  const path = '/classroom-hub/wall/student/submit'
  return baseOverseaRequest(path, params)
}

/**
 * 上报开启互动
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function interactOpen(params) {
  const path = '/classroom-hub/classroom/student/interact/partakereport'
  return baseOverseaRequest(path, params)
}

/**
 * 获取照片墙状态
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function getWallStatus(params) {
  const path = '/classroom-hub/wall/student/status'
  return baseOverseaRequest(path, params)
}

/**
 * 设置学生提交状态
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export function setSubmitStatus(params) {
  const path = '/api/wall/v1/student/handsup'
  return baseOverseaRequest(path, params)
}
