import { baseOverseaRequest } from '../overseaBase'

/**
 * 舒尔特方格历史恢复查询提交结果
 */
export const checkSubmitSchulteTable = async (params) => {
  let path = '/api/schultegrid/v1/student/check'
  return await baseOverseaRequest(path, params)
}

/**
 * 舒尔特方格提交游戏计时
 */
export const submitSchulteTable = async (params) => {
  let path = '/api/schultegrid/v1/student/commit'
  return await baseOverseaRequest(path, params)
}

//学生端抢红包接口
export function studentGrabRedBag(params) {
  let path = '/classroom-hub/redbag/student/grab'
  return baseOverseaRequest(path, params)
}

//学生端红包领取结果列表
export function studentRedBagRank(params) {
  let path = '/classroom-hub/redbag/student/ranking'
  return baseOverseaRequest(path, params)
}

//学生端红包领取状态
export function studentRedBagStatus(params) {
  let path = '/classroom-hub/redbag/student/status'
  return baseOverseaRequest(path, params)
}

/**
 * 红包页面打开上报数据
 */
export function submitRedPacketData(params) {
  const path = `/classroom-hub/classroom/student/interact/partakereport`
  return baseOverseaRequest(path, params)
}

// 红包雨上报
export function submitRedPacketRainData(params) {
  const path = `/api/redbagrain/student/reportStudentGetCoin`
  return baseOverseaRequest(path, params)
}

// 获取红包雨游戏
export function getRedPacketRain(params) {
  const path = '/api/redbagrain/student/getGamePackage'
  return baseOverseaRequest(path, params)
}

// 判断是否上报
export async function getRedPacketRainStatus(params) {
  const path = '/api/redbagrain/student/status'
  try {
    const { code, data } = await baseOverseaRequest(path, params)
    if (+code === 0) {
      return data.attendStatus === 1 // 1 上报 0 未上报
    }
    return true
  } catch (error) {
    return error
  }
}
