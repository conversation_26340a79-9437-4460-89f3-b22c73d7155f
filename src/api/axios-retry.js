const isFunction = val => toString.call(val) === `[object Function]`
export default (instance, { retry = 0, retryDelay = 0, retryCondition = null }) => {
  instance.interceptors.request.use(res => {
    return res
  })
  instance.interceptors.response.use(
    response => {
      return response
    },
    async error => {
      console.error('接口异常', error.config.url, error.config.baseURL)
      // 如果设置重试次数，开启重试
      if (retry && retry > 0) {
        console.log('需要重试')
        error.config.__retryCount = error.config.__retryCount || 0
        let needRetry = true
        // 获取重试条件是否满足
        if (isFunction(retryCondition)) {
          needRetry = retryCondition(error.config)
        }
        // 如果当前次数小于设置的重试次数且满足重试条件，进行重试
        if (error.config.__retryCount < retry && needRetry) {
          error.config.__retryCount += 1
          console.info('开始重试,等待重试')
          await new Promise(resolve => setTimeout(resolve, retryDelay || 1000))
          console.log('是否多域名')
          // 如果有多域名，则重试时进行切换,否则只是简单重试接口
          if (error.config.multipleDomain?.length > 0) {
            // 设置新的域名重新请求
            error.config.baseURL = switchApiDomain(
              error.config.baseURL,
              error.config.multipleDomain
            )
            console.log('切换域名', error.config.baseURL)
            error.config.retryed = true // 代表该接口经历过重试
          }
          return await instance(error.config)
        } else {
          console.log('重试次数已用完，或者不满足重试条件', error)
          return Promise.reject(error)
        }
      } else {
        console.log('不需要重试，或者重试次数为0')
        return Promise.reject(error)
      }
    }
  )
}
// 多域名格式
// domains = [
//   'https://oversea-api-akamai.thethinkacademy.com',
//   'https://oversea-api.thethinkacademy.com',
//   'https://oversea-api.talthinktech.com'
// ]
const switchApiDomain = (baseUrl, domains = []) => {
  const currentDomainIndex = domains.findIndex(domain => baseUrl == domain)
  return domains[currentDomainIndex + 1] ? domains[currentDomainIndex + 1] : domains[0]
}
