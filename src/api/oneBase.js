import request from '@/api/request'
import { getApiDomain } from '@/utils/domainConfig'

export const baseOneRequest = async (url, params, headers, sendToken = true) => {
  const { one } = getApiDomain()
  const res = await request({
    url,
    params,
    headers,
    apiDomain: one,
    sendToken,
  })
  return res
}

export const baseBizRequest = async (url, params, headers, sendToken = true) => {
  const envStr = import.meta.env.VITE_APP_MODE
  const envMap = {
    production: 'https://biz.thethinkacademy.com',
    preprod: 'https://biz-pre.thethinkacademy.com',
    beta: 'https://biz-beta.thethinkacademy.com',
    dev: 'https://biz-beta.thethinkacademy.com',
  }
  const bizUrl = envMap[envStr]
  const res = await request({
    url,
    params,
    headers,
    apiDomain: bizUrl,
    sendToken,
  })
  return res
}
