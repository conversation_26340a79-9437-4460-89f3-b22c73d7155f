import CryptoJS from 'crypto-js'
import { baseOneRequest } from '../oneBase'

/**
 * 手机快捷登录接口
 */
export const quickLoginApi = async (params) => {
  console.log('登录接口signature', params)
  const path = '/v2/ucenter/account/quick_login'
  const headers = addHeader(params)
  console.log('headers', headers)
  return await baseOneRequest(path, params, headers, false)
}

/**
 * 获取课程列表
 */
export const classListApi = async (params = {}) => {
  const path = 'v1/studyCenter/recent/infoV2'
  return await baseOneRequest(path, params)
}

// 添加header
export const addHeader = (params) => {
  const SECRET_KEY = 'FmEDIqMox3njFmdkRM' // 密钥
  const timestampInSeconds = Math.floor(Date.now() / 1000) // 获取当前时间戳（秒）
  const finalSecretKey = SECRET_KEY + timestampInSeconds // 拼接密钥和时间戳

  const requestBody = JSON.stringify(params) // 请求体
  const hmac = CryptoJS.HmacSHA256(requestBody, finalSecretKey) // 使用CryptoJS库的HmacSHA256方法计算签名
  const signatureBytes = hmac.words // 获取签名的字节数组
  const hexString = CryptoJS.enc.Hex.stringify(CryptoJS.lib.WordArray.create(signatureBytes)) // 将字节数组转换成十六进制字符串
  //  hashcode="c536572efb7d03f0703092b97582ddb0ce1825a131c3b8ed2a2f270bd269fd92"
  const signature = hexString + timestampInSeconds // 将时间戳拼接在后面
  const encodedSignature = btoa(signature) // 使用Base64编码签名结果
  // Sign="YzUzNjU3MmVmYjdkMDNmMDcwMzA5MmI5NzU4MmRkYjBjZTE4MjVhMTMxYzNiOGVkMmEyZjI3MGJkMjY5ZmQ5MjE2ODkzMTUzMjM="
  const headers = {
    'x-tal-signature': encodedSignature,
  }
  return headers
}
/**
 * 获取用户基础信息
 */
export const queryUserInfoApi = () => {
  const path = '/v1/aggregate/user/info'
  return baseOneRequest(path, {
    data: { fillInAttention: false },
  })
}
/**
 * 获取多账号信息
 */
export const queryAllAccount = () => {
  const path = '/v1/ucenter/account/associated/list'
  return baseOneRequest(path, {})
}
/**
 * 切换账号
 */
export const switchAccount = async params => {
  const path = '/v1/ucenter/account/switch_login'
  return await baseOneRequest(path, params)
}
/**
 * 登出
 */
export const logout = async params => {
  const path = '/v1/ucenter/account/logout'
  return await baseOneRequest(path, params)
}
/**
 * 是否多用户首次进入弹窗
 */
export const existAction = async params => {
  const path = '/v1/ucenter/behavior/existBehavior'
  return await baseOneRequest(path, params)
}
/**
 * 记录下首次进入课堂弹窗
 */
export const saveAction = async params => {
  const path = '/v1/ucenter/behavior/saveBehavior'
  return await baseOneRequest(path, params)
}
