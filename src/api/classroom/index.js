import { baseOverseaRequest } from '../overseaBase'
/**
 * 获取课件信息
 */
export const coursewareApi = async (params = {}) => {
  const path = '/api/hub/classroom/student/preClassPrepare'
  return await baseOverseaRequest(path, params)
}

// 获取上课需要的基础信息
export const webPreClassPrepare = async (params = {}) => {
  const path = '/api/hub/classroom/student/webPreClassPrepare'
  return await baseOverseaRequest(path, params)
}

/**
 * 获取上课需要的基础信息
 */
export const classroomStudentBasicApi = async (
  params = { bizId: null, planId: 0, courseId: null, isParentAudition: 0 }
) => {
  const path = '/classroom-hub/classroom/student/basic'
  return await baseOverseaRequest(path, params)
}

export const classStudentListApi = async (params = { planId: 0, classId: 0 }) => {
  const path = '/classroom-hub/classroom/student/classStudentList'
  return await baseOverseaRequest(path, params)
}
/**
 * 拉取所有页被授权学生的pageKey以及userList
 */
export const fetchIsAuthorizedUserList = async params => {
  const path = '/api/linkmic/v1/studentGetAllPageKey'
  return await baseOverseaRequest(path, params)
}

/**
 * 学生端上报RTC推流状态接口
 */
export const setRtcStatus = async params => {
  let path = '/classroom-hub/classroom/student/setRtcStatus'
  const res = await baseOverseaRequest(path, params)
  return res
}

// 小班勋章金币
export const studentCoinAndMedal = async params => {
  let path = '/api/hub/classroom/studentCoinAndMedal'
  const res = await baseOverseaRequest(path, params)
  return res
}

// 查看报告信息
export const lookExamReportApi = async params => {
  let path = '/api/hub/classroom/exam/examInfo'
  const res = await baseOverseaRequest(path, params)
  return res
}
/**
 * 课中动态表情
 */
export const getLottieEmojiList = async params => {
  let path = '/api/emoji/v1/student/detail'
  return await baseOverseaRequest(path, params)
}
/**
 * 更改表情包过期状态
 */
export const setEmojiOverHide = async params => {
  let path = '/api/emoji/v1/student/setOverHide'
  return await baseOverseaRequest(path, params)
}

/**
 *  作业盒子内容获取接口
 */
export async function queryAssignmentBoxList(context, params) {
  const path = `/classroom-hub/wall/student/photoBox`
  return await baseOverseaRequest(path, params)
}
/**
 *  作业盒子设置已读接口
 */
export async function queryReadMessage(context, params) {
  const path = `/classroom-hub/wall/student/readNewMessage`
  return await baseOverseaRequest(path, params)
}

/**
 * 提交学员照片
 * @param {*} params
 */
export const submitUserPhoto = async params => {
  const path = '/classroom-hub/wall/student/submit'
  return await baseOverseaRequest(path, params)
}

/**
 * 上报开启互动
 * @param {*} params
 */
export const interactOpen = async params => {
  const path = '/classroom-hub/classroom/student/interact/partakereport'
  return await baseOverseaRequest(path, params)
}

export const getWallStatus = async params => {
  const path = '/classroom-hub/wall/student/status'
  return await baseOverseaRequest(path, params)
}

/**
 * 设置学生提交状态
 * @param {*} params
 */
export const setSubmitStatus = async params => {
  const path = '/api/wall/v1/student/handsup'
  return await baseOverseaRequest(path, params)
}
/**
 *  金币总数
 */
export async function queryCoinsCount(context, params) {
  const path = `/classroom-hub/coin/student/getCoin`
  return await baseOverseaRequest(path, params)
}

/**
 * 获取礼物配置信息
 */
export async function queryGiftsListApi(context, params) {
  const path = `/classroom-hub/gift/v2/detail`
  return await baseOverseaRequest(path, params)
}

/**
 * 送礼物
 */
export async function sendGiftApi(context, params) {
  const path = `/classroom-hub/gift/student/v2/send`
  return await baseOverseaRequest(path, params)
}

/**
 * 状态恢复
 */
export async function recoverGifteStatusApi(context, params) {
  const path = `/classroom-hub/gift/student/status`
  return await baseOverseaRequest(path, params)
}

/**
 * 上报互动
 */
export async function submitInteractPartakereport(params) {
  const path = `/classroom-hub/classroom/student/interact/partakereport`
  return await baseOverseaRequest(path, params)
}

/**
 * 领取奖励接口
 */
export const submitPraiseApi = async params => {
  let path = '/classroom-hub/praise/student/submit'
  return await baseOverseaRequest(path, params)
}

/**
 * 学生抢答
 */
export const answerRob = async params => {
  let path = '/api/linkmic/v1/student/answerRob'
  return await baseOverseaRequest(path, params)
}

/**
 * 学生端排行榜数据（前5名）列表接口
 */
export async function querySmallClassRankList(context, params) {
  const path = `/classroom-hub/classRanking/student/rankingList`
  return await baseOverseaRequest(path, params)
}

/**
 * 学生端-上报设备和版本信息
 */
export const pushStudentDeviceInfo = async params => {
  let path = '/api/classroom/v1/student/reportDeviceInfo'
  return await baseOverseaRequest(path, params)
}
