import request from '@/api/request'
import { getApiDomain } from '@/utils/domainConfig'

/**
 * 海外基础请求函数
 * @param {string} url - 请求路径
 * @param {object} params - 请求参数
 * @param {object} [headers] - 请求头
 * @param {boolean} [needRetry=false] - 是否需要重试
 * @returns {Promise} 请求结果
 */
export const baseOverseaRequest = async (url, params, headers = {}, needRetry = false) => {
  const { oversea } = getApiDomain()
  const res = await request({
    url,
    params,
    headers,
    apiDomain: oversea,
    needRetry,
  })
  return res
}

/**
 * 获取 AWS STS Token
 * @param {object} params - 请求参数
 * @returns {Promise} 请求结果
 */
export const getSTSToken = async (params) => {
  return baseOverseaRequest('/classroom-hub/classroom/aws/getSTSToken', params)
}


