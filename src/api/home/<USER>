import { baseOverseaRequest } from '../overseaBase'
/**
 * 获取课后作业跳转内容云跳转url
 */
export const queryHomeWorkUrl = async (params) => {
  let path = '/homework-api/student/neirongyun/jumpUrl'
  const res = await baseOverseaRequest(path, params)
  return res
}
/**
 * 课后作业 - 学生端作业同步
 */
export const getPaperDetail = async (params) => {
  let path = '/homework-api/student/paperDetail'
  const res = await baseOverseaRequest(path, params)
  return res
}
/**
 * 录播课阶段考试 - 过渡页
 */
export const getRecordPaperOverview = async (params) => {
  let path = '/beibo/student/paperOverview'
  const res = await baseOverseaRequest(path, params)
  return res
}
/**
 * 阶段考试 - 过渡页
 */
export const getPaperOverview = async (params) => {
  let path = '/homework-api/student/paperOverview'
  const res = await baseOverseaRequest(path, params)
  return res
}
/**
 * 资料下载
 */
export const getDownloadResources = async params => {
  const path = '/classroom-hub/material/student/detail'
  return await baseOverseaRequest(path, params)
}