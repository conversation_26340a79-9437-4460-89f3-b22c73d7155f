import { baseOneRequest } from '../oneBase'
import { baseOverseaRequest } from '../overseaBase'
/**
 * 查询用户是否在课中
 */
export const getUserInClass = async params => {
  let path = '/api/hub/classroom/student/inClass'
  const res = await baseOverseaRequest(path, params)
  return res
}
/**
 * 获取学习中心上课方式
 */
export const getTeachMethod = async (params) => {
  let path = '/v1/studyCenter/teachMethod'
  const res = await baseOneRequest(path, params)
  return res
}
/**
 * 获取云控配置
 */

export const getCloudConfig = async params => {
  const path = '/api/smartline/v1/getCloudConfigs'
  return await baseOneRequest(path, params, {
    'Content-Type': 'application/json'
  })
}