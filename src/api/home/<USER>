import { baseOneRequest, baseBizRequest } from '../oneBase'
/**
 * 学习计划菜单是否隐藏
 */
export function getMenuRelateData(params) {
  const path = `/v1/studyCenter/client/getMenuRelateData`
  return baseOneRequest(path, params)
}
/**
 * 学习计划菜单地址
 */
export function getStudyPlanUrl(params) {
  const path = `/v1/practice/edu/client/student/report/getLearnSchedule`
  return baseOneRequest(path, params)
}
/**
 * 获取近期作业列表
*/
export function getAssignmentList(params) {
 const path = `/v1/studyCenter/recent/getAssignment`
 return baseOneRequest(path, params)
}
/**
 * 获取最新巩固练习游戏的地址
*/
export function getGameUrl(params) {
 const path = `/gameUser/gamer/afvercontrol/getActiveUrl`
 return baseBizRequest(path, params)
}
/**
 * 转换得到学练入口的token
*/
export function getPracticeToken(params) {
  const path = `/v1/ucenter/account/temp_token/get`
  return baseOneRequest(path, params)
}
/**
 * home页中today课程
*/
export function getNearLesson(params) {
  const path = `/v1/studyCenter/recent/getNearLesson`
  return baseOneRequest(path, params)
}
/**
 * home页中Courses课程
*/
export function getClassList(params) {
  const path = `/v1/studyCenter/classList/v3`
  return baseOneRequest(path, params)
}
/**
 * home页中Courses课程
*/
export function queryLessonListApi(params) {
  const path = `/v1/studyCenter/scheduleList`
  return baseOneRequest(path, params)
}

/**
 * 添加NPS反馈
*/
export function submitNpsFeedback(params) {
  const path = `/api/classroom/nps/saveNPS`
  return baseOneRequest(path, params)
}

// 获取NPS弹窗状态
export const getNpsStatus = params => {
  return baseOneRequest('/api/classroom/nps/getNPSStatus', params)
}