import axios from 'axios'
import axiosRetry from './axios-retry'
import { useRequestInfo } from '@/hooks/useRequestInfo'
import checkNetwork from '@/utils/checkNetwork'
import { getmultipleDomainByApi, setApiDomain } from '@/utils/domainConfig'
import { v4 as uuidv4 } from 'uuid'
import { emitter } from '@/hooks/useEventBus'
import { version } from '../../package.json'
import { removeCookies } from '@/utils/util.js';

// 创建实例
const axiosInstance = axios.create({
  timeout: 5000, // 超时时间(ms)
  withCredentials: false
})
axiosRetry(axiosInstance, {
  retry: 2,
  retryDelay: 1000, // 重试间隔时间(ms)
  retryCondition: config => {
    return config.needRetry
  }
})
const whiteCode = [0, 9, 30003, 48000, 48001, 51001]
const whiteUrl = ['/classroom-hub/raw/classroom/timestamp']

export default async function ({
  url,
  method = 'post',
  headers,
  params,
  apiDomain = '',
  sendToken = true,
  needRetry = true // 有的接口不需要重试。 默认需要重试
}) {
  const { getToken, setFirstToken, getSchoolCode, getTimezone, getLanguage } = useRequestInfo()
  const token = getToken()
  const schoolCode = getSchoolCode()
  const timezone = getTimezone()
  const language = getLanguage()
  let options = {
    method,
    url,
    params: {},
    data: params,
    needRetry,
    multipleDomain: getmultipleDomainByApi(apiDomain), // 根据请求的域名获取该接口的域名类型( OVERSEA || ONE )
    headers: {
      'Content-Type': 'application/json',
      clientType: 'WEB_PC_STUDENT',
      appName: 'studentWeb',
      appVersion: version,
      'x-tal-trace-id': uuidv4(),
      ...headers
    }
  }
  // token存在则添加
  if (sendToken && token) {
    options.headers['X-Token'] = token
  } else if (!token) {
    // 若不存在token, 则直接进行页面跳转至统一登录页
    window.open(`${window.location.origin}/passport/login?notReturnTempToken=1&retUrl=${window.location.origin}/study`, '_self');
    return
  }
  if (timezone && timezone !== 'undefined') {
    options.headers['timezone'] = timezone
  }
  if (schoolCode && schoolCode !== 'undefined') {
    options.headers['schoolCode'] = schoolCode
  }
  if (language) {
    options.headers['X-Lang'] = language
  }

  try {
    axiosInstance.defaults.baseURL = apiDomain
    let _res = await axiosInstance(options)
    _res.config?.retryed && setApiDomain(_res.config.baseURL) // 有重试的域名返回就更新本地保存的数据
    let res = _res.data || {}
    // code不存在 或者 不在白名单code时候上报
    if (
      (res.code == undefined || !whiteCode.includes(res.code)) &&
      !whiteUrl.includes(options.url)
    ) {
      errorCapture({
        url: options.url,
        request: options,
        response: res,
        error: '后端返回code不为0'
      })
    }

    if (res.code == 9 || res.code == 30003) {
      console.warn('Token失效、密码修改时')
      removeCookies('_official_token')
      setFirstToken('')
      window.open(`${window.location.origin}/passport/login?notReturnTempToken=1&retUrl=${window.location.origin}/study`, '_self');
    }

    return res
  } catch (e) {
    const isOnline = await checkNetwork()
    console.log('是否有网络', isOnline)
    const errorInfo = {
      url: options.url,
      request: options,
      error: e,
      isOnline
    }
    // 统一提示错误信息
    errorCapture(errorInfo)

    return Promise.reject(e)
  }
}
// 阿里云接口错误上报, 再加个接口错误上报sensor埋点
const errorCapture = ({ url, request, response = {}, error = '', isOnline }) => {
  let content = {
    msg: `http接口异常上报,  接口: ${url}`,
    request,
    response,
    error
  }
  const sensorsParams = {
    url: url,
    statusDesc: 'fail',
    errorType: 'code'
  }
  if (typeof isOnline === 'boolean') {
    content['isOnline'] = isOnline
    sensorsParams['errorType'] = 'network'
    sensorsParams['isOnline'] = isOnline
  } else {
    sensorsParams['code'] = response?.code
  }

  // 接口异常或报错埋点
}
