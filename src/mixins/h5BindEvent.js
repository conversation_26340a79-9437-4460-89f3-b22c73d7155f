import logger from '../utils/logger'
import { getUrlParam } from '../utils/util'
const bindEventMixin = {
  data: {
    return: {
      isSendLogger: false
    }
  },
  methods: {
    bindEvent() {
      window.addEventListener('message', this.bindMessage, false)
    },
    bindMessage(e) {
      // @log-ignore
      let parseData = e.data || {}
      console.log('message', parseData)

      if (typeof parseData === 'string') {
        try {
          parseData = JSON.parse(parseData)
        } catch (error) {
          console.error(error)
          parseData = e.data
        }
      }

      const actionType = parseData.action
      // console.log(this.backUrl, 'this.backUrl')
      // 退出作业事件
      if (actionType == 'jy-exit') {
        this.sendLoggers(
          'h5message',
          `退出作业事件:actionType: ${actionType}, parseData: ${JSON.stringify(parseData)}`
        )
        if (this.isBeforeClassTest()) {
          this.$router.push(`${this.backUrl}`)
        }
      }
      // 提交作业事件
      if (actionType == 'jy-submit') {
        this.sendLoggers(
          'h5message',
          `提交作业事件:actionType: ${actionType}, parseData: ${JSON.stringify(
            parseData
          )}, sendMessageDone: ${this.sendMessageDone}`
        )
        if (!this.sendMessageDone) {
          this.submitHomeworkEvent(parseData.data.state || 1, parseData.data.type || 0)
        }
      }
      // 请求超时事件
      if (actionType == 'jy-overtime') {
        this.sendLoggers(
          'h5message',
          `请求超时事件:actionType: ${actionType}, parseData: ${JSON.stringify(parseData)}`
        )
        if (this.isBeforeClassTest()) {
          this.$router.push(`${this.backUrl}`)
        }
        this.$Message.warning('Network error (or your account is signed in on another device)')
      }
      // 退出h5页面事件
      if (actionType == 'jy-close') {
        this.sendLoggers(
          'h5message',
          `退出h5页面事件:actionType: ${actionType}, parseData: ${JSON.stringify(parseData)}`
        )
        if (this.isBeforeClassTest()) {
          this.$router.push(`${this.backUrl}`)
        }
      }
      // 提交作业接口响应时长
      if (actionType == 'hwApiResponse') {
        const homeworkId = getUrlParam('homeworkId')
        const classId = getUrlParam('classId')
        const homeworkSubmit = {
          homeworkId: homeworkId,
          homeworkClassId: classId,
          homeworkApiPath: parseData.data.path,
          homeworkResponseTime: parseData.data.responseTime,
          homeworkStatus: parseData.data.status
        }
        this.sendLoggers(
          'h5message',
          `提交作业接口响应时长:actionType: ${actionType}, parseData: ${JSON.stringify(
            parseData
          )},homeworkSubmit:${JSON.stringify(homeworkSubmit)}`
        )
      }
      // 上传图片上传时长
      if (actionType == 'hwUploadImg') {
        const homeworkId = getUrlParam('homeworkId')
        const classId = getUrlParam('classId')
        const homeworkUpload = {
          homeworkId: homeworkId,
          homeworkClassId: classId,
          homeworkUploadDuration: parseData.data.uploadDuration,
          homeworkType: parseData.data.uploadType,
          homeworkUploadStatus: parseData.data.uploadStatus
        }
        this.sendLoggers(
          'h5message',
          `上传图片上传时长:actionType: ${actionType}, parseData: ${JSON.stringify(
            parseData
          )},homeworkUpload:${JSON.stringify(homeworkUpload)}`
        )
      }

      // 课中报告 监听h5交卷事件，通知主讲端
      if (actionType == 'completedExamSuccess') {
        this.sendSubmitEaxmToTeacher()
        this.sendLoggers(
          'h5message',
          `监听h5交卷事件:actionType: ${actionType}, parseData: ${JSON.stringify(parseData)}`
        )
      }
      // 课中报告 监听h5关闭事件，关闭课中iframe  'hwHomeWorkRepeat'表示如果该学生在其他课堂做过相同试卷
      if (actionType == 'dismissView' || actionType === 'hwHomeWorkRepeat') {
        this.showEaxmIframe = false
        this.examUrl = '' // 关闭iframe后清空url，再次打开考试后重新加载新的url
        this.sendLoggers(
          'h5message',
          `监听h5关闭事件:actionType: ${actionType}, parseData: ${JSON.stringify(parseData)}`
        )
      }
    },
    // 发送日志
    sendLoggers(tag, msg) {
      this.isSendLogger = true
      logger.send({
        tag,
        level: 'info',
        content: {
          msg
        }
      })
    },
    // 判断是否是课前考试
    isBeforeClassTest() {
      return this.$router ? true : false
    }
  },
  beforeDestroy() {
    window.removeEventListener('message', this.bindMessage)
  }
}
export default bindEventMixin
