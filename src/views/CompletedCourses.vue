<template>
  <div class="completed-courses">
    <template v-if="!init">
      <Spin class="loading" size="large">
      </Spin>
    </template>
    <template v-else>
      <BackPre :title="$t('courses.list.tabList.completed')" backPath="/home/<USER>"/>
      <div class="card-box" v-if="dataList.length">
        <CourseCard
          class="item-course"
          v-for="(item, index) in dataList"
          :key="index"
          :course-data="item"
        />
      </div>
    </template>
  </div>
</template>
<script setup>
import BackPre from '@/components/Common/BackPre.vue'
import CourseCard from '@/components/Courses/CourseCard.vue'
import { getClassList } from '@/api/home/<USER>'
import { ref } from 'vue'
import { Spin } from 'ant-design-vue'
const dataList = ref([])
const init = ref(false)
const getClassListData = () => {
  getClassList({
    data: {
      types: ['FORMAL', 'TEMPORARY']
    }
  }).then(res => {
    if (res.code === 0) {
      const data = res.data.classGroups || []
      data.forEach(item => {
        if (item.tabStatus === 'COMPLETED') {
          dataList.value = item.records
        }
      })
    }
  }).catch((err) => {
    console.log('报错了 getClassList', err)
  }).finally(() => {
    init.value = true
  })
}
getClassListData()
</script>
<style lang="scss" scoped>
.completed-courses {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 170px);
  position: relative;
  .loading {
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -12px;
    margin-top: -12px;
  }
  .card-box {
    background: #fff;
    display: grid;
    align-items: stretch;
    width: 878px;
    padding: 20px;
    //max-height: calc(100vh - 137px);
    overflow: scroll;
    border-radius: 12px;
    gap: 16px;
    grid-template-columns: 1fr 1fr;
  }
  .item-course {
    width: 410px;
    //height: 164px;
    background: #ffffff;
    border-radius: 12px;
    border: 2px solid #f1f2f4;
  }
}
</style>
