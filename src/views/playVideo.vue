<template>
  <div class="play-video">
    <video v-if="isShowVideo" id="myVideo" class="video-js"></video>
  </div>
</template>
<script setup>
import Video from 'video.js'
import 'video.js/dist/video-js.css'
import { ref, nextTick, onMounted } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const fileUrl = route.query.url;

const isShowVideo = ref(true)
// myPlayer.pause()
const handlePlay = () => {
  // isShowVideo.value = !isShowVideo.value
  // const fileUrl = 'https://download-pa-s3.thethinkacademy.com/media-convert/output/c8980a32-aaa4-4be1-9dff-747d8dc733e5/AppleHLS1/1676727398559DTWa5TUmEp.m3u8'
  nextTick(() => {
    //初始化视频方法
    const myPlayer = Video('myVideo', {
      //确定播放器是否具有用户可以与之交互的控件。没有控件，启动视频播放的唯一方法是使用autoplay属性或通过Player API。
      controls: true,
      //自动播放属性,muted:静音播放
      autoplay: true,
      //建议浏览器是否应在<video>加载元素后立即开始下载视频数据。
      preload: 'auto',
      // fluid: true,
      //设置视频播放器的显示宽度（以像素为单位）
      // width: '100%',
      //设置视频播放器的显示高度（以像素为单位）
      // height: '200px'
    })
    myPlayer.src({ src: fileUrl, type: 'application/x-mpegURL' })
    myPlayer.load(fileUrl) //重新加载视频
  })
}
onMounted(() => {
  handlePlay();
})
</script>
<style scoped>
.play-video {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  width: 100vw;
  height: 100vh;
}
</style>