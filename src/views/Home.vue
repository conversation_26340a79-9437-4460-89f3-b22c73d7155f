<template>
  <div class="home">
    <template v-if="!(initNearFlag && initFlag)">
      <Spin class="loading" size="large">
      </Spin>
    </template>
    <template v-else>
      <div class="tab today">
        <div class="header">
          <div class="title">{{ $t('today.recent_lessons_15_s6n') }}</div>
        </div>
        <div v-if="!nextLesson && !lastLesson && initNearFlag" class="empty">
          <Empty
            :image="emptyTodayImage"
            :image-style="{
              height: '160px',
              width: '160px',
              margin: '0 auto'
            }"
          >
            <template #description>
              <div class="empty-description">
                {{ $t('today.no_recent_upcoming_courses_15_vbl') }}
              </div>
            </template>
          </Empty>
        </div>
        <LessonList v-else-if="initNearFlag" :nextLesson="nextLesson" :lastLesson="lastLesson" />
      </div>
      <div class="tab courses">
        <div class="header">
          <div class="title">{{ $t('today.courses') }}</div>
          <div v-if="completedClassList.length" class="right" @click="goCompletedCourses">
            {{ $t('today.completed_courses_15_0vt') }}
            <i class="iconfont icon-gengduo"></i>
          </div>
        </div>
        <div
          class="empty"
          v-if="currentClassList.length + completedClassList.length === 0 && initFlag"
        >
          <Empty
            :image="emptyCoursesImage"
            :image-style="{
              height: '160px',
              width: '160px',
              margin: '0 auto'
            }"
          >
            <template #description>
              <div class="empty-description">
                {{ $t('today.no_courses_or_completed_15_l9h') }}
              </div>
            </template>
            <div class="goto-courses" @click="gotoCourses">
              {{ $t('today.enroll_course_15_hj4') }}
            </div>
          </Empty>
        </div>
        <div class="card-box" v-if="currentClassList.length">
          <CourseCard
            class="item-course"
            v-for="(item, index) in currentClassList"
            :key="index"
            :course-data="item"
          />
        </div>
      </div>
    </template>
    <NpsFeedback v-if="showNpsModal" :plan-id="planId" :classId="classId" :tag-list="tagList" />
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import LessonList from '@/components/Courses/LessonList.vue'
import CourseCard from '@/components/Courses/CourseCard.vue'
import { getNearLesson, getClassList } from '@/api/home/<USER>'
import emptyTodayImage from '@/assets/images/home/<USER>'
import emptyCoursesImage from '@/assets/images/home/<USER>'
import { emitter } from '@/hooks/useEventBus'
import NpsFeedback from '@/components/npsFeedback/index.vue'
import { useNps } from '@/hooks/useNpsEvent.js';
import { Empty, Spin } from 'ant-design-vue'
const router = useRouter()
const route = useRoute()
const obj = useNps(route, router)
const showNpsModal = ref(obj.showNpsModal)
const classId = ref(obj.classId)
const planId = ref(obj.planId)
const tagList = ref(obj.tagList)
const initNearFlag = ref(false)
const nextLesson = ref(null)
const lastLesson = ref(null)
const currentClassList = ref([])
const completedClassList = ref([])
const initFlag = ref(false)
const gotoCourses = () => {
  window.open(`${window.location.origin}`, '_blank')
}
const goCompletedCourses = () => {
  router.push({
    path: '/home/<USER>'
  })
}
const getTodayData = () => {
  getNearLesson({}).then(res => {
    if(res.code === 0) {
      const data = res.data
      nextLesson.value = data?.nextLesson || null
      lastLesson.value = data?.lastLesson || null
    }
  }).catch((err) => {
    console.log('报错了 getNearLesson', err)
  }).finally(() => {
    initNearFlag.value = true
  })
}
const getClassListData = () => {
  getClassList({
    data: {
      types: ['FORMAL', 'TEMPORARY']
    }
  }).then(res => {
    if(res.code === 0) {
      const data = res.data.classGroups || []
      data.forEach(item => {
        if (item.tabStatus === 'CURRENT') {
          currentClassList.value = item.records
        }
        if (item.tabStatus === 'COMPLETED') {
          completedClassList.value = item.records
        }
      })
    }
  }).catch((err) => {
    console.log('报错了getClassList' , err)
  }).finally(() => {
    initFlag.value = true
  })
}
const init = () => {
  getTodayData()
  getClassListData()
}
onMounted(() => {
  init()
  emitter.on('globalReFresh', init)
})
onBeforeUnmount(() => {
  emitter.off('globalReFresh', init)
})
</script>
<style lang="scss" scoped>
.empty-description {
  padding-top: 12px;
  height: 24px;
  font-size: 16px;
  font-family: Montserrat-Medium, Montserrat;
  font-weight: 500;
  color: #7a7a7a;
  line-height: 24px;
}
.goto-courses {
  display: inline-block;
  margin-top: 10px;
  height: 40px;
  line-height: 40px;
  padding: 0 23px;
  background: #ff9f0a;
  border-radius: 20px;
  font-size: 14px;
  font-family: Montserrat-SemiBold, Montserrat;
  font-weight: 600;
  color: #ffffff;
  cursor: pointer;
}
.home {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 130px);
  overflow-y: scroll;
  position: relative;
  .loading {
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -12px;
    margin-top: -12px;
  }
  .tab {
    .header {
      margin-bottom: 16px;
      display: flex;
      justify-content: space-between;
      .title {
        height: 28px;
        font-size: 24px;
        font-family: Montserrat-Bold, Montserrat;
        font-weight: bold;
        color: #222222;
        line-height: 28px;
      }
      .right {
        cursor: pointer;
        height: 28px;
        font-size: 16px;
        font-family: Montserrat-Medium, Montserrat;
        font-weight: 500;
        color: #7a7a7a;
        line-height: 28px;
      }
    }
    .empty {
      display: flex;
      align-items: center;
      justify-content: center;
      background: #fff;
      border-radius: 12px;
    }
  }
  .tab:first-child {
    margin-bottom: 30px;
  }
  .tab.today {
    .empty {
      height: calc((100vh - 338px) / 2);
    }
  }
  .tab.courses {
    margin-bottom: 40px;
    .empty {
      height: calc((100vh - 338px) / 2 + 50px);
    }
  }
  .card-box {
    background: #fff;
    display: grid;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-items: stretch;
    width: 878px;
    padding: 20px;
    //max-height: 400px;
    overflow: scroll;
    border-radius: 12px;
    gap: 16px;
    grid-template-columns: 1fr 1fr;
    overflow: hidden;
  }
  .item-course {
    width: 410px;
    //height: 164px;
    background: #ffffff;
    border-radius: 12px;
    border: 2px solid #f1f2f4;
  }
}
/* 自定义滚动条 */
.home::-webkit-scrollbar {
  width: 4px; /* 设置水平滚动条的高度 */
}

.home::-webkit-scrollbar-thumb {
  background-color: transparent; /* 滚动条的颜色 */
  border-radius: 3px; /* 圆角边框 */
}

.home::-webkit-scrollbar-track {
  background: transparent; /* 滚动条轨道背景色设为透明 */
}
</style>
