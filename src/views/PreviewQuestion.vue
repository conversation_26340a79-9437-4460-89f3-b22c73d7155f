<template>
  <div class="preview-wrapper">
    <BackPre :title="$t('courses.previewQuestion.title')" />
    <div class="preview-inner">
      <div class="preview-content">
        <!-- <Loading v-if="loading" margin-top="200px" /> -->
        <!-- <template> -->
        <section class="section-container">
          <div class="preview-class">
            <!-- banner 已过期展示 -->
            <div v-if="courseDetailParams.status == 6" class="deadline-banner">
              <img src="@/assets/images/courses/previewClass/no-answer-banner.png" alt="" />
              <span>{{ $t('courses.previewQuestion.deadlineBannerTips') }} </span>
            </div>
            <div class="syllabus-card">
              <div class="syllabus-info">
                <div class="title">
                  <span></span>
                  <span>{{ previewData.title || '' }}</span>
                </div>
              </div>
            </div>
            <div class="time-card">
              <div class="left">
                <span class="type">
                  <span class="line"></span>
                  <span class="btn"> {{ $t('courses.previewQuestion.deadlineName') }}</span>
                </span>
                <span class="time">{{ previewData.deadline || '' }}</span>
              </div>
              <span class="cutline"></span>
              <div class="right">
                <span class="type">
                  <span class="line"></span>
                  <span class="btn"> {{ $t('courses.previewQuestion.answerTimeName') }}</span>
                </span>
                <span class="time">{{ previewData.duration || '' }}</span>
              </div>
            </div>
            <div class="problem-descriptors">
              <div class="desc">
                <span class="left">
                  <span class="line"></span>
                  <label>{{ $t('courses.previewQuestion.problemDescriptors[0]') }}</label>
                </span>
                <span class="right">{{
                  $t('courses.previewQuestion.problemDescriptors[1]', {
                    totalScore: previewData.totalScore
                  })
                }}</span>
              </div>
              <div class="questions-type">
                <!-- <template> -->
                <section v-for="(previewQuestion, index) in previewData.questions" :key="index">
                  <div class="type-icon"></div>
                  <div class="type-desc">{{ previewQuestion.type }}</div>
                  <span>x{{ previewQuestion.count }}</span>
                </section>
                <!-- </template> -->
              </div>
            </div>
          </div>
          <div class="solutions-wrap">
            <div @click="showModal(courseDetailParams.status)" class="solutions">
              {{ btnDesc }}
            </div>
          </div>
        </section>
        <!-- </template> -->
      </div>
    </div>
    <!-- 确认弹窗 -->
    <a-modal
      :open="visible"
      :width="340"
      :closable="false"
      :footer="null"
      centered
      :confirm-loading="confirmLoading"
      @cancel="handleCancel"
    >
      <div class="content">
        <span class="title">
          {{ $t('courses.previewQuestion.confirmTitle') }}
        </span>
        <span class="sub-title">
          {{ $t('courses.previewQuestion.confirmSubtitle', { duration: previewData.duration }) }}
        </span>
        <div class="btn">
          <div @click="handleCancelPreview" class="no">
            {{ $t('common.no') }}
          </div>
          <div @click="handleJumpPreview" class="yes">
            {{ $t('common.yes') }}
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import BackPre from '@/components/Common/BackPre.vue'
import { queryHomeWorkUrl, getPaperDetail } from '@/api/home/<USER>'
// import Loading from 'components/Common/Loading'
export default {
  data() {
    return {
      title: 'Preview question',
      courseDetailParams: {},
      previewData: {},
      burryPointData: {},
      previewBtnloading: false,
      loading: false,
      isError: false,
      visible: false,
      confirmLoading: false,
      backUrlNext: ''
    }
  },
  components: {
    // Loading
    BackPre
  },
  computed: {
    btnDesc() {
      const { status } = this.courseDetailParams
      if (status == 4 || status == 5 || status == 6) {
        return this.$t('courses.previewQuestion.solutions')
      } else {
        return this.$t('courses.previewQuestion.start')
      }
    }
  },
  methods: {
    /**
     * 更新Header属性
     */
    updateHeaderAttr() {
      // console.log(11111111, this.$route.query.fromTo)
      // this.$bus.$emit('updateHeaderAttr', {
      //   title: this.$t('courses.previewQuestion.title'),
      //   showGoback: true,
      //   backUrl: this.$route.query.fromTo
      // })
    },
    async getPreviewData() {
      this.loading = true
      const res = await getPaperDetail({
        planId: parseInt(this.courseDetailParams.lessonId),
        paperId: this.courseDetailParams.homeworkId
      })
      // this.$bus.$emit('reload-completed')
      if (!res || !res.code == 0) {
        this.isError = true
        return
      }
      this.loading = false
      this.previewData = res.data
    },

    showModal(status) {
      if (status == 4 || status == 5 || status == 6) {
        this.handleJumpPreview()
      } else {
        this.visible = true
      }
    },
    handleCancelPreview() {
      this.visible = false
    },
    handleJumpPreview() {
      const newParmas = window.JSON.parse(window.localStorage.getItem('paperJumpUrlParam'))
      queryHomeWorkUrl(newParmas).then(res => {
        if (res) {
          const previewUrl = res.data
          if (previewUrl) {
            // if (this.$route.query.fromTo) {
            //   const urlSymbol = this.$route.query.fromTo.includes('?') ? '&' : '?'
            //   this.backUrlNext = `${this.$route.query.fromTo}${urlSymbol}isNeedBackToCurrentTap=true`
            // }

            // window.localStorage.setItem('previewUrl', previewUrl)
            // window.localStorage.setItem('jumpPreviewQuestionUrl', this.backUrlNext)
            // const path = `/assignment-h5?assignmentUrl=${encodeURIComponent(previewUrl)}&backUrl=${
            //   this.backUrlNext
            // }`
            //  todo 不需要这个backUrl 了，没有用

            this.$router.push({
              path: '/home/<USER>',
              query: {
                assignmentUrl: previewUrl
              }
            })
          }
        }
      })
    }
  },
  mounted() {
    this.courseDetailParams = this.$route.query
    console.log(this.courseDetailParams, 'this.courseDetailParams')
    // todo 这里为啥需要 h5 页 设置这个状态呢？不能通过接口获得么， 改成删掉了，看看有啥不同吧
    // const syncStatus = window.localStorage.getItem('previewStatus')
    // if (syncStatus) {
    //   this.courseDetailParams.status = syncStatus
    // }
    this.getPreviewData()
    // this.$bus.$on('reload', () => {
    //   this.getPreviewData()
    // })
    // this.updateHeaderAttr()
  }
  // watch: {
  //   '$i18n.locale'(newValue) {
  //     newValue && this.updateHeaderAttr()
  //   }
  // },
  // destroyed() {
  //   this.$bus.$off('reload')
  // }
}
</script>

<style lang="scss" scoped>
.preview-wrapper {
  overflow: hidden;
  .preview-inner {
    //height: calc(100vh - 74px);
    height: calc(100vh - 234px);
    overflow-y: auto;
  }
  .preview-content {
    //width: 944px;
    margin: 0 auto 15px;
    .section-container {
      display: flex;
      flex-flow: column;
      //min-height: 85vh;
      position: relative;
      .solutions-wrap {
        position: fixed;
        bottom: 40px;
        height: 48px;
        width: 878px;
        margin: auto;
        text-align: center;
        .solutions {
          bottom: 0;
          width: 100%;
          height: 48px;
          background: linear-gradient(45deg, #ffd518 0%, #ffaa0a 100%);
          border-radius: 24px;
          font-size: 14px;
          font-weight: 500;
          color: rgba(255, 255, 255, 1);
          text-align: center;
          line-height: 48px;
          cursor: pointer;
        }
        .solutions:hover {
          background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgb(228, 150, 4) 100%);
        }
      }
    }
    .deadline-banner {
      position: relative;
      margin-bottom: 20px;
      img {
        width: 100%;
      }
      span {
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        line-height: 16px;
        position: absolute;
        top: 60px;
        width: 100%;
        display: inline-block;
        left: 0;
        text-align: center;
      }
    }
    .syllabus-card {
      display: flex;
      border-radius: 10px;
      margin-bottom: 1px;
      padding: 17px;
      background: #fff;
      position: relative;
      .syllabus-info {
        width: 100%;
        .title {
          display: flex;
          span:first-child {
            width: 42px;
            height: 42px;
            margin-right: 8px;
            display: inline-block;
            background-repeat: no-repeat;
            background-position: 0 0;
            background-size: 100%;
            background-image: url('../assets/images/courses/previewClass/label.png');
          }
          span:last-child {
            font-size: 18px;
            font-weight: 600;
            color: rgba(23, 43, 77, 1);
            line-height: 42px;
          }
        }
        .date {
          display: flex;
          margin-top: 25px;
          .deadline {
            display: flex;
            align-items: center;
            margin-right: 40px;
            .btn {
              display: block;
              padding: 0px 6px;
              font-size: 12px;
              text-align: center;
              color: #a2aab8;
              background: rgba(244, 246, 250, 1);
              border-radius: 5px;
              border: 1px solid rgba(222, 226, 231, 1);
              margin-right: 8px;
            }
            .time {
              display: inline-block;
              font-size: 12px;
              color: rgba(162, 170, 184, 1);
            }
          }
        }
      }
    }
    .time-card {
      display: flex;
      border-radius: 10px;
      margin: 12px 0;
      padding: 17px;
      background: #fff;
      position: relative;
      justify-content: space-between;
      align-items: center;
      .cutline {
        width: 1px;
        height: 10px;
        background: rgba(230, 230, 230, 1);
      }
      .left,
      .right {
        width: 49%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .type {
          display: flex;
          align-items: center;
          .line {
            display: inline-block;
            width: 4px;
            height: 10px;
            background: rgba(255, 170, 10, 1);
            border-radius: 4px;
            margin-right: 10px;
          }
          .btn {
            font-size: 16px;
            font-weight: 500;
            color: #172b4d;
            line-height: 19px;
          }
        }
        .time {
          display: inline-block;
          font-size: 16px;
          color: #a2aab8;
          color: rgba(162, 170, 184, 1);
        }
      }
      .right {
        padding-left: 20px;
      }
      .left {
        padding-right: 20px;
      }
    }
    .problem-descriptors {
      border-radius: 10px;
      margin-bottom: 1px;
      padding: 17px;
      background: #fff;
      position: relative;
      margin-top: 10px;
      .desc {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          span {
            width: 4px;
            height: 10px;
            background: rgba(255, 170, 10, 1);
            border-radius: 4px;
            margin-right: 10px;
          }
          label {
            font-size: 16px;
            font-weight: 500;
            color: #172b4d;
            line-height: 19px;
          }
        }
        .right {
          background: #fff5ee;
          border-radius: 4px;
          padding: 4px 6px;
          font-weight: 500;
          color: #fe7d1c;
        }
      }
      .questions-type {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        flex-direction: row;
        section {
          width: 48%;
          height: 60px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-right: 12px;
          border: 1px solid rgba(222, 226, 231, 0.5);
          padding: 15px;
          margin-top: 20px;
          display: flex;
          align-items: center;
          .type-icon {
            width: 4px;
            height: 10px;
            background: #dee2e7;
            margin-right: 10px;
            border-radius: 2px;
          }
          .type-desc {
            font-size: 14px;
            font-weight: 500;
            color: #a2aab8;
            line-height: 16px;
          }
          span {
            margin-left: auto;
            height: 16px;
            font-size: 14px;
            font-weight: 500;
            color: rgba(162, 170, 184, 1);
            line-height: 16px;
          }
        }
      }
    }
    .download {
      padding: 16px;
      background: #fff;
      border-radius: 10px;
      margin-top: 12px;
      .downloadCard {
        margin-bottom: 12px;
        &:last-child {
          margin-bottom: 2px;
        }
      }
    }
  }
}

.content {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  .title-icon {
    width: 183px;
    height: 97px;
    position: absolute;
    top: -68px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100%;
    background-image: url('../assets/images/courses/previewClass/title.png');
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    color: rgba(23, 43, 77, 1);
    display: block;
    margin-top: 35px;
  }
  .sub-title {
    font-size: 14px;
    color: rgba(162, 170, 184, 1);
    margin-top: 4px;
  }
  .btn {
    display: flex;
    margin-top: 40px;
    width: 100%;
    justify-content: space-between;
    .no {
      width: 147px;
      height: 48px;
      background: rgba(255, 243, 220, 1);
      border-radius: 24px;
      color: #ffaa0a;
      font-size: 16px;
      line-height: 48px;
      text-align: center;
      cursor: pointer;
      transition:
        color 0.15s ease-in-out,
        background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out,
        box-shadow 0.15s ease-in-out;
    }
    .yes {
      margin-left: 12px;
      width: 147px;
      height: 48px;
      background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
      border-radius: 24px;
      font-size: 16px;
      line-height: 48px;
      color: #fff;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
