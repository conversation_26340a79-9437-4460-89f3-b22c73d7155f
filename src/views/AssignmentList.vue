<template>
  <div class="assignment-list">
    <template v-if="!isInit">
      <Spin class="loading" size="large">
      </Spin>
    </template>
    <template v-else>
      <div v-if="isShowPractice" class="enter-practice">
        <div class="content">
          <div class="text">
            Practice Class Assignment
          </div>
          <div v-if="gameNewUrl" class="enter-btn" @click="gotoPractice">
            Go to Math World >>
          </div>
        </div>
      </div>
      <HomeworkList v-show="isInit" :homework-list="dataList" style="flex: 1; flex-shrink: 0; overflow: scroll;"/>
    </template>
  </div>
</template>
<script setup>
import { getAssignmentList, getPracticeToken, getGameUrl } from '@/api/home/<USER>'
import { ref, onMounted } from 'vue'
import _debounce from 'lodash/debounce'
import { useBaseData } from '@/stores/baseData'
import { Empty, Spin } from 'ant-design-vue'
const store = useBaseData()
import { useRequestInfo } from '@/hooks/useRequestInfo'
const { getToken, getSchoolCode, getTimezone } = useRequestInfo()
import HomeworkList from '@/components/Study/HomeworkList.vue'
const isShowPractice = ref(false)
const dataList = ref([])
const gameNewUrl = ref('')
const envStr = import.meta.env.VITE_APP_MODE
const gotoPractice = _debounce(() => {
  getPracticeToken({"data":{},"header":{"schoolCode": getSchoolCode(),"timezone": getTimezone(),"appName":"Official","appVersion":"1.0.0","platform":"Web","platformVersion":"","origin":"","attribution":"event_type=0&event_id=0&source_type=0&source_id=0",
  "unifiedAccessToken": getToken()
  }}).then(res => {
    if (res.code === 0) {
      const token = res.data
      const envMap = {
        production: 'prod',
        preprod: 'pre',
        beta: 'beta',
        dev: 'beta',
      }
      const schoolCode = store.schoolCode
      window.open(`${gameNewUrl.value}?token=${token}&env=${envMap[envStr]}&schoolCode=${schoolCode}&sourceUrl=${window.location.origin}/study`, '_blank');
    }
  })
}, 200)
const isInit = ref(false)
onMounted(() => {
  getAssignmentList({
    header: {
      platform: 'Web'
    }
  }).then((res) => {
    if (res.code === 0) {
      dataList.value = (res?.data?.tasks || []).map(task => {
        return {
          taskType: task.type,
          ...task.taskInfo
        }
      })
      if(res?.data?.practiceEduEntrance?.homeworkEntrance) {
        isShowPractice.value = true
        getGameUrl({
            "header": {
                "uid": null
            },
            "data": null
        }).then((res) => {
          if (res.code === 0) {
            const data = res.data
            const envMap = {
              production: '',
              preprod: 'Pre',
              beta: 'Test',
              dev: 'Test',
            }
            gameNewUrl.value = data[`content${envMap[envStr]}`] || ''
          }
        }).catch((err) => {
          console.log('报错了 getGameUrl', err)
        })
      }
    }
  }).catch((err) => {
    console.log('报错了 getAssignmentList', err)
  }).finally(() => {
    isInit.value = true
  })
})
</script>
<style lang="scss" scoped>
.assignment-list {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 170px);
  position: relative;
}
.loading {
  color: #fff;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -12px;
  margin-top: -12px;
}
.enter-practice {
  width: 878px;
  height: 140px;
  background: url('../assets/images/home/<USER>') no-repeat;
  background-size: 100% 100%;
  margin-bottom: 20px;
  .content {
    padding: 18px 0 18px 66px;
    .text {
      width: 300px;
      height: 60px;
      font-size: 28px;
      font-family: Montserrat-ExtraBoldItalic, Montserrat;
      font-weight: normal;
      color: #FFFFFF;
      line-height: 30px;
      letter-spacing: 1px;
      text-shadow: 0px 0px 6px #2392C9;
      margin-bottom: 12px;
      padding-left: 10px;
    }
    .enter-btn {
      cursor: pointer;
      display: inline-block;
      padding: 0 10px;
      height: 32px;
      line-height: 32px;
      background: #FF9F0A;
      border-radius: 25px;
      font-size: 16px;
      font-family: Montserrat-SemiBoldItalic, Montserrat;
      font-weight: normal;
      color: #FFFFFF;
    }
  }
}
</style>
