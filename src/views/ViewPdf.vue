<template>
  <div class="pdf-container">
    <iframe
      :src="pdfPreviewUrl"
      width="100%"
      height="100%"
      frameborder="0"
    ></iframe>
  </div>
</template>

<script setup>
import { useRoute } from 'vue-router'
const route = useRoute()
const pdfUrl = route.query.url;
// "https://kjdsfz-cdn.thethinkacademy.com/preClassExam/20250319/85201/preClassExam_aYpBzKklqg.pdf";
const pdfPreviewUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(pdfUrl)}`;
</script>

<style scoped>
.pdf-container {
  width: 100vw;
  height: 100vh;
  border: 1px solid #ddd;
}
</style>
<!-- <template>
  <div id="pdfViewer" style="width: 100%; height: 600px; overflow: auto; border: 1px solid #ddd;"></div>
</template>

<script setup>
import { getDocument, GlobalWorkerOptions } from "pdfjs-dist";
import { onMounted, nextTick } from 'vue';

// 设置 Worker 来源（确保路径可访问）
GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.12.313/pdf.worker.min.js`;

// 输入 PDF 的在线路径
const pdfUrl = "https://kjdsfz-cdn.thethinkacademy.com/preClassExam/20250319/85201/preClassExam_aYpBzKklqg.pdf";

const renderPDF = async () => {
  try {
    // 加载 PDF 文件
    const loadingTask = getDocument(pdfUrl);
    const pdf = await loadingTask.promise;

    // 获取预览容器
    const container = document.getElementById("pdfViewer");

    // 按照页码逐个渲染 PDF
    for (let pageNumber = 1; pageNumber <= pdf.numPages; pageNumber++) {
      const page = await pdf.getPage(pageNumber);

      // 设置画布和比例
      const viewport = page.getViewport({ scale: 1.5 });
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      canvas.height = viewport.height;
      canvas.width = viewport.width;

      container.appendChild(canvas);

      // 渲染页面内容到画布
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      await page.render(renderContext).promise;
    }
  } catch (error) {
    console.error("Error rendering PDF:", error);
  }
};

onMounted(() => {
  nextTick(() => {
    renderPDF();
  });
});
</script> -->
