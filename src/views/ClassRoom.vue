<template>
  <div>
    <!-- 加载中 -->
    <div v-if="!rtcIsInitialized" margin-top="200px">loading</div>
    <template v-else>
      <!-- 设备检查阶段 -->
      <ClassSetupComponent v-if="setupPhase" @enter-classroom="handleEnterClassroom" />

      <!-- 课堂内容阶段 -->
      <div v-else-if="classroomReady" class="small-class-live">
        <!-- 原有的课堂组件 -->
        <div class="loading" v-if="isLoading">
          <ClassLoading></ClassLoading>
        </div>
        <div class="error" v-if="errorObject.isShow">
          <ErrorStatus
            :message="errorObject.message"
            :showRefresh="errorObject.showRefresh"
            :isClassLiveOrPlayback="true"
            scene="ClassLiving"
          ></ErrorStatus>
        </div>
        <ClassRoomComponent
          v-if="ircIsInitialized && !isLoading && !errorObject.isShow"
        ></ClassRoomComponent>
      </div>
    </template>
  </div>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { useInitClass } from '@/hooks/useInitClass'
import ClassSetupComponent from '@/components/ClassroomContent/ClassSetupCom.vue'
import ClassRoomComponent from '@/components/ClassroomContent/ClassRoomCom.vue'

import { useRtcService } from '@/hooks/useRtcService'
import { useIrcService } from '@/hooks/useIrcService'

import useRtcListener from '@/hooks/useRtcListener'

import useIrcEvent from '@/hooks/useIrcListener'
import { useMicroPhoneKvEvent } from '@/hooks/useMicroPhoneStatus.js'
import { useRemoteAudioStatus } from '@/hooks/useRemoteAudioStatus.js'
import { useCameraStatus } from '@/hooks/useCameraStatus'
import { useClassData } from '@/stores/classroom'
import { Modal } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import ClassLoading from '@/components/ClassLoading/index.vue'
import ErrorStatus from '@/components/Common/ErrorStatus.vue'

const { initClassData, isLoading, errorObject } = useInitClass()
const { removeCameraEvent, listenCameraEvent } = useCameraStatus()
const router = useRouter()
const { t } = useI18n()
const setupPhase = ref(true)
const classroomReady = ref(false)
let removeRtcListener = null
const classStore = useClassData()
classStore.resetStore()

// 检查下课状态的函数
const checkAfterClassStatus = baseData => {
  console.log('afterClass', baseData?.liveStatus?.afterClass)
  if (baseData?.liveStatus?.afterClass) {
    // 课程已结束，显示提示并退出教室
    showAfterClassModal()
    return true // 返回true表示已下课
  }
  return false // 返回false表示未下课
}

// 显示下课提示弹窗
const showAfterClassModal = () => {
  Modal.info({
    title: t('classroom.modules.classEnded.title'),
    content: t('classroom.modules.classEnded.content'),
    okText: t('classroom.modules.classEnded.okText'),
    closable: false,
    maskClosable: false,
    onOk() {
      // 退出教室的逻辑
      router.push('/home/<USER>')
    }
  })
}

const handleEnterClassroom = () => {
  setupPhase.value = false
  classroomReady.value = true

  // 如果初始化接口已完成，则检查下课状态，未下课才连接服务
  if (!isLoading.value) {
    const isAfterClass = checkAfterClassStatus(classStore.baseData)
    if (!isAfterClass) {
      connetService(classStore.baseData)
    }
  }
}
const { initRtcService, rtcIsInitialized, getRtcService, releaseRtcService } = useRtcService()
const { initIrcService, ircIsInitialized, getIrcService, releaseIrcService } = useIrcService()

const { listenRemoteAudioEvent, removeRemoteAudioEvent } = useRemoteAudioStatus()
listenRemoteAudioEvent()
useMicroPhoneKvEvent()
listenCameraEvent()

let removeIrcListenerFun = null
let removeIrcHandleFun = null
const initIrcHandle = baseData => {
  // 初始化irc
  const { configs, stuInfo, planInfo, isAudition } = baseData
  const { id: uid } = stuInfo
  const { id: planId } = planInfo
  const ircOptions = {
    appId: configs.appId,
    appKey: configs.appKey,
    nickname: configs.stuIrcId,
    ircRooms: configs.ircRooms,
    location: configs.ircServer.location,
    confService: configs.ircServer.confService,
    logService: configs.ircServer.logService
  }
  const IrcService = initIrcService({
    businessId: configs.liveTypeId,
    userId: uid.toString(),
    isAudition,
    planId,
    ircOptions
  })
  const { addIrcListener, addIrcHandle, removeIrcHandle, removeIrcListener } =
    useIrcEvent(IrcService)
  addIrcListener()
  addIrcHandle()
  removeIrcListenerFun = removeIrcListener
  removeIrcHandleFun = removeIrcHandle
}
const connetService = baseData => {
  // 初始化irc
  initIrcHandle(baseData)
  const RtcService = getRtcService()
  const IrcService = getIrcService()
  const { configs, isAudition, stuLiveInfo } = baseData
  const { rtcConfig = {} } = configs
  RtcService.on('joinSuccess', () => {
    console.log('thinkClass:rtc登录成功')
    if (ircIsInitialized.value) {
      IrcService.ircLogin()
      console.log('thinkClass:irc登录成功')
    } else {
      console.error('irc初始化失败')
    }
  })
  RtcService.joinRtcChannel({
    rtcConfig: rtcConfig,
    publishStatus: isAudition,
    uid: stuLiveInfo.stuId
  })
}
const init = () => {
  // 初始化thinkClass
  const RtcService = initRtcService()
  removeRtcListener = useRtcListener(RtcService)

  initClassData()
    .then(baseData => {
      console.log('初始化成功，baseData:', baseData)
      // 如果已经点击开始上课，则检查下课状态，未下课才连接服务
      if (!setupPhase.value) {
        const isAfterClass = checkAfterClassStatus(baseData)
        if (!isAfterClass) {
          connetService(baseData)
        }
      }
    })
    .catch(error => {
      console.error('初始化失败:', error)
    })
}
onMounted(() => {
  init()
})
onBeforeUnmount(async () => {
  removeRtcListener && removeRtcListener()
  removeRemoteAudioEvent()
  removeCameraEvent()
  removeIrcListenerFun && removeIrcListenerFun()
  removeIrcHandleFun && removeIrcHandleFun()
  // 等待所有子组件完成清理
  await nextTick()
  await nextTick() // 等待所有子组件完成清理

  releaseRtcService()
  releaseIrcService()
})
</script>
<style lang="scss" scoped>
.small-class-live {
  height: 100vh;
  width: 100vw;
  background-color: black;
  .loading {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .error {
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 99;
  }
}
</style>
