<template>
  <div class="preview-wrapper">
    <BackPre :title="$t('courses.exam.title')" />
    <div class="preview-inner">
      <div class="preview-content">
        <!-- {{ examData }} -->
        <!-- <Loading v-if="loading" margin-top="200px" /> -->
        <!-- <template> -->
        <section class="section-container">
          <div class="preview-class">
            <div class="syllabus-card">{{ examData.title || '' }}</div>
            <div class="time-card">
              <div class="left">
                <span class="type">
                  <span class="line"></span>
                  <span class="btn"> {{ $t('courses.previewQuestion.deadlineTime') }}</span>
                </span>
                <span class="time">{{ examData.deadline || '' }}</span>
              </div>
              <span class="cutline"></span>
              <div class="right">
                <span class="type">
                  <span class="line"></span>
                  <span class="btn"> {{ $t('courses.previewQuestion.answerTimeName') }}</span>
                </span>
                <span class="time">{{ examData.duration }}</span>
              </div>
            </div>
            <div class="problem-descriptors">
              <div class="desc">
                <span class="left">
                  <span class="line"></span>
                  <label>{{ $t('courses.previewQuestion.problemDescriptors[0]') }}</label>
                </span>
                <span class="right">{{
                  $t('courses.previewQuestion.problemDescriptors[1]', {
                    totalScore: examData.totalScore,
                  })
                }}</span>
              </div>
              <div class="questions-type">
                <!-- <template> -->
                <section v-for="(item, index) in examData.questions" :key="index">
                  <div class="type-icon"></div>
                  <div class="type-desc">{{ item.type }}</div>
                  <span>x{{ item.count }}</span>
                </section>
                <!-- </template> -->
              </div>
            </div>
          </div>
        </section>
        <!-- </template> -->
      </div>
    </div>
    <div class="solutions-wrap" v-if="examData.homeworkStatus !== 5">
      <div @click="showModal(examData.homeworkStatus)" class="solutions">
        {{ btnDesc }}
      </div>
      <!-- <div
        class="btnTip"
        v-if="examData.isReward && (examData.homeworkStatus == 2 || examData.homeworkStatus == 1)"
      >
        <span class="tipIcon"></span>
        <span class="tipText">{{ $t('courses.exam.coinsTip') }}</span>
      </div> -->
    </div>
    <!-- 超时按钮 -->
    <div class="timeout-wrapper" v-else>
      <div class="left-pop">
        <div class="text-box">
          <div class="top-text">
            <div class="text-bg"></div>
            <div class="text-con">{{ $t('courses.previewQuestion.deadlineTitle') }}</div>
          </div>
          <div class="bottom-text">
            <div class="text-bg"></div>
            <div class="text-con">{{ $t('courses.previewQuestion.deadlineDesc') }}</div>
          </div>
        </div>
        <div class="timeout-btn" @click="showModal(examData.homeworkStatus)">{{ btnDesc }}</div>
      </div>
      <div class="right-people"></div>
    </div>
    <!-- 限制时间时，确认作答弹窗 -->
    <Modal
      :open="visible"
      :width="340"
      :closable="false"
      :footer="null"
      centered
      :confirm-loading="confirmLoading"
      @cancel="handleCancel"
    >
      <div class="content">
        <div class="title-icon"></div>
        <span class="title">
          {{ $t('courses.exam.confirmTitle') }}
        </span>
        <span class="sub-title">
          {{ $t('courses.exam.confirmSubtitle', { duration: examData.duration }) }}
        </span>
        <div class="btn">
          <div @click="handleCancel" class="no">
            {{ $t('courses.exam.cancelBtn') }}
          </div>
          <div @click="handleJump" class="yes">
            {{ $t('courses.exam.confirmBtn') }}
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script>
import BackPre from '@/components/Common/BackPre.vue'
import { getPaperOverview, getRecordPaperOverview } from '@/api/home/<USER>'
// import Loading from 'components/Common/Loading'
import { Modal } from 'ant-design-vue'

export default {
  data() {
    return {
      title: 'Preview question',
      courseDetailParams: {},
      examData: {},
      loading: false,
      visible: false,
      iframeUrl: '',
    }
  },
  components: {
    // Loading
    Modal,
    BackPre,
  },
  computed: {
    btnDesc() {
      const { homeworkStatus } = this.examData
      if (homeworkStatus == 3 || homeworkStatus == 4) {
        return this.$t('courses.exam.report')
      } else if (homeworkStatus == 5) {
        return this.$t('courses.exam.solutions')
      } else {
        return this.$t('courses.exam.start')
      }
    },
  },
  methods: {
    /**
     * 更新Header属性
     */
    updateHeaderAttr() {
      let backUrl = ''
      // 返回录播课详情页
      if (this.courseDetailParams.fromTo) {
        backUrl = this.courseDetailParams.fromTo
      } else {
        if (this.courseDetailParams.classType === 'RECORD') {
          backUrl = `/record-courses-detail?studentCourseId=${this.courseDetailParams.studentCourseId}&planId=${this.courseDetailParams.planId}&isNeedBackToCurrentTap=true&from=exam`
        } else {
          backUrl = `/courses-detail?classId=${this.courseDetailParams.classId}&planId=${this.courseDetailParams.planId}&isNeedBackToCurrentTap=true&from=exam`
        }
      }

      // this.$bus.$emit('updateHeaderAttr', {
      //   title: this.$t('courses.exam.title'),
      //   showGoback: true,
      //   backUrl: backUrl
      // })
    },
    getData() {
      // 录播课
      if (this.courseDetailParams.classType === 'RECORD') {
        this.getRecordData()
        return
      }
      this.getLiveData()
    },
    // 直播课
    async getLiveData() {
      this.loading = true
      this.courseDetailParams.classId = Number(this.courseDetailParams.classId)
      this.courseDetailParams.homeworkType = Number(this.courseDetailParams.homeworkType)
      this.courseDetailParams.planId = Number(this.courseDetailParams.planId)
      const res = await getPaperOverview({
        ...this.courseDetailParams,
        platform: '3',
      })

      this.loading = false
      // this.$bus.$emit('reload-completed')
      this.examData = res.data
      this.iframeUrl = this.examData.homeworkUrl
      if (this.examData.reportType === 2) {
        this.iframeUrl = this.iframeUrl + '&reportType=2&platform=3'
      }
      if (this.iframeUrl) {
        let backUrl = ''
        if (this.courseDetailParams.fromTo) {
          backUrl = this.courseDetailParams.fromTo
        } else {
          if (this.courseDetailParams.classType === 'RECORD') {
            backUrl = `/record-courses-detail?studentCourseId=${this.courseDetailParams.studentCourseId}&planId=${this.courseDetailParams.planId}&isNeedBackToCurrentTap=true&from=exam`
          } else {
            backUrl = `/courses-detail?classId=${this.courseDetailParams.classId}&planId=${this.courseDetailParams.planId}&isNeedBackToCurrentTap=true&from=exam`
          }
        }

        // window.sessionStorage.setItem('iframeUrl', iframeUrl)
        window.sessionStorage.setItem('backUrl', backUrl)
      }
    },
    // 录播课
    async getRecordData() {
      this.loading = true
      const res = await getRecordPaperOverview({
        homeworkType: Number(this.courseDetailParams.homeworkType),
        entityId: this.courseDetailParams.entityId,
        bindType: 1,
        platform: '3',
      })
      this.loading = false
      // this.$bus.$emit('reload-completed')
      this.examData = res.data
      const { expirationTime, permanent } = this.courseDetailParams
      // 截止日期
      this.examData.deadline = permanent
        ? this.$t('courses.recordCourse.permanentTip')
        : expirationTime
      this.iframeUrl = this.examData.homeworkUrl
      const userInfo = JSON.parse(window.localStorage.getItem('userInfo'))
      if (this.iframeUrl) {
        // window.sessionStorage.setItem(
        //   'iframeUrl',
        //   `${iframeUrl}&token=${userInfo?.unifiedAccessToken}`
        // )
        // 需求调整：改为跳转到讲次列表页面
        window.sessionStorage.setItem(
          'backUrl',
          `/record-courses-detail?studentCourseId=${this.courseDetailParams.studentCourseId}&planId=${this.courseDetailParams.planId}&isNeedBackToCurrentTap=true&from=exam`,
        )
      }
    },
    showModal(status) {
      if (status == 2 && this.examData.limitTime) {
        this.visible = true
      } else {
        this.handleJump()
      }
    },
    handleCancel() {
      this.visible = false
    },
    handleJump() {
      this.$router.push({
        path: `/home/<USER>
        query: {
          assignmentUrl: this.iframeUrl,
          // backUrl: ''
        },
        // ?assignmentUrl=${encodeURIComponent(window.sessionStorage.getItem('iframeUrl'))}&backUrl=${encodeURIComponent(window.sessionStorage.getItem('backUrl'))}`
      })
    },
  },
  mounted() {
    this.courseDetailParams = this.$route.query
    this.getData()
    // this.$bus.$on('reload', () => {
    //   this.getData()
    // })
    this.updateHeaderAttr()
  },
  // watch: {
  //   '$i18n.locale'(newValue) {
  //     newValue && this.updateHeaderAttr()
  //   }
  // },
  unmounted() {
    // this.$bus.$off('reload')
  },
}
</script>

<style lang="scss" scoped>
.preview-wrapper {
  //height: calc(100vh - 73px);
  overflow: hidden;
  .preview-inner {
    height: calc(100vh - 234px);
    overflow-y: auto;
    padding-bottom: 56px;
  }
  /* 自定义滚动条 */
  .preview-inner::-webkit-scrollbar {
    width: 4px; /* 设置水平滚动条的高度 */
  }

  .preview-inner::-webkit-scrollbar-thumb {
    background-color: transparent; /* 滚动条的颜色 */
    border-radius: 3px; /* 圆角边框 */
  }

  .preview-inner::-webkit-scrollbar-track {
    background: transparent; /* 滚动条轨道背景色设为透明 */
  }
  .solutions-wrap {
    // margin-top: 150px;
    position: fixed;
    bottom: 40px;
    height: 48px;
    width: 878px;
    margin: auto;
    text-align: center;
    .btnTip {
      position: relative;
      border-radius: 10px;
      padding: 0 7px;
      border: 1px solid #ffaa0a;
      background-color: #fff3dc;
      top: -99px;
      display: inline-flex;
      align-items: center;

      .tipIcon {
        width: 27px;
        display: inline-block;
        height: 29px;
        margin: 6px 2px 2px 0;
        background: url('../assets/images/courses/start-tip-icon.png') no-repeat;
        background-size: 100%;
      }

      .tipText {
        font-size: 14px;
        font-weight: 500;
        color: #ffaa0a;
        line-height: 16px;
      }
      &:after {
        content: '';
        width: 0px;
        height: 0px;
        border-color: #fff3dc transparent transparent transparent;
        border-style: solid;
        border-width: 5px;
        position: absolute;
        bottom: -9px;
        left: 0;
        right: 0;
        margin: auto;
      }

      &:before {
        content: '';
        width: 0px;
        height: 0px;
        border-color: #ffaa0a transparent transparent transparent;
        border-style: solid;
        border-width: 5px;
        position: absolute;
        bottom: -10px;
        left: 0;
        right: 0;
        margin: auto;
      }
    }
    .solutions {
      bottom: 0;
      width: 100%;
      height: 48px;
      background: linear-gradient(45deg, #ffd518 0%, #ffaa0a 100%);
      border-radius: 24px;
      font-size: 14px;
      font-weight: 500;
      color: rgba(255, 255, 255, 1);
      text-align: center;
      line-height: 48px;
      cursor: pointer;
    }
    .solutions:hover {
      background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgb(228, 150, 4) 100%);
    }
  }
  .preview-content {
    //width: 944px;
    margin: 0 auto 10px;
    .section-container {
      display: flex;
      flex-flow: column;
      // min-height: 85vh;
      position: relative;
    }
    .deadline-banner {
      position: relative;
      margin-bottom: 20px;
      img {
        width: 100%;
      }
      span {
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
        line-height: 16px;
        position: absolute;
        top: 60px;
        width: 100%;
        display: inline-block;
        left: 0;
        text-align: center;
      }
    }
    .syllabus-card {
      background: #fff;
      border-radius: 10px;
      height: 80px;
      line-height: 80px;
      padding-left: 20px;
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100%;
      background-image: url('../assets/images/courses/exam_title_bg.png');
      font-size: 20px;
      font-weight: 600;
      color: hsl(0, 0%, 100%);
    }
    .time-card {
      display: flex;
      height: 60px;
      border-radius: 10px;
      margin: 10px 0;
      padding: 0 17px;
      background: #fff;
      position: relative;
      justify-content: space-between;
      align-items: center;
      .cutline {
        width: 1px;
        height: 10px;
        background: rgba(230, 230, 230, 1);
      }
      .left,
      .right {
        width: 49%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .type {
          display: flex;
          align-items: center;
          .line {
            display: inline-block;
            width: 4px;
            height: 10px;
            background: rgba(255, 170, 10, 1);
            border-radius: 4px;
            margin-right: 10px;
          }
          .btn {
            font-size: 16px;
            font-weight: 500;
            color: #172b4d;
            line-height: 19px;
          }
        }
        .time {
          display: inline-block;
          font-size: 16px;
          color: #a2aab8;
          color: rgba(162, 170, 184, 1);
        }
      }
      .right {
        padding-left: 20px;
      }
      .left {
        padding-right: 20px;
      }
    }
    .problem-descriptors {
      border-radius: 10px;
      margin-bottom: 1px;
      padding: 17px;
      background: #fff;
      position: relative;
      margin-top: 10px;
      .desc {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .left {
          display: flex;
          align-items: center;
          span {
            width: 4px;
            height: 10px;
            background: rgba(255, 170, 10, 1);
            border-radius: 4px;
            margin-right: 10px;
          }
          label {
            font-size: 16px;
            font-weight: 500;
            color: #172b4d;
            line-height: 19px;
          }
        }
        .right {
          background: #fff5ee;
          border-radius: 4px;
          padding: 4px 6px;
          font-weight: 500;
          color: #fe7d1c;
        }
      }
      .questions-type {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        flex-direction: row;
        section {
          width: 48%;
          height: 60px;
          background: #f8f9fa;
          border-radius: 8px;
          margin-right: 12px;
          border: 1px solid rgba(222, 226, 231, 0.5);
          padding: 15px;
          margin-top: 20px;
          display: flex;
          align-items: center;
          .type-icon {
            width: 4px;
            height: 10px;
            background: #dee2e7;
            margin-right: 10px;
            border-radius: 2px;
          }
          .type-desc {
            font-size: 14px;
            font-weight: 500;
            color: #a2aab8;
            line-height: 16px;
          }
          span {
            margin-left: auto;
            height: 16px;
            font-size: 14px;
            font-weight: 500;
            color: rgba(162, 170, 184, 1);
            line-height: 16px;
          }
        }
      }
    }
    .download {
      padding: 16px;
      background: #fff;
      border-radius: 10px;
      margin-top: 12px;
      .downloadCard {
        margin-bottom: 12px;
        &:last-child {
          margin-bottom: 2px;
        }
      }
    }
  }
}
.content {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  .title-icon {
    width: 183px;
    height: 97px;
    position: absolute;
    top: -68px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100%;
    background-image: url('../assets/images/courses/previewClass/title.png');
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    color: rgba(23, 43, 77, 1);
    display: block;
    margin-top: 35px;
  }
  .sub-title {
    font-size: 14px;
    color: rgba(162, 170, 184, 1);
    margin-top: 4px;
  }
  .btn {
    display: flex;
    margin-top: 40px;
    width: 100%;
    justify-content: space-between;
    .no {
      width: 147px;
      height: 48px;
      background: rgba(255, 243, 220, 1);
      border-radius: 24px;
      color: #ffaa0a;
      font-size: 16px;
      line-height: 48px;
      text-align: center;
      cursor: pointer;
      transition:
        color 0.15s ease-in-out,
        background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out,
        box-shadow 0.15s ease-in-out;
    }
    .yes {
      margin-left: 12px;
      width: 147px;
      height: 48px;
      background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
      border-radius: 24px;
      font-size: 16px;
      line-height: 48px;
      color: #fff;
      text-align: center;
      cursor: pointer;
    }
  }
}

.timeout-wrapper {
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  width: 944px;
  left: 0;
  right: 0;
  margin: auto;
  .left-pop {
    background: url('../assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;
    width: 818px;
    height: 98px;
    padding-left: 32px;
    display: flex;
    justify-content: space-between;
    .text-box {
      display: flex;
      flex-direction: column;
      .top-text {
        width: fit-content;
        font-size: 22px;
        font-weight: bold;
        color: #ffffff;
        line-height: 26px;
        text-shadow: 0px 2px 4px rgba(249, 92, 7, 0.5);
        padding-right: 15px;
        position: relative;
        margin-top: 22px;
      }
      .bottom-text {
        width: fit-content;
        font-size: 16px;
        font-weight: 600;
        color: #ffffff;
        line-height: 19px;
        text-shadow: 0px 2px 4px rgba(249, 92, 7, 0.5);
        padding-right: 13px;
        position: relative;
        margin-top: 9px;
      }
      .text-bg {
        position: absolute;
        bottom: -3px;
        width: 100%;
        border-radius: 9px;
        height: 16px;
        background: rgba(255, 78, 5, 0.36);
      }
      .text-con {
        position: relative;
        z-index: 2;
      }
    }
    .timeout-btn {
      margin: 27px 54px 0 0;
      width: 200px;
      height: 44px;
      background: #ffffff;
      border-radius: 22px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      color: #ffaa0a;
      line-height: 44px;
      cursor: pointer;
    }
  }
  .right-people {
    background: url('../assets/images/home/<USER>') no-repeat;
    background-size: 100% 100%;
    width: 121px;
    height: 118px;
  }
}
</style>
