<template>
  <div class="page-wrapper">
    <div class="common-assigment">
      <iframe @load="handleIframeLoad" id="common-assigment" class="iframe" :src="iframeUrl" />
    </div>
    <div v-if="visibleBigImage && showCoinModals" class="tips">
      <a-modal
        :width="420"
        centered
        :closable="false"
        :maskClosable="false"
        :footer="null"
        v-model="visibleBigImage"
      >
        <div class="content">
          <span class="label">
            <img src="@/assets/images/home/<USER>" alt="" />
          </span>
          <span class="text">
            {{ $t('courses.assignment.earnedNotice') }}
            <label
              >{{ coins.submitCoin }}
              <span v-if="coins.earlySubmitCoin">+{{ coins.earlySubmitCoin }}</span></label
            >{{ $t('common.coins') }}</span
          >
          <span v-if="coins.earlySubmitCoin" class="sub-text">
            {{ $t('courses.assignment.submissionNotice') }}
            <label>{{ coins.earlySubmitCoin }}</label>
            {{ $t('common.coins') }}
          </span>
          <a-button
            type="primary"
            shape="round"
            size="large"
            :class="[buttonClass]"
            :loading="loading"
            @click.stop="handleClose"
            data-log="知道了"
          >
            {{ $t('common.gotIt') }}
          </a-button>
        </div>
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
// import logger from 'utils/logger'
import { useBindEvent } from '@/hooks/useH5Event'
import { useBaseData } from '@/stores/baseData'
import { useRequestInfo } from '@/hooks/useRequestInfo'
const { getToken } = useRequestInfo()
const store = useBaseData()
const { bindEvent } = useBindEvent()
const route = useRoute()
const iframeUrl = ref('')

const updateHeaderAttr = () => {}
const initRouteParams = async () => {
  const baseUrl = route.query.assignmentUrl
  console.log('baseUrl', baseUrl)
  // iframeUrl.value = route.query.assignmentUrl
  //const baseUrl = route.query.url

  // 如果包含了，说明不用自己拼接了
  if (baseUrl.includes('token')) {
    // &device=pc 看起来还是不行呢，通讯这个当作移动端了呢，好奇怪呢
    iframeUrl.value = `${baseUrl}`
  } else {
    // 阶段考试场景需要自己拼接参数
    const schoolCode = store.schoolCode
    iframeUrl.value = `${baseUrl}&token=${getToken()}&schoolCode=${schoolCode}`
  }
  // console.log('taskExerciseBook', baseUrl, iframeUrl.value)
  // todo 传递了backUrl 但是并没有实际使用呢，直接用了路由回退就好了
  // backUrl.value = route.query.backUrl
  updateHeaderAttr()
}

// todo 待日日志完善
const handleIframeLoad = () => {
  // logger.send({
  //   content: {
  //     msg: `加载H5成功--${route.query.pageTypeName || 'common-assignmentUrl'}`,
  //     url: iframeUrl.value
  //   }
  // })
}
bindEvent()
onMounted(async () => {
  console.log('taskExerciseBook', route)
  // await initMessageParams()
  // bindEvent()
  initRouteParams()
  // updateHeaderAttr()
})
</script>

<style lang="scss" scoped>
.common-assigment {
  height: calc(100vh - 170px);
  overflow-y: hidden;
  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
.tips {
  height: 500px;
}
// todo 作业弹窗过期待验证格式
.content {
  text-align: center;
  .label {
    img {
      display: inline-block;
      width: 400px;
      position: absolute;
      left: 10px;
      top: -60px;
    }
  }
  .text {
    margin-top: 60px;
    display: inline-block;
    text-align: center;
    // margin: c;
    /* margin: 0 auto; */
    width: 100%;
    font-size: 18px;
    font-weight: 500;
    color: #172b4d;
    line-height: 21px;
    label,
    span {
      font-size: 24px;
      font-weight: 600;
      color: #ff503f;
      line-height: 29px;
      display: inline-block;
      margin-right: 5px;
    }
  }
  .sub-text {
    font-size: 16px;
    // font-weight: 500;
    color: #a2aab8;
    label {
      color: #ff503f;
      margin-right: 4px;
    }
  }
  .ant-btn.ant-btn-primary {
    width: 330px;
    margin-top: 25px;
    // border-radius: 16px 16px 0px 0px;
  }
}
// 修改modal样式
:deep(.ant-modal-mask) {
  background-color: rgba(0, 0, 0, 0.4);
}
:deep(.ant-modal-header) {
  display: none;
}
:deep(.ant-modal-content) {
  border-radius: 16px;
  // background: transparent;
  // width: 100%;
  // height: 580px;
  // background: linear-gradient(225deg, rgba(0, 223, 160, 1) 0%, rgba(90, 255, 77, 1) 100%);
  // border-radius: 20px;
}
:deep(.ant-modal) {
  height: 200px;
  top: 50%;
  margin-top: -150px;
  .ant-modal-body {
    height: 200px;
    padding: 0;
  }
}
</style>
