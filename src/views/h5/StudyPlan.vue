<template>
  <div class="page-wrapper">
    <div class="study-plan">
      <iframe v-if="iframeUrl" @load="handleIframeLoad" id="study-plan" class="iframe" :src="iframeUrl" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getStudyPlanUrl } from '@/api/home/<USER>'
// const iframeUrl = ref('https://student-homework.thethinkacademy.com/thinkPractice/#/studyPlan?id=437&token=146c1d9d8ff680db40eb8f707d4a5f41&schoolCode=85201')
const iframeUrl = ref('')
const init = () => {
  // https://student-homework.thethinkacademy.com/thinkPractice/#/studyPlan?id=437&token=146c1d9d8ff680db40eb8f707d4a5f41&schoolCode=85201
  getStudyPlanUrl({}).then((res) => {
    if(res.code === 0 && res?.data?.url) {
      iframeUrl.value = res?.data?.url
    }
  }).catch((err) => {
    console.log('报错了 getStudyPlanUrl', err)
  })
}
// todo 待日日志完善
const handleIframeLoad = () => {
  // logger.send({
  //   content: {
  //     msg: '加载H5成功-错题本',
  //     url: iframeUrl.value
  //   }
  // })
}
onMounted(() => {
  init()
})
</script>
<style lang="scss" scoped>
.study-plan {
  height: calc(100vh - 170px);
  overflow-y: hidden;
  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style>
