<template>
  <div class="page-wrapper">
    <div class="lessonReport">
      <iframe @onload="handleIframeLoad" id="lessonReport" class="iframe" :src="lessonReportUrl" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRequestInfo } from '@/hooks/useRequestInfo'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const { getToken, getSchoolCode, getTimezone } = useRequestInfo()
const token = getToken()
const schoolCode = getSchoolCode()
const timezone = getTimezone()
const lessonReportUrl = ref('')
// const params = ref({})
const initMessageParams = () => {
  // let params = {}
  // const { unifiedAccessToken, uid } = await getUserInfo()
  // const { appName, appVersion } = await getAppInfo()
  // const platform = getPlatform()

  // params.appName = appName
  // params.appVersion = appVersion
  // params.platform = platform

  // params.Authorization = token
  // params.uid = 7595828
  // console.log('看下一呢', schoolCode)
  // params.schoolCode = schoolCode
  // params.timezone = timezone
  // return params
  console.log('看下一下 token', timezone)
  return {
      Authorization: token,
      appName: "ThinkAcademy",
      appVersion: "3.6.0",
      platform: "mac",
      schoolCode: getSchoolCode(),
      timezone: getTimezone(),
      //uid: 7595828 看h5了可以不传的，没有报错, 取值studentId 取自 url 头部，但接口也没有返
    }
}
const handleIframeLoad = () => {
  // todo 日志
}
const initRouteParams = async () => {
  lessonReportUrl.value = `${route.query.lessonReportUrl}&schoolCode=${schoolCode}&appName=webStudent`
}
const sendMessageToH5 = (e) => {
    console.log('lessonReport-message', e)
    const data = e.data || {}
    const type = data.type
    // 跳转到登录页
    if (type == 'common.init') {
      // H5端需要的参数
      console.log('lessonReport-message contentWindow', document.getElementById('lessonReport').contentWindow)
      document.getElementById('lessonReport').contentWindow.postMessage(
        {
          type: 'common.init',
          params: initMessageParams()
          // params.value
        },
        '*'
      )
    }
    // 返回到课次列表
    if (type == 'common.backToClient') {
      // route_href('/#' + this.backUrl)
      router.back()
      // ({
      //   path: this.backUrl
      // })
    }
  }
const bindEvent = () => {
  window.addEventListener('message', sendMessageToH5, false)
}
onMounted(() => {
  // params.value = initMessageParams()
  bindEvent()
  nextTick(() => {
    initRouteParams()
  })
})
onUnmounted(() => {
  window.removeEventListener('message', sendMessageToH5)
})
</script>
<style lang="scss" scoped>
.lessonReport {
  height: calc(100vh - 170px);
  overflow-y: hidden;
  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style>
