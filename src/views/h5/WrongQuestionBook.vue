<template>
  <div class="page-wrapper">
    <Spin v-if="loading" class="loading" size="large"></Spin>
    <div class="wrongQuestionBook">
      <iframe @load="handleIframeLoad" id="wrongQuestionBook" class="iframe" :src="iframeUrl" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
// import logger from 'utils/logger'
import { useBindEvent } from '@/hooks/useH5Event';
import { useBaseData } from '@/stores/baseData'
import { useRequestInfo } from '@/hooks/useRequestInfo'
import { Spin } from 'ant-design-vue'
const { getToken } = useRequestInfo()
const store = useBaseData()
const {
  bindEvent
} = useBindEvent();
const route = useRoute()
const iframeUrl = ref(null)
const loading = ref(true)
const timer = ref(null)
const updateHeaderAttr = () => {
}

const initRouteParams = async () => {
  const baseUrl = import.meta.env.VITE_HOMEWORK_H5
  const schoolCode = store.schoolCode
  iframeUrl.value = `${baseUrl}/wqc/#/?token=${getToken()}&schoolCode=${schoolCode}&hideHeaderFlag=1`
  console.log('WrongQuestionBook11', baseUrl, iframeUrl.value)
  // backUrl.value = query.fromTo
  updateHeaderAttr()
}

// todo 待日日志完善
const handleIframeLoad = () => {
  // loading.value = false
  // logger.send({
  //   content: {
  //     msg: '加载H5成功-错题本',
  //     url: iframeUrl.value
  //   }
  // })
  nextTick(() => {
    timer.value = setTimeout(() => {
      loading.value = false
      clearTimeout(timer)
      timer.value = null
    }, 1000);
  })
}

onMounted(async () => {
  console.log('WrongQuestionBook', route)
  // await initMessageParams()
  bindEvent()
  initRouteParams()
  // updateHeaderAttr()
})
</script>

<style lang="scss" scoped>
.page-wrapper {
  position: relative;
  .loading {
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    margin-left: -12px;
    margin-top: -12px;
  }
}
.wrongQuestionBook {
  height: calc(100vh - 170px);
  overflow-y: hidden;
  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style>
