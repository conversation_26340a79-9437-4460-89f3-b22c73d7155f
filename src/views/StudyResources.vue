<template>
  <div class="resource-wrapper">
    <BackPre :title="$t('courses.studyResources.title')" />
    <div class="content">
      <div v-if="courseDetailData?.planBlackboards?.length">
        <!-- <div class="download-title">{{ $t('courses.detail.classNotes') }}</div> -->
        <div class="download">
          <DownloadCard
            v-for="(downloadItem, key) in courseDetailData.planBlackboards"
            :downloadItem="downloadItem"
            :burryPointData="burryPointData"
            :isNote="true"
            :key="key"
            @openPdf="openPdf(downloadItem.fileUrl, downloadItem.name, 1)"
            class="downloadCard"
          ></DownloadCard>
        </div>
      </div>
      <div v-if="courseDetailData?.materials?.length">
        <!-- <div class="download-title">{{ $t('courses.detail.learningMaterials') }}</div> -->
        <div class="download">
          <DownloadCard
            v-for="(downloadItem, key) in courseDetailData.materials"
            :downloadItem="downloadItem"
            :burryPointData="burryPointData"
            :key="key"
            @openPdf="openPdf(downloadItem.fileUrl, downloadItem.name, 2)"
            @openVideo="openVideo(downloadItem.fileUrl, downloadItem.name)"
            class="downloadCard"
          ></DownloadCard>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import BackPre from '@/components/Common/BackPre.vue'
import DownloadCard from '@/components/Courses/DownloadCard.vue'
import { useRoute, useRouter } from 'vue-router'
import { ref, onMounted } from 'vue'
import { getDownloadResources } from '@/api/home/<USER>'
const route = useRoute()
const router = useRouter()
const loading = ref(false)

const isError = ref(false)
const courseDetailData = ref([])
const courseDetailParams = ref({}) // 需要从父组件传入的内容

const openPdf = (fileUrl, name, flag) => {
  if (flag === 1) {
    window.open(fileUrl, '_blank');
  } else {
    name
    // // todo 新窗口打开这个
    // router.push({
    //   path: '/viewPdf',
    //   query: {
    //     url: fileUrl
    //   }
    // })
    const routeData = router.resolve({
      path: '/viewPdf',
      query: { url: fileUrl }
    });
    window.open(routeData.href, '_blank');
    // const url = `${window.location.origin}/viewPdf?url=${encodeURIComponent(fileUrl)}`;
    // window.open(url, '_blank');
  }
}
const openVideo = (fileUrl, name) => {
  name
  // todo 新窗口打开这个
  // router.push({
  //   path: '/playVideo'
  // })
  const routeData = router.resolve({
    path: '/playVideo',
    query: { url: fileUrl }
  });
  window.open(routeData.href, '_blank');
}
const getResourcesList = async () => {
  loading.value = true // 使用 ref 时通过 `.value` 修改值
  isError.value = false

  try {
    const res = await getDownloadResources({ planId: Number(courseDetailParams.value.lessonId) })    
    if (!res || res.code !== 0) {
      isError.value = true
      loading.value = false
      return
    }
    courseDetailData.value = res.data
  } catch (error) {
    isError.value = true
    console.error(error) // 捕获错误（如果有）
  } finally {
    loading.value = false
  }
}
onMounted(() => {
  courseDetailParams.value = route.query
  getResourcesList()
})
</script>
<style lang="scss" scoped>
.resource-wrapper {
  .content {
    background: #fff;
    border-radius: 12px;
    padding: 20px 20px 10px;
  }
}
</style>