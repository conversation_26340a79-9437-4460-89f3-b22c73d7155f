<template>
  <div class="page-wrapper" data-log="课程详情">
    <BackPre :title="$t('courses.detail.title')" :backPath="fromCompletedCourses ? '/home/<USER>' : '/home/<USER>'"/>
    <div class="syllabus-list" ref="syllabusList">
      <template v-if="isError">
        <ErrorStatus data-log="错误状态" @click-refresh="queryData" scene="CoursesDetails" />
      </template>
      <template v-else>
        <Loading v-if="loading" margin-top="200px" />
        <section v-else>
          <SyllabusNav :course-datas="courseDatas" />
          <SyllabusList :teachers="teachers" :course-datas="courseDatas" :data-list="dataList" />
        </section>
      </template>
      <NpsFeedback v-if="showNpsModal" :plan-id="planId" :classId="classId" :tag-list="tagList" />
    </div>
  </div>
</template>

<script>
import BackPre from '@/components/Common/BackPre.vue'
import Loading from '@/components/Common/Loading.vue'
import ErrorStatus from '@/components/Common/ErrorStatus.vue'
import SyllabusList from '@/components/Courses/SyllabusList.vue'
import SyllabusNav from '@/components/Courses/SyllabusNav.vue'
import { queryLessonListApi } from '@/api/home/<USER>'
// import { preloadCoursewarePackages, preloadCourseware } from 'utils/courses'
import NpsFeedback from '@/components/npsFeedback/index.vue'
// import npsHandler from '@/mixins/npsEvent.js'
import { useNps } from '@/hooks/useNpsEvent.js';

export default {
  name: 'CoursesDetail',
  components: {
    BackPre,
    ErrorStatus,
    Loading,
    SyllabusList,
    SyllabusNav,
    NpsFeedback
  },
  // mixins: [npsHandler],
  data() {
    const { classId, fromCompletedCourses = 0 } = this.$route.query
    const { showNpsModal, planId, tagList } = useNps(this.$route, this.$router)
    return {
      showNpsModal,
      planId,
      tagList,
      subject: '',
      isError: false,
      loading: true, // loading 判断是否加载内部组件，为false时加载内部组件。初始化为false会出现加载(初始化，loading=false)->销毁（调用接口，loading=true）->加载（调用完毕，loading=false），重复加载内部组件
      classId,
      dataList: [],
      teachers: [],
      courseDatas: {},
      fromCompletedCourses: Number(fromCompletedCourses),
    }
  },
  created() {
    this.updateHeaderAttr()
  },
  mounted() {
    this.queryData(() => {
      // this.checkUpdate()
      // this.preload()
      this.addContactTeacherSensor()
      this.$sensors.track('hw_class_detail_pv', {
        course_category: this.courseDatas.courseType || 0
      })
    })
  },
  methods: {
    addContactTeacherSensor() {
      const guideTeas = this.teachers.filter(item => item.identityType === 2)
      const arrMaster = this.teachers.filter(
        item => item.identityType === 1 && !!item.contactInfoListV2?.length
      )
      if (guideTeas.length) {
        const arrGuide = guideTeas.filter(item => !!item.contactInfoListV2?.length)
        if (arrGuide.length) {
          this.$sensors.track('hw_contact_teacher_icon_show', {
            teacher_category: 2
          })
        }
      } else {
        if (arrMaster.length) {
          this.$sensors.track('hw_contact_teacher_icon_show', {
            teacher_category: 1
          })
        }
      }
    },
    /**
     * 更新Header属性
     */
    updateHeaderAttr() {
      // const title = this.$t('courses.detail.title')
      // this.$bus.$emit('updateHeaderAttr', {
      //   title,
      //   showGoback: true,
      //   backUrl: '/home/<USER>'
      // })
    },

    /**
     * 查询讲次数据
     */
    queryData(callback) {
      this.loading = true
      this.isError = false
      queryLessonListApi({
        classId: this.classId
      })
        .then(res => {
          // @log-ignore
          this.loading = false
          if (!res || res.code != 0) {
            this.isError = true
            return
          }
          const data = res.data || {}
          const classInfo = data.classInfo
          this.teachers = classInfo.teachers || []
          this.dataList = data.scheduleList || []
          this.courseDatas = {
            tag: classInfo.tag || '',
            startDate: classInfo.startDate || '',
            endDate: classInfo.endDate || '',
            classId: classInfo.classId || '',
            className: classInfo.className || '',
            teachers: classInfo.teachers || [],
            timeDesc: classInfo.timeDesc,
            timeDesc2: classInfo.timeDesc2 || '',
            courseType: classInfo.courseType || 0,
            startTimestamp: classInfo.startTimestamp,
            endTimestamp: classInfo.endTimestamp,
          }
          callback && callback(data)

          const hashString = location.hash.split('&')[0]
          const lastClickPos = localStorage.getItem(hashString)
          if (lastClickPos) {
            this.$nextTick(() => {
              if (this.$refs.syllabusList) {
                this.$refs.syllabusList.scrollTo({
                  top: lastClickPos,
                  behavior: 'smooth'
                })
              }
            })
          } else {
            // 滚动到进行中的课程或者最近上过的一节课为第一个的位置的位置
            this.$nextTick(() => {
              this.dataList && this.handleScrollToLive()
            })
          }
        }).catch((err) => {
          console.log('报错了 queryLessonListApi', err)
        }).finally(() => {
          // this.$bus.$emit('reload-completed')
        })
    },
    // /**
    //  * 检查客户端更新
    //  */
    // checkUpdate() {
    //   this.$bus.$emit('System.CheckUpdate.backgroundCheckUpdate', {
    //     type: 'regular'
    //   })
    // },
    // /**
    //  * 预下载课件
    //  */
    // async preload() {
    //   // 预加载课件
    //   preloadCourseware()
    //   // 预加载课件基础资源包
    //   preloadCoursewarePackages()
    // },
    /**
     * 滚动到进行中的课程或者最近上过的一节课为第一个的位置的位置
     */
    handleScrollToLive() {
      const query = this.$route.query
      console.log(query, 'this.backUrl')
      // 区分是否从作业&课前侧&学习报告返回
      if (query.isNeedBackToCurrentTap) {
        const currentLivekey = this.dataList.findIndex(classItem => {
          if (query.from && query.from == 'exam') {
            if (classItem.scheduleType === 'EXAM') {
              return classItem.examDetails.jumpParams.planId == query.planId
            }
          } else {
            if (classItem.scheduleType === 'LESSON') {
              return classItem.lessonDetails.planId == query.planId
            }
          }
        })
        this.scrollIntoIndex(currentLivekey)
      } else {
        const currentLivekey = this.dataList.findIndex(classItem => {
          if (classItem.scheduleType === 'LESSON') {
            return classItem.lessonDetails.status == 2
          }
        })
        const lastLivekey = this.dataList.findIndex(classItem => {
          if (classItem.scheduleType === 'LESSON') {
            return classItem.lessonDetails.status == 1
          }
        })
        const jumpKey = currentLivekey != -1 ? currentLivekey : lastLivekey - 1
        if (jumpKey > -1) {
          this.scrollIntoIndex(jumpKey)
        }
      }
    },
    scrollIntoIndex(key) {
      const scrollDom = document.getElementsByClassName('syllabus-container')[key]
      if (scrollDom) {
        scrollDom.scrollIntoView({
          behavior: 'smooth'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.syllabus-list {
  height: calc(100vh - 200px);
  overflow-y: auto;
}
</style>
