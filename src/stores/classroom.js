import { ref, reactive, computed } from 'vue'
import { defineStore } from 'pinia'

export const useClassData = defineStore('classroom', () => {
  const teacherInfo = ref({
    inClass: false,
    videoMute: true,
    audioMute: true,
    volume: 0,
    privateChatStatus: false,
    onStageStatus: false
  })
  const studentInfoBase = {
    inClass: false,
    videoMute: true,
    audioMute: true,
    volume: 0,
    multVideoLinkStatus: false,
    isIntersecting: true, // 是否页面中可见，坐席上判断，可见才渲染
    joinClassTime: 0, // 学生加入房间时间
    raiseHandStatus: false
  }
  const currentSpeakType = ref(4) // 发言类型
  const studentList = reactive([])
  const studentListSort = computed(() =>
    studentList.sort((a, b) => b.joinClassTime - a.joinClassTime)
  )
  const studentlistNoSelf = computed(() => studentListSort.value.filter(item => !item.isSelf))
  const selfInfo = computed(() => studentList.find(item => item.isSelf))
  const baseData = reactive({})
  const coursewareInfo = reactive({})
  const networkQuality = ref(0)
  const deviceInfo = reactive({
    videoDeviceId: false,
    audioDeviceId: false,
    speakerDeviceId: false
  })
  // 是否进入全员上台状态
  const isAllOnStageStatus = ref(false)
  // 涂鸦画板老师正在查看学员信息
  const gratiffiBoardWatchingInfo = ref({})
  // 私聊信息
  const privateChatInfo = ref({})
  // 本人上台状态
  const selfVideoMicLink = ref(false)
  const remoteAudioStatus = ref(false) // 远程音频状态
  // IRC状态
  // 状态值 0-未知 1-网络不可用 2-服务器连接失败 3-服务器连接中 4-服务器连接成功 5-服务器断开连接
  const ircNetStatus = ref(-1)
  const ircStatus = ref(false) // irc连接状态
  const wallRandomCallStatus = ref(false) // 随机点名状态
  const sliderVisible = ref(true) //侧边栏可见状态
  const dynamicEmojiLists = reactive([]) // 动态表情列表

  const classPraiseInteractId = ref('') // 完课宝箱是否已经展示过

  // 红包雨信息
  const redBagInfo = reactive({
    onlinePath: '' // 红包雨地址
  })

  const chatBox = reactive({
    // chatbox 相关属性
    isShowBox: false, // 是否显示
    isOpenChat: true, // 开启聊天 默认为true
    isOnlyTeacher: false, // 只看老师
    historyList: [], // 消息备份
    hasNewMsg: false, // 是否显示小红点
    isMuted: false //单独禁言
  })
  // 互动状态
  const interactionStatus = reactive({
    redPacket: false, // 红包
    openGift: false, // 礼物
    vote: false, // 投票
    classRest: false, // 课堂休息
    random_video_mic: false, // 随机连麦
    interact: false, // h5课件互动题
    game_interact: false, // 游戏课件
    take_picture: false, // 拍照上墙
    fill_blank: false, // 填空题
    nonpreset_fill_blank: false, // 填空题
    random_call: false, // 随机点名
    mult_video_mic: false, // 多人上台
    small_random_call: false, //小班随机点名
    speedyHand: false, // 抢答
    schulte_table: false, //舒尔特方格
    class_praise_list: false //表扬榜
  })
  const videoMicLinkUsers = ref([]) // 多人上台用户列表
  // 更新互动状态
  const updateInteractionStatus = data => {
    const { interactionName, interactionStatus: status } = data
    if (!interactionName) {
      return
    }
    if (interactionStatus[interactionName] !== undefined) {
      interactionStatus[interactionName] = status
    }
    console.log('UPDATE_INTERACTION_STATUS', status)
  }
  // 更新聊天信息
  const updateChatBoxInfo = data => {
    Object.assign(chatBox, data)
  }

  function changeTeacherInfo(info = {}) {
    teacherInfo.value = { ...teacherInfo.value, ...info }
  }
  function changeStudentInfo(uid, info = {}) {
    let userId = uid
    if (uid == 0) {
      userId = baseData.stuInfo.id
    }
    const index = studentList.findIndex(item => item.userId == userId)
    console.log('changeStudentInfo', index, info)
    if (index > -1) {
      // 检查是否有实际变化
      let needUpdate = false
      for (const key in info) {
        if (studentList[index][key] !== info[key]) {
          needUpdate = true
          break
        }
      }
      // 只有在有实际变化时才更新
      if (needUpdate) {
        studentList[index] = { ...studentList[index], ...info }
      }
    }
  }
  const initStudentList = list => {
    studentList.splice(0)
    list.forEach(item => {
      if (item.userId == baseData.stuInfo.id) {
        item.isSelf = true
        item.videoMute = false
      } else {
        item.isSelf = false
      }
      studentList.push({ ...studentInfoBase, ...item })
    })
  }
  const initBaseData = data => {
    Object.assign(baseData, data)
  }

  const initCoursewareData = data => {
    Object.assign(coursewareInfo, data)
    console.log('hooks课件信息store', data)
  }

  const initRedBagInfo = data => {
    Object.assign(redBagInfo, data)
    console.log('红包雨信息store', data)
  }

  const updateAllOnStageStatus = data => {
    isAllOnStageStatus.value = data
  }
  const updateGraffitiBoardWatchingStutus = data => {
    gratiffiBoardWatchingInfo.value = data
  }
  const updateIrcNetStatus = data => {
    ircNetStatus.value = data
  }
  const updateClassPraiseInteractId = data => {
    classPraiseInteractId.value = data
  }
  const updatePrivateChatInfo = data => {
    privateChatInfo.value = data
  }
  const updateSelfVideoMicLink = data => {
    selfVideoMicLink.value = data
  }
  const updateSliderVisible = data => {
    sliderVisible.value = data
  }
  const updateCurrentSpeakType = data => {
    currentSpeakType.value = data
  }
  const updateDynamicEmojiLists = data => {
    dynamicEmojiLists.push(...data)
  }
  const changeDeviceInfo = data => {
    Object.assign(deviceInfo, data)
  }
  const updateNetworkQuality = data => {
    networkQuality.value = data
  }
  const setIrcStatus = data => {
    ircStatus.value = data
  }
  const updateVideoMicLinkUsers = data => {
    videoMicLinkUsers.value = data
  }
  const updateWallRandomCallStatus = data => {
    wallRandomCallStatus.value = data
  }
  const updateRemoteAudioStatus = data => {
    remoteAudioStatus.value = data
  }

  // 重置所有数据到初始状态
  const resetStore = () => {
    // 重置teacherInfo
    teacherInfo.value = {
      inClass: false,
      videoMute: true,
      audioMute: true,
      volume: 0,
      privateChatStatus: false,
      onStageStatus: false
    }

    // 重置学生列表
    studentList.splice(0)

    // 重置基础数据
    Object.keys(baseData).forEach(key => delete baseData[key])

    // 重置课件信息
    Object.keys(coursewareInfo).forEach(key => delete coursewareInfo[key])

    // 重置红包雨信息
    Object.assign(redBagInfo, {
      onlinePath: ''
    })

    // 重置聊天框信息
    Object.assign(chatBox, {
      isShowBox: false,
      isOpenChat: true,
      isOnlyTeacher: false,
      historyList: [],
      hasNewMsg: false,
      isMuted: false
    })

    // 重置互动状态
    Object.assign(interactionStatus, {
      redPacket: false,
      openGift: false,
      vote: false,
      classRest: false,
      random_video_mic: false,
      interact: false,
      game_interact: false,
      take_picture: false,
      fill_blank: false,
      nonpreset_fill_blank: false,
      random_call: false,
      mult_video_mic: false,
      small_random_call: false,
      speedyHand: false,
      schulte_table: false,
      class_praise_list: false
    })

    // 重置设备信息
    Object.assign(deviceInfo, {
      videoDeviceId: false,
      audioDeviceId: false,
      speakerDeviceId: false
    })

    // 重置动态表情列表
    dynamicEmojiLists.splice(0)

    // 重置ref类型的数据
    currentSpeakType.value = 4
    networkQuality.value = 0
    isAllOnStageStatus.value = false
    gratiffiBoardWatchingInfo.value = {}
    privateChatInfo.value = {}
    selfVideoMicLink.value = false
    remoteAudioStatus.value = false
    ircNetStatus.value = -1
    ircStatus.value = false
    wallRandomCallStatus.value = false
    sliderVisible.value = true
    classPraiseInteractId.value = ''
    videoMicLinkUsers.value = []
  }
  return {
    studentList,
    studentListSort,
    studentlistNoSelf,
    baseData,
    coursewareInfo,
    redBagInfo,
    teacherInfo,
    selfInfo,
    networkQuality,
    isAllOnStageStatus,
    chatBox,
    ircStatus,
    wallRandomCallStatus,
    privateChatInfo,
    remoteAudioStatus,
    sliderVisible,
    currentSpeakType,
    dynamicEmojiLists,
    deviceInfo,
    videoMicLinkUsers,
    selfVideoMicLink,
    classPraiseInteractId,
    interactionStatus,
    changeTeacherInfo,
    changeDeviceInfo,
    changeStudentInfo,
    initStudentList,
    initBaseData,
    initCoursewareData,
    initRedBagInfo,
    updateAllOnStageStatus,
    updateGraffitiBoardWatchingStutus,
    updateIrcNetStatus,
    updateChatBoxInfo,
    updateInteractionStatus,
    updatePrivateChatInfo,
    updateSelfVideoMicLink,
    updateSliderVisible,
    updateCurrentSpeakType,
    updateDynamicEmojiLists,
    updateNetworkQuality,
    setIrcStatus,
    updateVideoMicLinkUsers,
    updateClassPraiseInteractId,
    updateWallRandomCallStatus,
    updateRemoteAudioStatus,
    resetStore
  }
})
