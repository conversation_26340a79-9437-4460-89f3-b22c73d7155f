import { ref } from 'vue'
import { defineStore } from 'pinia'
import Cookies from 'js-cookie'

export const useBaseData = defineStore('baseData', () => {
  const schoolCode = ref('')
  const locale = ref('')
  const localConfig = ref({})
  const countryCode = ref('')
  const timezone =  ref(Cookies.get('_official_timezone') || '')
  const setLocale = (val) => {
    locale.value = val
  }
  const setSchoolCode = code => {
    schoolCode.value = code
  }
  const setCountryCode = code => {
    countryCode.value = code
  }
  const userInfo = ref({})
  const setUserInfo = info => {
    userInfo.value = info
  }
  const setTimezone = (val) => {
    timezone.value = val
  }
  return {
    timezone,
    setTimezone,
    schoolCode,
    locale,
    localConfig,
    countryCode,
    setLocale,
    setSchoolCode,
    setCountryCode,
    userInfo,
    setUserInfo
  }
})
