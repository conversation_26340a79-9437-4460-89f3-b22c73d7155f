// import { getCampusList } from 'utils/initConfig'
// import { useBaseData } from '@/stores/baseData'
// const store = useBaseData()
/**
 * 设置当前local信息
 */
export const setLocal = async (local) => {
  window.localStorage.setItem('local', local)
  await window.thinkApi.ipc.invoke('setStoreValue', 'local', local)
}

/**
 * 获取当前local
 */
export const getLocal = async () => {
  ///临时数据
  return 'hk'

  /**
  @疑问 这个方法需要重写，获取当前的分校
  @答 新然提供一个方法，
  @解答人 未知
  @问题创建人 白海禄
  @创建时间 2025-05-16 16:06:40
  */
  // const localForFile = await window.thinkApi.ipc.invoke('getStoreValue', 'local')
  // const local = window.localStorage.getItem('local') || localForFile || ''
  // // 判断是否存在该分校
  // const schoolsList = (await getCampusList()) || []
  // const currentSchool = schoolsList.find((item) => {
  //   return (
  //     (item.schoolForCustomerEnabled && item.schoolSimplifyName == local) ||
  //     (item.schoolSimplifyName == 'cn' && item.schoolSimplifyName == local)
  //   )
  // })
  // if (currentSchool) {
  //   return local
  // } else {
  //   return ''
  // }
}

/**
 * 设置当前TimeZone信息
 */
export const setTimezone = async (timezone) => {
  window.localStorage.setItem('timezone', timezone)
  await window.thinkApi.ipc.invoke('setStoreValue', 'timezone', timezone)
}

/**
 * 获取当前timezone
 */
export const getTimezone = async () => {
  const timezone = await window.thinkApi.ipc.invoke('getStoreValue', 'timezone')
  return window.localStorage.getItem('timezone') || timezone || ''
}
/**
 * 获取当前设备时区timezone
 */
export const getDeviceTimezone = () => {
  return Intl.DateTimeFormat().resolvedOptions().timeZone || ''
}

/**
 * 设置时区列表
 */
export const setTimezoneList = async (timezoneList) => {
  await window.thinkApi.ipc.invoke('setStoreValue', 'timezoneList', timezoneList)
}

/**
 * 获取时区列表
 */
export const getTimezoneList = async () => {
  const timezoneList = await window.thinkApi.ipc.invoke('getStoreValue', 'timezoneList')
  return timezoneList
}

// /**
//  * 获取本地信息
//  */
// export const getLocalInfo = async () => {
//   const local = await getLocal()
//   const schoolsList = (await getCampusList()) || []
//   const schoolInfo = schoolsList?.find((item) => item.schoolSimplifyName == local)
//   return schoolInfo || {}
// }

/**
 * 获取分校编码
 */
export const getSchoolCode = async () => {
  const local = await getLocal()
  if (!local) {
    return ''
  }
  const schoolsList = (await getCampusList()) || []
  const schoolInfo = schoolsList.find((item) => item.schoolSimplifyName == local)
  return schoolInfo?.schoolCode || ''
}

/**
 * 根据分校编码返查local
 */
export const getLocalBySchoolCode = async (schoolCode) => {
  const schoolsList = (await getCampusList()) || []
  const schoolInfo = schoolsList?.find((item) => item.schoolCode == schoolCode)
  return schoolInfo?.schoolSimplifyName || ''
}

/**
 * 判断是否是亚洲姓名顺序，即姓在前，名字在后
 */
export const checkIsEasternNameOrder = async () => {
  const curSchoolInfo = await getLocalInfo()
  return curSchoolInfo?.easternNameOrderEnabled
}
