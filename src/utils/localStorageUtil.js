const localStorageUtil = {
  setItem(key, value) {
    if (!key || !value) return
    const stringValue = JSON.stringify(value)
    localStorage.setItem(key, stringValue)
  },

  getItem(key) {
    if (!key) return null
    const item = localStorage.getItem(key)
    try {
      return JSON.parse(item)
    } catch (error) {
      console.error('Error parsing JSON from localStorage', error)
      return null
    }
  },

  removeItem(key) {
    if (!key) return
    localStorage.removeItem(key)
  },

  clear() {
    localStorage.clear()
  },
}

export default localStorageUtil
