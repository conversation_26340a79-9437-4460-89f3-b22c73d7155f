import axios from 'axios'
const checkNetwork = async () => {
  let isOnline = false
  try {
    const res = await axios
      .create({
        timeout: 6000, // 超时时间(ms)
      })
      .get('https://connectivitycheck.gstatic.com/generate_204')

    if (res?.status && res.status === 204) {
      isOnline = true
    }
  } catch (e) {
    e
    isOnline = false
  }

  return isOnline
}
export default checkNetwork
