syntax = "proto3";

message WXChatUpPoint {
    float x = 2;                                //坐标点
    float y = 3;
}

message WXChatUpStageInfo {
    string upStageId = 1;               // 上墙单元的id
    repeated WXChatUpPoint locations = 2;   // 左上角和右下角的坐标
    string avatar = 3;                  // 用户头像的链接
    string name = 4;                    // 用户显示的名字
    string content = 5;                 // 上墙的聊天内容
    uint32 continouous = 6;             // 连对
}

message WXChatUpData {
    WXChatUpStageInfo chatUpStageInfo = 1; // 聊天区上墙信息
    repeated string deletedChatUpStageInfos = 2; // 需要被删除的聊天区上墙信息的id （1和2互斥，同时只有一个存在）
    float coursewareAreaWidth = 3;  // 教师端课件显示区域的宽度（删除的时候不传）
    float coursewareAreaHeight = 4; // 教师端课件显示区域的高度（删除的时候不传）
}
