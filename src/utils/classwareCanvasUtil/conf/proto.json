{"nested": {"WXWBMessageType": {"values": {"WXWBMessageTypeRecover": 0, "WXWBMessageTypeCancel": 1, "WXWBMessageTypeUpdateShape": 2, "WXWBMessageTypeDraw": 3, "WXWBMessageTypeDeleteShape": 4, "WXWBMessageTypeClearAll": 5, "WXWBMessageTypeScroll": 6, "WXWBMessageTypeGeometry": 7, "WXWBMessageTypeChoose": 9, "WXWBMessageTypePointer": 10, "WXWBMessageTypeRuler": 11, "WXWBMessageTypeCompass": 12, "WXWBMessageTypeTimer": 13, "WXWBMessageTypeUpdateMiniBoard": 14, "WXWBMessageTypeDeleteMiniBoard": 16, "WXWBMessageTypeUpdateCustomView": 17, "WXWBMessageTypeBatchAction": 18, "WXWBMessageTypeTextBox": 19, "WXWBMessageTypeSignalCourseware": 20, "WXWBMessageTypeCustomBusiness": 1000}}, "WXTPointType": {"values": {"WXWBPointTypePen": 0, "WXWBPointTypeEraser": 1, "WXWBPointTypeLine": 2, "WXWBPointArrowLine": 3, "WXWBPointArrow2Line": 4, "WXWBPointTriangle": 5, "WXWBPointRect": 6, "WXWBPointEllipses": 7, "WXWBPointPrarllelogram": 8, "WXWBPointPolygons": 9, "WXWBPointTypeSelect": 10, "WXWBPointCircle": 11, "WXWBPointRightTriangle": 12, "WXWBPointAxis": 13, "WXWBPointCube": 14, "WXWBPointCylinder": 15, "WXWBPointCone": 16, "WXWBPointDashed": 17, "WXWBPointCoordinate": 18, "WXWBPointAutoShape": 19, "WXWBPointDiamond": 20, "WXWBPointPointer": 21, "WXWBPointIsoscelesTriangle": 22, "WXWBPointLine2": 23, "WXWBPointEllipses2": 24, "WXWBPointTwoCoordinateSystem": 30, "WXWBPointThreeCoordinateSystem": 31, "WXWBPointCurve1": 32, "WXWBPointCurve2": 33, "WXWBPointCurve3": 34, "WXWBPointCurve4": 35, "WXWBPointCurve5": 36, "WXWBPointCurve6": 37, "WXWBPointCurve7": 38, "WXWBPointCurve8": 39, "WXWBPointCurve9": 40, "WXWBPointCurve10": 41, "WXWBPointCurve11": 42, "WXWBPointCurve12": 43, "WXWBPointCurve13": 44, "WXWBPointIsoscelesTrapezoid": 45, "WXWBPointRightTrapezoid": 46, "WXWBPointPentagon": 47, "WXWBPointHexagon": 48, "WXWBPointFrontCube": 49, "WXWBPointSideCube": 50, "WXWBPointRectangularPyramid1": 51, "WXWBPointRriangularPyramid": 52, "WXWBPointTriangularPrism": 53, "WXWBPointCylinderPlatform": 54, "WXWBPointSphere": 55, "WXWBPointCubePlatform": 56, "WXWBPointCorpusTrapezoideum": 57, "WXWBPointTruncatedTriangularPrism": 58, "WXWBPointRectangularPyramid2": 59, "WXWBPointPhysicsCoordinate": 60, "WXWBPointPhysicsVector": 61, "WXWBPointPhysicsSwitch": 62, "WXWBPointPhysicsBattery": 63, "WXWBPointPhysicsLight": 64, "WXWBPointPhysicsAmmeter": 65, "WXWBPointPhysicsVoltmeter": 66, "WXWBPointPhysicsResistance": 67, "WXWBPointPhysicsRheostat": 68, "WXWBPointPhysicsElectromotor": 69, "WXWBPointPhysicsBell": 70, "WXWBPointChemistryElectronic1": 71, "WXWBPointChemistryElectronic2": 72, "WXWBPointChemistryElectronic3": 73, "WXWBPointChemistryCoordinate": 74, "WXWBPointChemistryTestTube": 75, "WXWBPointChemistryBeaker": 76, "WXWBPointChemistryWildMouthBottle": 77, "WXWBPointChemistryConicalFlask": 78, "WXWBPointChemistryFunnel": 79, "WXWBPointChemistryUShapePipe": 80, "WXWBPointChemistryLongNeckFunnel": 81, "WXWBPointChemistrySeparatingFunnel": 82, "WXWBPointChemistryBentPipe": 83, "WXWBPointChemistryElectrolyticTank1": 84, "WXWBPointChemistryElectrolyticTank2": 85, "WXWBPointChemistryElectrolyticTank3": 86, "WXWBPointChemistryBenzeneRing": 87, "WXWBPointChemistryAlcoholLamp": 88, "WXWBPointChemistryGasBottle1": 89, "WXWBPointChemistryGasBottle2": 90, "WXWBPointCustomCircular": 91, "WXWBPointCustomShapeImage": 100, "WXWBPointFluorescentPen": 1000, "WXWBMessageTypeBatchMove": 1001, "WXWBMessageTypeBatchDelete": 1002, "WXWBMessageTypeBatchCopy": 1003}}, "WXWBPageConfig": {"fields": {"currentPage": {"type": "string", "id": 1}, "pageList": {"rule": "repeated", "type": "string", "id": 2}}}, "WXWBRulerConfig": {"fields": {"enable": {"type": "bool", "id": 1}, "left": {"type": "float", "id": 2}, "top": {"type": "float", "id": 3}, "length": {"type": "float", "id": 4}, "rotate": {"type": "float", "id": 5}}}, "WXWBCompassesConfig": {"fields": {"enable": {"type": "bool", "id": 1}, "left": {"type": "float", "id": 2}, "top": {"type": "float", "id": 3}, "radius": {"type": "float", "id": 4}, "rotate": {"type": "float", "id": 5}}}, "WXWBChooseConfig": {"fields": {"selectShape": {"rule": "repeated", "type": "int32", "id": 1}, "selectLineWidth": {"type": "uint32", "id": 2}, "selectLineColor": {"type": "string", "id": 3}, "selectLineType": {"type": "uint32", "id": 4}, "selectStartX": {"type": "float", "id": 5}, "selectStartY": {"type": "float", "id": 6}, "selectEndX": {"type": "float", "id": 7}, "selectEndY": {"type": "float", "id": 8}}}, "WXWBRevokeAndRecoverConfig": {"fields": {"actionList": {"rule": "repeated", "type": "string", "id": 1}, "historyIndex": {"type": "uint32", "id": 2}, "isReverse": {"type": "uint32", "id": 3}}}, "WXWBPointAction": {"values": {"WXWBPointActionStart": 0, "WXWBPointActionMove": 1, "WXWBPointActionEnd": 2}}, "WXWBLineType": {"values": {"WXWBLineTypeSolid": 0, "WXWBLineTypeDashed": 1, "WXWBLineTypeDotted": 2, "WXWBLineTypeDashed2": 3}}, "WXWBPoint": {"fields": {"action": {"type": "WXWBPointAction", "id": 1}, "x": {"type": "float", "id": 2}, "y": {"type": "float", "id": 3}, "lineWidth": {"type": "float", "id": 4}, "erase": {"type": "string", "id": 10}}}, "WXWBOffset": {"fields": {"offsetX": {"type": "float", "id": 1}, "offsetY": {"type": "float", "id": 2}}}, "WXWBChooseShapeOffset": {"fields": {"startOffsetX": {"type": "float", "id": 1}, "startOffsetY": {"type": "float", "id": 2}, "endOffsetX": {"type": "float", "id": 3}, "endOffsetY": {"type": "float", "id": 4}}}, "WXWBCanvasInfo": {"fields": {"canvasId": {"type": "uint32", "id": 1}, "x": {"type": "float", "id": 2}, "y": {"type": "float", "id": 3}, "width": {"type": "float", "id": 4}, "height": {"type": "float", "id": 5}, "lineWidth": {"type": "float", "id": 6}, "strokeColor": {"type": "uint32", "id": 7}, "fillColor": {"type": "uint32", "id": 8}}}, "WXWBUserInfo": {"fields": {"uname": {"type": "string", "id": 1}}}, "WXWBRefConfig": {"fields": {"refId": {"type": "string", "id": 1}, "imgUrl": {"type": "string", "id": 2}, "text": {"type": "string", "id": 3}, "fontSize": {"type": "float", "id": 4}, "fontFamily": {"type": "string", "id": 5}, "color": {"type": "uint32", "id": 6}, "lineHeight": {"type": "float", "id": 7}, "textAlign": {"type": "uint32", "id": 9}, "defaultWidth": {"type": "float", "id": 10}, "defaultHeight": {"type": "float", "id": 11}}}, "WXWBSelectAreaConfig": {"fields": {"lineIndexs": {"rule": "repeated", "type": "uint64", "id": 1}, "shapeIndexs": {"rule": "repeated", "type": "uint64", "id": 2}, "lineOffsets": {"keyType": "uint64", "type": "WXWBOffset", "id": 5}, "layerLocs": {"keyType": "uint64", "type": "WXWBChooseShapeOffset", "id": 6}, "lassoOffset": {"type": "WXWBOffset", "id": 7}}}, "WXWBTCPPacketData": {"fields": {"type": {"type": "WXWBMessageType", "id": 1}, "pointType": {"type": "WXTPointType", "id": 2}, "courseId": {"type": "string", "id": 3}, "pageId": {"type": "string", "id": 4}, "lineIndex": {"type": "uint64", "id": 5}, "pointList": {"rule": "repeated", "type": "WXWBPoint", "id": 7}, "width": {"type": "int32", "id": 8}, "height": {"type": "int32", "id": 9}, "msgId": {"type": "uint64", "id": 11}, "lineWidth": {"type": "float", "id": 12}, "strokeColor": {"type": "uint32", "id": 13}, "dataCreateTimestamp": {"type": "uint64", "id": 14}, "rotation": {"type": "float", "id": 15}, "fillColor": {"type": "uint32", "id": 16}, "lineType": {"type": "WXWBLineType", "id": 17}, "pageConfig": {"type": "WXWBPageConfig", "id": 18}, "revokeAndRecoverConfig": {"type": "WXWBRevokeAndRecoverConfig", "id": 19}, "chooseConfig": {"type": "WXWBChooseConfig", "id": 20}, "rulerConfig": {"type": "WXWBRulerConfig", "id": 21}, "compassesConfig": {"type": "WXWBCompassesConfig", "id": 22}, "refConfig": {"type": "WXWBRefConfig", "id": 23}, "userInfo": {"type": "WXWBUserInfo", "id": 27}, "uid": {"type": "string", "id": 30}, "canvasInfo": {"type": "WXWBCanvasInfo", "id": 31}, "selectConfig": {"type": "WXWBSelectAreaConfig", "id": 32}, "cursorPosition": {"type": "WXWBPoint", "id": 33}, "myBusiness": {"type": "google.protobuf.Any", "id": 1000}, "businessType": {"type": "uint32", "id": 1001}}}, "google": {"nested": {"protobuf": {"options": {"csharp_namespace": "Google.Protobuf.WellKnownTypes", "go_package": "github.com/golang/protobuf/ptypes/any", "java_package": "com.google.protobuf", "java_outer_classname": "AnyProto", "java_multiple_files": true, "objc_class_prefix": "GPB"}, "nested": {"Any": {"fields": {"typeUrl": {"type": "string", "id": 1}, "value": {"type": "bytes", "id": 2}}}}}}}, "WXVideoActionType": {"values": {"WXVideoActionPlay": 0, "WXVideoActionPause": 1, "WXVideoActionStop": 2}}, "WXVideoData": {"fields": {"type": {"type": "WXVideoActionType", "id": 1}, "url": {"type": "string", "id": 2}, "time": {"type": "int32", "id": 3}, "x": {"type": "float", "id": 4}, "y": {"type": "float", "id": 5}, "width": {"type": "float", "id": 6}, "height": {"type": "float", "id": 7}}}, "WXChatUpPoint": {"fields": {"x": {"type": "float", "id": 2}, "y": {"type": "float", "id": 3}}}, "WXChatUpStageInfo": {"fields": {"upStageId": {"type": "string", "id": 1}, "locations": {"rule": "repeated", "type": "WXChatUpPoint", "id": 2}, "avatar": {"type": "string", "id": 3}, "name": {"type": "string", "id": 4}, "content": {"type": "string", "id": 5}, "continouous": {"type": "uint32", "id": 6}}}, "WXChatUpData": {"fields": {"chatUpStageInfo": {"type": "WXChatUpStageInfo", "id": 1}, "deletedChatUpStageInfos": {"rule": "repeated", "type": "string", "id": 2}, "coursewareAreaWidth": {"type": "float", "id": 3}, "coursewareAreaHeight": {"type": "float", "id": 4}}}}}