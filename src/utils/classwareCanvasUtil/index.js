import Long from 'long'
// import ClasswareCanvas from '@thinkacademy/think-web-tboard'
import * as protobufjs from 'protobufjs'

import jsonData from './conf/proto.json'
let pbRoot = protobufjs.Root.fromJSON(jsonData)
let pbType = pbRoot.lookupType('WXWBTCPPacketData')

function longDataToNumber(target) {
  for (var key in target) {
    if (Object.prototype.hasOwnProperty.call(target, key)) {
      if (typeof target[key] === 'object') {
        if (Long.isLong(target[key])) {
          target[key] = target[key].toNumber()
        } else {
          longDataToNumber(target[key])
        }
      }
    }
  }
  return target
}

function formatBusinesMessage(data) {
  const { typeUrl, type_url = '', value } = data
  const typeString = type_url || typeUrl

  const type = typeString.split('/')[1]

  return pbRoot.lookupType(type).decode(value)
}

function formatBinaryData(content) {
  const result = pbType.decode(content)

  if (result.type === 1000 && result.businessData) {
    result.businessData = formatBusinesMessage(result.businessData)
  }

  return result
}

// const { formatBinaryData } = ClasswareCanvas

export { formatBinaryData, longDataToNumber }
