import { useRequestInfo } from '@/hooks/useRequestInfo'
const { getLanguage, getTimezone } = useRequestInfo()
// 唯一保留的配置项
let config = {
  // hour12: true, // 默认12小时制
  // hour12: false,
  locale: getLanguage() || 'en-US', //zh-CN
  // timeZone: getTimezone()
  // || 'America/New_York',
};
if (getTimezone()) config.timeZone = getTimezone()
// locale 传了，现在要加上时区

// todo 当官网出现时区重新设置时怎么办
// const timezone = Cookies.get('_official_timezone')

// zh-CN
// 初始化语言环境
// initIntlPolyfill(config.locale);

/**
 * 设置时间显示模式
 * @param {boolean} hour12 - 是否使用12小时制
 */
export const setHour12 = (hour12) => {
  config.hour12 = hour12;
};

// 核心格式化方法
const formatWithFallback = (date, locale = config.locale, options = {}) => {
  const dateObj = date instanceof Date ? date : new Date(date);
  let params = {
    ...options,
    // hour12: config.hour12,
  }
  if (config.timeZone) params['timeZone'] = config.timeZone
  try {
    // console.log('看下 formatWithFallback：：', new Intl.DateTimeFormat(locale, params).format(dateObj), dateObj, locale, params)
    return new Intl.DateTimeFormat(locale, params).format(dateObj);
  } catch (e) {
    console.warn(`Intl格式化失败，使用降级方案 (${locale})`, e);
    //yyyy-MM-dd hh:mm:ss
    const Y = dateObj.getFullYear() + '-';
    const M = (dateObj.getMonth()+1 < 10 ? '0'+(dateObj.getMonth()+1) : dateObj.getMonth()+1) + '-';
    const D = dateObj.getDate() + ' ';
    const h = dateObj.getHours() + ':';
    const m = dateObj.getMinutes() + '';
    // const s = dateObj.getSeconds();
    // document.write(Y+M+D+h+m+s);
    // 最基础的时间兼容处理
    if (options.timeStyle === 'short') {
      return Y+M+D+h+m
    } else if (options.hour === '2-digit' && options.minute === '2-digit') {
      return h+m
    } else {
      return Y+M+D
    }
    // return fallbackFormat(dateObj, locale, params);
  }
};

/**
 * 智能降级格式化函数
 * @param {Date|number} date - 日期对象或时间戳
 * @param {string} [locale='en-US'] - 语言环境
 * @param {Object} [options={}] - 格式化选项
 * @returns {string} 格式化后的日期时间字符串
 */
// todo 这里显示的有问题，反而不太对了，降级之后，原来是否用的 12小时制并不清楚，也不能判断，怎么办呢
const fallbackFormat = (date, locale = 'en-US', options = {}) => {
  // 确保是Date对象
  if (!(date instanceof Date)) date = new Date(date);

  // 获取日期组件
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  // 日期格式化
  const formatDate = () => {
    if (options.dateStyle === 'medium') {
      return locale.startsWith('zh') ?
        `${year}年${month}月${day}日` :
        `${month}/${day}/${year}`;
    }
    // 其他dateStyle可以在这里扩展
    return locale.startsWith('zh') ?
      `${year}年${month}月${day}日` :
      `${year}-${month}-${day}`;
  };

  // 获取时间组件
  let hours = date.getHours();
  const minutes = date.getMinutes().toString().padStart(2, '0');

  // 时间格式化
  const formatTime = () => {
    // const hour12 = options.hour12 ?? true; // 默认12小时制

    // 处理12小时制
    let period = '';
    // if (hour12) {
    //   const [am, pm] = getAmPmTexts(locale);
    //   period = hours < 12 ? am : pm;
    //   hours = hours % 12;
    //   hours = hours === 0 ? 12 : hours; // 0点显示为12
    // }

    // 格式化小时部分
    const formattedHour = options.hour === '2-digit' ?
                        hours.toString().padStart(2, '0') :
                        hours.toString();

    // 构建时间字符串
    let timeStr = `${formattedHour}:${minutes}`;
    if (period) {
      timeStr = locale.startsWith('zh') ?
               `${period}${timeStr}` :
               `${timeStr} ${period}`;
    }
    return timeStr;
  };

  // 根据选项决定返回格式
  if (options.dateStyle && !(options.timeStyle || options.hour || options.minute)) {
    // 仅返回日期
    return formatDate();
  } else if (options.timeStyle || options.hour || options.minute) {
    // 返回时间或日期+时间
    const timeStr = formatTime();
    return options.dateStyle ?
           `${formatDate()} ${timeStr}` :
           timeStr;
  }

  // 默认返回完整日期时间
  return `${formatDate()} ${formatTime()}`;
};



// // 获取当前语言的上午/下午文本
// const getAmPmTexts = (locale) => {
//   // 尝试通过Intl获取（即使主格式化失败，这个小功能可能仍可用）
//   try {
//     const am = new Intl.DateTimeFormat(locale, { hour: 'numeric', hour12: true })
//       .formatToParts(new Date(2023, 0, 1, 9))
//       .find(part => part.type === 'dayPeriod')?.value || 'AM';

//     const pm = new Intl.DateTimeFormat(locale, { hour: 'numeric', hour12: true })
//       .formatToParts(new Date(2023, 0, 1, 15))
//       .find(part => part.type === 'dayPeriod')?.value || 'PM';

//     return [am, pm];
//   } catch {
//     // 终极降级方案
//     return locale.startsWith('zh') ? ['上午', '下午'] : ['AM', 'PM'];
//   }
// };


// 公共API保持不变
export const formatDate = (date, locale = config.locale) => {
  return formatWithFallback(date, locale, { dateStyle: 'medium' });
};

export const formatDateTime = (date, locale = config.locale) => {
  return formatWithFallback(date, locale, {
    dateStyle: 'medium',
    timeStyle: 'short'
  });
};

export const formatDateRange = (start, end, locale = config.locale) => {
  const [startDate, endDate] = [start, end].map(d => d instanceof Date ? d : new Date(d));
  // console.log('isSameDay', startDate, endDate, isSameDay(startDate, endDate), formatDate(startDate, locale))
  if (isSameDay(startDate, endDate)) {
    const datePart = formatDate(startDate, locale);
    // console.log('isSameDayoooooooo99999', datePart)
    const timePart = `${formatTime(startDate, locale)} - ${formatTime(endDate, locale)}`;
    // console.log('isSameDay111111', datePart, timePart, formatTime(startDate, locale))

    return `${datePart} ${timePart}`;
  }
  return `${formatDateTime(startDate, locale)} - ${formatDateTime(endDate, locale)}`;
};

// 辅助方法
const formatTime = (date, locale) => {
  let params = {
    hour: '2-digit',
    minute: '2-digit',
    // hour12: config.hour12,
  }
  if (config.timeZone) params['timeZone'] = config.timeZone
  return formatWithFallback(date, locale, params);
};

const isSameDay = (date1, date2) => {
  return date1.getFullYear() === date2.getFullYear() &&
         date1.getMonth() === date2.getMonth() &&
         date1.getDate() === date2.getDate();
};

export const formatDateRangeWithWeek = (start, end, locale = config.locale) => {
  // weekday: 'long' 设置中添加这个
  const [startDate, endDate] = [start, end].map(d => d instanceof Date ? d : new Date(d));
  // console.log('isSameDay', startDate, endDate, isSameDay(startDate, endDate), formatDate(startDate, locale))
  if (isSameDay(startDate, endDate)) {
    const datePart = formatDate(startDate, locale);
    // console.log('isSameDayoooooooo99999', datePart)
    const timePart = `${formatTime(startDate, locale)} - ${formatTime(endDate, locale)}`;
    // console.log('isSameDay111111', datePart, timePart, formatTime(startDate, locale))

    return `${datePart} ${timePart}`;
  }
  return `${formatDateTime(startDate, locale)} - ${formatDateTime(endDate, locale)}`;
};