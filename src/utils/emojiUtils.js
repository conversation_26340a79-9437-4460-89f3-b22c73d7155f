import { localEmojiConfig } from '@/config/localEmoji'
import { getLottieEmojiList, setEmojiOverHide } from '@/api/classroom/index'
/**
 *  后端返回动态表情列表合并本地表情 && 动态表情过期后同步给后端
 */
export const getLottieEmojiLists = async () => {
  let allEmojiList = [localEmojiConfig] // 加入本地图片表情
  let overShowIds = []
  let res = await getLottieEmojiList() // 获取动态表情列表
  if (!res || res.code != 0) {
    return false
  }
  if (res.data) {
    res.data.list.forEach((emojiGroup) => {
      if (emojiGroup.isOver) {
        // 过期表情
        overShowIds.push(parseInt(emojiGroup.orderId)) // 存入orderIds
      }
      allEmojiList.push({
        isLocal: false, // 区分动态表情和本地表情
        ...emojiGroup,
      })
    })
  }
  // 动态表情过期后同步给后端（接口实际操作了：更改overShow状态）
  setEmojiOverHide({ orderIds: overShowIds })
  return allEmojiList
}
/**
 * 发送emoji 点击表情发送 埋点配置
 */
export const sendEmojiBurryPoint = async (options, params, emojiGroup, key, packageId) => {
  // new Vue().$sensors.track('hw_classroom_emoji_send', {
  //   ...emojiBurryPointParams(options, packageId),
  //   emoji_id: emojiGroup?.emojiPackageId || 0,
  //   emoji_detai_id: params.type === 1 ? key : params.emojiId || 0,
  //   emoji_detai_name: params.name,
  //   emoji_free: params.type === 1,
  //   emoji_name: params.type === 1 ? '默认' : emojiGroup.emojiPackageName,
  // })
}
/**
 * 打开表情面板 埋点配置
 */
export const openEmojiBurryPoint = async (options, packageId) => {
  // new Vue().$sensors.track('hw_classroom_emoji_icon_click', {
  //   ...emojiBurryPointParams(options, packageId),
  // })
}
/**
 * 表情埋点公共参数
 */
const emojiBurryPointParams = (options, packageId) => {
  const { planId, classType, classId } = options
  // 班级类型
  const classTypes = {
    0: '大班',
    1: '伪小班',
    2: '小班',
  }
  let classTypeName
  if (classTypes[classType]) {
    classTypeName = classTypes[classType]
  } else {
    console.error(classTypes[classType])
  }
  return {
    plan_id: planId,
    class_id: classId,
    lesson_type: classTypeName,
    package_id: packageId,
    // package_id: options.planInfo.packageId,
    previous_source: '课中',
  }
}
