import { createStorage } from '@/hooks/useStorage'

// 预设的后端接口域名
export const DEFAULT_DOMAIN = {
  production: {
    one: [
      'https://one-akamai.thethinkacademy.com',
      'https://one.thethinkacademy.com',
      'https://one.talthinktech.com',
    ],
    oversea: [
      'https://oversea-api-akamai.thethinkacademy.com',
      'https://oversea-api.thethinkacademy.com',
      'https://oversea-api.talthinktech.com',
    ],
  },
  preprod: {
    one: ['https://one-pre.thethinkacademy.com'],
    oversea: ['https://oversea-api-pre.thethinkacademy.com'],
  },
  beta: {
    one: [
      'https://beta-one-akamai.thethinkacademy.com',
      'https://beta-one.thethinkacademy.com',
      'https://beta-one.talthinktech.com',
    ],
    oversea: [
      'https://oversea-api-beta-akamai.thethinkacademy.com',
      'https://oversea-api-beta.thethinkacademy.com',
      'https://oversea-api-beta.talthinktech.com',
    ],
  },
  // beta: {
  //   one: ['https://one-akamai.thethinkacademy.com', 'https://one.thethinkacademy.com'],
  //   oversea: [
  //     'https://oversea-api-akamai.thethinkacademy.com',
  //     'https://oversea-api.thethinkacademy.com'
  //   ]
  // },
  dev: {
    one: ['http://one-dev.thethinkacademy.com'],
    oversea: ['http://oversea-api-dev.thethinkacademy.com'],
  },
}
const API_DOMAIN_STORAGE = createStorage({ storage: localStorage })

/**
 * 预埋一组默认接口域名
 */
export const setDefaultApiDomain = (apiObj) => {
  let obj
  if (apiObj) {
    obj = apiObj
  } else {
    const env = import.meta.env.VITE_APP_MODE
    console.log('env', env)
    obj = DEFAULT_DOMAIN[env]
  }
  // 预埋一组默认的云控域名
  API_DOMAIN_STORAGE.set('DEFAULT_API_LIST', obj)
  // 预埋一组初始域名
  API_DOMAIN_STORAGE.set('ONE_API_DOMAIN', obj['one'][0])
  API_DOMAIN_STORAGE.set('OVERSEA_API_DOMAIN', obj['oversea'][0])
}

/**
 * 设置默认的请求域名
 * 当有域名不通的时候，请求库进行了域名更换，且成功返回了。则会调用该方法更新本地的默认接口域名
 * @param {*} domain
 */
export const setApiDomain = (domain) => {
  const domainKey = domain.includes('oversea-api') ? 'OVERSEA_API_DOMAIN' : 'ONE_API_DOMAIN'
  API_DOMAIN_STORAGE.set(domainKey, domain)
}

/**
 * 获取请求接口的默认域名埋下的域名
 * @returns
 */
export const getApiDomain = () => {
  const env = import.meta.env.VITE_APP_MODE
  const one = API_DOMAIN_STORAGE.get('ONE_API_DOMAIN', DEFAULT_DOMAIN[env]['one'][0])
  const oversea = API_DOMAIN_STORAGE.get('OVERSEA_API_DOMAIN', DEFAULT_DOMAIN[env]['oversea'][0])
  return {
    one,
    oversea,
  }
}

/**
 * 根据请求的域名类型获取云控域名
 * @param {*} domain
 * @returns
 */
export const getmultipleDomainByApi = (domain) => {
  if (!domain) return null
  const env = import.meta.env.VITE_APP_MODE
  const type = domain.includes('oversea-api') ? 'oversea' : 'one'
  const API_DOMAIN = API_DOMAIN_STORAGE.get('DEFAULT_API_LIST', DEFAULT_DOMAIN[env])
  return API_DOMAIN[type]
}
