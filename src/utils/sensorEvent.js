// import Vue from 'vue'

// let joinIrcStartTime = null // irc 开始加入时间
// let isFirstJoinIrc = true // 是否第一次加入irc
// let re_connect_num = 0 //重连次数
// export const subPlatformTypeMap = {
//   0: '大班',
//   1: '伪小班',
//   2: '小班'
// }

/**
 * 大班神策埋点
 */
export const sensorEvent = (eventName, options, params = {}) => {
  // Vue.prototype.$sensors.track(eventName, {
  //   lesson_type: subPlatformTypeMap[options.classType],
  //   class_id: options.classId,
  //   plan_id: options.planId,
  //   ...params
  // })
}
// 回放上报神策埋点
// export const playVideoSensorPush = (eventName, params) => {
//   Vue.prototype.$sensors.track(eventName, params)
// }
// // irc上报神策埋点
// // 上报神策埋点
// export const ircSensorPush = ({
//   type = 'link',
//   result,
//   errorType = '',
//   code = 0,
//   msg = '无',
//   liveInfo = {},
//   msgInfo = {}
// }) => {
//   let params = {}
//   // 消息形式神策埋点构造
//   if (type == 'message') {
//     params = {
//       result,
//       irc_message_type: msg
//     }
//     if (result == 'fail') {
//       Object.assign(params, {
//         error_type: errorType,
//         msg_info: JSON.stringify(msgInfo),
//         error_msg: `错误码=${code}，错误描述=${msg}失败`
//       })
//     }
//     Vue.prototype.$sensors.track('hw_irc_send_message', params)
//   } else {
//     // 链路形式神策埋点构造
//     params = {
//       result,
//       re_connect_num,
//       is_first: isFirstJoinIrc ? '是' : '否',
//       location: liveInfo.location
//     }
//     if (result == 'fail') {
//       Object.assign(params, {
//         live_info: JSON.stringify(liveInfo),
//         error_type: errorType,
//         error_msg: `错误码=${code}，错误描述=${msg}`
//       })
//       // 第一次失败后就不为首次，改为false
//       isFirstJoinIrc = false
//       // 失败后重连次数+1，下次进行神策上报
//       re_connect_num += 1
//     } else if (result == 'success') {
//       Object.assign(params, {
//         irc_join_room_duration: Date.now() - joinIrcStartTime
//       })
//       // 成功后设置为false
//       isFirstJoinIrc = false
//       // 成功后重连次数+1，下次进行神策上报
//       re_connect_num += 1
//     } else if (result == 'start') {
//       joinIrcStartTime = Date.now()
//     }
//     Vue.prototype.$sensors.track('hw_irc_join_room', params)
//   }
// }
// // rtc上报神策埋点
// // ps: 大班有学生和老师两个rtc频道，需要实例化两个对象进行埋点
// export class RtcSensor {
//   constructor() {
//     this.joinRtcStartTime = null // rtc 开始加入时间
//     this.isFirstJoinChannel = true // 是否第一次加入rtc频道
//   }
//   rtcSensorPush = ({ result, errorType = '', code = 0, msg = '无' }) => {
//     let params = {
//       result,
//       is_first: this.isFirstJoinChannel ? '是' : '否'
//     }
//     if (result == 'fail') {
//       Object.assign(params, {
//         error_type: errorType,
//         error_msg: `错误码=${code}，错误描述=${msg}`
//       })
//     } else if (result == 'success') {
//       Object.assign(params, {
//         rtc_join_room_duration: Date.now() - this.joinRtcStartTime
//       })
//     } else if (result == 'start') {
//       this.joinRtcStartTime = Date.now()
//     }
//     Vue.prototype.$sensors.track('hw_rtc_join_room', params)
//   }
// }

// /**
//  * 进课埋点
//  */
// export const enterClassEvent = commonOption => {
//   let course_type = ''
//   if (commonOption.isParent) {
//     course_type = '家长旁听'
//   } else {
//     const typeMap = {
//       FORMAL: '正式课',
//       PLAYBACK: '回放',
//       AUDITION: '旁听课',
//       TEMPORARY: '临时课'
//     }
//     course_type = typeMap[commonOption.lessonType]
//   }
//   // 进课埋点
//   Vue.prototype.$sensors.track('hw_stu_enter_class_room', {
//     package_id: commonOption.packageId,
//     class_id: commonOption.classId,
//     previous_source: commonOption.from == 'course' ? '学习中心一级页' : '学习中心二级页',
//     plan_id: commonOption.planId,
//     plan_mode: commonOption.isPlayBack == 0 ? '直播' : '回放',
//     lesson_type: subPlatformTypeMap[commonOption.classType],
//     course_type: course_type
//   })
//   // 统计pc学生端上课人数，仅需直播课即可
//   if (commonOption.isPlayBack == 0) {
//     Vue.prototype.$sensors.track('pc_enter_class_room', {
//       previous_source: commonOption.from == 'course' ? '学习中心一级页' : '学习中心二级页',
//       course_type: course_type,
//       is_start_class: commonOption.isPlayBack ? false : commonOption.isStartClass
//     })
//   }
// }

// /**
//  * Today埋点
//  */
// export const todaySensorEvent = (eventName, params) => {
//   Vue.prototype.$sensors.track(eventName, params)
// }
