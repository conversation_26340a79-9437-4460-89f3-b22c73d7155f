import { sensorEvent } from './index'
// 用户登录
export const osta_login = id => {
  sensorEvent('osta_login', {
    hw_user_id: `${id}`
  })
}
// 检查到新版本
export const osta_update_new_version = params => {
  try {
    sensorEvent('osta_update_new_version', {
      new_version: params?.newVersion,
      is_force_update: params?.needForceUpgrade ? true : false
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 跳过新版本
export const osta_update_skip = params => {
  sensorEvent('osta_update_skip', {
    new_version: params?.newVersion || ''
  })
}
// 安装新版本
export const osta_update_install = params => {
  sensorEvent('osta_update_install', {
    new_version: params?.newVersion
  })
}
// 打开课前测
export const osta_do_pre_test = planId => {
  sensorEvent('osta_do_pre_test', {
    plan_id: `${planId}`
  })
}
// 打开作业
export const osta_do_homework = planId => {
  sensorEvent('osta_do_homework', {
    plan_id: `${planId}`
  })
}
// 打开课堂报告
export const osta_class_report = planId => {
  sensorEvent('osta_class_report', {
    plan_id: `${planId}`
  })
}
// 打开学习资料列表
export const osta_leraning_material = planId => {
  sensorEvent('osta_leraning_material', {
    plan_id: `${planId}`
  })
}
// 学员签到
export const osta_sign_in = params => {
  try {
    sensorEvent('osta_sign_in', {
      plan_id: `${params.planId}`, //string, 课次id, 例如："78786"
      coins_count: params.coins, //number, 签到获得的奖励数量, 例如：20.0
      start_difference: new Date().getTime() - params.time * 1000 //number, 点击签到时距离正式上课时间的时间差（当前时间 - 正式上课时间，单位：秒）, 例如：120.0
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 进入教室
export const osta_enter_classroom = (params, deviceStatus, coursewareInfo, timeOffset = 0) => {
  try {
    sensorEvent('osta_enter_classroom', {
      time_offset: timeOffset,
      plan_id: `${params.planId}`, //string, 课次id，需要设置为公共属性, 例如："78786"
      class_type: params.subPlatformType == 2 ? 'small' : 'dual', //string, 班级容量类型：dual, small  其中双师大班为dual，小班为small, 例如："dual","small"
      class_id: `${params.classId}`, //string, 班级id, 例如："78786"
      is_parent_audit: params.isParent ? true : false, //bool, 是否是家长旁听, 例如：True
      is_camera_ok: deviceStatus.camDetecting ? false : true, //bool, 当摄像头检测通过时设置为true，否则为false, 例如：True
      is_mic_ok: deviceStatus.micDetecting ? false : true, //bool, 当麦克风检测通过时设置为true，否则为false, 例如：True
      network_quality:
        deviceStatus.netDetecting === 0 ? 'good' : params.networkStatus === 1 ? 'poor' : 'unknown', //string, 枚举值："good","poor","unknown"，分别对应网络状态良好、网络较差、以及网络检查中或者位置状态, 例如："good","poor","unknown"
      courseware_localcache_exit: coursewareInfo.exit ? true : false, //bool, 本地是否已存在此课件无需下载, 例如：True
      courseware_download_size: coursewareInfo.exit
        ? 0
        : coursewareInfo.size
        ? +coursewareInfo.size.toFixed(2)
        : 0, //number, 需要下载的课件大小，注意当课件本地存在无需下载时，此值传0。单位：KB, 例如：1002.0
      courseware_download_time: coursewareInfo.exit
        ? 0
        : coursewareInfo.endTime
        ? (coursewareInfo.endTime - coursewareInfo.startTime) / 1000
        : 0, //number, 课件下载耗时，注意当课件本地存在无需下载时，此值传0。单位：秒, 例如：23.0
      start_difference: new Date().getTime() + timeOffset - coursewareInfo.courseStartTime * 1000 //number, 学员点击进入教室时距离正式上课时间的时间差（当前时间 - 正式上课时间，单位：秒）, 例如：12000.0
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 进入回放
export const osta_enter_playback = (params, coursewareInfo, timeOffset = 0) => {
  try {
    sensorEvent('osta_enter_playback', {
      plan_id: `${params.planId}`, //string, 课次id，需要设置为公共属性, 例如："78786"
      class_id: `${params.classId}`,
      courseware_localcache_exit: coursewareInfo.exit, //bool, 本地是否已存在此课件无需下载, 例如：True
      courseware_download_size: coursewareInfo.exit ? 0 : +coursewareInfo.size.toFixed(2), //number, 需要下载的课件大小，注意当课件本地存在无需下载时，此值传0。单位：KB, 例如：1002.0
      courseware_download_time: coursewareInfo.exit
        ? 0
        : (coursewareInfo.endTime - coursewareInfo.startTime) / 1000, //number, 课件下载耗时，注意当课件本地存在无需下载时，此值传0。单位：秒, 例如：23.0
      end_difference: new Date().getTime() + timeOffset - coursewareInfo.courseEndTime * 1000 //number, 学员点击进入回放时距离课程已结束的时间差（当前时间 - 正式下课时间，单位：秒）, 例如：12000.0
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
