// import { sensorEvent } from './index'
import { timeBetween } from '@/utils/util'
import { sensorEvent } from '@/utils/sensorTrack'
const votePattern = ['choice', 'true_false', 'understand', 'fill_blank']
const getQuestionType = type => {
  switch (type) {
    case '5':
      return 'true_false'
    case '1':
      return 'single_choice'
    case '2':
      return 'multiple_choice'
    case '4':
      return 'fill_blank'
  }
}
// 学员音频状态变更
// TODO 丢失
export const osta_rtc_audio_change = params => {
  try {
    sensorEvent('osta_rtc_audio_change', {
      action: params.microphoneStatus ? 'unmute' : 'mute'
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 学员视频状态变更
// TODO 丢失
export const osta_rtc_vidio_change = cameraStatus => {
  sensorEvent('osta_rtc_vidio_change', {
    action: cameraStatus ? 'unmute' : 'mute'
  })
}
// 大班课堂小组视频开关
export const osta_group_video_change = action => {
  sensorEvent('osta_group_video_change', {
    action: action ? 'on' : 'off' //string, 枚举值："on","off"，分别对应小组视频开启和小组视频关闭状态, 例如："on","off"
  })
}
// 当前学员被视频连麦（大班）
export const osta_ia_video_link = (interactId = '', from, action) => {
  sensorEvent('osta_ia_video_link', {
    inter_id: interactId,
    from: from || 'master_teacher', // 枚举值："master_teacher","guiding_teacher"，对应主讲连麦和辅导连麦
    action: action ? 'start' : 'end' //string, 枚举值:"start","end"，对应当前学员被连麦和当前学员结束连麦
  })
}
// 当前学员被上台
export const osta_ia_on_stage = (interactId = '', action) => {
  sensorEvent('osta_ia_on_stage', {
    inter_id: interactId,
    action: action ? 'start' : 'end' //string, 枚举值:"start","end"，对应当前学员被上台和当前学员被下台
  })
}
// 投票互动-提交答案
export const osta_ia_vote_submit = (interact, answer) => {
  try {
    sensorEvent('osta_ia_vote_submit', {
      inter_id: interact?.interactId, //string, 互动id, 例如："87t0q9w7e09qw809e"
      vote_type: interact.pattern ? votePattern[interact.pattern - 1] : 'fill_blank', //string, 投票类型:"choice","fill_blank","true_false","understand"，分别对应：选择、填一填、判断、懂不懂, 例如："choice","fill_blank","true_false","understand"
      student_answer: answer?.option, //string, 提交的答案, 例如："A"
      is_correct_answer: answer?.answerStat == 1 ? true : false //bool, 回答是否正确：true、false，仅对于发布题目时即说明了正确答案的题目（可以在用户提交后自动判题的）需要传此值，包括：有正确答案的选择题、有正确答案的判断题, 例如：True
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 拍照上墙-提交答案
export const osta_ia_photowall_submit = (interactId, answer) => {
  sensorEvent('osta_ia_photowall_submit', {
    inter_id: interactId, //string, 互动id, 例如："87t0q9w7e09qw809e"
    photo_url: answer?.url //string, 提交的拍照上墙图片地址, 例如："https://xxxxx.jpg"
  })
}
// 涂鸦画板-提交答案
export const osta_ia_graffitiboard_submit = params => {
  sensorEvent('osta_ia_graffitiboard_submit', {
    inter_id: params?.interactId //string, 互动id, 例如："87t0q9w7e09qw809e"
  })
}
// 打开普通红包
export const osta_ia_redpacket = (interactId, answer) => {
  try {
    sensorEvent('osta_ia_redpacket', {
      inter_id: interactId, //string, 互动id, 例如："87t0q9w7e09qw809e"
      coins_count: answer.data.coin //number, 获得的金币数量, 例如：20.0
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 红包雨获取金币
export const osta_ia_coinshower = (interactId, coin) => {
  sensorEvent('osta_ia_coinshower', {
    inter_id: interactId, //string, 互动id, 例如："87t0q9w7e09qw809e"
    coins_count: coin //number, 红包雨结束后获得的金币数量, 例如：20.0
  })
}
// 收到金币奖励（定向金币）
export const osta_ia_coins = (interactId, coin) => {
  sensorEvent('osta_ia_coins', {
    inter_id: interactId, //string, 互动id, 例如："87t0q9w7e09qw809e"
    coins_count: coin //number, 自己获得的金币数量, 例如：20.0
  })
}
// 打开完课宝箱
export const osta_ia_praise = (interactId, coin) => {
  sensorEvent('osta_ia_praise', {
    inter_id: interactId, //string, 互动id, 例如："87t0q9w7e09qw809e" todo
    coins_count: coin //number, 打开完课宝箱获得的金币数量, 例如：20.0
  })
}
// 送礼物互动-赠送老师礼物
export const osta_ia_gifts = (interactId, selectedGift) => {
  try {
    sensorEvent('osta_ia_gifts', {
      inter_id: interactId, //string, 互动id, 例如："87t0q9w7e09qw809e"
      gift_name: selectedGift.giftName, //string, 赠送的老师礼物名称, 例如："flower"
      gift_cost: selectedGift.coin //number, 赠送礼物消耗的金币数量, 例如：20.0
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 预置互动题-提交
export const osta_ia_base_interaction_submit = (interact, quesTypeId, isRight, isGames) => {
  try {
    sensorEvent('osta_ia_base_interaction_submit', {
      inter_id: interact.interactId, //string, 互动id, 例如："87t0q9w7e09qw809e"
      question_type: !isGames ? getQuestionType(`${quesTypeId}`) : 'game', //string, 互动题类型，枚举值：single_choice（单选题）、multiple_choice（多选题）、true_false（判断题）、fill_blank（填空题）、game（游戏题）, 例如："single_choice", "multiple_choice", "true_false", "fill_blank", "game"
      is_correct_answer: isRight == 1 ? true : false //bool, 回答是否正确：true、false, 例如：True
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// 预加载游戏加载时长
export const osta_ia_game_loading = (eventName, params) => {
  sensorEvent(eventName, params)
}
// 学员举手
// TODO 丢失
export const osta_raise_hand = () => {
  sensorEvent('osta_raise_hand', {})
}
// Chatbox发送消息
export const osta_cb_send_msg = content => {
  try {
    sensorEvent('osta_cb_send_msg', {
      msg_type: content.type == 139 ? 'private' : 'group', //string, 枚举值，发送的消息类型：group（群聊），private（私聊）, 例如："group","private"
      content_type: content.contentType || 'text', //string, 枚举值，发送的内容类型：text（文本），emoji（表情）, 例如："emoji","text"
      content: content.msg //string, 发送的消息内容（文本或者emoji_id）, 例如："hello"
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
// Chatbox学员禁言
export const osta_cb_input_cooling = () => {
  sensorEvent('osta_cb_input_cooling', {})
}
// 窗口失去焦点（PC学员端）
export const osta_app_not_focused = params => {
  sensorEvent('osta_app_not_focused', {
    action: params ? 'not_focused' : 'focused' //string, 枚举值：not_focused（窗口失去焦点）、focused（窗口获取焦点）, 例如："not_focused","focused"
  })
}
// 学员离开直播教室
export const osta_exit_classroom = (timeOffset = 0, endTimeStamp) => {
  sensorEvent('osta_exit_classroom', {
    end_difference: new Date().getTime() + timeOffset - endTimeStamp * 1000 //number, 学员离开教室时距离课程下课的时间差（当前时间 - 正式下课时间，单位：秒）, 例如：12000.0
  })
}
// 课堂内学员按下麦克风发言
export const osta_speak = duration => {
  sensorEvent('osta_classroom_speaking', {
    speaking_duration: duration //number, 学员发言时长（单位：毫秒）, 例如：1200
  })
}

// 学生课中退出或刷新
export const sendExitOrRefreshSensor = (eventName, params) => {
  // 时间限制
  const { startStampTime = '', endStampTime = '', id = '' } = params.planInfo || {}
  if (timeBetween(startStampTime, endStampTime)) {
    const end_difference = new Date().getTime() + params.timeOffset - endStampTime * 1000
    sensorEvent(eventName, {
      isStartClass: true,
      isKickout: false, // 是否是强踢
      planId: +id,
      course_type: params.lessonType,
      class_type_name: params.classTypeName,
      end_difference: end_difference,
      exit_class_time_stage: -end_difference / ((endStampTime - startStampTime) * 1000),
      current_interact_names: Object.keys(params.interactionStatus)
        .filter(key => params.interactionStatus[key])
        .join(',')
    })
  }
}

// 收到拍一拍提醒
export const osta_nudge_received = () => {
  // sensorEvent('osta_nudge_received', {})
}

export const osta_nps_submit = params => {
  sensorEvent('osta_nps_submit', params)
}

export const osta_nps_exposed = params => {
  sensorEvent('osta_nps_exposed', params)
}

// 性能指标
export const ostt_app_performance = (memory_usage, cpu_usage) => {
  try {
    sensorEvent('ostt_app_performance', {
      memory_usage: Number(memory_usage), //number
      cpu_usage: Number(cpu_usage) // number
    })
  } catch (err) {
    console.error('神策埋点报错:', err)
  }
}
