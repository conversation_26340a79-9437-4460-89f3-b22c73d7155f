// src/plugins/sensorsPlugin.js
import { emitter } from '@/hooks/useEventBus'
import sensors from 'sa-sdk-javascript'
import { version } from '../../../package.json'
// Vite 环境变量，神策埋点上报地址
const senesorPath = import.meta.env.VITE_APP_SENESOR_PATH

const sensorsPlugin = {
  install: (app, options = {}) => {
    // emitter.emit('update-user-info', res.data)
    emitter.on('update-user-info', userInfo => {
      // console.log('@@@@@@@update-user-info', userInfo)
      sensorRegister(userInfo)
    })

    // 初始化神策分析
    sensors.init({
      server_url: options.serverUrl || senesorPath,
      is_track_single_page: options.isTrackSinglePage !== false,
      use_client_time: options.useClientTime !== false,
      send_type: options.sendType || 'beacon',
      heatmap: options.heatmap || {
        clickmap: 'default',
        scroll_notice_map: 'default'
      },
      ...options.extraConfig
    })

    // 自动追踪页面浏览事件
    sensors.quick('autoTrack')

    // 注册公共属性
    // const pageProps = options.pageProps || {
    //   event_belong: '海外Web课堂',
    //   device_type: 'WEB'
    // }
    // sensors.registerPage(pageProps)

    sensorRegister()

    // 挂载到 Vue 实例和全局对象
    app.config.globalProperties.$sensors = sensors
    window.$sensors = sensors

    // 提供composition API方式使用
    app.provide('sensors', sensors)
  }
}

// 注册公共属性
export const sensorRegister = function (userInfo = null) {
  sensors.registerPage({
    event_belong: '海外Web课堂',
    nick_name: userInfo ? userInfo.nickName : '',
    card: userInfo ? userInfo.card : '',
    is_login: userInfo ? true : false,
    device_type: 'WEB',
    hw_user_id: userInfo ? userInfo.uid : null,
    $app_version: version, // 新增统计app版本属性
    $app_name: 'Think Academy',
    hw_user_role: 'student'
  })
  const userAttributes = {
    hw_user_id: userInfo?.uid,
    country_calling_code: userInfo?.countryCallingCode,
    card: userInfo?.card,
    nick_name: userInfo?.nickName,
    version_id: version
  }
  sensors.setProfile(userAttributes)
}

export default sensorsPlugin

// 导出 sensors 实例，方便直接引用
export { sensors }
