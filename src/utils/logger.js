// import { nativeApi } from 'utils/electronIpc'
// import { getSchoolCode } from 'utils/local'
// import { getParams } from 'utils/util'
// import { getUserInfo } from 'utils/userInfo'
// import SlsWebLogger from 'js-sls-logger'
// import axios from 'axios'
// const isDev = process.env.VUE_APP_MODE === 'development'
// 线上环境和预发布环境都为生产环境
// const isProd = process.env.VUE_APP_MODE === 'production'

class Logger {
  // constructor() {
  //   this.options = {
  //     host: 'us-west-1.log.aliyuncs.com',
  //     project: 'pa-web',
  //     // logstore: isProd ? 'pa-web-prod' : 'pa-web-test', // 这里只区分测试环境和线上环境
  //     time: 3,
  //     count: 10,
  //   }
  // this.logger = new SlsWebLogger(this.options)
  // }
  async buildParams() {
    // const deviceInfo = await nativeApi.getDeviceInfo()
    // const schoolCode = await getSchoolCode()
    // const { planId } = await getParams()
    // const { uid } = await getUserInfo()
    // return {
    //   tag,
    //   level,
    //   content,
    //   extra: {
    //     time: Date.now(),
    //     appType: 'student-app',
    //     userId: String(uid),
    //     schoolCode: schoolCode,
    //     planId: planId,
    //     clientType: deviceInfo.platform,
    //     appVersion: deviceInfo.appVersion,
    //     userAgent: window.navigator.userAgent,
    //     cpuModel: deviceInfo.cpuModel,
    //     cpuArch: deviceInfo.arch,
    //     totalMem: deviceInfo.totalMem,
    //     usageMem: deviceInfo.usageMem,
    //     appUsageMem: deviceInfo.appUsageMem,
    //     appUsageCPU: deviceInfo.appUsageCPU,
    //     env: process.env.VUE_APP_MODE, // 当前的环境。 上报上去的日志，可能是测试环境、预发布环境、线上环境
    //   },
    // }
  }
  async send({ tag, level = 'info', content = {} }) {
    // this.nativeLogger({ level, content })
    // if (isDev) return
    // const params = await this.buildParams({ tag, level, content })
    // this.logger.send(params)
  }
  nativeLogger({ level = 'info', content = {} }) {
    // TODO 加个云控
    // window.thinkApi.logger(level, JSON.stringify(content))
  }
  // 通过http请求上传日志，获取返回值，用于上传errorapi使用，成功则将上传成功的一条删除
  async request({ tag, level = 'info', content = {} }, callback) {
    // if (isDev) return // 开发环境不上传日志
    // const { project, host, logstore } = this.options
    // const url = `https://${project}.${host}/logstores/${logstore}/track`
    // let params = await this.buildParams({ tag, level, content })
    // let newParams = {}
    // for (let [key, value] of Object.entries(params)) {
    //   if (typeof value == 'object') {
    //     newParams[key] = JSON.stringify(value)
    //   } else {
    //     newParams[key] = String(value)
    //   }
    // }
    // const reqPayload = JSON.stringify({
    //   __logs__: [newParams],
    // })
    // const blob = new Blob([reqPayload], {
    //   type: 'application/x-protobuf',
    // })
    // axios
    //   .post(url, blob, {
    //     headers: {
    //       'x-log-apiversion': '0.6.0',
    //       'x-log-bodyrawsize': reqPayload.length,
    //     },
    //   })
    //   .then((res) => {
    //     res.status == 200 && callback(content)
    //   })
  }
}
export const logTag = {
  // irc: 'irc', // irc相关tag
  // rtc: 'rtc', // rtc相关tag
  // interaction: 'interact', // 互动相关tag
  // http: 'http', // http接口相关,
  // boardTool: 'boardTool', // 面板编辑工具栏
  // boardOperation: 'boardOperation', // 面板操作按钮
  // liveHeader: 'liveHeader', // 直播header按钮
  // action: 'action', // 用户点击操作
  // inperson: 'inperson', // 面授课
  // service: 'service', // irc和rtc集合服务
  // courseware: 'coursewarePackagesDownload', // 课件下载
  // playback: '回放', // 回放
  // component: 'component', // 组件生命周期
}
export default new Logger()
