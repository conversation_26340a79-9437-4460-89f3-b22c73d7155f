import Logan from 'logan-web-vite'

///定义枚举字符串 包含info、error、success、action、warning
export const LogLevel = {
  INFO: 'info',
  ERROR: 'error',
  SUCCESS: 'success',
  ACTION: 'action',
  WARNING: 'warning'
}

///定义日志方法
// 这个logger可以接收多个参数，
// 但是第一个参数必须是tag，第二个参数是日志内容
function log(tag, level, ...args) {
  ///args 可能是对象，如果是对象则转换为字符串，然后在执行拼接
  args = args.map(arg => {
    if (typeof arg === 'object') {
      try {
        return JSON.stringify(arg, null, 2) // 格式化对象为字符串
      } catch (e) {
        return String(arg) // 如果转换失败，直接转为字符串
      }
    }
    return String(arg) // 确保所有参数都是字符串
  })
  const logContent = args.join('\n')
  Logan.log(tag, logContent)
  // console.log(`【${tag}】`, logContent)
  ///根据level打印不同颜色的日志
  switch (level) {
    case LogLevel.INFO:
      console.info(`【${tag}】`, logContent)
      break
    case LogLevel.ERROR:
      console.error(`【${tag}】`, logContent)
      break
    case LogLevel.SUCCESS:
      console.log(`【${tag}】`, logContent)
      break
    case LogLevel.ACTION:
      console.log(`【${tag}】`, logContent)
      break
    case LogLevel.WARNING:
      console.warn(`【${tag}】`, logContent)
      break
    default:
      console.log(`【${tag}】`, logContent)
  }
}

class Logger {
  // 为特定tag创建四个级别的日志方法
  createTagMethods(tag) {
    return {
      info: (...args) => log(tag, LogLevel.INFO, '【】', ...args),
      error: (...args) => log(tag, LogLevel.ERROR, '【❌】', ...args),
      success: (...args) => log(tag, LogLevel.SUCCESS, '【✅】', ...args),
      action: (...args) => log(tag, LogLevel.ACTION, '【👆】', ...args),
      warning: (...args) => log(tag, LogLevel.WARNING, '【⚠️】', ...args)
    }
  }
}
const logger = new Logger()

// ---------------------------业务log定义----------------------------
export const coursewareLog = logger.createTagMethods('课件')
export const redRainLog = logger.createTagMethods('红包雨')
export const inClassExamLog = logger.createTagMethods('课中考试')
export const classRestLog = logger.createTagMethods('课间休息')
export const RTCLog = logger.createTagMethods('声网RTC')
export const IRCLog = logger.createTagMethods('IRC')
export const remindLog = logger.createTagMethods('拍一拍')
export const chatLog = logger.createTagMethods('聊天')
export const screenShareLog = logger.createTagMethods('屏幕共享')
export const whiteBoardLog = logger.createTagMethods('涂鸦')
export const gameLog = logger.createTagMethods('游戏')
export const pictureWallLog = logger.createTagMethods('拍照上墙')
export const handsUpLog = logger.createTagMethods('举手')
export const fillblankLog = logger.createTagMethods('填一填')
export const speedyHandLog = logger.createTagMethods('抢答')
export const deviceTestLog = logger.createTagMethods('设备检测')
export const studentOnStageLog = logger.createTagMethods('学生上台')
export const stuSeatingLog = logger.createTagMethods('学生坐席')
export const coursewareAnswerLog = logger.createTagMethods('课件内选择题')

// export const coursewareLog = () => {}
// export const redRainLog = () => {}
// export const inClassExamLog = () => {}
// export const classRestLog = () => {}
// export const RTCLog = () => {}
