import LoganDB from 'logan-web-vite/build/lib/logan-db'
import { LOG_DAY_TABLE_PRIMARY_KEY } from 'logan-web-vite/build/lib/logan-db.js'
import Config from 'logan-web-vite/build/global-config.js'
import logManager from './LogUploadManager'
import {
  dayFormat2Date,
  ONE_DAY_TIME_SPAN,
  dateFormat2Day
} from 'logan-web-vite/build/lib/utils.js'
import { invokeInQueue } from 'logan-web-vite/build/logan-operation-queue'
let LoganDBInstance

async function getLogAndSend(reportName, reportConfig) {
  const logItems = await LoganDBInstance.getLogsByReportName(reportName)
  if (logItems.length > 0) {
    console.log(
      '[日志上传]条数大于0,上报一次日志',
      'reportName',
      reportName,
      'reportConfig',
      reportConfig,
      logItems
    )
    const pageIndex = LoganDBInstance.logReportNameParser(reportName).pageIndex
    const logReportOb = LoganDBInstance.logReportNameParser(reportName)

    // 对象或值转换为 JSON 字符串
    const data = JSON.stringify({
      client: 'Web',
      webSource: `${reportConfig.webSource || ''}`,
      deviceId: reportConfig.deviceId,
      environment: `${reportConfig.environment || ''}`,
      customInfo: `${reportConfig.customInfo || ''}`,
      logPageNo: logReportOb.pageIndex + 1, // pageNo start from 1,
      fileDate: logReportOb.logDay,
      logArray: logItems
        .map(logItem => {
          return encodeURIComponent(logItem.logString)
        })
        .toString()
    })
    let uploadLogUrl = await logManager.upload(data)
    const firstObj = logItems[0]
    const lastObj = logItems[logItems.length - 1]

    console.log('[日志上传] 日志上传成功，上传成功改的pageIndex', pageIndex, uploadLogUrl)
    return {
      pageIndex,
      uploadLogUrl: uploadLogUrl.url,
      logCreateTime: firstObj.logCreateTime,
      logEndTime: lastObj.logCreateTime
    }
  } else {
    return Promise.resolve(null)
  }
}

export default async function reportLog(reportConfig) {
  if (!LoganDB.idbIsSupported()) {
    throw new Error('ResultMsg.DB_NOT_SUPPORT')
  } else {
    if (!LoganDBInstance) {
      LoganDBInstance = new LoganDB(Config.get('dbName'))
    }
    return await invokeInQueue(async () => {
      const logDaysInfoList = await LoganDBInstance.getLogDaysInfo(
        reportConfig.fromDayString,
        reportConfig.toDayString
      )
      console.log('[日志上传]logDaysInfoList', logDaysInfoList)
      const logReportMap = logDaysInfoList.reduce((acc, logDayInfo) => {
        return {
          [logDayInfo[LOG_DAY_TABLE_PRIMARY_KEY]]: logDayInfo.reportPagesInfo
            ? logDayInfo.reportPagesInfo.pageSizes.map((i, pageIndex) => {
                return LoganDBInstance.logReportNameFormatter(
                  logDayInfo[LOG_DAY_TABLE_PRIMARY_KEY],
                  pageIndex
                )
              })
            : [],
          ...acc
        }
      }, {})

      console.log('[日志上传]logReportMap', logReportMap)

      const reportResult = {}
      const startDate = dayFormat2Date(reportConfig.fromDayString)
      const endDate = dayFormat2Date(reportConfig.toDayString)

      /// ONE_DAY_TIME_SPAN = 24 * 60 * 60 * 1000; 表示一天
      for (let logTime = +startDate; logTime <= +endDate; logTime += ONE_DAY_TIME_SPAN) {
        const logDay = dateFormat2Day(new Date(logTime))
        if (logReportMap[logDay] && logReportMap[logDay].length > 0) {
          try {
            const batchReportResults = await Promise.all(
              ///map方法返回一个新的数组，设个数组里都是promise对象的数组
              logReportMap[logDay].map(reportName => {
                console.log('[日志上传]', 'logDay:', logDay, 'reportName:', reportName)
                return getLogAndSend(reportName, reportConfig)
              })
            )
            console.log(
              `[日志上传]${logDay}日志上传成功',
              batchReportResults.length,
              batchReportResults`
            )
            let successLog = batchReportResults.filter(item => item !== null)
            reportResult[logDay] = { msg: 'ResultMsg.REPORT_LOG_SUCC', successLog }
            try {
              const reportedPageIndexes = batchReportResults
                .filter(item => item !== null)
                .map(item => item.pageIndex)
              if (reportedPageIndexes.length > 0 && reportConfig.incrementalReport) {
                console.log('[日志上传]logDay', logDay)
                await LoganDBInstance.incrementalDelete(logDay, reportedPageIndexes)
              }
              // eslint-disable-next-line no-unused-vars
            } catch (e) {
              /* empty */
            }
          } catch (e) {
            console.log('[日志上传]batchReportResults❌无', e)
            reportResult[logDay] = {
              msg: 'ResultMsg.REPORT_LOG_FAIL',
              desc: e.message || e.stack || JSON.stringify(e)
            }
          }
        } else {
          reportResult[logDay] = { msg: 'ResultMsg.NO_LOG' }
        }
      }
      return reportResult
    })
  }
}
