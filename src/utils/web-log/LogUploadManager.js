import sensors from 'sa-sdk-javascript'
import localStorageUtil from '@/utils/localStorageUtil'
import reportLog from '@/utils/web-log/hw-report-log'
import { useBaseData } from '@/stores/baseData'
import Main from '@/utils/upload'

class LogManager {
  constructor() {
    if (!LogManager.instance) {
      LogManager.instance = this
    }
    return LogManager.instance
  }

  async upload(longString) {
    try {
      // 使用通用上传工具
      const upload = new Main({
        scene: 'logan/*' // 使用日志上传场景
      })

      // 将字符串转换为 Blob
      const blob = new Blob([longString], { type: 'text/plain' })

      // 生成文件名
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
      const fileName = `logan-${timestamp}.log`

      // 使用通用上传方法
      const result = await upload.putFile({
        filePath: fileName,
        file: blob,
        progress: percent => {
          console.log(`[日志上传]上传进度: ${percent}%`)
        },
        success: res => {
          console.log('[日志上传]Upload success:', res)
        },
        fail: error => {
          console.error('[日志上传]Upload failed:', error)
        }
      })

      return {
        url: result.url
      }
    } catch (error) {
      console.error('[日志上传]Error uploading data:', error)
      throw error
    }
  }

  uploadWithDay(fromDayString, toDayString) {
    console.log('[日志上传]fromDayString', fromDayString, 'toDayString', toDayString)
    const store = useBaseData()

    const hw_user_id = store?.userInfo.id
    const userInfo = JSON.parse(localStorageUtil.getItem('userInfo'))

    console.log(
      '[日志上传]userInfo',
      userInfo,
      'fromDayString',
      fromDayString,
      'toDayString',
      toDayString,
      hw_user_id
    )
    if (!hw_user_id) {
      console.error('[日志上传]用户信息不存在，无法上传日志')
      return
    }

    reportLog({
      fromDayString,
      toDayString,
      environment: 'prod',
      incrementalReport: true
    })
      .then(obj => {
        console.log('[日志上传]✅res', obj)
        for (let key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            if (obj[key].successLog) {
              obj[key].successLog.forEach(item => {
                console.log(
                  '[日志上传]上传成功',
                  item.uploadLogUrl,
                  item.logCreateTime,
                  item.logEndTime
                )
                sensors.track('upload_log_url', {
                  log_url: item.uploadLogUrl,
                  hw_user_id: hw_user_id,
                  localFileUploadCreateTime: item.logCreateTime,
                  localZipFileUploadCreateTime: item.logEndTime
                })
              })
            }
          }
        }
      })
      .catch(err => {
        console.log('[日志上传]❌err', err)
      })
  }

  ///上传最近10天的日志方法
  uploadWithTenDays() {
    const today = new Date()
    const tenDaysAgo = new Date(today.getTime() - 10 * 24 * 60 * 60 * 1000)
    this.uploadWithDay(tenDaysAgo.toISOString().split('T')[0], today.toISOString().split('T')[0])
  }
}

const logManager = new LogManager()
Object.freeze(logManager)
export default logManager
