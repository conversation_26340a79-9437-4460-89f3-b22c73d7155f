import Cookies from 'js-cookie';
// pxToVh
export const px2vh = px => {
  return `${((100 / 720) * px).toFixed(2)}vh`
}
export const is = (val, type) => toString.call(val) === `[object ${type}]`

export const isObject = val => val !== null && is(val, 'Object')
/**
 * 根据参数获取对应的value
 * @param {*} param
 * @returns
 */
export const getUrlParam = param => {
  let href = location.href
  let parts = href.split('?')
  if (parts.length < 2) {
    return null
  }
  let params = parts[1].split('&')
  for (let i = 0; i < params.length; i++) {
    if (params[i].indexOf(param + '=') == 0) {
      return params[i].substring(param.length + 1)
    }
  }
  return null
}

/**
 * 节流函数 - 限制函数在指定时间内只执行一次
 * @param {Function} fn 需要节流的函数
 * @param {Number} delay 延迟时间，单位毫秒
 * @returns {Function} 节流后的函数
 */
export const throttle = (fn, delay) => {
  let timer = null
  let lastExecTime = 0

  return function (...args) {
    const now = Date.now()
    const remaining = delay - (now - lastExecTime)

    if (remaining <= 0) {
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
      lastExecTime = now
      fn.apply(this, args)
    } else if (!timer) {
      timer = setTimeout(() => {
        lastExecTime = Date.now()
        timer = null
        fn.apply(this, args)
      }, remaining)
    }
  }
}

/**
 * 比较当前时间是否在开始结束时间之间, 注意后端返回的时间是秒级，需要✖️1000进行比较
 * @returns
 */
export const timeBetween = (start, end) => {
  const cur = +new Date()
  return start && end && start * 1000 < cur && cur < end * 1000
}

/**
 * base64转blob
 * @param {*} dataUrl
 * @returns
 */
export const dataURLtoBlob = dataUrl => {
  if (!dataUrl) return
  const arr = dataUrl.split(',')
  const mime = arr[0].match(/:(.*?);/)[1]
  let bstr = atob(arr[1])
  let n = bstr.length
  let u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

/**
 * Blob转File
 * @param {*} theBlob
 * @param {*} fileName
 * @returns File Object
 */
export const blobToFile = (theBlob, fileName) => {
  theBlob.lastModifiedDate = new Date()
  theBlob.name = fileName
  return theBlob
}

// 添加样式
export const addClass = (el, cls) => {
  if (!el) return
  let curClass = el.className
  let classes = (cls || '').split(' ')

  for (let i = 0, j = classes.length; i < j; i++) {
    let clsName = classes[i]
    if (!clsName) continue

    if (el.classList) {
      el.classList.add(clsName)
    } else if (!hasClass(el, clsName)) {
      curClass += ' ' + clsName
    }
  }
  if (!el.classList) {
    el.className = curClass
  }
}
// 移除样式
export const removeClass = (el, cls) => {
  if (!el || !cls) return
  let classes = cls.split(' ')
  let curClass = ' ' + el.className + ' '

  for (let i = 0, j = classes.length; i < j; i++) {
    let clsName = classes[i]
    if (!clsName) continue

    if (el.classList) {
      el.classList.remove(clsName)
    } else if (hasClass(el, clsName)) {
      curClass = curClass.replace(' ' + clsName + ' ', ' ')
    }
  }
  if (!el.classList) {
    el.className = trim(curClass)
  }
}
export const trim = string => {
  return (string || '').replace(/^[\s\uFEFF]+|[\s\uFEFF]+$/g, '')
}

// 是否存在样式名
export const hasClass = (el, cls) => {
  if (!el || !cls) return false
  if (cls.indexOf(' ') !== -1) {
    throw new Error('className 不应包含空格.')
  }
  if (el.classList) {
    return el.classList.contains(cls)
  } else {
    return (' ' + el.className + ' ').indexOf(' ' + cls + ' ') > -1
  }
}

export const compress = async (base64String, w, quality) => {
  let newImage = new Image()
  let imgWidth, imgHeight
  let promise = new Promise(resolve => (newImage.onload = resolve))
  newImage.src = base64String
  return promise.then(() => {
    imgWidth = newImage.width
    imgHeight = newImage.height
    let canvas = document.createElement('canvas')
    let ctx = canvas.getContext('2d')
    if (Math.max(imgWidth, imgHeight) > w) {
      if (imgWidth > imgHeight) {
        canvas.width = w
        canvas.height = (w * imgHeight) / imgWidth
      } else {
        canvas.height = w
        canvas.width = (w * imgWidth) / imgHeight
      }
    } else {
      canvas.width = imgWidth
      canvas.height = imgHeight
    }
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    ctx.drawImage(newImage, 0, 0, canvas.width, canvas.height)
    let base64 = canvas.toDataURL('image/jpeg', quality)
    return base64
  })
}

export const delay = async ms => new Promise(resolve => setTimeout(resolve, ms))

export const chunk = (arr, size) => {
  var objArr = new Array()
  var index = 0
  var objArrLen = arr.length / size
  for (var i = 0; i < objArrLen; i++) {
    var arrTemp = new Array()
    for (var j = 0; j < size; j++) {
      arrTemp[j] = arr[index++]
      if (index == arr.length) {
        break
      }
    }
    objArr[i] = arrTemp
  }
  return objArr
}

export const flatten = arr => {
  return arr.reduce((flat, toFlatten) => {
    return flat.concat(Array.isArray(toFlatten) ? flatten(toFlatten) : toFlatten)
  }, [])
}

/**
 * 设置accessToken
 */
export const setCookies = (tokenName = '_official_token', token) => {
  let domain = window.location.hostname;
  const reg = /\./g;
  let domainTestResult = domain.match(reg);
  // cookies存储在二级域名上
  if(domainTestResult && domainTestResult.length > 1) {
    const regDomain = /\.[a-zA-Z\d\.]+$/;
    const result = domain.match(regDomain);
    // eslint-disable-next-line prefer-destructuring
    if (result && result.length > 0) {
      if (domain.includes('.think-matrix.com')) {
        domain = '.think-matrix.com'
      } else if (domain.includes('.bettermeedu.com')) {
        domain = '.bettermeedu.com'
      } else {
        domain = result[0];
      }
    }
  }
  // 历史原因，设置Cookies之前，先删除原完整域名下的cookies
  Cookies.remove(tokenName);
  // 设置官网的Cookies
  Cookies.set(tokenName, token, { expires: 365, domain });
}

/**
 * 移除Cookies
 */
export const removeCookies = (tokenName = '_official_token') => {
  let domain = window.location.hostname;
  const reg = /\./g;
  let domainTestResult = domain.match(reg);
  // cookies存储在二级域名上
  if(domainTestResult && domainTestResult.length > 1) {
    const regDomain = /\.[a-zA-Z\d\.]+$/;
    const result = domain.match(regDomain);
    // eslint-disable-next-line prefer-destructuring
    if (result && result.length > 0) {
      if (domain.includes('.think-matrix.com')) {
        domain = '.think-matrix.com'
      } else {
        domain = result[0];
      }
    }
  }

  // 清除官网的Cookies
  Cookies.remove(tokenName, {domain});
  Cookies.remove(tokenName);
}
