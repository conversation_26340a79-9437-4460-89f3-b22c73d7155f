/**
 * 智能云控接口下发
 */

import { smartlineHostApi } from '@/api/init'
import axios from 'axios'
// import address from 'address'
import { setDefaultApiDomain } from '@/utils/domainConfig'
const axiosInstance = axios.create({
  timeout: 6000,
})
let dnsAddress = '' // IP地址
axiosInstance.interceptors.response.use(
  function (response) {
    // window.performance
    //   .getEntriesByName(response.config.url || response.request.responseURL)
    //   .forEach(item => {
    //     // @log-ignore
    //     window.$sensors.track('hw_net_work_request_metrics', {
    //       hw_net_success: 1, // 网络请求是否成功
    //       error_msg: response.statusText, // 请求错误原因
    //       error_type: response.status, // 请求错误码
    //       hw_net_sum_value: item.duration, // 请求整体时长
    //       hw_net_response_value: item.responseEnd - (item.responseStart || item.fetchStart), // 响应时长
    //       hw_net_service_value: item.responseStart - item.requestStart, // 服务器处理时长
    //       hw_net_request_value: item.responseStart - item.requestStart, // request时长
    //       hw_net_tls_value: item.requestStart - item.secureConnectionStart, // TLS握手时长（https）
    //       hw_net_tcp_value: item.connectEnd - item.connectStart, // TCP建连时长
    //       hw_net_dns_value: item.domainLookupEnd - item.domainLookupStart, // DNS解析时长
    //       hw_metrics_fetch_type: 'POST', // 资源获取方式
    //       hw_net_protocol: item.nextHopProtocol || 'h2', // https协议版本
    //       hw_net_request_url: response.config.url?.split('&')[0], // 请求url
    //       hw_dns_servers: dnsAddress || '' // 用户dns/ip地址
    //     })
    //   })
    return response
  },
  function (error) {
    // window.$sensors.track('hw_net_work_request_metrics', {
    //   hw_net_success: 0, // 网络请求是否成功
    //   error_msg: error.response ? error.response.statusText : error.toString(), // 请求错误原因
    //   error_type: error.response ? error.response.status : '6000', // 请求错误码
    //   hw_net_sum_value: 30000, // 请求整体时长
    //   hw_dns_servers: dnsAddress || '' // 用户dns/ip地址
    // })

    return Promise.reject(error)
  },
)

/**
 * 更新云控域名
 */
let requestAllCount = 0 // 总请求次数
let requestCount = 0 //当前请求次数
const overseaApiHostMap = new Map()
const newOverseaApiHost = []
const newOverseaApiTimeSort = []
const oneHostMap = new Map()
const newOneHost = []
const newOneHostTimeSort = []
export const updateSmartlineHost = () => {
  // address.dns(function (err, addrs) {
  //   dnsAddress = JSON.stringify(addrs)
  // })
  smartlineHostApi().then((res) => {
    if (res && res.code === 0) {
      const data = res.data || {}
      const { overseaApiHost, oneHost } = data
      if (!Array.isArray(overseaApiHost) || !Array.isArray(oneHost)) return
      // 计算线路,重新排序
      requestAllCount = (overseaApiHost.length + oneHost.length) * 5
      overseaApiHost.map((origin) => {
        getRequestTime(origin, overseaApiHostMap, data)
      })
      oneHost.map((origin) => {
        getRequestTime(origin, oneHostMap, data)
      })
    }
  })
}

async function getRequestTime(item, apiMap, data) {
  const startTime = +new Date()
  let count = 5
  const apiPath = '/api/health?payload=4096' //请求地址
  let requestAll = []
  while (count--) {
    requestAll.push(axiosInstance.post(`${item}${apiPath}&num=${count}`))
  }
  Promise.allSettled(requestAll).then((results) => {
    requestCount += results.length
    const endTime = +new Date()
    const totalTime = endTime - startTime
    apiMap.set(totalTime, item)
    if (requestCount == requestAllCount) {
      sortApi(overseaApiHostMap, newOverseaApiHost, newOverseaApiTimeSort, data.overseaApiHost)
      sortApi(oneHostMap, newOneHost, newOneHostTimeSort, data.oneHost)
      setDefaultApiDomain({
        one: newOneHost,
        oversea: newOverseaApiHost,
      })
    }
  })
}

function sortApi(apiMap, apiArr, timeArr, oldArr) {
  Array.from(apiMap.keys())
    .sort()
    .forEach((key) => {
      apiArr.push(apiMap.get(key))
      timeArr.push(key)
    })

  // window.$sensors.track('hw_net_work_sniffer_info', {
  //   hw_net_sniffer_diff_value: timeArr[timeArr.length - 1] - timeArr[0], // 最优线路与最差线路探测总时长差值
  //   hw_net_sniffer_min_value: timeArr[0], // 最优线路探测总时长
  //   hw_net_sniffer_max_value: timeArr[timeArr.length - 1], // 最差线路探测总时长
  //   hw_net_sniffer_real_url: apiArr[0], // 探测完成后最优线路
  //   hw_net_sniffer_default_url: oldArr[0], // 服务器下发默认的最优线路
  //   hw_net_sniffer_is_change: apiArr[0] === oldArr[0] ? 0 : 1,
  // })
}
