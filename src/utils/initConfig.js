/**
 * 初始化配置存储、获取
 */
import { useRequestInfo } from '@/hooks/useRequestInfo'

/**
 * 更新云控配置
 */
export const setLocalCloudConfig = config => {
  localStorage.setItem('cloudConfig', JSON.stringify(config))
}

/**
 * 获取云控配置
 */
export const getLocalCloudConfig = () => {
  const configStr = localStorage.getItem('cloudConfig')
  const config = configStr ? JSON.parse(configStr) : {}
  return config
}

/**
 * 更新localStorage
 */
export const setLocalStorageConfig = (key, config) => {
  localStorage.setItem(key, JSON.stringify(config))
}

/**
 * 获取localStorage
 */
export const getLocalStorageConfig = key => {
  try {
    return JSON.parse(localStorage.getItem(key))
  } catch (err) {
    console.error(`获取localStorage${key}错误，err:${JSON.stringify(err)}`)
    return ''
  }
}

/**
 * 获取系统是否开启该功能
 */
export const isOpenTheFunc = key => {
  const config = getLocalCloudConfig()
  if (config && config.configs) {
    const store = useRequestInfo()
    const schoolCode = store.getSchoolCode()

    const arr = config.configs.filter(item => {
      if (item.configKey === key) {
        const configValues = item?.configValue.split(',')
        return configValues.includes(schoolCode)
      }
    })
    return arr.length > 0
  }
}

export const cloudConfigValueBySchoolCode = async key => {
  const config = getLocalCloudConfig()
  let defaultValue = ''
  if (config && config.configs) {
    const store = useRequestInfo()
    const schoolCode = store.getSchoolCode()

    config.configs.forEach(item => {
      if (item.configKey === key) {
        try {
          const valueMap = JSON.parse(item.configValue)
          defaultValue = valueMap[schoolCode]
          console.info('云控格式化', key, defaultValue)
        } catch (error) {
          console.error('云控格式化错误', error)
        }
      }
    })
  }
  return defaultValue
}

export const getValueByCloudConfigKey = (key, returnValueKey = '') => {
  const cloudConfig = getLocalCloudConfig()
  if (cloudConfig && cloudConfig.configs) {
    const configs = cloudConfig.configs.filter(item => item.configKey === key)
    if (configs && configs.length) {
      return returnValueKey ? configs[0][returnValueKey] : configs[0]
    }
    return ''
  }
}
