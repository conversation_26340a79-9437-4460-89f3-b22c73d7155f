<template>
  <RouterView />
  <GoLoginModal :visible="open" :isRefresh="isRefresh" @closeModal="() => (open = false)" />
</template>
<script setup>
import GoLoginModal from '@/components/Common/GoLoginModal.vue'
import { RouterView } from 'vue-router'
import { updateSmartlineHost } from '@/utils/smartlineHost'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { baseConfig } from '@thinkacademy/website-base'
import { useBaseData } from '@/stores/baseData'
import { initConfigApi } from '@/api/init'
import { queryUserInfoApi } from '@/api/user'
import { i18nLocaleUpdate } from './locale/index.js'
import { emitter } from '@/hooks/useEventBus'
import { useRouter, useRoute } from 'vue-router'
import { getCloudConfig } from '@/api/home/<USER>'
import { setLocalStorageConfig } from '@/utils/initConfig'
import Cookies from 'js-cookie'
import { useRequestInfo } from '@/hooks/useRequestInfo'
import { isTouchAndToDownload } from '@/utils/isTouchToDownload.js'
const { setFirstToken } = useRequestInfo()
const router = useRouter()
const route = useRoute()
const store = useBaseData()

const setBaseInfo = () => {
  const {
    domainsLocaleMap, // 国家域名语言环境映射关系
    countryCodeMap, //国家编码映射关系
    schoolCodeMap,
    fallbackLocale
  } = baseConfig
  // 主机名
  const { hostname } = window.location
  console.log('hostname123', hostname, baseConfig)
  // 默认语言环境
  let locale = fallbackLocale

  // 根据主机名定位语言环境
  Object.keys(domainsLocaleMap).forEach(key => {
    if (hostname.includes(key)) {
      locale = domainsLocaleMap[key]
    }
  })
  if (hostname.includes('localhost')) {
    locale = 'cn'
  }
  isTouchAndToDownload(locale)

  // if (locale === 'hk' || locale === 'ai') {
  //   app.head.htmlAttrs.lang = 'zh-Hant'
  // }
  // 设置语言环境
  store.setLocale(locale)
  console.log('setLocale', fallbackLocale, domainsLocaleMap, locale, countryCodeMap, schoolCodeMap)

  // 设置国家编码
  const countryCode = countryCodeMap[locale]
  store.setCountryCode(countryCode)
  // 设置分校编码
  const schoolCode = schoolCodeMap[locale]
  console.log('countryCode', schoolCode)

  store.setSchoolCode(schoolCode)
  i18nLocaleUpdate(locale)
}

const getInitInfo = async () => {
  const res = await initConfigApi()
  // 业务异常阻断
  if (Number(res.code) !== 0) {
    return
  }
  const resData = res.data || {}

  if (Object.keys(resData).length === 0) return
  return resData
}
const getUserInfo = async () => {
  const res = await queryUserInfoApi()
  if (res.code !== 0) {
    console.error('获取用户信息失败')
    return
  }
  store.setUserInfo(res.data)

  emitter.emit('update-user-info', res.data)
}
const init = () => {
  setBaseInfo()
  updateSmartlineHost()
  getInitInfo().then(res => {
    console.log('init', res)
    // 处理分校默认时区，没有就不处理了
    if (!store.timezone) {
      const schoolCode = store.schoolCode
      const timezoneV2 = res.timezoneV2 || {}
      const timezone = timezoneV2[schoolCode]
      timezone && store.setTimezone(timezone)
    }
  })
  getUserInfo()
  getCloudConfig({
    appCode: '1005', // 应用Code，必传参数，由各端维护
    deviceId: 'ThinkAcademy' // 设备唯一ID，必传参数
  }).then(res => {
    if (!res || res.code != 0) {
      return
    }
    const configInfo = res.data || {}
    setLocalStorageConfig(configInfo.configs)
  })
}
init()
// 处理滚轮事件
const handleWheel = e => {
  // 关键检测点：区分触控板事件
  if (e.ctrlKey || e.metaKey) return // 排除缩放操作

  if (Math.abs(e.deltaX) > 0) {
    // 动态阈值调整
    e.preventDefault()
  }
}

const open = ref(false)
const isRefresh = ref(false)
const globalLoginOrRefresh = flag => {
  open.value = true
  isRefresh.value = flag
}
const handleGlobalFresh = (flag = true) => {
  flag && setFirstToken(Cookies.get('_official_token') || '')
  const path = route.path
  console.log('要进入首页 -- 本身在首页', path)

  if (path.includes('home/today')) {
    console.log('要进入首页 -- 本身在首页', path)
  } else {
    console.log('要进入首页')
    router.push('/home/<USER>')
  }
}
// 生命周期钩子
onMounted(() => {
  window.addEventListener('wheel', handleWheel, {
    passive: false,
    capture: true // 必须捕获阶段处理
  })
  emitter.on('globalLoginOrRefresh', globalLoginOrRefresh)
  emitter.on('globalReFresh', handleGlobalFresh)
  // throw new Error('Hello World11')
})

onBeforeUnmount(() => {
  window.removeEventListener('wheel', handleWheel)
  emitter.off('globalLoginOrRefresh', globalLoginOrRefresh)
  emitter.off('globalReFresh', handleGlobalFresh)
})
</script>

<style scoped>
/* 锁定横向滑动 */
html {
  scrollbar-width: none; /* Firefox */
  overscroll-behavior-x: none;
  overflow-x: clip;
}

::-webkit-scrollbar {
  display: none; /* Chrome/Safari */
}

/* 触控板滚动特征检测 */
@media (pointer: fine) and (hover: hover) {
  body {
    touch-action: pan-y;
  }
}
</style>
