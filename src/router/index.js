import { createRouter, createWebHashHistory } from 'vue-router'
// import { useRequestInfo } from '@/hooks/useRequestInfo'
// const { getToken } = useRequestInfo()
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/home/<USER>' // 默认重定向到 /home/<USER>
    },
    // {
    //   path: '/courseList',
    //   name: 'courseList',
    //   component: () => import('../views/CourseList.vue'),
    // },
    {
      path: '/classroom',
      name: 'ClassRoom',
      component: () => import('../views/ClassRoom.vue')
    },
    // {
    //   path: '/login',
    //   name: 'Login',
    //   component: () => import('../views/LoginPage.vue'),
    // },
    {
      path: '/playVideo',
      name: 'PlayVideo',
      component: () => import('../views/playVideo.vue')
    },
    {
      path: '/viewPdf',
      name: 'ViewPdf',
      component: () => import('../views/ViewPdf.vue')
    },
    {
      path: '/home',
      name: 'Home',
      redirect: '/home/<USER>',
      component: () => import(/* webpackChunkName: "Home" */ '../layout/index.vue'),
      children: [
        {
          path: 'today',
          name: 'Today',
          component: () => import(/* webpackChunkName: "Today" */ '../views/Home.vue')
        },
        {
          path: 'completed-courses',
          name: 'CompletedCourses',
          component: () =>
            import(/* webpackChunkName: "CompletedCourses" */ '../views/CompletedCourses.vue')
        },
        {
          path: 'assignment-list',
          name: 'AssignmentList',
          component: () =>
            import(/* webpackChunkName: "AssignmentList" */ '../views/AssignmentList.vue')
        },
        {
          path: 'wrong-question-book',
          name: 'WrongQuestionBook',
          component: () =>
            import(/* webpackChunkName: "WrongQuestionBook" */ '../views/h5/WrongQuestionBook.vue')
        },
        {
          path: 'courses-detail',
          name: 'CoursesDetail',
          component: () =>
            import(/* webpackChunkName: "CoursesDetail" */ '../views/CoursesDetail.vue')
        },
        {
          path: 'study-plan',
          name: 'StudyPlan',
          component: () => import(/* webpackChunkName: "StudyPlan" */ '../views/h5/StudyPlan.vue')
        },
        {
          path: 'studyResources',
          name: 'StudyResources',
          component: () =>
            import(/* webpackChunkName: "StudyResources" */ '../views/StudyResources.vue')
        },
        // 进入h5 的前置页面
        {
          path: 'previewQuestion',
          name: 'PreviewQuestion',
          component: () =>
            import(/* webpackChunkName: "PreviewQuestion" */ '../views/PreviewQuestion.vue')
        },
        {
          path: 'examIntro',
          name: 'examIntro',
          component: () => import(/* webpackChunkName: "examIntro" */ '../views/ExamIntro.vue')
        },
        // 通用的课前测、(新)作业、考试相关h5、巩固练习
        {
          path: 'assignment-h5',
          name: 'assignmentH5',
          component: () =>
            import(/* webpackChunkName: "TaskExercise" */ '../views/h5/CommonAssignment.vue')
        },
        {
          path: 'lessonReport',
          name: 'lessonReport',
          component: () =>
            import(/* webpackChunkName: "TaskExercise" */ '../views/h5/LessonReport.vue')
        }
      ]
    }
  ]
})

///检测localStorage中是否有token
router.beforeEach((to, from, next) => {
  // const token = getToken()
  // if (!token) {
  //   window.open(`${window.location.origin}/passport/login?retUrl=${window.location.origin}/study`, '_self');
  //   return
  // } else {
  //   next()
  // }
  next()
})

export default router
