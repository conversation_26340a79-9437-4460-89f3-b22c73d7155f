export const ConnectionDisconnectedReason = {
  CHANNEL_BANNED: '当前频道被禁用',
  IP_BANNED: '当前 IP 被踢出',
  LEAVE: '用户正常退出',
  NETWORK_ERROR: '网络异常，经过重试后不可恢复',
  SERVER_ERROR: '服务端返回出现异常，通常是因为集成过程中参数有误',
  TOKEN_EXPIRE: '当前用户的 Token 已过期',
  UID_BANNED: '当前用户被踢出'
}

export const ArSdkErrorCode = {
  10000001: '当前浏览器环境不兼容',
  10000002: '当前渲染上下文丢失',
  10000003: '渲染耗时长', //考虑降低视频分辨率或屏蔽功能
  10000005: '输入源解析错误',
  10000006: '浏览器特性支持不足，可能会出现卡顿情况',
  10001101: '设置特效出错',
  10001102: '设置滤镜出错',
  10001103: '特效强度参数不正确',
  10001201: '调起用户摄像头失败',
  10001202: '摄像头中断',
  10001203: '没有获取到摄像头权限',
  20002001: '缺少鉴权参数',
  20001002: '接口请求失败',
  20001003: '设置特效接口鉴权失败',
  40000000: '未捕获的异常',
  40000001: '当前使用 SDK 版本过低，部分特效无法正确展示，请升级 SDK 版本',
  50000002: '分辨率改变导致特效丢失' //需要重新设置特效
}
