/**
 * @name ChatResultCode
 * @description irc chat sdk 状态码
 */
const ChatResultCode = {
  Success: 0,
  ParaError: 1,
  TimeOut: 2,
  SeqRepeat: 3,
  GslbServerError: 201, // 连接调度服务器失败
  AccessServerError: 202, // 连接接入服务器失败
  AppIDNotExist: 6,
  AppTokenInvalid: 7,
  UnInit: 11,
  UnLogined: 12,
  PathNotExist: 13,
  SendTooFast: 14,
  SendTooLarge: 15,
  TaskTooMuch: 16,
  AlreadyLogined: 17,
  Logging: 18,
  AlreadyInit: 19,
  ParseError: 203, // 解析失败
  NotSupport: 99,
  OtherError: 100,
  PsIdNotExsit: 200, // 登陆参数错误
  NicknameNotExist: 401, // 昵称不存在
  RoomNotExist: 403, // 聊天室不存在
  JoinTooManyRooms: 405, // 加入的聊天室过多
  InvalidNickname: 204, // 错误的昵称
  NicknameAlreadyExist: 205, // 昵称已存在
  InvalidPassword: 206, // 注册密码错误
  NotInRoom: 442, // 你不在该聊天室
  TargetRoomIsFull: 471, // 聊天室上限已满
  RoomData: 322, // 聊天室信息
  RoomDataEnd: 323, // 聊天室信息返回结束
  RoomUserList: 52, // 聊天室昵称列表
  KickoutRepeat: 301, // 重复登录被踢下线
  KickoutRequest: 302, // 业务应用向消息系统发起踢出用户请求
  RoomUserListEnd: 53 // 聊天室昵称列表结束
}

export { ChatResultCode }
