import { jwtDecode } from 'jwt-decode'
import AgoraRTC from 'agora-rtc-sdk-ng'
import ThinkBase from './ThinkBase'
import { ConnectionDisconnectedReason } from './config/RtcErrorCodeMap'
import { RTCLog } from '@/utils/web-log/HWLogDefine'

export default class AgoraService extends ThinkBase {
  constructor() {
    super()
    this.teacherDeviceType = '' // pc: pc教师端、ipad: iPad教师端
    this.readyStatus = false
    this.rtcClient = null
    this.localAudioTrack = null
    this.localVideoTrack = null
    this.version = '__VERSION__'
    this.readyQueue = []

    this.usersVideoTrack = new Map()
    this.usersAudioTrack = new Map()
  }
  async initServe() {
    AgoraRTC.enableLogUpload()
    this.rtcClient = this.createRtcClient()
    this.rtcEventListeners()
    console.log('AgoraService: initServe ready')
  }
  ready(callback) {
    if (!this.readyStatus) {
      this.readyQueue.push(callback)
    } else {
      callback()
    }
  }
  createData(options) {
    const { rtcConfig = {}, publishStatus = true, uid = 0 } = options
    const { token, teacherUid, teacherAudioUid, teacherVideoUid, teacherShareUid } = rtcConfig
    this.options = options
    this.uid = uid
    this.teacherUid = Number(teacherUid) // PC教师端uid
    this.teacherVideoUid = Number(teacherVideoUid) // android推流端uid
    this.teacherAudioUid = Number(teacherAudioUid) // iPad主讲端uid
    this.teacherShareUid = Number(teacherShareUid) // 老师屏幕共享id
    this.publishStatus = publishStatus
    this.isKickout = false

    const data = jwtDecode(token)
    this.config = {
      // Pass your app ID here.
      appId: data.attachAppid || data.appid,
      // Set the channel name.
      channel: String(data.room),
      // Use a temp token
      token: data.token,
      // Uid
      uid: data.user
    }
  }
  async joinRtcChannel(options) {
    this.createData(options)
    RTCLog.info('AgoraService: joinRtcChannel ready')
    await this.join()
    RTCLog.info('AgoraService: 加入频道完成')
    if (this.publishStatus) {
      RTCLog.info('AgoraService: track准备完成')
      await this.publish()
      if (!this.localVideoTrack) {
        this.emit('videoError')
      }
      // 进入课中默认静音
      this.muteLocalAudio(true)
      this.readyStatus = true
      this.emit('joinSuccess')
      this.readyQueue.forEach(callback => {
        callback()
      })
    }
    RTCLog.info('AgoraService: joinRtcChannel ready2')
  }
  getVideoDevices = async () => {
    let cams = []
    try {
      cams = await AgoraRTC.getCameras()
    } catch (error) {
      RTCLog.warning('AgoraService: getVideoDevices error', error)
    }
    return cams
  }
  getAudioRecordingDevices = async () => {
    let mics = []
    try {
      mics = await AgoraRTC.getMicrophones()
      // mics = microphones.filter(device => device.deviceId !== 'default')
    } catch (error) {
      console.log('geterror', error)
    }
    return mics
  }
  getSpeakerList = async () => {
    let devices = []
    try {
      devices = await AgoraRTC.getDevices()
    } catch (error) {
      RTCLog.warning('获取音频输出设备失败:', error)
      return []
    }
    const speakerList = devices.filter(device => device.kind === 'audiooutput')
    return speakerList
  }
  createBufferSourceAudioTrack = async source => {
    try {
      const audioTrack = await AgoraRTC.createBufferSourceAudioTrack({
        source
      })
      return audioTrack
    } catch (error) {
      console.error('创建音频轨道失败:', error)
      return null
    }
  }
  checkAudioOutputDeviceSupport = async () => {
    try {
      await AgoraRTC.checkAudioOutputDeviceSupport()
      return true
    } catch (error) {
      console.error('检查音频输出设备支持失败:', error)
      return false
    }
  }
  // 检测摄像头权限
  async checkCameraPermission() {
    const cams = await this.getVideoDevices()
    return cams.length > 0
  }

  // 检测麦克风权限
  async checkMicPermission() {
    const mics = await this.getAudioRecordingDevices()
    return mics.length > 0
  }
  async initVideoTracks(deviceId = '') {
    if (this.localVideoTrack) {
      this.localVideoTrack.close()
      this.localVideoTrack = null
      this.logger('info', '关闭已存在的视频轨道')
    }
    try {
      let config = {}
      if (deviceId) {
        config.cameraId = deviceId
      }
      this.localVideoTrack = await AgoraRTC.createCameraVideoTrack(config)
    } catch (error) {
      RTCLog.error('开始广播，摄像头权限被拒绝', error)

      this.emit('rtcError', {
        code: error.code,
        msg: error.message,
        type: 'video'
      })
      this.logger(
        'error',
        '获取视频流失败',
        `errorCode:${error.code}, errorMessage:${error.message}`
      )
    }
  }
  async initAudioTracks(deviceId = '') {
    if (this.localAudioTrack) {
      this.localAudioTrack.close()
      this.localAudioTrack = null
      this.logger('info', '关闭已存在的音频轨道')
    }
    try {
      let config = {
        AEC: true,
        ANS: true
      }
      if (deviceId) {
        config.microphoneId = deviceId
      }
      this.localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack(config)
    } catch (error) {
      this.emit('rtcError', {
        code: error.code,
        msg: error.message,
        type: 'audio'
      })
      this.logger(
        'error',
        '获取音频流失败',
        `errorCode:${error.code}, errorMessage:${error.message}`
      )
    }
  }
  getVolumeLevel() {
    if (this.localAudioTrack) {
      return this.localAudioTrack.getVolumeLevel()
    }
    return 0
  }
  getCurrentVideoDevice() {
    const videoLabel = this.localVideoTrack?.getTrackLabel() || ''
    return videoLabel
  }
  getCurrentAudioDevice() {
    const audioLabel = this.localAudioTrack?.getTrackLabel() || ''
    return audioLabel
  }
  switchVideoDevice(deviceId) {
    this.localVideoTrack?.setDevice(deviceId)
  }
  switchAudioDevice(deviceId) {
    this.localAudioTrack?.setDevice(deviceId)
  }

  /**
   * 初始化
   */
  createRtcClient() {
    const client = AgoraRTC.createClient({ mode: 'live', codec: 'vp8' })
    client.setClientRole('host')
    AgoraRTC.setParameter('AUDIO_VOLUME_INDICATION_INTERVAL', 300)
    this.logger('info', '设备采集准备完成')
    return client
  }

  rtcEventListeners() {
    /**
     * 远端用户加入频道
     * @param { Object } user
     * @param { String } user.uid - 远端用户的 uid
     * @param { Object } user.audioTrack - 远端用户的音频轨道
     * @param { Object } user.videoTrack - 远端用户的视频轨道
     * @param { Boolean } user.hasAudio - 远端当前是否在发送音频。
     * @param { Boolean } user.hasVideo - 远端当前是否在发送视频。
     * @returns
     */
    this.rtcClient.on('user-joined', user => {
      this.remoteUserJoined(user.uid)
      this.logger(
        'info',
        '远端用户加入频道',
        `user:${user.uid},hasVideo:${user.hasVideo},hasAudio:${user.hasAudio}`
      )
    })

    /**
     * 远端用户离线 正常离开、超时掉线、用户角色从主播变为观众。
     * 在直播场景中，只有角色为主播的用户会触发该回调。
     * @param { Object } user
     * @param { String } reason - 用户离线的原因。'Quit'、'ServerTimeOut'、'BecomeAudience'
     * @returns
     */
    this.rtcClient.on('user-left', (user, reason) => {
      const uid = user.uid
      // PC教师端离开频道
      if (this.teacherDeviceType == 'pc' && uid == this.teacherUid) {
        // 广播主讲老师离开频道
        this.emit('teacherLeaveChannel', this.teacherDeviceType)
        this.emit('teacherVideoLeaveChannel', uid)
      }
      // iPad教师端离开频道
      if (this.teacherDeviceType == 'ipad' && uid == this.teacherAudioUid) {
        // 广播主讲老师离开频道
        this.emit('teacherLeaveChannel', this.teacherDeviceType)
      }
      if (this.teacherDeviceType == 'ipad' && uid == this.teacherVideoUid) {
        // 广播主讲视频端离开频道
        this.emit('teacherVideoLeaveChannel', uid)
      }
      if (uid == this.teacherShareUid) {
        // 广播屏幕共享离开频道
        this.emit('shareScreenLeaveChannel')
      }
      this.emit('remoteLeaveChannel', uid)

      let msg = '远端用户离开频道'
      let level = 'info'
      if (reason === 'ServerTimeOut') {
        msg = '远端用户超时掉线(有可能主动离开)'
        level = 'error'
      } else if (reason === 'BecomeAudience') {
        msg = '用户角色从主播切换为观众'
      }
      this.logger(level, msg, `user:${user.uid},reason:${reason}`)
    })

    /**
     * 远端用户发布了新的音频轨道或者视频轨道。
     * @param { Object } user
     * @param { String } mediaType - 媒体类型 "audio" | "video"
     * @returns
     */
    this.rtcClient.on('published-user-list', async users => {
      let subscribeList = []
      users.forEach(user => {
        if (user.hasVideo) {
          subscribeList.push({
            user,
            mediaType: 'video'
          })
        }
        if (user.hasAudio) {
          subscribeList.push({
            user,
            mediaType: 'audio'
          })
        }
        this.remoteUserJoined(user.uid)

        this.logger(
          'info',
          '远端用户加入频道',
          `user:${user.uid},hasVideo:${user.hasVideo},hasAudio:${user.hasAudio}`
        )
      })
      const result = await this.rtcClient.massSubscribe(subscribeList)

      for (const { track, mediaType, error, user } of result) {
        if (error) {
          console.error(error)
          continue
        }
        if (track) {
          const uid = user.uid
          if (mediaType === 'audio') {
            this.usersAudioTrack.set(+uid, user)
            user.audioTrack.stop()
            this.emitAudioStatus(uid, false)
          } else {
            this.usersVideoTrack.set(+uid, user)
            this.emitVideoStatus(uid, false)
          }
          this.logger('info', '远端用户发布音视频', `user:${uid},mediaType:${mediaType}`)
        }
      }
    })

    this.rtcClient.on('user-published', async (user, mediaType) => {
      const { uid } = user
      try {
        await this.rtcClient.subscribe(user, mediaType)
      } catch (error) {
        this.logger('error', '订阅失败', `user:${uid},mediaType:${mediaType},error:${error}`)
      }
      if (mediaType === 'video') {
        this.usersVideoTrack.set(+uid, user)
        this.emitVideoStatus(uid, false)
      } else if (mediaType === 'audio') {
        this.usersAudioTrack.set(+uid, user)

        console.log(`xxr:用户发布音频，+uid:${+uid}`)
        user.audioTrack.stop()
        this.emitAudioStatus(uid, false)
      }
      this.logger('info', '远端用户发布音视频', `user:${uid},mediaType:${mediaType}`)
    })

    /**
     * 远端用户取消发布了音频或视频轨道。
     * @param { Object } user
     * @param { String } mediaType - 媒体类型 "audio" | "video"
     * @returns
     */
    // 如果远端用户主动取消发布，本地会收到 user-unpublished 回调，收到该回调时 SDK 会自动释放相应的 RemoteTrack 对象，你无需再调用 unsubscribe。
    this.rtcClient.on('user-unpublished', async (user, mediaType) => {
      const { uid } = user
      if (mediaType === 'video') {
        this.emitVideoStatus(uid, true)
        this.usersVideoTrack.delete(+uid)
      } else if (mediaType === 'audio') {
        console.log(`xxr:用户取消发布，+uid:${+uid}`)

        this.emitAudioStatus(uid, true)
        this.usersAudioTrack.delete(+uid)
      }
      this.logger('info', '远端用户取消发布音视频', `user:${uid},mediaType:${mediaType}`)
    })

    /**
     * 会每 2 秒触发本地用户网络上下行质量报告回调。
     * @param { Object } stats - 网络质量"downlinkNetworkQuality"  | "uplinkNetworkQuality"
     * @param { String } stats.downlinkNetworkQuality - 下行网络质量。0 | 1 | 2 | 3 | 4 | 5 | 6
     * @param { String } stats.uplinkNetworkQuality - 上行网络质量。0 | 1 | 2 | 3 | 4 | 5 | 6
     * @returns
     */
    this.rtcClient.on('network-quality', quality => {
      this.emit('localNetworkQuality', quality)
    })

    /**
     * SDK 与服务器的连接状态发生改变回调。https://doc.shengwang.cn/api-ref/rtc/javascript/globals#connectionstate
     * @param { string } curState - 当前的连接状态。"DISCONNECTED"  | "CONNECTING" | "RECONNECTING" | "CONNECTED" | "DISCONNECTING"
     * @param { String } revState - 之前的连接状态。"DISCONNECTED" | "CONNECTING" | "RECONNECTING" | "CONNECTED" | "DISCONNECTING"
     * @param { String } reason - 断开连接的原因。 | "mute-video" | "enable-local-video" | "unmute-audio" | "unmute-video" | "disable-local-video"
     * @returns
     */
    this.rtcClient.on('connection-state-change', (curState, revState, reason) => {
      if (revState === 'CONNECTING' && curState === 'CONNECTED') {
        // this.rtcSensor.rtcSensorPush({ result: 'success' })
      } else if (revState === 'RECONNECTING' && curState === 'CONNECTED') {
        // 本地重连加入频道成功
        this.emit('rejoinChannelSuccess')
        RTCLog.success('AgoraService: 本地重连加入频道成功')
      } else if (curState === 'DISCONNECTED') {
        if (reason === 'LEAVE' || reason === 'UID_BANNED') {
          return
        }
        this.emit('rtcError', {
          code: 'DISCONNECTED',
          msg: '本地断开连接'
        })
        RTCLog.error(
          'AgoraService: 本地断开连接',
          `curState:${curState},revState:${revState},reason:${reason},reasonMsg:${ConnectionDisconnectedReason[reason]}`
        )
      } else if (revState === 'DISCONNECTED' && curState === 'RECONNECTING') {
        // this.rtcSensor.isFirstJoinChannel = false
        // this.rtcSensor.rtcSensorPush({ result: 'start' })
      }
      this.logger(
        'info',
        '本地连接状态',
        `curState:${curState},revState:${revState},reason:${reason}`
      )
    })

    /**
     * 报告频道内正在说话的本地和远端用户及其音量的回调。
     * @param { object[] } result
     * @param { String } result.level - 音量，范围是 0 到 100。
     * @param { String } result.uid - 说话者的用户 ID。
     * @returns
     */
    this.rtcClient.on('volume-indicator', volumeUsers => {
      volumeUsers.forEach(item => {
        if (item.uid == this.uid) {
          // 本地音量
          this.emit('localAudioVolume', item.level)
        } else if (item.uid == this.teacherUid || item.uid == this.teacherAudioUid) {
          // 老师音量
          this.emit('teacherAudioVolume', item.level)
        } else {
          // 远端音量
          this.emit('remoteAudioVolume', item.uid, item.level)
        }
      })
      this.emit('groupAudioVolumeIndication', volumeUsers)
    })
  }

  async join() {
    RTCLog.info(
      'AgoraService: join',
      `join:${this.config.appId}, channel:${this.config.channel}, token:${this.config.token}, user:${this.config.uid}`
    )
    try {
      await this.rtcClient.join(
        this.config.appId,
        this.config.channel,
        this.config.token,
        this.config.uid
      )
    } catch (error) {
      RTCLog.error(
        'AgoraService: 加入频道失败',
        `errorName:${error.name}, errorMessage:${error.message}`
      )
    }
    // 加入频道成功
    this.emit('localJoinChannel')
    RTCLog.success('AgoraService: 本地加入频道成功')
    this.rtcClient.enableAudioVolumeIndicator()
  }
  async publish() {
    let tracks = []
    if (this.localVideoTrack) {
      tracks.push(this.localVideoTrack)
    }
    if (this.localAudioTrack) {
      tracks.push(this.localAudioTrack)
    }
    if (tracks.length === 0) {
      RTCLog.error('AgoraService: 无可用track')
      return
    }
    await this.rtcClient.publish(tracks)
    this.logger('info', '开始本地推流', tracks.length)
  }
  async unpublish() {
    await this.rtcClient.unpublish()
    this.logger('info', '本地停止推流')
  }
  publishVideo() {
    if (!this.localVideoTrack) {
      this.logger('error', 'unmute无localVideoTrack')
      return
    }
    this.rtcClient.publish([this.localVideoTrack])
  }
  unpublishVideo() {
    if (!this.localVideoTrack) {
      this.logger('error', 'mute无localVideoTrack')
      return
    }
    this.rtcClient.unpublish([this.localVideoTrack])
  }
  /**
   * 创建本地视频视图
   * @param {String} id
   */
  createLocalVideo(id, mirror = true) {
    RTCLog.info('localVideoTrack', this.localVideoTrack)
    if (!this.localVideoTrack) {
      RTCLog.error('createLocalVideo无localVideoTrack')
      return
    }
    this.localVideoTrack.play(id, {
      fit: 'cover',
      mirror
    })
  }
  setLocalVolume(volume) {
    if (this.localAudioTrack) {
      this.localAudioTrack.setVolume(volume)
    } else {
      RTCLog.warning('无localAudioTrack')
    }
  }
  setRemoteVoiceVolume(volume, uid) {
    const user = this.usersAudioTrack.get(+uid)
    if (user) {
      user.audioTrack.setVolume(volume)
    } else {
      RTCLog.warning(`无uid:${uid}的user`)
    }
  }
  /**
   * 设置视频的编码参数，包括分辨率、帧率、码率。
   */
  setVideoEncoderConfiguration(config) {
    if (this.localVideoTrack) {
      this.localVideoTrack.setEncoderConfiguration(config)
    } else {
      RTCLog.warning('无localVideoTrack')
    }
  }

  /**
   * 启用/禁用本地音频
   * @param {Boolean} mute
   */
  muteLocalAudio(mute) {
    if (this.localAudioTrack) {
      console.log('muteLocalAudio', mute, this.localAudioTrack)

      this.localAudioTrack.setMuted(mute)
    } else {
      console.warn('无microphoneAudioTrack')
    }
  }
  /**
   * 创建远端视频视图
   * @param {*} uid uid
   * @param {*} id id
   */
  createRemoteVideo(
    uid,
    domId,
    config = {
      fit: 'cover'
    }
  ) {
    const user = this.usersVideoTrack.get(+uid)
    if (user) {
      user.videoTrack.play(domId, config)
    } else {
      this.logger('error', '创建远端视频视图用户不存在', `uid:${uid}, domId:${domId}`)
    }
  }
  /**
   * 销毁远端视频视图
   * @param {*} uid uid
   */
  destroyRemoteVideo(uid) {
    const user = this.usersVideoTrack.get(+uid)
    if (user && user.videoTrack) {
      user.videoTrack.stop()
    } else {
      RTCLog.warning('销毁远端视频视图不存在', `uid:${uid}`)
    }
  }
  destroyLocalVideo() {
    if (this.localVideoTrack) {
      this.localVideoTrack.stop()
    } else {
      RTCLog.warning('销毁本地视频视图不存在')
    }
  }
  /**
   * 启用/禁用远端视频
   * @param {*} uid
   * @param {*} mute
   */
  muteRemoteVideo(uid, mute) {
    const user = this.usersVideoTrack.get(+uid)
    if (user && user.videoTrack) {
      if (mute) {
        user.videoTrack.stop()
      } else {
        user.videoTrack.play()
      }
    } else {
      this.logger('error', '操作视频用户不存在', `uid:${uid},mute:${mute}`)
    }
  }

  /**
   * 启用/禁用远端音频
   * @param {*} uid
   * @param {*} mute
   */
  muteRemoteAudio(uid, mute) {
    if (uid == 'teacher') {
      uid = this.teacherUid
    }
    const user = this.usersAudioTrack.get(+uid)
    console.log(`audio:调用muteRemote:${+uid},mute:${mute}`)
    if (user && user.audioTrack) {
      // 远端推流状态
      console.log(`audio:运行stop或者play，muteRemote:${+uid},mute:${mute},user:${user.audioTrack}`)

      if (mute) {
        user.audioTrack.stop()
      } else {
        user.audioTrack.play()
      }
    } else {
      this.logger('warn', '操作音频用户不存在', `uid:${uid},mute:${mute}`)
    }
  }
  /**
   * 退出频道
   */
  async leaveChannel() {
    await this.rtcClient.leave()
  }
  /**
   * 启用/禁用视频轨道
   * @param {boolean} enable
   */
  enableLocalVideo(enable) {
    if (!this.localVideoTrack) {
      this.logger('error', '启用video，无localVideoTrack')
      return
    }
    this.localVideoTrack.setEnabled(enable)
  }
  getRTCStats() {
    return this.rtcClient?.getRTCStats()
  }
  // 是否资源
  async release() {
    if (!this.isKickout) {
      await this.unpublish()
      await this.leaveChannel()
    }

    this.usersVideoTrack.clear()
    this.usersAudioTrack.clear()
    if (this.localVideoTrack) {
      this.localVideoTrack.close()
    }
    if (this.localAudioTrack) {
      this.localAudioTrack.close()
    }
    this.rtcClient.removeAllListeners()
    AgoraRTC.removeAllListeners()
    this.rtcClient = null
    this.localAudioTrack = null
    this.localVideoTrack = null
    this.cameraMedia = null
  }
  emitVideoStatus(uid, mute) {
    if (uid == this.teacherUid || uid == this.teacherVideoUid) {
      // 老师视频正常播放状态
      this.emit('teacherVideoMute', mute)
    } else if (uid !== this.teacherShareUid) {
      this.emit('studentVideoMute', {
        uid,
        mute
      })
    } else if (uid === this.teacherShareUid) {
      this.emit('screenShareVideo', mute)
    }
  }
  emitAudioStatus(uid, mute) {
    console.log('studentAudioMute', 'uid:', uid, 'mute:', mute)
    if (uid == this.teacherUid || uid == this.teacherAudioUid) {
      // 老师视频正常播放状态
      this.emit('teacherAudioMute', mute)
    } else if (uid !== this.teacherShareUid) {
      this.emit('studentAudioMute', { uid, mute })
    }
  }
  remoteUserJoined(uid) {
    // PC教师端加入频道
    if (uid == this.teacherUid) {
      this.teacherDeviceType = 'pc'
      // 广播主讲老师加入频道
      this.emit('teacherJoinChannel', this.teacherDeviceType)
    }
    // iPad教师端加入频道
    if (uid == this.teacherAudioUid) {
      this.teacherDeviceType = 'ipad'
      // 广播主讲老师加入频道
      this.emit('teacherJoinChannel', this.teacherDeviceType)
    }
    if (uid == this.teacherVideoUid) {
      // 广播主讲视频端加入频道
      this.emit('teacherVideoJoinChannel')
    }
    this.emit('remoteJoinChannel', uid)
  }
  /**
   * 获取老师视频uid
   */
  _getTeacherVideoUid() {
    return this.teacherDeviceType === 'pc' ? this.teacherUid : this.teacherVideoUid
  }
  /**
   * 创建老师视频视图
   * @param {*} id Dom
   */
  createTeacherVideo(id) {
    const uid = this._getTeacherVideoUid()
    this.createRemoteVideo(uid, id)
  }
  destroyTeacherVideo() {
    const uid = this._getTeacherVideoUid()
    this.destroyRemoteVideo(uid)
  }
  muteLocalVideo(mute) {
    if (mute) {
      this.unpublishVideo()
    } else {
      this.publishVideo()
    }
    // this.rtc.muteLocalVideo(mute)
  }

  /**
   * 记录日志信息
   * @param {string} type - 日志类型，支持 'error', 'warn', 'info', 'success', 'action'，其他值默认使用'info'
   * @param {string} msg - 日志消息内容
   * @param {string|object} [content=''] - 额外的日志内容，会以 "- content" 的形式附加在消息后
   * @param {string} [title='AgoraRtcEngine'] - 日志前缀标题，会以 "[title]" 的形式显示
   * @returns {void}
   */
  logger(type, msg, content = '', title = 'AgoraRtcEngine') {
    try {
      // 参数类型检查
      const safeType = typeof type === 'string' ? type.toLowerCase() : 'info'
      const safeMsg = typeof msg === 'string' ? msg : String(msg)
      const safeTitle = typeof title === 'string' ? title : 'AgoraRtcEngine'

      // 格式化内容
      let formattedContent = ''
      if (content) {
        formattedContent =
          typeof content === 'string' ? ` - ${content}` : ` - ${JSON.stringify(content)}`
      }

      // 格式化完整日志消息
      const logMessage = `[${safeTitle}] ${safeType}: ${safeMsg}${formattedContent}`

      // 使用映射对象替代多个条件判断
      const logFunctions = {
        error: RTCLog?.error,
        warn: RTCLog?.warning,
        info: RTCLog?.info,
        success: RTCLog?.success,
        action: RTCLog?.action
      }

      // 获取对应的日志函数，默认使用 info
      const logFunction = logFunctions[safeType] || RTCLog?.info

      // 安全调用日志函数
      if (typeof logFunction === 'function') {
        logFunction(logMessage)
      } else {
        // 降级到控制台
        console.log(logMessage)
      }
    } catch (e) {
      // 确保即使发生错误也不会崩溃
      try {
        console.error('Logger error:', e)
      } catch {
        // 最终防线
      }
    }
  }
}
