import mitt from 'mitt'

export default class ThinkBase {
  constructor() {
    this.emitter = mitt()
  }

  on(event, handler) {
    this.emitter.on(event, handler)
  }

  off(event, handler) {
    this.emitter.off(event, handler)
  }

  emit(event, payload) {
    this.emitter.emit(event, payload)
  }

  padZero(num, position = 2) {
    if (position === 2) {
      return num >= 0 && num < 10 ? `0${num}` : String(num)
    } else {
      if (num < 10) {
        return `00${num}`
      }
      if (num < 100) {
        return `0${num}`
      }
      return String(num)
    }
  }

  formatDate(formats, timestamp = '') {
    formats = formats || 'Y-m-d'
    let ret
    const myDate = timestamp ? new Date(timestamp) : new Date()
    const opt = {
      'Y+': myDate.getFullYear().toString(), // 年
      'M+': this.padZero(myDate.getMonth() + 1).toString(), // 月
      'D+': this.padZero(myDate.getDate()).toString(), // 日
      'H+': this.padZero(myDate.getHours()).toString(), // 时
      'm+': this.padZero(myDate.getMinutes()).toString(), // 分
      's+': this.padZero(myDate.getSeconds()).toString(), // 秒
      'S+': this.padZero(myDate.getMilliseconds(), 3).toString() // 毫秒
    }
    for (let k in opt) {
      ret = new RegExp('(' + k + ')').exec(formats)
      if (ret) {
        formats = formats.replace(ret[1], opt[k])
      }
    }
    return formats
  }

  serialize(value) {
    if (typeof value === 'string') return value
    return JSON.stringify(value)
  }

  logger(type, msg, content = '', title = 'AgoraRtcEngine') {
    console.log(
      `%c ${title} [${this.formatDate('HH:mm:ss.SSS')}] %c [${msg}] %c ${this.serialize(content)} `,
      `background:#${
        title === 'AgoraRtcEngine' ? '4c4c4c' : '576b95'
      }; padding: 2px; line-height:16px;border-radius: 3px 0 0 3px; color: #fff;`,
      `background:#${
        title === 'AgoraRtcEngine' ? '1BB934' : '539bf5'
      }; padding: 2px; line-height:16px;border-radius: 0;  color: #fff;`,
      `background:#${
        type === 'info' ? (title === 'AgoraRtcEngine' ? '049be3' : '6cb6ff') : 'E1112C'
      }; padding: 2px; line-height:16px;border-radius: 0 3px 3px 0;  color: #fff;`
    )
  }
}
