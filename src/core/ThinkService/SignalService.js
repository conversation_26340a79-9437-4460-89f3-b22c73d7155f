import ThinkBase from './ThinkBase'
import { ChatResultCode } from './config/chatreResCode'
import { ignoreRoomDataNotices } from './config/classroom'
import { useClassData } from '@/stores/classroom'
import { IRCLog } from '@/utils/web-log/HWLogDefine'
// import { ircSensorPush } from 'utils/sensorEvent'
// const TalMsgClient = window.TalMsgClient
// const sdkVersion = '3.0.5'

export default class SignalService extends ThinkBase {
  constructor(options = {}) {
    super()
    const { planId, businessId, userId, isAudition, ircOptions } = options
    this.ircHaveStartSensor = false // irc是否有开始埋点，如果没有需要补开始埋点
    this.timeOut = 5
    this.canLogin = false
    this.ircTimer = null
    this.planId = planId
    this.businessId = businessId
    this.userId = userId
    this.isAudition = isAudition
    this.ircOptions = ircOptions || {}
    this.liveInfo = {}
    this.inRoom = false // 当前是否在房间中，重连不需要重复加入房间
    this.chatInstance = null // irc实例
    try {
      this.triggerTimeMap = localStorage.getItem('triggerTimeMap')
        ? JSON.parse(localStorage.getItem('triggerTimeMap'))
        : {} // kv消息触发时间存储
    } catch (error) {
      IRCLog.error('triggerTimeMap报错', error)
      this.triggerTimeMap = {}
    }

    this._initChat()
    this._listener()
  }
  // 登陆开始倒计时
  beginTimer() {
    this.sendLogger('irc 开始倒计时', {}, 'info')
    this.ircTimer = setTimeout(() => {
      this.emit('showNotification', '402')
    }, this.timeOut * 1000)
  }
  // 结束倒计时
  endTimer() {
    this.sendLogger('irc 结束倒计时', {}, 'info')
    this.emit('closeNotification', '402')
    clearTimeout(this.ircTimer)
    this.ircTimer = null
  }
  // 调用irc初始化方法
  initSdk() {
    const { appId, appKey, nickname, location } = this.ircOptions
    this.liveInfo = {
      nickname,
      businessId: this.businessId,
      classId: '0',
      liveId: String(this.planId),
      location: location || 'China'
    }
    IRCLog.info('liveInfo', this.liveInfo)
    const TalMsgClient = window.TalMsgClient
    const client = new TalMsgClient(appId, 'v3.2.1')
    window.ChatClient = this.chatInstance = client.getInstance(1)
    // 初始化sdk
    const chatResult = this.chatInstance.init(appId, appKey)
    // 日志
    this.sendLogger(
      `irc 调用初始化方法,code: ${chatResult},appId:${appId},appKey:${appKey}`,
      {},
      chatResult == 0 ? 'info' : 'error'
    )
    // 链路开始埋点
    // ircSensorPush({ result: 'start', liveInfo: this.liveInfo })
    this.ircHaveStartSensor = true
    return chatResult
  }
  // 调用irc 设置房间信息方法
  setLiveInfo() {
    const res = this.chatInstance.setLiveInfo(this.liveInfo)
    // 日志
    this.sendLogger(`irc 设置直播信息,code:${res}`, this.liveInfo, res == 0 ? 'info' : 'error')
    return res
  }
  // 设置配置信息
  setSdkProperties() {
    const { confService, logService } = this.ircOptions
    const sdkPramas = {
      confService: {
        hostname: confService.hostname,
        url: confService.url,
        protocol: confService.protocol
      },
      logService: {
        hostname: logService.hostname,
        url: logService.url,
        protocol: logService.protocol
      }
    }
    const setSdkRes = this.chatInstance.setSdkProperties(sdkPramas)
    // 日志
    this.sendLogger(
      `irc 设置sdk配置信息,code:${JSON.stringify(setSdkRes)}`,
      sdkPramas,
      setSdkRes == 0 ? 'info' : 'error'
    )
    return setSdkRes
  }
  // 登陆成功后加入房间
  joinRoom() {
    const RoomChatManager = this.chatInstance.RoomChatManager
    const joinChatRes = RoomChatManager.joinChatRoomsWithJoinMode(this.ircOptions.ircRooms, 1)
    IRCLog.success('【SignalService】加入房间', {
      rooms: this.ircOptions.ircRooms,
      result: joinChatRes
    })
    if (joinChatRes === 0) {
      this.sendLogger(`irc调用加入房间接口成功`)
    } else {
      this.sendLogger(`irc调用加入房间接口失败,code:${joinChatRes}`, {}, 'error')
      // ircSensorPush({
      //   result: 'fail',
      //   errorType: '调用加入房间接口失败',
      //   code: joinChatRes,
      //   liveInfo: { ...this.liveInfo, ircRooms: this.ircOptions.ircRooms }
      // })
    }
  }
  /**
   * 初始化chat sdk
   */
  _initChat() {
    // 调用初始化
    const initRes = this.initSdk()
    if (initRes === ChatResultCode.Success) {
      // 设置房间信息
      const setLiveInfoRes = this.setLiveInfo()
      if (setLiveInfoRes == 0) {
        const setSdkRes = this.setSdkProperties()
        if (setSdkRes == 0) {
          this.canLogin = true
        } else {
          // ircSensorPush({
          //   result: 'fail',
          //   errorType: '设置配置信息失败',
          //   code: setSdkRes,
          //   liveInfo: this.liveInfo
          // })
        }
      } else {
        // 埋点
        // ircSensorPush({
        //   result: 'fail',
        //   errorType: '设置直播信息失败',
        //   code: setLiveInfoRes,
        //   liveInfo: this.liveInfo
        // })
      }
    } else {
      // 埋点
      // ircSensorPush({
      //   result: 'fail',
      //   errorType: '初始化失败',
      //   code: initRes,
      //   liveInfo: this.liveInfo
      // })
    }
  }
  /**
   * irc 登陆
   */
  ircLogin() {
    // 加入频道成功后，不重复登陆
    // if (store.state.smallClass.ircStatus) return
    const res = this.chatInstance.loginWithMode(this.userId, this.userId, 0)
    IRCLog.info('【SignalService】IRC登录', {
      userId: this.userId,
      result: res
    })
    if (res == 0) {
      // 调用登陆之后开始倒计时，5s未连接显示超时弹窗
      this.beginTimer()
      this.sendLogger(`irc调用登陆方法成功`)
    } else {
      this.sendLogger(`irc调用登录接口失败,code:${res}`, this.ircOptions, 'error')
      // ircSensorPush({
      //   result: 'fail',
      //   errorType: '调用登录接口失败',
      //   code: res,
      //   liveInfo: this.liveInfo
      // })
    }
  }
  /**
   * 消息监听
   */
  async _listener() {
    this._chatListener()
    this._roomListener()
    this._peerListener()
  }

  /**
   * 监听Chat消息
   */
  _chatListener() {
    const RoomChatManager = this.chatInstance.RoomChatManager
    // 登录通知
    this.chatInstance.on('onLoginResponse', res => {
      const { code } = res
      if (code == ChatResultCode.Success) {
        if (!this.inRoom) {
          this.joinRoom() // 登陆成功，加入房间
          this.sendLogger(`irc 登陆回调返回成功`)
        } else {
          IRCLog.info('当前已在irc房间中，无需重复加入')
        }
      } else {
        this.sendLogger(`irc 登陆回调返回失败`, res, 'error')
        // ircSensorPush({ result: 'fail', errorType: '登录失败', code, liveInfo: this.liveInfo })
      }
    })
    // 加入聊天室回调
    RoomChatManager.on('onJoinRoomResponse', res => {
      if (res.code == 0) {
        this.inRoom = true
        // 关闭超时倒计时
        this.endTimer()
        const store = useClassData()
        store.setIrcStatus(true)
        // 广播登录成功消息
        this.emit('ready', true)
        // 获取聊天室所有参数
        RoomChatManager.getAllRoomData(this.ircOptions.ircRooms[0])
        // 获取历史消息
        RoomChatManager.getRoomHistoryMessage(this.ircOptions.ircRooms[0], 0)
        this.sendLogger(`irc 加入房间回调成功`)
        // ircSensorPush({ result: 'success', liveInfo: this.liveInfo })
      } else {
        this.sendLogger(`irc 加入房间回调失败`, res, 'error')
        // ircSensorPush({
        //   result: 'fail',
        //   errorType: '加入房间失败',
        //   code: res.code,
        //   liveInfo: this.liveInfo
        // })
      }
    })
    // 退出通知
    this.chatInstance.on('onLogoutNotice', res => {
      this.sendLogger(`irc 频道内人员退出:${JSON.stringify(res)}`)
      const { code } = res
      if (code == ChatResultCode.Success) {
        // 广播退出成功消息
        this.emit('logoutSuccess')
      }
    })
    // 网络状态变更通知
    // 状态值，0-未知；1-网络不可用；2-服务器连接失败；3-服务器连接中；4-服务器连接成功；5-服务器断开连接
    this.chatInstance.on('onNetStatusChanged', async res => {
      IRCLog.info(`[hw_irc_join_room]触发网络状态回调${res.netStatus}`)
      if (res.netStatus != 4) {
        // irc未连接成功时，触发提示弹窗
        res.netStatus != 3 && this.emit('showNotification', res.netStatus)
        // 日志
        const netStatusMap = {
          0: '未知',
          1: '网络不可用',
          2: '服务器连接失败',
          3: '服务器连接中',
          5: '服务器断开连接'
        }
        this.sendLogger(
          `irc 网络状态改变,${netStatusMap[res.netStatus]}`,
          {},
          res.netStatus == 3 ? 'info' : 'error'
        )
        // 神策埋点逻辑
        if (res.netStatus == 0 || res.netStatus == 1 || res.netStatus == 2) {
          // 检测当前是否有网络
          // ircSensorPush({
          //   result: 'fail',
          //   errorType: '连接失败',
          //   code: res.netStatus,
          //   msg: `${isOnline ? '有网络' : '没有网络'}`,
          //   liveInfo: this.liveInfo
          // })
        } else if (res.netStatus == 3) {
          // ircSensorPush({ result: 'start', liveInfo: this.liveInfo })
          this.ircHaveStartSensor = true
        }
      } else {
        // irc连接成功后， 关闭弹窗
        this.emit('closeNotification', res.netStatus)
        this.sendLogger(`irc 网络状态改变,连接成功 关闭弹窗`)
      }
      this.emit('onNetStatusChanged', res)
    })
    // 踢出通知
    // 被踢错误码
    // 300-服务器调度，希望客户端连接其它接入
    // 301-重复登录被踢下线
    // 302-业务应用向消息系统发起踢出用户请求
    // 303-ping响应超时
    // 399-其它异常操作，如登录超时等
    this.chatInstance.on('onKickoutNotice', res => {
      this.sendLogger(`irc 被其他客户端顶掉:${JSON.stringify(res)}`, {}, 'error')
      this.emit('onKickoutNotice', res)
    })
    // 调度状态回调
    // 0-成功
    // 1-调度错误
    // 2-配置错误
    // 100 -未知错误
    this.chatInstance.on('onSDKProvisionStatusNotice', res => {
      if (!this.ircHaveStartSensor) {
        // 如果没有成对的开始埋点，需要补一个开始，因为调度服务会重试，导致一个开始点对应很多失败点
        // ircSensorPush({ result: 'start', liveInfo: this.liveInfo })
      }
      // 走到调度服务，将开始点消费
      this.ircHaveStartSensor = false
      if (res.status != 0) {
        // 调度服务连接失败显示提醒
        this.emit('showNotification', res.status)
        this.sendLogger(`irc 连接调度服务失败 显示弹窗`, res, 'error')
        // ircSensorPush({
        //   result: 'fail',
        //   errorType: '调度失败',
        //   code: res.status,
        //   liveInfo: this.liveInfo
        // })
      } else {
        this.emit('closeNotification', res.status)
        this.sendLogger(`irc 连接调度服务成功 关闭弹窗`)
      }
      this.emit('onSDKProvisionStatusNotice', res)
    })
  }

  /**
   * 监听Room消息
   */
  _roomListener() {
    const RoomChatManager = this.chatInstance.RoomChatManager
    RoomChatManager.on('onSetRoomDataResponse', res => {
      this.emit('onSetRoomDataResponse', res)
    })
    // 涂鸦消息
    RoomChatManager.on('onRecvRoomBinMessageNotice', res => {
      this.emit('onRecvRoomBinMessageNotice', res)
    })
    // 涂鸦历史消息
    RoomChatManager.on('onGetRoomHistoryBinMessageNotice', res => {
      this.emit('onGetRoomHistoryBinMessageNotice', res)
    })
    // 聊天室消息-(kv消息)
    RoomChatManager.on('onRecvRoomDataUpdateNotice', res => {
      const { datas, handler } = res
      const isHistory = datas.size > 1 // 是否是历史消息
      IRCLog.info('收到KV消息', {
        isHistory,
        handler,
        datas
      })

      datas.forEach((item, key) => {
        // 旁听身份kv消息忽略处理
        if (this.isAudition && ignoreRoomDataNotices.includes(key)) {
          IRCLog.info('【SignalService】旁听课不参与互动', { key })
          return
        }
        const noticeValue = JSON.parse(item.value)
        const sendTime = noticeValue.sendTime
        // 通过sendTime本地缓存，记录当前互动是否被触发过。
        const isTrigger = this.isTriggerFun(key, noticeValue)
        const noticeContent =
          typeof noticeValue[key] == 'boolean'
            ? noticeValue[key]
            : noticeValue[key] || noticeValue.data
        IRCLog.info('KV消息处理结果', {
          key,
          isHistory,
          isTrigger,
          noticeContent
        })
        this.emit('onRecvRoomDataUpdateNotice', {
          key,
          isHistory,
          noticeContent,
          sendTime,
          isTrigger
        })
      })
    })
    // 获取聊天室参数响应
    RoomChatManager.on('onGetRoomDataResponse', res => {
      this.emit('onGetRoomDataResponse', res)
    })
    // 群聊消息
    RoomChatManager.on('onRecvRoomMessage', res => {
      const { content, fromUserInfo, messagePriority, msgId, timestamp } = res
      IRCLog.info('【SignalService】收到群聊消息', {
        content: JSON.parse(content),
        fromUserInfo,
        messagePriority,
        msgId,
        timestamp
      })
      this.emit('onRecvRoomMessage', {
        content: JSON.parse(content),
        fromUserInfo,
        messagePriority,
        msgId,
        timestamp
      })
      this.sendLogger(`irc 收到群聊消息:${JSON.stringify(res)}`)
    })
    // 接收聊天室主题
    RoomChatManager.on('onRecvRoomTopic', res => {
      this.emit('onRecvRoomTopic', res)
    })
    // 群聊历史消息
    RoomChatManager.on('onGetRoomHistoryMessageResponse', res => {
      this.emit('onGetRoomHistoryMessageResponse', res)
    })
    // 他人进入聊天室
    RoomChatManager.on('onJoinRoomNotice', res => {
      this.emit('onJoinRoomNotice', res)
    })
    // 接收聊天室用户列表
    RoomChatManager.on('onRecvRoomUserList', res => {
      this.emit('onRecvRoomUserList', res)
    })
    // 他人离开聊天室通知
    RoomChatManager.on('onLeaveRoomNotice', res => {
      this.emit('onLeaveRoomNotice', res)
    })
    // 发送群聊消息回复
    RoomChatManager.on('onSendRoomMessageResponse', res => {
      this.emit('onSendRoomMessageResponse', res)
      if (res.code == 0) {
        // ircSensorPush({
        //   type: 'message',
        //   result: 'success',
        //   msg: '群聊'
        // })
      } else {
        // ircSensorPush({
        //   type: 'message',
        //   result: 'fail',
        //   errorType: '群聊消息发送失败',
        //   msg: '群聊',
        //   code: res.code,
        //   msgInfo: res
        // })
        this.sendLogger(`irc 群聊消息发送失败`, res, 'error')
      }
    })
    // 在线人数消息
    RoomChatManager.on('onRoomUserCountNotice', res => {
      this.emit('onRoomUserCountNotice', res)
    })
    // 涂鸦发送事件回调
    RoomChatManager.on('onSendRoomBinMessageResp', res => {
      this.emit('onSendRoomBinMessageResp', res)
      if (res.code == 0) {
        // ircSensorPush({
        //   type: 'message',
        //   result: 'success',
        //   msg: '群聊二进制'
        // })
      } else {
        // ircSensorPush({
        //   type: 'message',
        //   result: 'fail',
        //   errorType: '群聊二进制消息发送失败',
        //   msg: '群聊二进制',
        //   code: res.code,
        //   msgInfo: res
        // })
        this.sendLogger(`irc 发送二进制涂鸦消息失败`, res, 'error')
      }
    })
  }

  /**
   * 监听私聊消息
   */
  _peerListener() {
    const PeerChatManager = this.chatInstance.PeerChatManager
    // 发送单聊消息的回复
    PeerChatManager.on('onSendPeerMessageResponse', res => {
      this.emit('onSendPeerMessageResponse', res)
      if (res.code == 0) {
        // ircSensorPush({
        //   type: 'message',
        //   result: 'success',
        //   msg: '私聊'
        // })
      } else {
        // ircSensorPush({
        //   type: 'message',
        //   result: 'fail',
        //   errorType: '私聊消息发送失败',
        //   msg: '私聊',
        //   code: res.code,
        //   msgInfo: res
        // })
        this.sendLogger(`irc 发送私聊失败,code: ${res.code}`, res, 'error')
      }
    })
    // 私聊消息
    PeerChatManager.on('onRecvPeerMessage', res => {
      this.sendLogger(`监听irc私聊消息${JSON.stringify(res)}`)
      const { content, fromUserInfo, messagePriority, msgId, timestamp } = res
      this.emit('onRecvPeerMessage', {
        content: JSON.parse(content),
        fromUserInfo,
        messagePriority,
        msgId,
        timestamp
      })
      this.sendLogger(`irc 私聊消息监听:${JSON.stringify(res)}`)
    })
  }

  /**
   * 网校sdk获取二进制历史数据
   * @param {Object} data
   */
  getRoomHistoryBinMessage(data) {
    const { roomid, key } = data
    this.sendLogger(`拉取历史数据 ${JSON.stringify(data)}`)

    this.chatInstance.RoomChatManager.getRoomHistoryBinMessage(roomid, key, '0', true)
  }
  /**
   * 培优sdk获取二进制历史数据
   * @param {Object} data
   */
  // getRoomHistoryBinMessageNew(data) {
  //   const { roomid, key } = data // key是该学员的dbkey
  //   // 遍历老师以及所有学员的dbkey
  //   const teachdbkey = key
  //     .split('_')
  //     .splice(1)
  //     .join('_')

  //   this.chatInstance.RoomChatManager.getRoomHistoryBinMessage(roomid, teachdbkey, '0', true)
  //   // 获取所有页被授权学员列表
  //   const isAuthorizedUserList = store.state.smallClass.isAuthorizedUserList
  //   // 获取该页（相同dbkey）被授权学员列表
  //   const curPageKeyUserList = isAuthorizedUserList.find(e => e.pageKey === teachdbkey)
  //   if (curPageKeyUserList && curPageKeyUserList.userIdList) {
  //     curPageKeyUserList.userIdList.forEach(stu => {
  //       const studbkey = `${stu}_${teachdbkey}`
  //       this.chatInstance.RoomChatManager.getRoomHistoryBinMessage(roomid, studbkey, '0', true)
  //     })
  //   }
  //   this.sendLogger(
  //     'irc',
  //     `拉取老师和学员历史数据 teachdbkey: ${teachdbkey}, curPageKeyUserList: ${JSON.stringify(
  //       curPageKeyUserList
  //     )}`
  //   )
  // }

  /**
   * 发送私聊消息
   * @param {Object} params 参数对象
   * @param params.content 消息内容
   * @param params.chatMsgPriority 消息优先级
   * @param params.nickname 昵称
   */
  sendPeerMessage(params = {}) {
    const { content, nickname, chatMsgPriority = 1 } = params
    return this.chatInstance.PeerChatManager.sendPeerMessage(
      [{ nickname }],
      JSON.stringify(content),
      chatMsgPriority
    )
  }

  /**
   * 给房间发消息
   * @param {Object} params 参数对象
   * @param params.roomList 房间号
   * @param params.content 消息
   * @param params.chatMsgPriority 消息优先级
   */
  sendRoomMessage(params = {}) {
    const { content, roomList, chatMsgPriority = 99 } = params
    IRCLog.info('【SignalService】发送群聊消息', {
      content,
      roomList,
      chatMsgPriority
    })
    return this.chatInstance.RoomChatManager.sendRoomMessage(
      roomList || this.ircOptions.ircRooms,
      JSON.stringify(content),
      chatMsgPriority
    )
  }
  // 涂鸦发送消息
  sendRoomBinMessage(roomId, dbKey, keyMsgId, content) {
    return this.chatInstance.RoomChatManager.sendRoomBinMessage(roomId, dbKey, keyMsgId, content)
  }
  // 涂鸦历史消息
  getRoomBatchHistoryBinaryMessages(reqData) {
    return this.chatInstance.RoomChatManager.getRoomBatchHistoryBinaryMessages(reqData)
  }
  /**
   * 给房间发消息返回preMsgId
   * @param {Object} params 参数对象
   * @param params.roomList 房间号
   * @param params.content 消息
   * @param params.chatMsgPriority 消息优先级
   */
  sendRoomMessageWithPreMsgId(params = {}) {
    const { content, roomList, chatMsgPriority = 99 } = params
    return this.chatInstance.RoomChatManager.sendRoomMessageWithPreMsgId(
      roomList || this.ircOptions.ircRooms,
      JSON.stringify(content),
      chatMsgPriority
    )
  }
  /**
   * 获取聊天历史记录
   */
  getRoomHistoryMessage() {
    this.chatInstance.RoomChatManager.getRoomHistoryMessage(this.ircOptions.ircRooms[0], 0)
  }
  setRoomData(roomId, data) {
    this.chatInstance.RoomChatManager.setRoomData(roomId, data)
  }
  /**
   * 退出
   */
  logout() {
    // store.dispatch('smallClass/updateIrcStatus', false)
    this.chatInstance.logout && this.chatInstance.logout()
  }

  /**
   * 反初始化RTC
   */
  unInit() {
    this.chatInstance && this.chatInstance.uninit()
  }

  /**
   * 释放资源
   */
  release() {
    this.logout()
    this.unInit()
  }
  /**
   * 判断kv消息是否被触发过
   */
  isTriggerFun(key, noticeValue) {
    const storageKey = `${key}_time`
    // 优先内存取
    let oldTrigger = this.triggerTimeMap[storageKey]
    // 内存没有取本地存储
    if (!oldTrigger) {
      const storageMap = localStorage.getItem('triggerTimeMap')
      if (storageMap) {
        oldTrigger = JSON.parse(storageMap)[storageKey] ? JSON.parse(storageMap)[storageKey] : 0
      } else {
        // 内存和本地存储都没有的情况
        oldTrigger = 0
      }
    }
    const triggerTime = noticeValue.sendTime ? noticeValue.sendTime : 0 //当前触发时间
    let isTrigger = false // 是否触发过
    // 当前触发时间大于之前触发时间，则判断为新互动，未触发过互动。否则为触发过互动。
    if (Number(triggerTime) > Number(oldTrigger)) {
      this.triggerTimeMap[storageKey] = triggerTime
      localStorage.setItem('triggerTimeMap', JSON.stringify(this.triggerTimeMap))
      isTrigger = false
      if (key !== 'canvas_switch_courseware') {
        this.sendLogger(`kv消息未触发过，触发，key: ${key}`)
      }
    } else {
      this.sendLogger(`kv消息已触发过，不再触发，key: ${key}`)
      isTrigger = true
    }
    return isTrigger
  }
  sendLogger(msg, content = '', type = 'info') {
    // this.logger(type, msg, content, 'ircService')
    try {
      // 参数类型检查
      const safeType = typeof type === 'string' ? type.toLowerCase() : 'info'
      const safeMsg = typeof msg === 'string' ? msg : String(msg)

      // 格式化内容
      let formattedContent = ''
      if (content) {
        formattedContent =
          typeof content === 'string' ? ` - ${content}` : ` - ${JSON.stringify(content)}`
      }
      // 格式化完整日志消息
      const logMessage = `${safeType}: ${safeMsg}${formattedContent}`

      // 使用映射对象替代多个条件判断
      const logFunctions = {
        error: IRCLog?.error,
        warn: IRCLog?.warning,
        info: IRCLog?.info,
        success: IRCLog?.success,
        action: IRCLog?.action
      }

      // 获取对应的日志函数，默认使用 info
      const logFunction = logFunctions[safeType] || IRCLog?.info

      // 安全调用日志函数
      if (typeof logFunction === 'function') {
        logFunction(logMessage)
      } else {
        // 降级到控制台
        console.log(logMessage)
      }
    } catch (e) {
      // 确保即使发生错误也不会崩溃
      try {
        console.error('Logger error:', e)
      } catch {
        // 最终防线
      }
    }
  }
}
