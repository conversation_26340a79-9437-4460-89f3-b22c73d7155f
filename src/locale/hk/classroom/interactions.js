/**
 * 互动配置
 */
export default {
  // 投票
  vote: {
    title: '投票',
    results: '結果',
    voteResult: '答題結果',
    true: '正確',
    false: '錯誤',
    correctTitle: '恭喜',
    correctTips: `你真棒`,
    rightTipsByPlayback: [`回答正確`, `你獲得了金幣`, `非常棒！`],
    rightTipsByLive: [`不對哦`, `你獲得了金幣`, `下次繼續努力！`],
    repeatSubmitMessage: `你已經提交過啦`,
    // 懂不懂
    isUnderstand: '聽懂老師講的內容了嗎？',
    understand: '听懂了',
    notUnderstand: '没听懂',
    understandFeedback: '非常棒，繼續保持哦!',
    notUnderstandFeedback: '沒關係，繼續加油哦!'
  },
  // 游戏互动
  gameCourseware: {},
  // 课间休息
  classRest: {
    title: '課間休息',
    startTip: '課間休息',
    endTip: '休息結束'
  },
  // 送礼物
  gift: {
    // 请选择礼物提示
    pleaseChooseGiftNotice: '選個禮物送給老師吧~',
    // 金币不足
    notEnoughCoin: '你的金幣不夠哦 :(',
    // 可送礼物次数已到达
    notEnoughTimes: '謝謝，但每次最多只能送3個禮物哦:)'
  },
  // 答题板
  coursewareBoard: {
    true: '正確',
    false: '錯誤',
    yourAnswer: '你的答案',
    rightAnswer: '正確答案',
    correctAnswer: '正確答案'
  },
  // 拍照上墙涂鸦
  graffiti: {
    yellow: '黃',
    red: '紅',
    blue: '藍'
  },
  // 拍照上墙
  photoWall: {
    graffitiBtnName: '塗鴉畫板',
    graffitiDesc1: `拍照不清楚？`,
    graffitiDesc2: `使用塗鴉畫板提交練習吧!`,
    cameraBtnName: '使用相機',
    confirmNotice: [`確認提交嗎？`, `提交後將不可再次修改`],
    shortCutTip: `提示：點擊空格鍵也可以拍照`,
    notjoinTip: `老師已經結束了作答，期待下次積極參與哦!`,
    hasAccessNotice: [
      `在講義或者草稿紙上完成練習`,
      `點擊下方的按鈕拍照上傳`,
      `提交後可在『練習』中查看批改結果`
    ],
    notAccessNotice: [`相機不可用`, `使用塗鴉畫板提交練習吧`],
    endNotice: [`老師已經結束了互動，現在提交你的答案嗎？`, `5秒後返回課堂`],
    grading: '批改中',
    correct: '正確',
    incorrect: '錯誤',
    halfCorrect: '部分正確',
    answerCorrect: '回答正確'
  },
  // 随机连线
  randomCall: {
    title: '隨機點名',
    finishedNotice: {
      self: `恭喜，你被選中了！`,
      other: '{name}被選中了'
    }
  },
  // 红包
  redPacket: {
    received: 'Received', // 查看红包结题里显示的, 目前功能已隐藏, 暂不翻译
    unfortunatelyRedNotice: ['紅包已被領取完', ''],
    oopsRedNotice: `你已經收到了這個紅包`,
    congrats: '恭喜!'
  },
  // 视频连麦
  videoLink: {
    joinMessage: '舉手加入視頻連麥吧!',
    joinButtonName: '舉手',
    waitMessage: '等待老師選擇',
    cancelButton: '取消',
    raiseHandButton: '舉手',
    speaking: '發言中...'
  },
  // 集体发言
  collectiveSpeech: {
    permissionDeniedTips: '麥克風權限未開啟，請在系統設置中開啟麥克風權限'
  },
  redRain: {
    collect: '收下金幣',
    toastTip: '老師結束了互動',
    loading: '遊戲加載中..',
    NetworkFailTip: '服務器連接超時，請查看網絡情況後重試',
    RetryBtn: '重試',
    title: '紅包雨'
  },
  // 填一填
  nonPersetFillBlank: {
    submitSuccessfully: '提交成功',
    submitFailed: '提交失敗，請重試',
    inputPlaceholder: '請輸入答案',
    clear: '清空',
    submit: '提交',
    myAnswer: '我的答案'
  },
  praiseList: {
    fileName: '課堂表揚榜_{firstTitle}_{lessonName}_{courseName}',
    saveText: '截图已保存至',
    savePath: '桌麵/Think-Academy-Screenshot',
    savingText: '保存中',
    emptyText: '本班暫時無人上榜'
  },
  schulteTable: {
    schulteTable: '舒爾特方格',
    naturalNumber: '自然數',
    primeNumber: '質數',
    normalMode: '普通模式',
    shuffleMode: '隨機模式',
    nextNumber: '下一個數字',
    duration: '{duration}秒',
    gameInstructions: [
      '從小到大按順序從{start}到{end}點擊下方自然數',
      '從小到大按順序從{start}到{end}點擊下方質數'
    ],
    gameObjectives: ['從小到大按順序點擊數字，', '點擊越快獲得金幣越多'],
    gameCompleted: '遊戲完成',
    newRecord: '新紀錄',
    firstChallenge: '首次挑戰',
    yourCurrentTime: '本次用時',
    yourBestTime: '歷史最佳',
    leaderboard: '通關時間榜',
    congratulations: '太棒了! 恭喜你成功上榜！',
    tryAgain: '啊哦！只差一點點，下次繼續加油呀！',
    gameOver: '遊戲時間到',
    leaderboardTip: ['老師即將發布排行榜！', '你上榜了嗎？馬上揭曉！'],
    submitFailTitle: '遊戲結果提交失敗',
    submitFailTip: '服務器連接超時，請查看網絡情況後重試',
    submitFailBtn: '重試'
  }
}
