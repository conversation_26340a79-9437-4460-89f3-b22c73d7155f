/**
 * 互动配置
 */
export default {
  // 聊天
  chats: {
    newMessage: '新消息',
    teacherOnly: '僅看老師',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: '聊天已關閉',
      2: '聊天已關閉',
      3: '',
      4: '',
      5: '加載中'
    },
    inputPlaceholder: '說點什麼吧',
    hotWordList: ['完成！', '我不懂', '我聽懂了', '哈哈哈'],
    msgTip: {
      CONNECT: '聊天服務已連接',
      DISCONNECT: '聊天服務已斷開',
      RECONNECT: '聊天服務重新連接中',
      BAN_SPEECH: '輔導老師已將你禁言', // 禁言
      RELIEVE_SPEECH: '輔導老師已將你解除禁言', // 解除禁言
      SPEECH_INTERVAL: '請3秒後再發言',
      SPEECH_MINE: '我',
      SPEECH_SYS: '系統消息',
      SPEECH_TEACHER: '老師',
      SPEECH_EMPTY: '輸入的消息不合法',
      REMOTE_LOGIN: '當前地點不是常用登錄地點，請刷新此頁面',
      CHAT_DISCONNECT: '服務器連接已斷開，當前不能發言',
      ENTER_ROOM: '進入了教室'
    },
    level: {
      wood: '青木',
      gold: '黃金',
      silver: '白銀',
      diamond: '星鉆'
    },
    chatbox: '班級聊天',
    frequently: '消息發送過於頻繁',
    onlyteacher: '老師關閉了群聊，僅允許發送消息給老師。',
    all: '老師打開了群聊，允許發送消息給所有人。',
    teacherOff: '老師關閉了聊天',
    teacherOn: '老師開啟了聊天',
    chatboxClosed: '班級聊天已關閉',
    connecting: '連接中...',
    sendTo: '發送給',
    sendToAll: '所有人',
    sendToTeacher: '老師',
    privateMessage: '私訊',
    To: '發給',
    mutedByTeacher: '你已被老師禁言',
    unmutedByTeacher: '你的禁言狀態已被解除',
    underMute: '禁言中'
  }
}
