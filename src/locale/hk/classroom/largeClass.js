/**
 * 大班直播
 */
export default {
  // 导航区
  header: {
    groupVideoButtonName: '小組視頻',
    deviceTestButtonName: '設備檢測',
    teacherMessageOnly: '僅看老師消息'
  },
  // 播放器区
  players: {
    mute: '靜音',
    unmute: '取消靜音'
  },
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: '老師暫未在教室，請稍等',
    gameConfig: {
      gameTip: '老師演示中',
      gameToast: '請稍等老師演示結束'
    }
  },
  // 聊天
  chats: {
    newMessage: '新消息',
    teacherOnly: '僅看老師',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: '聊天已關閉',
      2: '聊天已關閉',
      3: '',
      4: '',
      5: '加載中'
    },
    inputPlaceholder: '說點什麼吧',
    hotWordList: ['完成！', '我不懂', '我聽懂了', '哈哈哈'],
    msgTip: {
      CONNECT: '聊天服務已連接',
      DISCONNECT: '聊天服務已斷開',
      RECONNECT: '聊天服務重新連接中',
      BAN_SPEECH: '輔導老師已將你禁言', // 禁言
      RELIEVE_SPEECH: '輔導老師已將你解除禁言', // 解除禁言
      SPEECH_INTERVAL: '請3秒後再發言',
      SPEECH_MINE: '我',
      SPEECH_SYS: '系統消息',
      SPEECH_TEACHER: '老師',
      SPEECH_EMPTY: '輸入的消息不合法',
      REMOTE_LOGIN: '當前地點不是常用登錄地點，請刷新此頁面',
      CHAT_DISCONNECT: '服務器連接已斷開，當前不能發言',
      ENTER_ROOM: '進入了教室'
    },
    level: {
      wood: '青木',
      gold: '黃金',
      silver: '白銀',
      diamond: '星鉆'
    },
    reply: '回復',
    gotIt: '收到'
  },
  // 视频回显
  videoGroup: {
    textConfig: {
      matching: '匹配中...',
      noticeAccessDenied: '請在系統設置中打開Think Academy的攝像頭權限'
    },
    tabNames: ['視頻', '聊天'],
    closedNotice: '小組視頻已關閉',
    localVideoAudition: {
      notice: '你正在上旁聽課'
    }
  },
  // 金币显示
  coins: {
    title: '我的金幣'
  },
  // 举手
  raiseHand: {
    buttonName: '舉手'
  },
  // 回放
  playback: {
    notice: '觀看回放'
  }
}
