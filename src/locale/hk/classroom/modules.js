/**
 * 模块配置
 */
export default {
  // 导航
  header: {
    // 返回确认窗口
    backConfirm: {
      content: `確認退出直播教室嗎？`,
      exitByCoursewareIdChange: '課件異常，請退出后重新進入教室'
    }
  },
  // 反馈
  feedback: {
    headerName: '問題描述',
    optionNames: ['學習相關', 'APP相關', '不當的行為', '其它'],
    placeholder: `1.請選擇對應的反饋類型標籤
2.請詳細描述你的問題以便老師能快速解決問題`,
    screenshotTips: '我同意將當前屏幕截圖發送給教學團隊',
    sendSuccessNotice: '謝謝你的反饋，我們將很快與你或者你的父母取得聯繫',
    defaultMsg: '發送反饋',
    playback: {
      header: '問題反饋',
      placeholder: '請在此描述你遇到的問題',
      screenshotTips: '我同意提交當前設備信息和屏幕截圖給技術支持團隊',
      cancel: '取消',
      send: '發送',
      success: '提交成功',
      failed: '提交失敗'
    }
  },
  // 设备检测
  deviceTest: {
    dialogTitle: '設備檢測',
    statusNames: {
      disabled: '禁用',
      usable: '啟用',
      unauthorized: ' 未授權'
    },
    cameraTitle: '攝像頭檢測: ',
    microphoneTitle: '麥克風檢測: ',
    audioTitle: '音頻檢測: ',
    authorizeGuide: ['去授權, ', '點擊鏈接']
  },
  checkDevice: {
    network: '網絡',
    microphone: '麥克風',
    camera: '攝像頭',
    voice: '揚聲器',
    网络满足上课需求: '網絡滿足上課需求',
    网络不满足上课需求: '網絡不滿足上課需求',
    你的设备满足上课需求: '當前設備符合上課要求',
    你的设备不满足上课需求: '您的網絡似乎存在問題， 點擊這裡查看解決建議',
    音视频测试: '音視頻服務',
    课堂互动测试: '課堂互動服務',
    服务器检测: '服務器連接速度測試',
    课件下载测试: '課件下載測試',
    此项测试大约需要花费一分钟: '此過程大概花費1~3分鐘',
    常见网络问题建议: '常見網絡問題解決建議',
    尝试重启路由器: '如果你使用Wi-Fi無線網絡，請嘗試距離路由器更近一點以及嘗試重新啟動路由器',
    检查网络权限: '請檢查並確保您的設備允許我們訪問網絡',
    切换其它网络: '嘗試切換使用其它的網絡',
    重新测试: '重新測試',
    下一项: '下一項',
    没有麦克风权限: 'Think Academy無法使用你的麥克風',
    没有找到可用的麦克风: '沒有找到可用的麥克風',
    请检查你的麦克风或者佩戴耳机: '請檢查你的麥克風設置或者嘗試佩戴耳機',
    尝试你大声读出下面的文字: '嘗試大聲讀出下面的文字',
    你好: '你好',
    你的麦克风似乎存在问题: '你的麥克風似乎存在問題',
    我们没有听到你的声音: '我們沒有聽到你的聲音，請嘗試更換設備後或者佩戴耳機後再次嘗試',
    麦克风检测完成: '麥克風檢測完成',
    你的麦克风工作正常: '你的麥克風工作正常',
    去设置: '去設置',
    没有摄像头权限: 'Think Academy無法使用你的攝像頭',
    在设置中打开麦克风权限: '請在系統設置中打開Think Academy的麥克風使用權限',
    在设置中打开摄像头权限: '請在系統設置中打開Think Academy的攝像頭使用權限',
    没有找到可用的摄像头: '沒有可用的攝像頭設備',
    请检查你的摄像头: '請檢查您的攝像頭是否正確安裝或者使用其它設備上課',
    可以在上面看到自己吗: '可以在上面的視頻框中看到自己嗎？',
    正在使用摄像头: '當前正在使用攝像頭，工作正常',
    你的摄像头似乎存在问题: '你的攝像頭似乎存在問題',
    请检查你的摄像头是否正确安装: '請檢查您的攝像頭是否正確安裝或者使用其它設備上課',
    摄像头检测完成: '攝像頭檢測完成',
    你的摄像头工作正常: '你的攝像頭工作正常',
    当前音量: '當前音量：',
    可以听到正在播放的音乐吗: '可以聽到正在播放的音樂嗎？',
    请注意调整系统音量到适当大小: '請注意調整系統音量到適當大小',
    能: '能',
    不能: '不能',
    你的扬声器似乎存在问题: '你的揚聲器似乎存在問題',
    建议尝试佩戴耳机或者更换其它设备: '建議嘗試佩戴耳機或者更換其它設備上課',
    你的设备满足上课要求: '你的設備滿足上課要求',
    你的设备不完全满足上课要求: '你的設備不完全滿足上課要求',
    继续使用此设备上课可能会降低你的体验: '繼續在此設備上課可能會降低您的上課體驗',
    知道了: '知道了',
    你的网络是乎有些问题: '你的網絡是乎有些問題，點擊這裡查看',
    解决方案: '解決方案'
  },
  // 网络状态提示
  networkStatus: {
    statusMap: {
      good: {
        title: '網絡連接良好'
      },
      normal: {
        title: '網絡連接一般'
      },
      weak: {
        title: '網絡連接較差',
        description: '正在嘗試重新連接，你也可以嘗試切換其它更好的網絡或者設備'
      }
    }
  },
  // 网络错误提示
  networkError: {
    notice: ['沒有網絡', '請檢查網絡連接，或者等待重新連接']
  },
  // 课件组件
  courseware: {
    errorNotice: ['課件加載失敗', '課件加載失敗', '點擊『重新加載』再試一次']
  },
  // 截屏
  screenThumbnail: {
    successNotice: '已保存到桌面'
  },
  // 媒体权限
  mediaSecurityAccess: {
    camera: `開啟攝像頭權限才能參與課中互動，請確認開啟`,
    microphone: `請允許ThinkAcademy訪問你的麥克風`,
    deniedAccess: '開啟攝像頭和麥克風權限才能參與課中互動，請確認開啟',
    tip: '開啟權限後，請重新啟動應用',
    confirm: '同意',
    cancel: '拒絕'
  },
  // 踢出
  liveKickout: {
    notice: '你的賬號已在其他設備登入，請重新登入'
  },
  // 下课提示
  classEnded: {
    title: '課程已結束',
    content: '本節課程已經結束，請返回首頁',
    okText: '確定'
  },
  // 定向金币
  orientationCoins: {
    notice: ['這些學員獲得了', '金幣！'],
    Congrats: '獲得金幣',
    others: '和其它同學',
    smallCongrats: '非常棒！'
  },
  // 作业盒子
  assignmentBox: {
    modalTitle: 'Basic Modal',
    nodataNotice: '暫無課中練習',
    checkBigPictures: {
      buttonName: '訂正'
    },
    checkedExerciseToast: {
      notice: '輔導老師已經批改了你的練習，快來看看吧',
      buttonName: '查看練習',
      loadingName: '圖片加載中',
      pictureDesc: '{assisTeacher}已經批改了你的練習，快來看看吧'
    },
    checkErrorTips: '好遺憾，繼續努力'
  },
  // 小班涂鸦板
  smallClassGraffitiCorrect: {
    yellow: '黃色',
    red: '紅色',
    blue: '藍色'
  },
  // 连续作答
  continuousCorrect: {
    rightLevelDesc: '再答對一題後勛章可升級為{nextTitle}',
    rightTopLevelDesc: '你已經獲得本節課最高等級勛章啦',
    wrongLevelDesc: '再答對一題後勛章可升級為{nextTitle}',
    encourageNotice: '金幣獎勵',
    noanswerNotice: '參與課中互動題來升級你的勛章吧',
    continuousText: '回答正確，非常棒！',
    highestLevelText: '你的徽章已經到達最高等級！',
    upgradedLevelText: '你的徽章已升級到Lv {curLevel}',
    wrongTitleText: '哎呀失誤了',
    wrongDescText: '下次加油哦~'
  },
  // 辅导连麦
  tutorVideoLink: {
    title: '輔導私聊',
    errorMsg: ['', '出了點小問題']
  },
  // 系统出错
  systemError: {
    message: '系統錯誤，請重試',
    noPlaybackMessage: '視頻回放未生成',
    refreshButtonName: '刷新',
    backButtonName: '返回到課程'
  },
  // 老师上台
  teacherOnStage: {
    notice: ['額，老師待會回來呦', ''],
    privateChat: '視頻私聊'
  },
  // 课中考试
  ClassExam: {
    rankHeaderTitle: '考試排行榜'
  }
}
