import common from './common'
import login from './login'
import courses from './courses'
import setting from './setting'
import account from './account'
import classroom from './classroom'
import today from './today'
import views from './views'
import growthHandbook from './growthHandbook.js'
import blindBox from './blindBox.js'
import myBackpack from './myBackpack.js'

export default {
  common,
  login,
  setting,
  courses,
  account,
  classroom,
  today,
  growthHandbook,
  blindBox,
  myBackpack,
  ...views,
  liveroomMicFirstTipAlertTitlePC: '按住空格鍵或者麥克風進行發言',
  liveroomMicFirstTipAlertDoneTitle: '我知道了',
  liveroomMicSpeakingHUDTitle: '正在發言',
  liveroomKeepMuteModeToast: '老師已關閉所有人的麥克風',
  liveroomPushToTalkModeToast: '老師已開啟所有人的麥克風',
  liveroomKeepMicOnModeToast: '老師已將麥克風設置為按住發言模式',
  老师已将麦克风设置为自由发言模式: '老師已將麥克風設置為自由發言模式',
  liveroomHoldDownMicBtnWhenSpeakingToastForPC: '發言時請按住空格鍵或麥克風按钮',
  liveroomDisableOthersVideoMenu: '關閉他人視訊',
  磁盘可用空间不足: '磁盤可用空間不足，請清理磁盤'
}
