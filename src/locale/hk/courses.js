// 课程中心
export default {
  // 课程列表页
  list: {
    title: '我的課程',
    titleTip: '您當前是在火星分校',
    tabList: {
      all: '全部',
      current: '上課中',
      completed: '已結課'
    },
    courseTabList: {
      live: '直播課',
      record: '錄播課'
    },
    noCourses: '還沒有課程 / 此類別下沒有課程',
    switchSingleAccount: '你的子帳號"{account}"下有課程，可點擊切換',
    switchMultipleAccount: '你在其他多個子帳號裏有課程，可點擊切換',
    switchMultipleSchool: '你在其他多個分校裏有課程，可點擊切換',
    switchSingleSchool: '你的分校"{school}"下有課程，可點擊切換',
    switchAccountText: '切換學員',
    switchSchoolText: '切換分校',
    planName: '課次'
  },
  lessonType: {
    formal: '正式課',
    playback: '回放課',
    audition: '旁聽課',
    temporary: '臨時課'
  },
  // 课程卡片
  courseCard: {
    classInSession: '上課中',
    nextSession: '下一節課',
    enter: '進入教室',
    completed: '已完成',
    refunded: '已退費',
    playback: '回放',
    startAuditing: '去旁聽',
    view: '查看',
    reportTips: '“{course}”的課後報告已生成。',
    parentAudit: '家長旁聽',
    alreadyStarted: `課次 {courseNum} 已開始`,
    nextLesson: '下一課次'
  },
  // 录播课
  recordCourse: {
    courseValidUntil: '此課程有效期至：',
    enter: '進入',
    refunded: '已退費',
    expired: '已過期',
    expiredTip: '此課程已失效',
    permanentTip: '此課程永久有效',
    goToTest: '去作答',
    report: '考試報告',
    reload: '重新加載',
    loadFailed: '加載失敗',
    loading: '加載中',
    saveShotScreen: '截图已保存',
    switchChannel: '切換線路',
    playNextVideo: '播放下一視頻'
  },
  // 录播课播放器提示
  recordPlayerTips: {
    0: '播放',
    '0-1': '暫停',
    1: '快進10秒',
    2: '快退10秒',
    9: '截圖'
  },
  // 课程详情页
  detail: {
    title: '課程詳情',
    specialTitle: '{prefix}詳情',
    // 播放按钮
    playButton: {
      // 未开始状态
      startSoon: '暫未開始',
      playbackGenerating: '回放生成中',
      playback: '看回放',
      playbackExpired: '回放已過期',
      unpaid: '未購買',
      live: '進入教室',
      finished: '已結束',
      startAuditing: '去旁聽'
    },
    classRoom: '教室',
    homework: {
      status: ['待發布', '待提交', '已批改', '已提交', '已過期', '查看答案', '待提交'],
      title: '作業'
    },
    exam: {
      status: ['未發佈', '去考試', '已作答', '已批改', '已過期']
    },
    examTip: '考試尚未發佈哦',
    resources: '資料',
    playBackTip: '直播課結束2小時後可播放',
    reportBtnText: '已批改',
    reportSubmitText: '已提交',
    reportExpiredText: '已過期',
    startTest: '去考試',
    reportTips: `{canViewTime}之後可查看報告`,
    lessonReport: '課堂報告',
    expiredTips: '超過作業提交的截止時間了，下次要準時提交作業呀',
    file: '文件',
    files: '文件',
    transferred: '調課',
    classNotes: '板書',
    learningMaterials: '學習資料'
  },
  switchAccount: {
    title: '切換學員',
    subTitle: '你的以下子帳號下有課程，可點擊切換',
    cancel: '取消',
    confirm: '確定',
    switchSuccess: '切換成功',
    switchFail: '切換失敗，請稍後再試',
    switchIng: '切換中......',
    emptyTip: '請選擇一個學生賬號',
    loginTitle: '此家庭組內有多個營員',
    loginSubTitle: '請選擇你想登入哪個營員？'
  },
  switchSchool: {
    title: '切換分校',
    subTitle: '你的以下分校有課程，可點擊切換'
  },
  // 面授课 - 如何上课
  faceToFace: {
    attendClass: '查看上課方式',
    finished: '已結束',
    location: '地點',
    copy: '復製',
    zoomId: 'Zoom ID',
    zoomPassword: 'Zoom 密碼',
    zoomDownload: '下載Zoom',
    zoomDescribe: `並如上所述輸入Zoom ID和密碼。在Zoom現場會議前10分鐘進入課堂，盡情思考學院經驗。`,
    classinLink: 'Classin 鏈接',
    classinDownload: '下載Classin',
    classinDescribe: `並在註冊課程時使用您的帳戶註冊。在Classin現場會議前10分鐘進入課堂，享受你的Think Academy體驗。`,
    classroomDescribe: `請提前10分鐘到達教室，享受您的Think Academy體驗。`,
    ipadDescribe: `請使用iPad應用程序Thinkhub上課。如果您有任何問題，請致電`,
    confirm: '知道了'
  },
  // 课后作业
  assignment: {
    title: '作業',
    earnedNotice: '你獲得了',
    submissionNotice: '提前完成，額外獎勵'
  },
  // 课前测
  previewQuestion: {
    title: '課前測',
    deadlineName: '截止日期',
    deadlineTime: '截止時間',
    answerTimeName: '作答時間',
    deadlineBannerTips: '超過作答截止時間了，下次要準時作答哦！',
    problemDescriptors: [`題目描述`, `（共 {totalScore}分）`],
    confirmTitle: '確定開始作答？',
    confirmSubtitle: `作答時間共{duration}，加油！`,
    solutions: '解析',
    start: '開始作答',
    deadlineTitle: '測試已結束，未按時參加',
    deadlineDesc: '哎呀，超過作答截止時間了，下次要準時作答哦'
  },
  // 学习资料
  studyResources: {
    title: '學習資料',
    download: '下載',
    play: '播放'
  },
  // 课堂报告
  lessonReport: {
    title: '課堂報告'
  },
  // 阶段考试
  exam: {
    title: '考試須知',
    solutions: '查看題目解析',
    report: '查看報告',
    start: '開始作答',
    coinsTip: '作答完成有金幣獎勵哦',
    confirmTitle: '作答提醒',
    confirmSubtitle: `作答時間共{duration}，加油哦！`,
    confirmBtn: '確定',
    cancelBtn: '取消'
  },
  // 临时教室
  temporaryClassroom: {
    title: '臨時教室',
    noCourses: '暫無課程',
    registrationCourses: '報名課程',
    noCoursewareNotice: '本課程未綁定課件',
    statusName: {
      startSoon: '暫未開始',
      playbackGenerating: '回放生成中',
      playback: '看回放',
      playbackExpired: '回放已過期',
      enter: '進入教室'
    }
  },
  confirmModal: {
    title: '進課提醒',
    content: '當前賬號對應學生已在直播課程中，若選擇進課，則會{warning}請選擇是否繼續進課？',
    warning: '將現存賬號踢出直播課程，',
    enterBtn: '確認',
    backBtn: '取消',
    confirm: '確認'
  },
  switchSchoolModal: {
    title: '提醒',
    content: '您是否切換為此分校？'
  },
  resolutionRatioModal: {
    title: '提醒',
    content:
      '您當前的熒幕分辨率低於ThinkAcademy需要的最低分辨率，\n請在“系統設置 - 顯示”中降低顯示縮放比。',
    confirm: '知道了'
  },
  playback: {
    courseInformationError: '課程信息獲取失敗',
    playbackInformationError: '回放信息獲取失敗',
    playbackInterfaceError: '回放接口訪問錯誤',
    playbackSourceIsNull: '回放地址為空',
    playbackStampError: '打點信息獲取失敗'
  },
  noCoursesWare: '當前課次沒有課件',
  teacherContact: {
    tip: '如您有任何問題，請聯系您的老師',
    copy: '複製',
    addContact: '請掃描二維碼添加老師微信'
  },
  // 课堂评价
  nps: {
    feedback: '課程評價',
    enjoyTip: '這節課學的開心嗎？',
    contactTip: '告訴我們原因幫助 Think Academy做的更好',
    reason: '可以在這裡寫下詳細原因',
    submitBtn: '提交',
    submitSuccess: '提交成功，感謝你的反饋'
  },
  expand_fold: '收起',
  expand_more: '更多',
}
