export default {
  // 积分商城
  coinsMall: {
    title: '積分商城'
  },
  // 设备拦截
  deviceIntercept: {
    interceptInfo: {
      title: '不兼容此設備',
      content: `抱歉，暫不支持當前設備，請在符合最低運行要求的設備上運行Think Academy應用`
    },
    recommendInfo: {
      title: '不建議使用此設備',
      content: `抱歉，您當前的設備可能無法流暢運行Think Academy應用，請在符合最低運行要求的設備上運行Think Academy應用`
    },
    deviceInfo: {
      compatibility: '操作系統',
      macTips: 'MacOS 10.12或者更高版本',
      windowsTips: 'Windows 7或者更高版本',
      configurationsTitle: '最低硬件配置',
      configurationsInfo: '4核心CPU，4GB運行內存',
      deviceTitle: '設備',
      notAvailableDevice: '不支持ChromeBook和Surface Book系列筆記本電腦'
    }
  },
  // 课前准备页
  prepareClass: {
    great: '正常',
    gotIt: '收下',
    title: `課前準備`,
    audioAccess: '麥克風權限未打開',
    audioAvailable: '沒有可用的麥克風',
    cameraAccess: '攝像頭權限未打開',
    cameraAvailable: '沒有可用的攝像頭',
    audioAccessWindow: '未開啟麥克風權限',
    audioAccessContent: '請在系統設置中打開Think Academy的麥克風權限，否則老師將無法聽到你的發言。',
    cameraAccessWindow: '未開啟攝像頭權限',
    cameraAccessContent: '請在系統設置中打開Think Academy的攝像頭權限，否則老師將無法看到你。',
    networkWeek: '網絡連接較差',
    networkGood: '網絡良好',
    networkGoodContent: '您的網絡連接良好，可以流暢使用Think Academy課堂。',
    networkWeekContent:
      '網絡不佳可能會導致您上課時出現卡頓，請嘗試切換至其它網絡或者如果您使用Wi-Fi網絡接入，請嘗試重新啟動路由器並靠近您的無線路由器以改善網絡連接質量。',
    retestNetwork: '重新測試',
    testSpeed: '測試網絡速度',
    enterName: '輸入您的暱稱',
    setNickName: '設置暱稱',
    setNickNameTip: '不超過 65 個字符，不能包含特殊字符',
    setNickNameError: '昵稱修改失敗，請重試',
    enterBtn: '輸入名稱',
    hasNotEnter: `您還沒有輸入昵稱`,
    enterClassroom: '進入教室',
    enterClassroomError: '進入教室失敗，請重試',
    editNickname: '編輯昵稱',
    validateInput: `輸入無效`,
    done: '已下載',
    redownload: '重試下載',
    coursewareDownload: '課件下載完成',
    onlineCourseware: '使用在線課件',
    onlineCoursewareContent: '僅當無法下載離線課件時可嘗試使用在線課件，但此方式並不可靠。',
    confirmUser: '確認使用',
    coursewareDownloading: '正在下載課件，{speed}，剩餘{minutes}',
    coursewareDownloadingSlow: '時間大于60分钟',
    coursewareDownloadWeek: '課件下載失敗，請重試',
    coursewareDownloadWeekTitle: '課件下載失敗',
    coursewareDownloadWeekContent: '請檢查您的網絡並嘗試重新下載',
    playbackError: '該課程沒有回放或者回放還未生成',
    minute: '分鐘',
    second: '秒',
    waiting: '請等待課件下載完成',
    networkBtn: {
      good: '良好',
      normal: '一般',
      retest: '重試網絡',
      weak: '差'
    },
    startAgo: '課程已開始{changeMinutes}分鐘，快進入教室吧',
    willStart: ' {changeMinutes}分鐘後開始上課',
    missClass: '如果你錯過了現場課程，請觀看回放',
    joinClass: '上課',
    continue: '繼續',
    confirm: '確定',
    downloadTip: {
      coursewareDownloading: '課件下載',
      downloadReson: '提前下載課件可以節省課堂時間',
      incomplete: ' 課件下載不完整',
      downloadComplete: '下載完成',
      DownloadFailed: '下載失敗'
      // subtitle: '提前下載課件可以節省課堂時間'
    },
    checkIn: {
      onTime: '準時到達教室',
      title: '簽到',
      missTips: ['課程已開始，', `無法獲得簽到獎勵啦:(`],
      firstCheckInMessage: '請先簽到',
      checkInButtonName: '簽到',
      checkInSuccess: '簽到成功',
      checkInFail: '簽到失敗'
    }
  }
}
