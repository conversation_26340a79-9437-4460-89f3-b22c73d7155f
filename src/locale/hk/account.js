// 账户相关
export default {
  // 菜单
  menus: {
    editProfile: '編輯個人信息',
    myAccount: '我的賬號',
    temporaryClassroom: '臨時教室',
    accountInfo: '賬號信息'
  },
  // 个人信息
  personalInformation: {
    title: '個人信息',
    displayName: '暱稱',
    displayNamePlaceholder: '請輸入暱稱',
    displayNameUse: '不超過 65 個字符，不能包含特殊字符',
    studentFirstName: '名字',
    studentLastName: '姓氏',
    enterFirstName: '請填寫中文名字',
    enterLastName: '請填寫中文姓氏',
    select: '選擇年級',
    email: '郵箱',
    enterEmail: '編輯郵箱',
    backToCourses: '回到我的課程',
    save: '保存',
    gradeTitle: '年級',
    uploadAvatarTitle: '上傳頭像',
    line: 'Line',
    weChat: '微信',
    whatsApp: 'WhatsApp',
    enterLine: '編輯Line',
    enterWeChat: '編輯微信',
    enterWhatsApp: '編輯WhatsApp',
    description: '微信/WhatsApp/Line將用於教師與家長溝通學生的學習情况。'
  },
  validateInput: {
    inputRequire: '此輸入為必填項',
    maxLength: '最大支持32個字符',
    invalid: '輸入不符合要求'
  },
  // 修改密码
  modifyPassword: {
    noEmailOrPhoneLinked: '未綁定注册手機號和注册郵箱，暫時無法修改密碼',
    changePasswordTips:
      '更改密碼後，所有其他已登錄的設備都將登出。因此，請確保您的孩子目前不在直播課堂。',
    cancel: '取消',
    confirmChange: '修改密碼',
    confirm: '確認',
    title: ' 密碼設置',
    subTitle: ' 設置密碼後，可使用[手機號+密碼]或[郵箱+密碼]或[學員編號+密碼]的方式登入',
    modifyText: [
      '此密碼為您家庭中的所有學生帳戶共享。',
      `請輸入6-20個字符，至少包含一個字母和一個數字。僅包含字母和數字
      數字是可以接受的。`
    ],
    placeholder: ['新密碼', '確認密碼'],
    lengthError: '密碼必須爲6-20個字符之間',
    notMatch: '密碼不匹配',
    notFormat: '密碼格式錯誤',
    successMessage: '密碼設置成功',
    failMessage: '密碼設置失敗'
  },
  accountVerification: {
    title: '帳戶驗證',
    subTitle: '請確認您的身份以保護您的帳戶安全',
    nextStep: '下一步',
    confirmPolicy: '請勾選同意下面的使用協議和私隱保護政策才能繼續登錄',
    clickToResetPw: ['點擊此處', `重新設置您的登錄密碼`],
    copy: '複製',
    current: 'Current',
    addStudent: '添加一名學生',
    otherStudents: '其他學生',
    note: '最多可關聯6名學生',
    maxAccount: '已滿6個賬號',
    switchedSuccessfully: '切换成功',
    switchedFailed: '切换失敗',
    copiedFailed: '複製失敗',
    copiedSuccessfully: '複製成功',
    backToAccount: '回到我的賬戶',
    done: '完成',
    noAccountAssociated: '沒有關聯的賬戶',
    newPassword: '新密碼',
    confirmPassword: '確認密碼',
    validationNotices: [
      '請輸入您的帳戶信息',
      '請輸入您的手机號碼',
      '手机號碼不正確',
      '請輸入短信驗證碼'
    ],
    validationEmailNotices: [
      '請輸入您的帳戶信息',
      '請輸入您的電郵箱地址',
      '郵箱地址不正確',
      '請輸入驗證碼'
    ],
    sendFailedTryAgain: '發送失敗, 請重試'
  },
  myAccount: {
    title: '我的賬號',
    addFailedMessage: '添加學生失敗，請重試！',
    addSuccessedMessage: '添加學生成功'
  },
  accountInfo: {
    title: '賬號信息',
    tip: '昵稱會用於您在課堂的名稱展示、評論等',
    注册信息: '註冊信息',
    手机号: '手機號',
    邮箱: '郵箱',
    已绑定: '已綁定',
    未绑定: '未綁定',
    你即将离开当前页面: '你即將離開當前頁面，並跳轉到第三方頁面',
    下一步: '下一步',
    取消: '取消',
    解绑X账号: '解绑 {type} 賬號',
    解绑后: '解绑後，您將不能使用 {type} 進行登錄',
    账号绑定成功: '賬號綁定成功'
  },
  change: {
    title: '變更手機號',
    账号安全: '賬號安全：驗證您的身份信息',
    请输入验证码进行验证: '請輸入驗證碼進行驗證 {phone}',
    绑定手机号: '綁定手機號',
    请输入变更后的手机号: '請輸入變更後的手機號',
    绑定邮箱: '綁定郵箱',
    请输入变更后的邮箱: '請輸入變更後的郵箱'
  }
}
