// 我的背包
export default {
  backpack: '我的背包',
  effect: '形象',
  decoration: '裝飾',
  suit: '套裝',
  head: '頭部',
  face: '臉部',
  hand: '手部',
  avatarFrame: '頭像框',
  messageBox: '消息氣泡',
  stageFrame: '上臺視頻框',
  suitEmptyTips: '集齊的形象套裝在這裏展示哦',
  emptyTips: '當前分類下還沒有物品，快去抽盒吧',
  openCamera: '打開攝像頭',
  openCameraTips: '打開攝像頭預覽形象效果哦',
  selectedDefaultTips: '選擇物品即可預覽',
  selectedGoodsTips: '打開攝像頭才能預覽形象效果哦',
  effectSceneTips: '當老師在課堂中開啟「允許展示形象」時，就可以在視頻框看到你的形象啦',
  avatarFrameSceneTips: '選擇一個頭像框，你的頭像都會佩戴上哦',
  messageBoxSceneTips: '課堂中，在聊天區發言就可以看到你獨特的消息氣泡咯',
  stageFrameSceneTips: '課堂中，上臺發言時就可以看到你獨特的上臺視頻框咯',
  requireCamera: 'Think Academy Classroom請求獲取攝像頭權限',
  requireCameraDesc: '打開攝像頭即可預覽形象貼紙效果',
  allow: '好的',
  notAllow: '不允許',
  previewTitle: '獎品預覽',
  previewEffectTips: '你的形象效果會在課堂視頻區展示，同學們都可以看到哦',
  previewAvatarFrameTips: '頭像框會佩戴在你的頭像上哦',
  previewMessageBoxTips: '消息氣泡會在課堂聊天區展示，同學們都可以看到哦',
  previewStageFrameTips: '上臺視頻框會在你課堂上臺時展示，同學們都可以看到哦',
  legendary: '傳奇',
  epic: '史詩',
  rare: '稀有',
  uncommon: '高級',
  common: '基礎',
  ruleTitle: '說明',
  rules1:
    '1. 背包內的虛擬物品僅支持在線小班課堂內展示，在在線大班課堂、面授課堂、旁聽課等課堂內暫無法展示。',
  rules2: '2. 在在線小班課堂中，主講老師可控製是否允許課堂內學生展示其形象（含頭飾、面飾）。',
  rules3:
    '3. 當主講老師在教師端打開「允許展示學生虛擬形象」開關時，同學們就可以看到你的虛擬形象貼紙了。',
  rules4: '4. 裝飾類物品及部分形象類物品（如舉手動效），主講老師不作限製，佩戴上即可在課堂中展示。',
  gotIt: '我知道了',
  widdowRequireCamera: '請前往「設置」允許Think Academy訪問攝像頭'
}
