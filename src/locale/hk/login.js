// 登录页
export default {
  // 通用
  common: {
    email: '郵箱',
    emailAddress: '郵箱地址',
    phoneNumber: '手机號碼',
    phoneNumberOrEmail: '手機號或郵箱',
    verificationCodePlaceholder: '請輸入驗證碼',
    passwordPlaceholder: '請輸入您的密碼',
    studentId: '學員編號',
    signIn: '登錄',
    inputInvalid: '輸入無效',
    incorrectId: '學員編號或密碼不正確，請重試',
    loginAgain: '重新登錄',
    emailReg: '請輸入有效的郵箱地址',
    continue: '繼續使用',
    是否还没有账号: '是否還沒有賬號？',
    立即注册: '立即註冊',
    已经注册过账号: '已經註冊過賬號？',
    立即登录: '立即登錄',
    注册: '註冊',
    我们向您发送了一个验证码: '我們向您發送了一個驗證碼',
    请在下方输入以进行验证: '請在下方輸入以進行驗證 {phone}',
    您似乎还未注册: '您似乎還未註冊？',
    您已经注册过账号: '您已經註冊過賬號？',
    切换其他登录方式: '切換其他登錄方式',
    找到X个可能是您的账号: '找到{num}個可能是您的賬號',
    如果是您的账号: '如果是您的賬號，建議您可以直接登錄，若不是則創建新的賬號',
    已存在的账号: '已存在的賬號',
    继续创建新账号: '繼續創建新賬號',
    您是否要更新现有账户: '您是否要更新現有賬戶？',
    将此注册信息更新到现有账户: '將此註冊信息更新到現有賬戶',
    账号更新成功: '賬號更新成功',
    手机: '手機',
    邮箱: '郵箱'
  },
  // 验证码登录页
  verificationCodePage: {
    title: '驗證碼登入',
    description: `未註冊的手機號在登入後將自動為您創建賬號。`,
    emailDesc: '若您沒有註冊郵箱，會在登入後自動為您創建帳號',
    footerTitle: '驗證碼登入',
    skip: '跳過',
    send: '發送',
    reSend: '重新發送',
    back: '返回',
    verificationTips: {
      confirmPolicy: '請勾選同意下面的用户協議、私隱政策和兒童私隱政策才能繼續',
      enterAccount: '請輸入你的賬號',
      enterPhoneNumber: '請輸入你的手機號碼',
      phoneNumberIncorrect: '手機號碼不正確',
      enterVerificationCode: '請輸入短信驗證碼'
    }
  },
  // 密码登录页
  passwordPage: {
    title: '密碼登入',
    mobileDescription: `如果您已為帳戶設置了密碼，則可以使用密碼登入。`,
    studentIdDescription: `登入後，您可以在“我的帳戶”中找到學員編號。`,
    footerTitle: '密碼登入',
    forgotPassword: '忘記密碼？',
    confirmPolicyTip: '請勾選同意下面的用户協議、私隱政策和兒童私隱政策才能繼續',
    otherLoginType: '或使用其他方式密碼登入'
  },
  // 协议信息
  policyInfo: {
    desc: '請先勾選同意我們的',
    and: '和'
  },
  // 踢出提示
  kickoutNotice: {
    message: '為了您的賬戶安全，請重新登錄'
  }
}
