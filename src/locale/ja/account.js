// 账户相关
export default {
  // 菜单
  menus: {
    editProfile: '個人情報編集',
    myAccount: 'アカウント',
    temporaryClassroom: '臨時教室',
    accountInfo: 'アカウント情報'
  },
  // 个人信息
  personalInformation: {
    title: '個人情報',
    displayName: 'ニックネーム',
    displayNamePlaceholder: 'ニックネームを入力してください',
    displayNameUse: '65文字以内、特殊文字は不可',
    studentFirstName: '名前',
    studentLastName: '苗字',
    enterFirstName: '中国語の名前を入力してください。',
    enterLastName: '中国語の苗字を入力してください。',
    select: '学年を選択してください',
    email: 'メール',
    enterEmail: 'メールアドレスを入力してください',
    backToCourses: '私のコースに戻る',
    save: '保存',
    gradeTitle: '学年',
    uploadAvatarTitle: 'プロフィール写真のアップロード',
    line: 'LINE',
    weChat: 'WeChat（微信）',
    whatsApp: 'WhatsApp（ワッツアップ）',
    enterLine: 'LINEアカウントを入力してください',
    enterWeChat: 'WeChat（微信）アカウントを入力してください',
    enterWhatsApp: 'WhatsApp（ワッツアップ）アカウントを入力してください',
    description:
      'WeChat/WhatsApp/Lineは生徒の学習状況について教師が保護者とコミュニケーションを取るに使用されます。'
  },
  validateInput: {
    inputRequire: '必須項目です',
    maxLength: '最大32文字まで入力',
    invalid: 'ご入力は無効であった'
  },
  // 修改密码
  modifyPassword: {
    noEmailOrPhoneLinked:
      '携帯電話番号とメールアドレスがバインドされていないため、現時点ではパスワードを変更できません。',
    changePasswordTips:
      'パスワードを変更すると、ほかのお使いのデバイスがすべてログアウトされるため、お子さんが現在ライブクラスにいないことを確認してください。',
    cancel: 'キャンセル',
    confirmChange: 'パスワード変更',
    confirm: 'OK',
    title: ' パスワード設定',
    subTitle:
      ' パスワード設定後、[携帯電話番号＋パスワード]、[メールアドレス＋パスワード]、[学籍番号＋パスワード]でログインできます。',
    modifyText: [
      'このパスワードは、ご家族内のすべての学生アカウントで共有されます。',
      `半角英数字の組合せ（6～20文字）で入力してください。 半角英数字のみ
      数字が使用可能です。
      `
    ],
    placeholder: ['新しいパスワード', '確定'],
    lengthError: '＊6～20文字で',
    notMatch: 'パスワードが一致しません',
    notFormat: 'パスワードの形式は正しくありません',
    successMessage: 'パスワードの設定が完了しました',
    failMessage: 'パスワードの設定に失敗しました'
  },
  accountVerification: {
    title: 'アカウントの確認',
    subTitle: 'セキュリティ保護のため、ご本人であることをご確認ください',
    nextStep: '次へ',
    confirmPolicy:
      'ログインを続けるには、ご利用規約およびプライバシーポリシーに同意していただく必要があります',
    clickToResetPw: ['こちらをクリックしてください', `パスワードを再設定してください`],
    copy: 'コピー',
    current: 'Current',
    addStudent: '学生を追加する',
    otherStudents: 'ほかの学生',
    note: 'アカウントを最大で5つまで追加することができます',
    maxAccount: 'アカウント数が6つに達しています',
    switchedSuccessfully: 'アカウントを切り替えました',
    switchedFailed: 'アカウントの切り替えができませんでした',
    copiedFailed: 'コピーに失敗しました',
    copiedSuccessfully: 'コピーしました',
    backToAccount: 'マイアカウントに戻る',
    done: '完成',
    noAccountAssociated: 'リンクアカウントがありません',
    newPassword: '新しいパスワード',
    confirmPassword: '確定',
    validationNotices: [
      'アカウント情報を入力してください',
      '携帯番号を入力してください',
      '携帯番号が正しくありません',
      'SMS検証コードを入力してください'
    ],
    validationEmailNotices: [
      'アカウント情報を入力してください',
      'メールアドレスを入力してください',
      'メールアドレスが正しくありません',
      'SMS検証コードを入力してください'
    ],
    sendFailedTryAgain: '失敗しました。 やり直してください。'
  },
  myAccount: {
    title: 'アカウント',
    addFailedMessage: '学生の追加に失敗しました。やり直してください。',
    addSuccessedMessage: '学生を追加しました。'
  },
  accountInfo: {
    title: 'アカウント情報',
    tip: 'ニックネームは授業中やコメントなどに表示されます',
    注册信息: '登録情報',
    手机号: '携帯',
    邮箱: 'メール',
    已绑定: '連携済み',
    未绑定: '連携していません',
    你即将离开当前页面: '今のページからほかのページに移動することになります',
    下一步: 'はい',
    取消: 'いいえ',
    解绑X账号: 'アカウント{type}の連携を解除する',
    解绑后: '解除後、アカウント{type}でログインできなくなります',
    账号绑定成功: 'アカウントの連携が完了しました'
  },
  change: {
    title: '携帯番号を変更する',
    账号安全: 'アカウントのセキュリティ：身分確認',
    请输入验证码进行验证: '{phone}を認証するために確認コードを入力してください',
    绑定手机号: '携帯番号と連携する',
    请输入变更后的手机号: '新しい携帯番号を入力してください',
    绑定邮箱: 'メールアドレスを紐付ける',
    请输入变更后的邮箱: '変更後のメールアドレスを入力してください'
  }
}
