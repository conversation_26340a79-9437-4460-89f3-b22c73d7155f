// 成长手册
export default {
  handBook: '成長ブック',
  guideTitle: '成長ブックとは？',
  gotIt: 'わかりました!',
  skip: 'スキップ',
  growthLevel: '成長ランク',
  growthTask: '成長任務',
  weekTask: '今週の任務',
  challengeTask: 'チャレンジ任務',
  activeTask: 'アクティブ任務',
  process: '進捗度',
  unfinished: '進行中',
  drawDown: '受け取る',
  claimed: '受け取り済',
  highestLevel: 'レベルマックスに達しました',
  needExp: '次のレベルまであと{exp}経験値が必要',
  tasksCompleted: '必要な任務数:{count}',
  tasksUncompleted: '未完成',
  congratulations: '以下のアイテムをゲットしました！',
  accept: '受け取る',
  startChallenge: 'チャレンジする',
  countDownTextForDay: '今週残り{days}日{hours}時間',
  countDownTextForHour: '今週残り{hours}時間',
  countDownTextForMinute: '今週残り{minutes}分',
  requestError: 'リクェストタイムアウト',
  backHome: 'ホームページに戻る',
  rewardCollected: '賞品受け取り済',
  systemError: 'システムエラー（エラーコード:{code}）',
  getExpDesc: '学習任務をクリアして経験値を積もう',
  upgradeDesc: 'ランクを上げて賞品を開放しましょう',
  toClassDesc: '授業へ',
  doHomeworkDesc: '宿題をやる',
  upgradeTitle: 'ランクアップ',
  initUpgradeTitle: '初期ランクが決まりました！',
  exp: '経験値',
  coin: 'コイン',
  retry: '再試行',
  networkErr: 'ネットワークエラーです。ネットに接続して再試行してください',
  摄像头初始化: 'カメラ初期化中です。3秒後に再試行してください。'
}
