/**
 * 模块配置
 */
export default {
  // 导航
  header: {
    // 返回确认窗口
    backConfirm: {
      content: `ライブ教室から退室しますか？`,
      exitByCoursewareIdChange: 'コースウェアエラー。退室してからもう一度入ってください'
    }
  },
  // 反馈
  feedback: {
    headerName: '問題説明',
    optionNames: ['学習関連', 'APP関連', '不正当な行為', 'その他'],
    placeholder: `1.対応するフィードバックタイプのタブをえらんでください
2.先生が速やかに解決できるように問題を詳しく説明してください`,
    screenshotTips: 'このスクリーンショットを教育研究チームに送ることに同意します',
    sendSuccessNotice:
      'フィードバックをいただきどうもありがとうございます。なるべく早くご連絡させていただきます',
    defaultMsg: 'フィードバックを送る',
    playback: {
      header: 'フィードバック',
      placeholder: 'ここで問題を説明してください',
      screenshotTips:
        'このデバイスの情報とスクリーンショットを技術サポートチームに送ることに同意します',
      cancel: 'キャンセル',
      send: '送る',
      success: '提出成功',
      failed: '提出失敗'
    }
  },
  // 设备检测
  deviceTest: {
    dialogTitle: 'デバイステスト',
    statusNames: {
      disabled: '禁止',
      usable: '起動',
      unauthorized: ' 未許可'
    },
    cameraTitle: 'カメラテスト: ',
    microphoneTitle: 'マイクテスト: ',
    audioTitle: '音声テスト: ',
    authorizeGuide: ['許可へ, ', 'リンクをタップする']
  },
  checkDevice: {
    network: 'ネットワーク',
    microphone: 'マイク',
    camera: 'カメラ',
    voice: 'スピーカー',
    网络满足上课需求: 'ネット環境が良いです',
    网络不满足上课需求: 'ネット環境が悪いです',
    你的设备满足上课需求: 'お使いのデバイスは授業の要件を満たしています',
    你的设备不满足上课需求: 'ネットの調子が悪いです｡タップしてソリューションを確認できます',
    音视频测试: 'オーディオ・ビデオサービス',
    课堂互动测试: '授業中やりとりサービス',
    服务器检测: 'ネットワーク回線速度テスト',
    课件下载测试: 'コースウェアダウンロードテスト',
    此项测试大约需要花费一分钟: '1～3分くらいかかります',
    常见网络问题建议: 'よくあるネットワーク接続問題の解決法',
    尝试重启路由器:
      'Wi-Fiをお使いの方は、ルーターをもう少し近くに置いて、ルーターを再起動してみてください',
    检查网络权限: 'デバイスのネットワーク権限をチェックして許可してください',
    切换其它网络: 'ほかのネットワークに接続してみてください',
    重新测试: '再テスト',
    下一项: '次の項目',
    没有麦克风权限: 'Think Academyによるマイクの利用ができません',
    没有找到可用的麦克风: '利用可能なマイクがありません',
    请检查你的麦克风或者佩戴耳机:
      'マイクの設定をチェックしてください。あるいはイヤホンをつけてみてください',
    尝试你大声读出下面的文字: '大声で以下のテキストを読み出してください',
    你好: 'こんにちは',
    你的麦克风似乎存在问题: 'マイクに問題があるようです',
    我们没有听到你的声音:
      '声が聞こえません。デバイスを変更するかイヤホンを付けてからもう一度やり直してください',
    麦克风检测完成: 'マイクテスト完了',
    你的麦克风工作正常: 'マイクが正常に動作しています',
    去设置: '設定へ',
    没有摄像头权限: 'Think Academyのカメラ権限が許可されていません',
    在设置中打开麦克风权限: '設定でThink Academyのマイク権限を許可してください',
    在设置中打开摄像头权限: '設定でThink Academyのカメラ権限を許可してください',
    没有找到可用的摄像头: '使えるカメラがありません',
    请检查你的摄像头:
      'カメラが動作しているかあるいはほかのデバイスで授業をするかをチェックしてください',
    可以在上面看到自己吗: 'ビデオフレームに自分がちゃんと映っていますか?',
    正在使用摄像头: 'カメラが正常に動作しています',
    你的摄像头似乎存在问题: 'カメラに異常があるようです',
    请检查你的摄像头是否正确安装:
      'カメラが動作しているかあるいはほかのデバイスで授業をするかをチェックしてください',
    摄像头检测完成: 'カメラチェック完了',
    你的摄像头工作正常: 'カメラが正常に動作しています',
    当前音量: '今の音量：',
    可以听到正在播放的音乐吗: '流れている音楽が聞こえますか？',
    请注意调整系统音量到适当大小:
      'システム音量を好みの大きさに調整してください请注意调整系统音量到适当大小',
    能: 'できる',
    不能: 'できない',
    你的扬声器似乎存在问题: 'スピーカーに問題があるようです',
    建议尝试佩戴耳机或者更换其它设备:
      'イヤホンを付けるかデバイスを変更して授業を受けることをお勧めします',
    你的设备满足上课要求: 'お使いのデバイスは授業の要件を満たしています',
    你的设备不完全满足上课要求: 'お使いのデバイスは授業の要件を完全に満たしているのではありません',
    继续使用此设备上课可能会降低你的体验:
      'このデバイスで授業を続けると授業体験が悪くなるかもしれません',
    知道了: 'わかりました',
    你的网络是乎有些问题: 'ネットワーク接続に問題があるようです。ここをタップして確認',
    解决方案: 'ソリューション'
  },
  // 网络状态提示
  networkStatus: {
    statusMap: {
      good: {
        title: 'ネットワーク接続状態良好'
      },
      normal: {
        title: 'ネットワーク接続状態普通'
      },
      weak: {
        title: 'ネットワーク接続状態不良',
        description: '再接続中です。接続状況の良いネットワークやデバイスに変わってもいいです'
      }
    }
  },
  // 网络错误提示
  networkError: {
    notice: [
      'ネットワークに接続されていません',
      'ネットワーク接続を確認するか再度接続されるのを待ってください'
    ]
  },
  // 课件组件
  courseware: {
    errorNotice: [
      'コースウェア読み込み失敗',
      'コースウェア読み込み失敗',
      '‘リロード’をタップしてもう一度やり直してください'
    ]
  },
  // 截屏
  screenThumbnail: {
    successNotice: 'デスクに保存しました'
  },
  // 媒体权限
  mediaSecurityAccess: {
    camera: `カメラの権限許可がないと授業中のやりとりに参加できません。許可しているかどうかを確認してください`,
    microphone: `ThinkAcademyがあなたのマイクロフォンにアクセスすることを許可してください。`,
    deniedAccess:
      'カメラとマイクの権限許可がないと授業中のやりとりに参加できません。許可しているかどうかを確認してください',
    tip: '権限を許可してからアプリを再起動してください',
    confirm: '同意',
    cancel: '拒否'
  },
  // 踢出
  liveKickout: {
    notice: 'アカウントは既に他のデバイスでログインされています。ログインし直してください'
  },
  // 下课提示
  classEnded: {
    title: '授業終了',
    content: 'この授業は終了しました。ホームページに戻ってください',
    okText: 'OK'
  },
  // 定向金币
  orientationCoins: {
    notice: ['以下の生徒が', 'コインを獲得しました！'],
    Congrats: 'コイン獲得',
    others: '他のクラスメートと',
    smallCongrats: 'よくできました！'
  },
  // 作业盒子
  assignmentBox: {
    modalTitle: 'Basic Modal',
    nodataNotice: 'まだ授業中練習問題がありません',
    checkBigPictures: {
      buttonName: '訂正'
    },
    checkedExerciseToast: {
      notice: '事務先生があなたの練習問題をチャックしました。早く確認に行きましょう',
      buttonName: '練習問題を確認',
      loadingName: '画像読み込み中',
      pictureDesc: '{assisTeacher}があなたの練習問題をチャックしました。早く確認に行きましょう'
    },
    checkErrorTips: '残念です。引き続き頑張ってください'
  },
  // 小班涂鸦板
  smallClassGraffitiCorrect: {
    yellow: '黄色',
    red: '赤色',
    blue: '青色'
  },
  // 连续作答
  continuousCorrect: {
    rightLevelDesc: 'もう1問に正解するとバッジは{nextTitle}にレベルアップできます',
    rightTopLevelDesc: 'この授業の最高レベルのバッジを獲得しました',
    wrongLevelDesc: 'もう1問に正解するとバッジは{nextTitle}にレベルアップできます',
    encourageNotice: 'コインボーナス',
    noanswerNotice: 'クイズに参加してバッジをレベルアップさせましょう',
    continuousText: '正解です。よくできました！',
    highestLevelText: 'バッジは最高レベルになりました！',
    upgradedLevelText: 'バッジはLv {curLevel}にレベルアップしました',
    wrongTitleText: '失敗しちゃった',
    wrongDescText: '次回は頑張ってくださいね～'
  },
  // 辅导连麦
  tutorVideoLink: {
    title: '指導チャット',
    errorMsg: ['', 'エラーが発生しました']
  },
  // 系统出错
  systemError: {
    message: 'システムエラー。もう一度やり直してください',
    noPlaybackMessage: `プレイバックビデオが生成されていません`,
    refreshButtonName: '刷新',
    backButtonName: '授業に戻る'
  },
  // 老师上台
  teacherOnStage: {
    notice: ['先生はもうすぐ戻ってきますよ', ''],
    privateChat: 'ビデオチャット'
  },
  // 课中考试
  ClassExam: {
    rankHeaderTitle: 'ランキング'
  }
}
