/**
 * 互动配置
 */
export default {
  // 投票
  vote: {
    title: '投票',
    results: '結果',
    voteResult: '回答結果',
    true: '正解',
    false: '不正解',
    correctTitle: 'おめでとう',
    correctTips: `素晴らしい`,
    rightTipsByPlayback: [`正解です`, `コインをゲットしました`, `よくできました！`],
    rightTipsByLive: [`残念です`, `コインをゲットしました`, `これからも頑張ってくださいね！`],
    repeatSubmitMessage: `もう提出しました`,
    // 懂不懂
    isUnderstand: '授業の内容ちゃんと理解しましたか？',
    understand: 'わかりました',
    notUnderstand: 'わかりませんでした',
    understandFeedback: 'よくできました。この調子で頑張りましょう!',
    notUnderstandFeedback: '大丈夫です。引き続き頑張りましょう!'
  },
  // 游戏互动
  gameCourseware: {},
  // 课间休息
  classRest: {
    title: '業間休み',
    startTip: '休憩時間',
    endTip: '時間切れです'
  },
  // 送礼物
  gift: {
    // 请选择礼物提示
    pleaseChooseGiftNotice: '先生にプレゼントしましょうか~',
    // 金币不足
    notEnoughCoin: 'コインが足りませんよ :(',
    // 可送礼物次数已到达
    notEnoughTimes: 'ありがとう！プレゼントは毎回最大3個しか贈れませんよ:)'
  },
  // 答题板
  coursewareBoard: {
    true: '正解',
    false: '不正解',
    yourAnswer: 'あなたの回答',
    rightAnswer: '正解',
    correctAnswer: '正解'
  },
  // 拍照上墙涂鸦
  graffiti: {
    yellow: '黄',
    red: '赤',
    blue: '青'
  },
  // 拍照上墙
  photoWall: {
    graffitiBtnName: 'お絵かきボード',
    graffitiDesc1: `カメラの画質が悪い？`,
    graffitiDesc2: `お絵かきボードで練習問題を提出しましょう！!`,
    cameraBtnName: 'カメラを使用',
    confirmNotice: [`本当に提出しますか？`, `提出したら修正することができません`],
    shortCutTip: `ヒント：空白をタップしても写真が撮れます`,
    notjoinTip: `先生がやりとりを終了しました。今度はちゃんと参加してくださいね!`,
    hasAccessNotice: [
      `プリントか下書き用紙で練習問題を解く`,
      `下のボタンをタップして写真を撮ってからアップロードする`,
      `提出後「練習」でチェック結果を確認できる`
    ],
    notAccessNotice: [`カメラが使えません`, `お絵かきボードで練習問題を提出しましょう`],
    endNotice: [`先生がやりとりを終了しました。回答を提出しますか？`, `5秒後に授業に戻ります`],
    grading: 'チェック中',
    correct: '正解',
    incorrect: '不正解',
    halfCorrect: '一部正解',
    answerCorrect: '正解'
  },
  // 随机连线
  randomCall: {
    title: 'ランダム指名',
    finishedNotice: {
      self: `指名されました。おめでとう！`,
      other: '{name}が指名されました'
    }
  },
  // 红包
  redPacket: {
    received: 'Received', // 查看红包结题里显示的, 目前功能已隐藏, 暂不翻译
    unfortunatelyRedNotice: ['レッドポケット受領済', ''],
    oopsRedNotice: `このレッドポケットは受領済です`,
    congrats: 'おめでとう!'
  },
  // 视频连麦
  videoLink: {
    joinMessage: '挙手してカメラをオンにして発言しましょう!',
    joinButtonName: '挙手',
    waitMessage: '先生の指名をお待ちください',
    cancelButton: 'キャンセル',
    raiseHandButton: '挙手',
    speaking: '発言中...'
  },
  // 集体发言
  collectiveSpeech: {
    permissionDeniedTips: 'マイクの権限が許可されていません。設定でマイクの権限を許可してください'
  },
  redRain: {
    collect: '受け取る',
    toastTip: '先生がやりとりを終了しました',
    loading: 'ゲーム読み込み中..',
    NetworkFailTip:
      'サーバー接続タイムアウト。ネットワーク接続を確認してからもう一度やり直してください',
    RetryBtn: '再試行',
    title: 'レッドポケット大量出現'
  },
  // 填一填
  nonPersetFillBlank: {
    submitSuccessfully: '提出成功',
    submitFailed: '提出失敗。もう一度やり直してください',
    inputPlaceholder: '回答を入力してください',
    clear: '消去',
    submit: '提出',
    myAnswer: '私の回答'
  },
  praiseList: {
    fileName: '表彰ランキング_{firstTitle}_授業{lessonName}_{courseName}',
    saveText: 'スクリーンショットは保存されました',
    savePath: 'デスク/Think-Academy-Screenshot',
    savingText: '保存中',
    emptyText: 'このクラスにランク入りした生徒がいません'
  },
  schulteTable: {
    schulteTable: 'シュルテテーブル',
    naturalNumber: '自然数',
    primeNumber: '素数',
    normalMode: '普通モード',
    shuffleMode: 'ランダムモード',
    nextNumber: '次の数',
    duration: '{duration}秒',
    gameInstructions: [
      `自然数を{start}～{end}の順で押してください`,
      `素数を{start}～{end}の順で押してください`
    ],
    gameObjectives: [
      '小さいほうから大きいほうまで数字をタップしてください，',
      'もっと速くタップすればもっと多くのコインがゲットできます'
    ],
    gameCompleted: 'ゲーム終了',
    newRecord: '新記録',
    firstChallenge: '初回挑戦',
    yourCurrentTime: '所要時間',
    yourBestTime: 'ベスト記録',
    leaderboard: 'クリア時間ランキング',
    congratulations: 'ランク入りしました！おめでとう! ！',
    tryAgain: '惜しい！今度も頑張ってくださいね！',
    gameOver: 'タイムアップ',
    leaderboardTip: ['もうすぐランキング発表！', 'ランク入りしたか？今すぐ公開！'],
    submitFailTitle: 'ゲーム結果提出失敗',
    submitFailTip:
      'サーバー接続タイムアウト。ネットワーク接続を確認してからもう一度やり直してください',
    submitFailBtn: '再試行'
  }
}
