/**
 * 小班直播
 */
export default {
  // 视频回显区
  videoGroup: {
    hide: '非表示',
    show: '表示',
    localVideoAudition: {
      notice: '参観授業中です',
      offline: 'オフライン'
    }
  },
  speaking: '話し中：',
  douhao: '，',
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: '先生はしばらく席を外しています。お待ちください',
    gameConfig: {
      gameTip: '先生がプレゼン中',
      gameToast: '先生のプレゼンが終わるまでお待ちください'
    }
  },
  // 金币
  coins: {
    buttonName: '詳細',
    currentLessonCoinsTips: '今回ゲットしたコイン数',
    totalCoinsTips: '累計コイン数',
    levelNameList: ['レベル1', 'レベル2', 'レベル3', 'レベル4', 'レベル5', 'レベル6', 'レベル7'], // 等级名称列表
    levelNotice_0: `授業中のクイズを解いて勲章のレベルを上げましょう`, // 0级别
    levelNotice_7: `勲章がレベルマックスに達しました`, // 7级别
    levelNotice_other: `もう一問に正解すると{levelName}にアップできます` // 其它级别
  },
  // 麦克风
  microphone: {
    buttonName: ['ミュート', 'ミュート解除']
  },
  // 举手
  raiseHand: {
    buttonName: ['挙手', '挙手']
  },
  // 发送表情
  sendEmoji: {
    buttonName: ['絵文字', 'しばらく利用できません'],
    sendTooFastNotice: '少し休憩してから再試行してください',
    forbiddenSendMessage: '絵文字が利用できなくなります',
    sendTooFast: 'メッセージが頻繁すぎるから、{num} 秒後にやり直してください'
  },
  // 动态表情过期
  dynamicEmoji: ['絵文字の利用期限が切れました', 'もう一度購入してください'],
  // 更多设置
  moreSettings: {
    buttonName: 'もっと'
  },
  // 反馈
  feedback: {
    buttonName: 'フィードバック'
  },
  screenShot: {
    buttonName: 'スクリーンショット'
  },
  // 摄像头
  camera: {
    buttonName: 'カメラ'
  },
  visualEffect: {
    buttonName: 'スタンプ',
    unableOpen: 'スタンプ機能が利用不可能',
    teacherClose: 'スタンプ機能がオフにされた',
    openTip: 'スタンプ機能が利用可能、ここでオンにすることができます',
    failLoadEffect: 'スタンプロード失敗',
    confirm: 'わかりました'
  },
  // 课后作业
  homework: {
    buttonName: '練習問題'
  },
  // 考试报告
  examReport: {
    buttonName: 'テストの報告書'
  },
  // 查看考试报告提示文案
  examReportTips: {
    msg: '報告書はテスト終了後に確認できます',
    confirm: 'OK'
  },
  // 摄像头授权确认窗口
  cameraAllowConfirm: {
    notice: '先生によるメッセージ:カメラをオンにしてください'
  },
  // 麦克风授权确认窗口
  microphoneAllowConfirm: {
    notice: '先生によるメッセージ:マイクをオンにしてください'
  },
  // 老师禁用麦克风后开启麦克风时提示
  teacherDisabledOnMicrophone: {
    notice: `この時間帯には、マイクをオンしてはいけません`
  },
  // 老师禁用麦克风后关闭麦克风时提示
  teacherDisabledOffMicrophone: {
    notice: `この時間帯には、マイクをオフしてはいけません`
  },
  // 旁听状态面版
  auditionPanel: {
    notice: '参観授業中です'
  },
  // 回放
  playback: {
    notice: 'プレイバックを見る'
  },
  courseEnd: '授業が終わりました',
  视频采集失败: 'ビデオの収集に失敗しました',
  没有摄像头权限: 'ビデオの収集に失敗しました｡カメラ権限が許可されていません',
  找不到可用摄像头设备: 'ビデオの収集に失敗しました｡使えるカメラがありません',
  摄像头被其他应用占用: 'ビデオの収集に失敗しました｡カメラはほかのアプリで使われています',
  请检查摄像头设备: 'ビデオの収集に失敗しました｡カメラをチェックしてください',
  音频采集失败: '音声の収集に失敗しました｡',
  没有麦克风权限: '音声の収集に失敗しました｡マイク権限が許可されていません',
  麦克风被其他应用占用: '音声の収集に失敗しました｡マイクはほかのアプリで使われています',
  请检查麦克风设备: '音声の収集に失敗しました｡マイクをチェックしてください',
  没有可用的麦克风: '音声の収集に失敗しました｡使えるマイクがありません',
  没有可用的音频播放设备: '音声の収集に失敗しました｡音声再生デバイスがありません｡',
  说出你的答案吧: 'あなたの答えを言ってください',
  正确答案: '正解：',
  请注意专心听讲哦: '注意して授業を聞いてください',
  我知道了: '分かりました'
}
