/**
 * 大班直播
 */
export default {
  // 导航区
  header: {
    groupVideoButtonName: 'グループビデオチャット',
    deviceTestButtonName: 'デバイステスト',
    teacherMessageOnly: '先生からのメッセージのみ表示'
  },
  // 播放器区
  players: {
    mute: 'ミュート',
    unmute: 'ミュート解除'
  },
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: '先生はしばらく席を外しています。お待ちください',
    gameConfig: {
      gameTip: '先生のプレゼン中',
      gameToast: '先生のプレゼン終了をお待ちください'
    }
  },
  // 聊天
  chats: {
    newMessage: '新着メッセージ',
    teacherOnly: '先生のみ',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: 'チャットは終了しています',
      2: 'チャットは終了しています',
      3: '',
      4: '',
      5: '読み込み中'
    },
    inputPlaceholder: '何か言いましょう',
    hotWordList: ['完成！', 'わかりません', 'わかりました', 'ハハハ'],
    msgTip: {
      CONNECT: 'チャットサービスに接続済',
      DISCONNECT: 'チャットサービス接続が切れました',
      RECONNECT: 'チャットサービス再度接続中',
      BAN_SPEECH: '事務先生に発言を禁止されました', // 禁言
      RELIEVE_SPEECH: '事務先生に発言禁止を解禁されました', // 解除禁言
      SPEECH_INTERVAL: '３秒後に発言してください',
      SPEECH_MINE: '私',
      SPEECH_SYS: 'システムメッセージ',
      SPEECH_TEACHER: '先生',
      SPEECH_EMPTY: '入力したメッセージが規約違反です',
      REMOTE_LOGIN: '現在地はいつものログイン地ではありません。画面を刷新してください',
      CHAT_DISCONNECT: 'サーバー接続が切れたため、発言できません',
      ENTER_ROOM: '教室に入りました'
    },
    level: {
      wood: 'ブロンズ',
      gold: 'ゴールド',
      silver: 'シュルバー',
      diamond: 'ダイヤモンド'
    },
    reply: '返事',
    gotIt: '了解'
  },
  // 视频回显
  videoGroup: {
    textConfig: {
      matching: 'マッチ中...',
      noticeAccessDenied: '設定でThink Academyのカメラ権限を許可してください'
    },
    tabNames: ['ビデオ', 'チャット'],
    closedNotice: 'グループビデオチャットは終了しています',
    localVideoAudition: {
      notice: '授業参観中'
    }
  },
  // 金币显示
  coins: {
    title: '私のコイン'
  },
  // 举手
  raiseHand: {
    buttonName: '挙手'
  },
  // 回放
  playback: {
    notice: 'プレイバックを見る'
  }
}
