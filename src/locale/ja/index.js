import common from './common'
import login from './login'
import courses from './courses'
import setting from './setting'
import account from './account'
import classroom from './classroom'
import today from './today'
import views from './views'
import growthHandbook from './growthHandbook.js'
import blindBox from './blindBox.js'
import myBackpack from './myBackpack.js'

export default {
  common,
  login,
  setting,
  courses,
  account,
  classroom,
  today,
  growthHandbook,
  blindBox,
  myBackpack,
  ...views,
  liveroomMicFirstTipAlertTitlePC: 'スペースキーまたはマイクボタンを押しながら発言してください',
  liveroomMicFirstTipAlertDoneTitle: '分かった',
  liveroomMicSpeakingHUDTitle: '発言中',
  liveroomKeepMuteModeToast: '先生は全員のマイクをミュートしました',
  liveroomPushToTalkModeToast: '先生は全員のマイクをオンにしました',
  liveroomKeepMicOnModeToast: '先生はマイクをプッシュトゥトークモードに設定しました',
  老师已将麦克风设置为自由发言模式: '先生はマイクをオープンマイクモードに設定しました',
  liveroomHoldDownMicBtnWhenSpeakingToastForPC:
    '発言する際は、スペースバーまたはマイクボタンを押し続けてください',
  liveroomDisableOthersVideoMenu: '他者ビデオミュート',
  磁盘可用空间不足: 'ディスクの空き容量が不足しています。ディスクをクリーンアップしてください'
}
