// 登录页
export default {
  // 通用
  common: {
    email: 'メールアドレス',
    emailAddress: 'メールアドレス',
    phoneNumber: '電話番号',
    phoneNumberOrEmail: '携帯番号かメール',
    verificationCodePlaceholder: 'メール確認コード',
    passwordPlaceholder: 'パスワードを入力してください',
    studentId: '生徒番号',
    signIn: 'ログイン',
    inputInvalid: '無効な入力です',
    incorrectId: '生徒番号またはパスワードが正しくありません｡再試行してください',
    loginAgain: '再ログイン',
    emailReg: '使えるメールアドレスを入力してください',
    continue: '以下のアプリでログイン',
    是否还没有账号: 'アカウントをお持ちですか?',
    立即注册: '今すぐ登録',
    已经注册过账号: '既に登録済みですか？',
    立即登录: 'すぐログイン',
    注册: '登録',
    我们向您发送了一个验证码: '確認コードを送りました。',
    请在下方输入以进行验证: '{phone}を認証するために、コードを入力してください',
    您似乎还未注册: 'まだ登録していないようですが、',
    您已经注册过账号: '既に登録済みですが、',
    切换其他登录方式: '他のログイン方法に切り替える',
    找到X个可能是您的账号: 'アカウントを{num}つ見つかりました',
    如果是您的账号:
      'お持ちのアカウントならそのままログインしてください。でないと新しいアカウントが作成されます',
    已存在的账号: '既存アカウント',
    继续创建新账号: '新しいアカウントを作成する',
    您是否要更新现有账户: '既存のアカウントを更新しますか？',
    将此注册信息更新到现有账户: 'この登録情報を既存のアカウントに更新する',
    账号更新成功: 'アカウント更新完了',
    手机: '携帯',
    邮箱: 'メール'
  },
  // 验证码登录页
  verificationCodePage: {
    title: '確認コード',
    description: `未登録の電話番号がログイン後自動的に登録されます`,
    emailDesc: '未登録のメールでログインすると自動的にアカウントが作成されます',
    footerTitle: '確認コード',
    skip: 'スキップ',
    send: '送る',
    reSend: '送り直す',
    back: '戻る',
    verificationTips: {
      confirmPolicy:
        '下記のユーザー規約、プライバシーポリシー、児童プライバシーポリシーに同意し、チェックを入れてください',
      enterAccount: 'アカウントを入力してください',
      enterPhoneNumber: '電話番号を入力してください',
      phoneNumberIncorrect: '電話番号が正しくありません',
      enterVerificationCode: '確認コードを入力してください'
    }
  },
  // 密码登录页
  passwordPage: {
    title: 'パスワードでログイン',
    mobileDescription: `パスワード設定後はパスワードでログインできます`,
    studentIdDescription: `ログイン後、「マイアカウント」で生徒番号を確認できます`,
    footerTitle: 'パスワードでログイン',
    forgotPassword: 'パスワードを忘れた?',
    confirmPolicyTip:
      '下記のユーザー規約、プライバシーポリシー、児童プライバシーポリシーに同意し、チェックを入れてください',
    otherLoginType: 'ほかのログイン方法'
  },
  // 协议信息
  policyInfo: {
    desc: '同意し、チェックを入れてください',
    and: 'と'
  },
  // 踢出提示
  kickoutNotice: {
    message: 'アカウント安全のために再ログインしてください'
  }
}
