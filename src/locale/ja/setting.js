// setting
export default {
  // 设置菜单
  menus: {
    settings: '設定',
    update: '更新',
    cleanCache: 'キャッシュクリア',
    aboutUs: 'Think Academyについて',
    reportLogs: 'ログをアップロード',
    feedback: 'フィードバック',
    logout: 'ログアウト',
    checkDevice: 'デバイステスト'
  },
  // 关于我们
  aboutUs: {
    // 隐私协议
    policyTips: {
      temsOfUse: 'ユーザー規約',
      privacyPolicy: 'プライバシーポリシー',
      internetSafetyPolicy: 'インタネットセキュリティポリシー',
      childPrivacy: '児童プライバシーポリシー'
    }
  },
  // 设置
  settings: {
    savePicture: 'スクリーンショット画像の保存先',
    chooseCampus: 'アドレス',
    chooseLanguage: '言語を選択する',
    browse: '見通す',
    cancel: 'キャンセル',
    confirm: 'OK',
    renderMode: 'レンダーモード',
    oneRender: 'ワンレンダー',
    twoRender: 'ダブリングレンダー',
    采集模式: '採集モード',
    通用: '汎用',
    兼容: '互換性'
  },
  // 反馈
  feedback: {
    title: 'フィードバック',
    problemDescriptorText: '問題説明',
    problemDescriptors: ['機能異常', '機能提案', '教育', '体験全般', 'ライブ授業', 'その他'],
    feedBackInput: 'フィードバック（必須）',
    images: '写真',
    optional: '任意',
    maximuImages: '最大{num}枚写真',
    maxFileSize: 'ファイルのサイズが10Mを超えています。もう一度やり直してください！',
    fileFormatIncorrect: 'ファイル形式が正しくありません。もう一度やり直してください！',
    uploadFailTryAgain: 'ファイルのアップロードに失敗しました。もう一度やり直してください！',
    submit: '提出',
    backToCourses: '私のコースに戻る',
    inputInvilidate: 'フィードバックの追加情報をこちらにご記入ください'
  },
  // 版本相关
  version: {
    versionChecking: 'バージョンチェック...',
    version: 'バージョン',
    alreadyLastUpdated: '最新バージョンです',
    newVersion: '新しいバージョン',
    nextTime: '次回更新',
    updateNow: '今すぐ更新',
    downloadFailed:
      'ダウンロードに失敗しました。公式ウェブサイトから最新バージョンをダウンロードしてください。 ',
    byClickingHere: 'ここをタップして',
    checkFailMessage: 'アップデートの確認に失敗しました。もう一度やり直してください！',
    betaVersion: 'ベータ版',
    downloading: 'アップグレードファイルのダウンロード中...',
    wait: `新しいバージョンがインストールされています。アプリが自動的に再起動するまでお待ちください。所要時間は約2～3分…`,
    winWait: `新しいバージョンをダウンロードしました。次の手順に従ってインストールを完了させてください`
  },
  // 清除缓存
  cleanCache: {
    content: 'コースウェアのキャッシュを消去しますか？',
    successMessage: '消去完了',
    failMessage: '消去に失敗しました。もう一度やり直してください'
  },
  // 上报日志
  reportLogs: {
    content: '異常のデータを報告しますか？',
    logSubmitted: 'データ提出成功、フィードバックいただきどうもありがとうございます。',
    uploadfailed: 'ダウンロードに失敗しました。もう一度やり直してください。'
  },
  // 登出
  logout: {
    content: 'ログアウトしますか？'
  },
  // 时区
  timezone: {
    current: '現在のタイムゾーンは',
    choose: 'タイムゾーンを選択する',
    search: 'タイムゾーンを検索する',
    noResult: '一致する結果がありません',
    invalidMsg: 'タイムゾーンは存在せず、デフォルトのタイムゾーンに戻りました：'
  },
  localsetting: {
    chooseTitle: '教育システムを選択する',
    chooseDesc: 'お子様に合った教育システムまたは地域を選択してください'
  }
}
