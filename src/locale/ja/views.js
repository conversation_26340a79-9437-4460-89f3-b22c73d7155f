export default {
  // 积分商城
  coinsMall: {
    title: 'ポイントモール'
  },
  // 设备拦截
  deviceIntercept: {
    interceptInfo: {
      title: 'このデバイスとの互換性がありません',
      content: `申し訳ありませんが、現在のデバイスには対応しておらず、最低限の動作要件を満たしたデバイスでThink Academyアプリを利用してください。`
    },
    recommendInfo: {
      title: 'このデバイスを使用しないほうがいい',
      content: `申し訳ありません。お使いのデバイスはスムーズにThink Academyアプリを利用できないので、最低限の動作要件を満たしたデバイスでThink Academyアプリを利用してください`
    },
    deviceInfo: {
      compatibility: 'オペレーティングシステム',
      macTips: 'MacOS 10.12または以降のバージョン',
      windowsTips: 'Windows 7または以降のバージョン',
      configurationsTitle: '最低ハードウェア構成',
      configurationsInfo: '4コアのCPU、4GBメモリ',
      deviceTitle: 'デバイス',
      notAvailableDevice: 'ChromeBookとSurface BookシリーズのPCでのご利用ができません'
    }
  },
  // 课前准备页
  prepareClass: {
    great: '正常',
    gotIt: '受け取る',
    title: `授業前準備`,
    audioAccess: 'マイクの権限が許可されていません',
    audioAvailable: '使用可能なマイクがありません',
    cameraAccess: 'カメラの権限が許可されていません',
    cameraAvailable: '使用可能なカメラがありません',
    audioAccessWindow: 'マイクの権限が許可されていません',
    audioAccessContent:
      '設定でThink Academyのマイク権限を許可してください。さもなければ、先生はあなたの声が聞こえません。',
    cameraAccessWindow: 'カメラの権限が許可されていません',
    cameraAccessContent:
      '設定でThink Academyのカメラ権限を許可してください。さもなければ、先生はあなたの顔が見えません。',
    networkWeek: 'ネットワーク接続の調子が悪い',
    networkGood: 'ネットワークの調子が良い',
    networkGoodContent: 'ネットワークの調子が良いです。Think Academyを流暢に利用できます。',
    networkWeekContent:
      'ネットワークの調子が悪いと授業中に画面がカクカクする可能性があるため、他のネットワークに切り替えてみてください。Wi-Fiを利用している場合、ルーターを再起動してから、ネットワークの調子をより良くするためにルーターに近づいてください。',
    retestNetwork: 'もう一度テストする',
    testSpeed: 'ネットワーク回線速度テスト',
    enterName: 'ニックネームを入力してください',
    setNickName: 'ニックネームを入力する',
    setNickNameTip: '65文字以内、特殊文字は不可',
    setNickNameError: 'ニックネームの変更に失敗しました。もう一度やり直してください',
    enterBtn: 'ニックネームを入力してください',
    hasNotEnter: `ニックネームをまだ入力していません`,
    enterClassroom: '教室に入る',
    enterClassroomError: '教室に入りませんでした、もう一度やり直してください',
    editNickname: 'ニックネームを変更する',
    validateInput: `無効な入力です`,
    done: 'ダウンロード済',
    redownload: '再試行',
    coursewareDownload: 'コースウェアダウンロード完了',
    onlineCourseware: 'オンライン教材',
    onlineCoursewareContent:
      'オフラインコースウェアをダウンロードできない場合にのみ、オンラインコースウェアを使用することができますが、良い方法ではありません。',
    confirmUser: '使用を確認',
    coursewareDownloading: 'コースウェアダウンロード中、{speed}、残り{minutes}分',
    coursewareDownloadingSlow: '残り時間60分以上',
    coursewareDownloadWeek: 'コースウェアの読み込みに失敗しました。もう一度やり直してください',
    coursewareDownloadWeekTitle: 'コースウェアの読み込みに失敗しました',
    coursewareDownloadWeekContent: 'ネットワークをチェックしてからもう一度ダウンロードしてください',
    playbackError:
      'この授業はプレイバックがありません。あるいはプレイバックがまだ生成されていません',
    minute: '分',
    second: '秒',
    waiting: 'コースウェアダウンロード中。お待ちください',
    networkBtn: {
      good: '良い',
      normal: '普通',
      retest: '再試行する',
      weak: '悪い'
    },
    startAgo: '授業は{changeMinutes}分前に始まっています。早く教室に入りましょう！',
    willStart: ' あと{changeMinutes}分で授業が始まる',
    missClass: 'ライブ授業を見逃したら、見逃し動画をご覧ください。',
    joinClass: '授業に入る',
    continue: '続行',
    confirm: 'OK',
    downloadTip: {
      coursewareDownloading: 'コースウェアをダウンロードする',
      downloadReson: '事前にコースウェアをダウンロードすれば授業時間を節約できます',
      incomplete: ' コースウェアのダウンロードが不完全',
      downloadComplete: 'ダウンロード完了',
      DownloadFailed: 'ダウンロード失敗'
      // subtitle: '事前にコースウェアをダウンロードすれば授業時間を節約できます'
    },
    checkIn: {
      onTime: '時間通りに教室に入る',
      title: 'チェックイン',
      missTips: ['授業はもう始まっています、', `チェックインコインがもらえなくなりますよ:(`],
      firstCheckInMessage: 'まずはチェックインしてください',
      checkInButtonName: 'チェックイン',
      checkInSuccess: 'チェックイン成功',
      checkInFail: 'チェックイン失敗'
    }
  }
}
