// 课程中心
export default {
  // 课程列表页
  list: {
    title: '私のコース',
    titleTip: '現在、あなたは「火星キャンパス」にいます',
    tabList: {
      all: '全部',
      current: '授業中',
      completed: '授業終了'
    },
    courseTabList: {
      live: 'ライブ授業',
      record: '映像授業'
    },
    noCourses: 'コースがない / このカテゴリーにはコースはない',
    switchSingleAccount: 'サブアカウント{account}にはコースがります。タップして切り替えます',
    switchMultipleAccount: '複数のサブアカウントにコースがあります。タップして切り替えます',
    switchMultipleSchool:
      '複数の分校に購入済のコースがあります。ここをタップして分校を切り替えます',
    switchSingleSchool: '分校{school}にはコースがあります。タップして切り替えます',
    switchAccountText: '生徒切り替え',
    switchSchoolText: '分校切り替え',
    planName: '授業'
  },
  lessonType: {
    formal: '正式授業',
    playback: '録画授業',
    audition: '参観授業',
    temporary: '追加授業'
  },
  // 课程卡片
  courseCard: {
    classInSession: '授業中',
    nextSession: '次の授業',
    enter: '教室に入る',
    completed: '完了',
    refunded: '払い戻し済',
    playback: 'プレイバック',
    startAuditing: '参観',
    view: '詳細',
    reportTips: '“{course}”の報告書が作成されました',
    parentAudit: '保護者参観',
    alreadyStarted: `授業{courseNum}は始まっています`,
    nextLesson: '次の授業'
  },
  // 录播课
  recordCourse: {
    courseValidUntil: 'この授業の有効期間は',
    enter: '入る',
    refunded: '払い戻し済',
    expired: '期限切れ',
    expiredTip: 'このコースは学習期限が過ぎた',
    permanentTip: 'このコースは永久に有効です',
    goToTest: '回答へ',
    report: 'テスト報告書',
    reload: 'リロード',
    loadFailed: 'ロード失敗',
    loading: 'ロード中',
    saveShotScreen: 'スクリーンショットが保存されました',
    switchChannel: '経路切り替え',
    playNextVideo: '次の動画を再生する'
  },
  // 录播课播放器提示
  recordPlayerTips: {
    0: '再生',
    '0-1': 'ストップ',
    1: '10秒早送り',
    2: '10秒巻き戻し',
    9: 'スクリーンショット'
  },
  // 课程详情页
  detail: {
    title: '授業詳細',
    specialTitle: '{prefix}詳細',
    // 播放按钮
    playButton: {
      // 未开始状态
      startSoon: 'まだ始まっていません',
      playbackGenerating: 'プレイバック生成中',
      playback: 'プレイバックを見る',
      playbackExpired: 'プレイバックが期限切れです',
      unpaid: '未購入',
      live: '教室に入る',
      finished: '終了',
      startAuditing: '参観する'
    },
    classRoom: '教室',
    homework: {
      status: ['未発表', '未提出', 'チェック済', '提出済', '期限切れ', '解答を見る', '未提出'],
      title: '宿題'
    },
    exam: {
      status: ['未発表', 'テストへ', '回答済', 'チェック済', '期限切れ']
    },
    examTip: 'テストはまだ未発表です',
    resources: '資料',
    playBackTip: 'ライブ授業終了後2時間後に再生可能',
    reportBtnText: 'チェック済',
    reportSubmitText: '提出済',
    reportExpiredText: '期限切れ',
    startTest: 'テストへ',
    reportTips: `{canViewTime}以降報告書を確認できる`,
    lessonReport: '授業報告書',
    expiredTips: '宿題の提出期限を過ぎてしいます。次回はちゃんと時間内に提出してくださいね',
    file: 'ファイル',
    files: 'ファイル',
    transferred: '授業振り替え',
    classNotes: '板書',
    learningMaterials: '学習資料'
  },
  switchAccount: {
    title: '生徒切り替え',
    subTitle: '以下のサブアカウントに授業があります。タップしてアカウントを切り替えます',
    cancel: 'キャンセル',
    confirm: 'OK',
    switchSuccess: '切り替え成功',
    switchFail: '切り替え失敗。しばらくしてからもう一度やり直してください',
    switchIng: '切り替え中…',
    emptyTip: '生徒アカウントを選択してください',
    loginTitle: 'ご家庭にいくつかの生徒アカウントがあります',
    loginSubTitle: 'ログインしたいアカウントを選択してください'
  },
  switchSchool: {
    title: '分校切り替え',
    subTitle: '以下の分校にコースがあります。タップして分校を切り替えます'
  },
  // 面授课 - 如何上课
  faceToFace: {
    attendClass: '受講方法を見る',
    finished: '終了',
    location: '場所',
    copy: 'コピー',
    zoomId: 'Zoom ID',
    zoomPassword: 'Zoom パスワード',
    zoomDownload: 'Zoomをダウンロードする',
    zoomDescribe: `それから上記Zoom IDとパスワードを入力してください。Zoomミーティングの十分前に教室に入り、学習経験をよく考えましょう`,
    classinLink: 'Classinリンク',
    classinDownload: 'Classinをダウンロードする',
    classinDescribe: `そしてコースに申し込む時にあなたのアカウントで登録してください。Classinミーティングの10分前に教室に入り、Think Academy体験をお楽しみください。`,
    classroomDescribe: `10分前に教室に入りください、Think Academy体験をお楽しみください。`,
    ipadDescribe: `iPadでThinkhub APPを利用して授業を受けてください。問題があれば、連絡してください。`,
    confirm: 'わかりました'
  },
  // 课后作业
  assignment: {
    title: '宿題',
    earnedNotice: '獲得しました',
    submissionNotice: '早めに完成してさらにボーナスが配布されます'
  },
  // 课前测
  previewQuestion: {
    title: '授業前テスト',
    deadlineName: '締め切り',
    deadlineTime: '締め切り',
    answerTimeName: '回答時間',
    deadlineBannerTips: '回答時間が終わりました。次回はちゃんと時間内に解いてくださいね~！',
    problemDescriptors: [`問題説明`, `（計 {totalScore}分）`],
    confirmTitle: '本当に回答を始めますか？',
    confirmSubtitle: `回答時間は計{duration}、頑張ってください！`,
    solutions: '解説',
    start: '回答開始',
    deadlineTitle: 'テスト終了しました。時間通りに参加できませんでした',
    deadlineDesc: 'おっと、回答時間が終わりました。次回はちゃんと時間内に解いてくださいね～'
  },
  // 学习资料
  studyResources: {
    title: '学習資料',
    download: 'ダウンロード',
    play: '再生'
  },
  // 课堂报告
  lessonReport: {
    title: '授業報告書'
  },
  // 阶段考试
  exam: {
    title: '試験の注意事項',
    solutions: '問題解説を見る',
    report: '報告書を見る',
    start: '回答開始',
    coinsTip: '回答完了後コインがもらえますよ',
    confirmTitle: '回答にご注意',
    confirmSubtitle: `回答時間は計{duration}、頑張ってくださいね！`,
    confirmBtn: 'OK',
    cancelBtn: 'キャンセル'
  },
  // 临时教室
  temporaryClassroom: {
    title: '臨時教室',
    noCourses: 'まだ授業がありません',
    registrationCourses: 'コースに申し込む',
    noCoursewareNotice: 'このコースはまだコースウェアと紐付けていません',
    statusName: {
      startSoon: 'まだ始まっていません',
      playbackGenerating: 'プレイバック生成中',
      playback: 'プレイバックを見る​',
      playbackExpired: 'プレイバックが期限切れです',
      enter: '教室に入る'
    }
  },
  confirmModal: {
    title: '授業通知',
    content:
      '当アカウントは既にライブ授業を受けているため、この教室に入ると{warning}本当に教室に入りますか？',
    warning: '当アカウントを他のデバイスは教室から追い出す',
    enterBtn: 'OK',
    backBtn: 'キャンセル',
    confirm: 'OK'
  },
  switchSchoolModal: {
    title: '通知',
    content: 'この分校に切り替えますか？'
  },
  resolutionRatioModal: {
    title: '通知',
    content:
      '現在の画面解像度がThinkAcademyが要求する最低解像度より低い、\n「設定」 - 「画面表示」”で表示倍率を縮小してください。',
    confirm: 'わかりました'
  },
  playback: {
    courseInformationError: 'コース詳細の取得に失敗しました',
    playbackInformationError: 'プレイバックのの取得に失敗しました',
    playbackInterfaceError: 'プレイバックのインターフェースアクセスエラー',
    playbackSourceIsNull: 'プレイバックのデータソースがありません',
    playbackStampError: '情報の取得に失敗しました'
  },
  noCoursesWare: 'この授業にはコースウェアがありません',
  // 教师联系方式
  teacherContact: {
    tip: '何かご質問がありましたら、担当の先生に連絡してください',
    copy: 'コピー',
    addContact: 'QRコードをスキャンして、先生のWeChatを追加してください'
  },
  // 课堂评价
  nps: {
    feedback: '授業評価',
    enjoyTip: '楽しい授業でしたか？',
    contactTip: '理由を教えてくださればThink Academyをより良くできます',
    reason: 'ここで詳しい理由を書くことができます',
    submitBtn: '提出',
    submitSuccess: '提出成功、フィードバックいただきどうもありがとうございます'
  },
  wrongQuestionBook: {
    title: '間違いノート'
  },
  expand_fold: '最小化',
  expand_more: 'もっと',
}
