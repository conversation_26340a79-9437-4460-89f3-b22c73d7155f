/**
 * 互动配置
 */
export default {
  // 聊天
  chats: {
    newMessage: 'Nouveau message',
    teacherOnly: 'Uniquement les enseignants',
    // 聊天区input提示文案 1:Le professeur est fermé.2:Interdiction de parler 3:Le professeur démarre la conversation 4:Impossible d'envoyer un autre message dans les 5 secondes5Non activé
    inputStatusMap: {
      1: 'Le chat est fermé',
      2: 'Le chat est fermé',
      3: '',
      4: '',
      5: 'Chargement en cours'
    },
    inputPlaceholder: 'Di<PERSON> quelque chose',
    hotWordList: ['Terminé !', 'Je ne comprends pas', 'Je comprends', 'Hahaha'],
    msgTip: {
      CONNECT: 'Le service de chat est connecté',
      DISCONNECT: 'Le service de chat est déconnecté',
      RECONNECT: 'Reconnexion du service de chat en cours',
      BAN_SPEECH: `L'enseignant de soutien vous a interdit de parler`, // 禁言
      RELIEVE_SPEECH: `L'enseignant vous a levé l'interdiction de parler`, // 解除禁言
      SPEECH_INTERVAL: 'Veuillez attendre 3 secondes avant de parler.',
      SPEECH_MINE: 'Je',
      SPEECH_SYS: 'Message du système',
      SPEECH_TEACHER: 'Enseignant',
      SPEECH_EMPTY: `Le message saisi n'est pas valide`,
      REMOTE_LOGIN: `L'emplacement actuel n'est pas un lieu de connexion habituel， veuillez rafraîchir cette page.`,
      CHAT_DISCONNECT:
        'La connexion au serveur a été interrompue, vous ne pouvez pas parler actuellement.',
      ENTER_ROOM: 'Entré dans la salle de classe'
    },
    level: {
      wood: 'Bois',
      gold: 'Or',
      silver: 'Argent',
      diamond: 'Diamant'
    },
    chatbox: 'Chat de classe',
    frequently: 'Envoi de messages trop fréquent',
    onlyteacher: `L'enseignant  a fermé le chat de groupe et autorise uniquement l'envoi de messages à enseignant .`,
    all: `L'enseignant a ouvert le chat de groupe et autorisé l'envoi de messages à tout le monde.`,
    teacherOff: `L'enseignant a fermé le chat`,
    teacherOn: `L'enseignant a ouvert la discussion`,
    chatboxClosed: `Le chat de classe est désactivé`,
    connecting: 'Connexion en cours...',
    sendTo: 'Envoyer à',
    sendToAll: 'Tout',
    sendToTeacher: 'professeur',
    privateMessage: 'Chat privé',
    To: 'à',
    mutedByTeacher: `Vous avez été banni par l'enseignant`,
    unmutedByTeacher: 'Votre état de bannissement a été levé',
    underMute: 'En cours de bannissement'
  }
}
