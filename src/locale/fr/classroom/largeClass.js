/**
 * 大班直播
 */
export default {
  // 导航区
  header: {
    groupVideoButtonName: 'Vidéo de groupe',
    deviceTestButtonName: `Détection de l'équipement`,
    teacherMessageOnly: 'Voir uniquement les messages des enseignants'
  },
  // 播放器区
  players: {
    mute: 'Silence',
    unmute: 'Annuler le mode silencieux'
  },
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: `L'enseignant n'est pas encore dans la salle de classe, veuillez patienter.`,
    gameConfig: {
      gameTip: `Démo de l'enseignant en cours`,
      gameToast: `Veuillez patienter la fin de la démonstration de l'enseignant`
    }
  },
  // 聊天
  chats: {
    newMessage: 'Nouveau message',
    teacherOnly: 'Uniquement les enseignants',
    // 聊天区input提示文案 1:Professeur fermé2:Interdiction de parler 3:Le professeur démarre la conversation 4:Impossible d'envoyer un autre message dans les 5 secondes 5Non activé
    inputStatusMap: {
      1: 'Le chat est fermé',
      2: 'Le chat est fermé',
      3: '',
      4: '',
      5: 'Chargement en cours'
    },
    inputPlaceholder: 'Dites quelque chose',
    hotWordList: ['Terminé!', 'Je ne comprends pas', "J'ai compris", 'Hahaha'],
    msgTip: {
      CONNECT: 'Le service de chat est connecté',
      DISCONNECT: 'Le service de chat est déconnecté',
      RECONNECT: 'Reconnexion du service de chat en cours',
      BAN_SPEECH: `L'enseignant de soutien vous a interdit de parler`, // 禁言
      RELIEVE_SPEECH: `L'enseignant vous a levé l'interdiction de parler`, // 解除禁言
      SPEECH_INTERVAL: 'Veuillez attendre 3 secondes avant de parler.',
      SPEECH_MINE: 'Moi',
      SPEECH_SYS: 'Message du système',
      SPEECH_TEACHER: 'Enseignant',
      SPEECH_EMPTY: `Le message saisi n'est pas valide`,
      REMOTE_LOGIN: `L'emplacement actuel n'est pas un lieu de connexion habituel. Veuillez rafraîchir cette page.`,
      CHAT_DISCONNECT:
        'La connexion au serveur a été interrompue, vous ne pouvez pas parler actuellement.',
      ENTER_ROOM: 'Entré dans la salle de classe'
    },
    level: {
      wood: 'Bois',
      gold: 'Or',
      silver: 'Argent',
      diamond: 'Diamant'
    },
    reply: 'Répondre',
    gotIt: 'Reçu'
  },
  // 视频回显
  videoGroup: {
    textConfig: {
      matching: 'En cours de correspondance...',
      noticeAccessDenied:
        'Veuillez ouvrir les autorisations de la caméra pour ThinkAcademy dans les paramètres du système.'
    },
    tabNames: ['vidéo', 'chat'],
    closedNotice: 'La vidéo de groupe est désactivée',
    localVideoAudition: {
      notice: 'Vous assistez à un cours en auditeur libre'
    }
  },
  // 金币显示
  coins: {
    title: `Mes pièces d'or`
  },
  // 举手
  raiseHand: {
    buttonName: 'lever la main'
  },
  // 回放
  playback: {
    notice: 'Regarder la rediffusion'
  }
}
