/**
 * 互动配置
 */
export default {
  // 投票
  vote: {
    title: 'réf<PERSON>rendum',
    results: 'Résultat',
    voteResult: 'Résultat du questionnaire',
    true: 'Vrais',
    false: 'Faux',
    correctTitle: 'Félicitations',
    correctTips: `Tu es génial`,
    rightTipsByPlayback: [`<PERSON><PERSON>ponse correcte`, `Vous avez obtenu des pièces d'or`, `Génial！`],
    rightTipsByLive: [
      `ce n'est pas correct.`,
      `Vous avez obtenu des pièces d'or`,
      `Continuer à faire des efforts la prochaine fois！`
    ],
    repeatSubmitMessage: `Vous avez déjà soumis`,
    // 懂不懂
    isUnderstand: 'Avez-vous compris ce que le professeur a dit ?',
    understand: 'Compris',
    notUnderstand: `Je n'ai pas compris`,
    understandFeedback: 'Très bien, continuez comme ça !',
    notUnderstandFeedback: 'Pa<PERSON> de problème, continuez à vous battre!'
  },
  // 游戏互动
  gameCourseware: {},
  // 课间休息
  classRest: {
    title: 'Récréation',
    startTip: 'Temps de pause',
    endTip: 'Le temps est écoulé'
  },
  // 送礼物
  gift: {
    // 请选择礼物提示
    pleaseChooseGiftNotice: 'Offrez un cadeau à votre professeur~',
    // 金币不足
    notEnoughCoin: `Tu n'as pas assez de pièces d'or.:(`,
    // 可送礼物次数已到达
    notEnoughTimes: 'Merci, mais vous ne pouvez envoyer que 3 cadeaux maximum à chaque fois.:)'
  },
  // 答题板
  coursewareBoard: {
    true: 'Vrais',
    false: 'Faux',
    yourAnswer: 'Votre réponse',
    rightAnswer: 'La réponse correcte',
    correctAnswer: 'La réponse correcte'
  },
  // 拍照上墙涂鸦
  graffiti: {
    yellow: 'jaune',
    red: 'rouge',
    blue: 'bleu'
  },
  // 拍照上墙
  photoWall: {
    graffitiBtnName: 'Tableau de dessin',

    graffitiDesc1: `* Photo floue ?？`,
    graffitiDesc2: `Utilisez le tableau de dessin pour soumettre vos exercices!!`,
    cameraBtnName: `Utilisation de l'appareil photo`,
    confirmNotice: [
      `Confirmez-vous la soumission ？`,
      `Une fois soumis, il ne sera plus possible de le modifier.`
    ],
    shortCutTip: `Astuce : Vous pouvez également prendre une photo en appuyant sur la touche d'espace.`,
    notjoinTip: `Le professeur a terminé de répondre, nous attendons votre participation active la prochaine fois !`,
    hasAccessNotice: [
      `Faites les exercices sur les notes de cours ou sur du papier brouillon.`,
      `Cliquez sur le bouton ci-dessous pour prendre une photo et la télécharger`,
      `Les résultats de la correction peuvent être consultés dans la section 'Exercices' après soumission.`
    ],
    notAccessNotice: [
      `La caméra n'est pas disponible`,
      `Utilisez le tableau de dessin pour soumettre vos exercices`
    ],
    endNotice: [
      `L'enseignant a terminé l'interaction, voulez-vous soumettre votre réponse maintenant ?`,
      `Retour en classe dans 5 secondes`
    ],
    grading: 'En cours de correction',
    correct: 'Vrais',
    incorrect: 'Faux',
    halfCorrect: 'Partiellement correct',
    answerCorrect: 'Réponse correcte'
  },
  // 随机连线
  randomCall: {
    title: 'Appel au hasard',
    finishedNotice: {
      self: `Félicitations, vous avez été sélectionné ！`,
      other: '{name} a été sélectionné'
    }
  },
  // 红包
  redPacket: {
    received: 'Received', // Veuillez consulter la conclusion de la rubrique des enveloppes rouges, les fonctionnalités sont actuellement masquées et ne sont pas traduites pour le moment.
    unfortunatelyRedNotice: ['Les enveloppes rouges ont toutes été réclamées.', ''],
    oopsRedNotice: `Vous avez reçu cette enveloppe rouge.`,
    congrats: 'Félicitations!'
  },
  // 视频连麦
  videoLink: {
    joinMessage: `Levez la main pour rejoindre la vidéo en direct!`,
    joinButtonName: 'lever la main',
    waitMessage: `En attente du choix de l'enseignant`,
    cancelButton: 'canceller',
    raiseHandButton: 'lever la main',
    speaking: 'En parlant...'
  },
  // 集体发言
  collectiveSpeech: {
    permissionDeniedTips: `L'autorisation du microphone n'est pas activée, veuillez l'activer dans les paramètres du système.`
  },
  redRain: {
    collect: `Accepter`,
    toastTip: `L'enseignant a terminé l'interaction`,
    loading: 'Chargement du jeu en cours..',
    NetworkFailTip:
      'Connexion au serveur expirée, veuillez vérifier votre connexion réseau et réessayer.',
    RetryBtn: 'Réessayer',
    title: 'Pluie de enveloppes rouges'
  },
  // 填一填
  nonPersetFillBlank: {
    submitSuccessfully: 'Soumis avec succès',
    submitFailed: 'Échec de la soumission, veuillez réessayer',
    inputPlaceholder: 'Veuillez entrer la réponse',
    clear: 'Vider',
    submit: 'Soumettre',
    myAnswer: 'Ma réponse'
  },
  praiseList: {
    fileName: `Tableau d'honneur de la classe_{firstTitle}_Leçon {lessonName}_{courseName}`,
    saveText: `La capture d'écran a été enregistrée sous`,
    savePath: 'Bureau/Think-Academy-Screenshot',
    savingText: 'Enregistrement en cours',
    emptyText: `Aucun élève de cette classe n'est actuellement classé`
  },
  schulteTable: {
    schulteTable: 'Grille de Schulte',
    naturalNumber: 'Nombre naturel',
    primeNumber: 'Nombre premier',
    normalMode: 'Mode normal',
    shuffleMode: 'Mode aléatoire',
    nextNumber: 'Le prochain',
    duration: '{duration}s',
    gameInstructions: [
      `Cliquez sur {start} à {end} ci-dessous dans l'ordre croissant.`,
      `Cliquez sur les nombres premiers de {start} à {end} dans l'ordre croissant.`
    ],
    gameObjectives: [
      `Cliquez sur les chiffres dans l'ordre croissant,`,
      `plus vous cliquez rapidement, plus vous gagnez de pièces d'or`
    ],
    gameCompleted: 'Jeu terminé',
    newRecord: 'Nouveau record',
    firstChallenge: 'Premier défi',
    yourCurrentTime: 'Temps écoulé',
    yourBestTime: `Meilleur de l'histoire`,
    leaderboard: 'Classement',
    congratulations: 'Super ! Félicitations pour votre réussite ！',
    tryAgain: 'Vous y étiez presque, continuez à vous entraîner la prochaine fois !',
    gameOver: 'Temps de jeu écoulé',
    leaderboardTip: [
      'Le professeur va bientôt publier le classement !',
      'Es-tu sur la liste ? Révélation imminente !'
    ],
    submitFailTitle: 'Échec de la soumission des résultats du jeu',
    submitFailTip:
      'Connexion au serveur expirée. Veuillez vérifier votre connexion réseau et réessayer.',
    submitFailBtn: 'Réessayer'
  }
}
