/**
 * 模块配置
 */
export default {
  // 导航
  header: {
    // 返回确认窗口
    backConfirm: {
      content: `Confirmez-vous de vouloir quitter la salle de classe en direct ?`,
      exitByCoursewareIdChange:
        'Problème avec le matériel de cours, veuillez quitter la salle de classe et y revenir ensuite.'
    }
  },
  // 反馈
  feedback: {
    headerName: 'Description du problème',
    optionNames: ['Lié aux études', `Lié à l'application`, 'Comportement inapproprié', 'Autre'],
    placeholder: `1. Choisissez une étiquette correspondante pour le type de commentaire. 2. Veuillez décrire en détail votre problème afin que notre enseignant puisse le résoudre rapidement.`,
    screenshotTips: `J'accepte d'envoyer la capture d'écran actuelle à l'équipe éducative.`,
    sendSuccessNotice:
      'Merci pour vos commentaires, nous vous contacterons ou contacterons vos parents rapidement',
    defaultMsg: 'Envoyer le retour',
    playback: {
      header: 'Commentaires',
      placeholder: 'Décrivez votre problème ici',
      screenshotTips: `J'accepte de soumettre les informations et la capture d'écran de l'appareil actuelles à l'équipe de support technique.`,
      cancel: 'Annuler',
      send: 'Envoyer',
      success: 'Soumis avec succès',
      failed: 'Echec de la soumission'
    }
  },
  // 设备检测
  deviceTest: {
    dialogTitle: 'Test du matériel',
    statusNames: {
      disabled: 'Désactivé',
      usable: 'Utilisable',
      unauthorized: ' Non autorisé'
    },
    cameraTitle: 'Test de la caméra: ',
    microphoneTitle: 'Test du microphone: ',
    audioTitle: 'Test audio: ',
    authorizeGuide: ['Autoriser, ', 'cliquez sur le lien']
  },
  checkDevice: {
    network: 'réseau',
    microphone: 'microphone',
    camera: 'caméra',
    voice: 'haut-parleur',
    网络满足上课需求: 'Le réseau répond aux exigences de cours',
    网络不满足上课需求: 'Le réseau ne répond pas aux exigences de cours',
    你的设备满足上课需求: 'Votre appareil répond aux exigences de cours',
    你的设备不满足上课需求:
      'Votre réseau semble avoir des problèmes. Cliquez ici pour des suggestions de résolution.',
    音视频测试: 'Test audio et vidéo',
    课堂互动测试: `Test d'interaction en classe`,
    服务器检测: 'Test de vitesse de connexion au serveur',
    课件下载测试: 'Test de téléchargement de cours',
    此项测试大约需要花费一分钟: 'Ce processus prendra environ 1 à 3 minutes',
    常见网络问题建议: 'Suggestions pour les problèmes de réseau courants',
    尝试重启路由器:
      'Si vous utilisez un réseau sans fil Wi-Fi, essayez de vous rapprocher du routeur et de redémarrer le routeur',
    检查网络权限: `Veuillez vérifier et vous assurer que votre appareil nous permet d'accéder au réseau`,
    切换其它网络: 'Essayez de passer à un autre réseau',
    重新测试: 'Re-test',
    下一项: 'Prochaine étape',
    没有麦克风权限: 'Think Academy ne peut pas utiliser votre microphone',
    没有找到可用的麦克风: `Aucun microphone disponible n'a été trouvé`,
    请检查你的麦克风或者佩戴耳机:
      'Veuillez vérifier vos paramètres de microphone ou essayer de mettre des écouteurs',
    尝试你大声读出下面的文字: 'Essayez de lire à haute voix le texte ci-dessous',
    你好: 'Bonjour',
    你的麦克风似乎存在问题: 'Il semble que votre microphone ait des problèmes.',
    我们没有听到你的声音: `Nous n'avons pas entendu votre voix, veuillez essayer à nouveau en changeant de dispositif ou en portant des écouteurs.`,
    麦克风检测完成: 'Le test de microphone est terminé.',
    你的麦克风工作正常: 'Votre microphone fonctionne normalement.',
    去设置: 'Aller aux paramètres',
    没有摄像头权限: 'Think Academy ne peut pas utiliser votre caméra.',
    在设置中打开麦克风权限: `Veuillez ouvrir l'autorisation d'utilisation du microphone de Think Academy dans les paramètres système.`,
    在设置中打开摄像头权限: `Veuillez ouvrir l'autorisation d'utilisation de la caméra de Think Academy dans les paramètres système.`,
    没有找到可用的摄像头: `Aucun périphérique de caméra disponible n'a été trouvé.`,
    请检查你的摄像头:
      'Veuillez vérifier si votre caméra est correctement installée ou si vous utilisez un autre appareil pour la classe.',
    可以在上面看到自己吗: 'Pouvez-vous vous voir dans la boîte vidéo ci-dessus?',
    正在使用摄像头: `La caméra est actuellement en cours d'utilisation et fonctionne normalement.`,
    你的摄像头似乎存在问题: 'Il semble y avoir un problème avec votre caméra.',
    请检查你的摄像头是否正确安装:
      'Veuillez vérifier si votre caméra est correctement installée ou si vous utilisez un autre appareil pour la classe.',
    摄像头检测完成: 'La détection de la caméra est terminée.',
    你的摄像头工作正常: 'Votre caméra fonctionne correctement.',
    当前音量: 'Volume actuel:',
    可以听到正在播放的音乐吗: 'Pouvez-vous entendre la musique en cours de lecture?',
    请注意调整系统音量到适当大小: 'Veuillez ajuster le volume du système à un niveau approprié.',
    能: 'Oui',
    不能: 'Non',
    你的扬声器似乎存在问题: 'Il semble y avoir un problème avec vos haut-parleurs.',
    建议尝试佩戴耳机或者更换其它设备: `Essayez d'utiliser un casque d'écoute ou de changer d'appareil pour la classe.`,
    你的设备满足上课要求: 'Votre appareil répond aux exigences de la classe.',
    你的设备不完全满足上课要求:
      'Votre appareil ne répond pas complètement aux exigences de la classe.',
    继续使用此设备上课可能会降低你的体验:
      'Continuer à utiliser cet appareil pour la classe pourrait réduire votre expérience de la classe.',
    知道了: 'Compris',
    你的网络是乎有些问题:
      'Il semble y avoir un problème avec votre connexion Internet. Cliquez ici pour en savoir plus.',
    解决方案: 'Solution'
  },
  // 网络状态提示
  networkStatus: {
    statusMap: {
      good: {
        title: 'Connexion au réseau bonne'
      },
      normal: {
        title: 'Connexion au réseau moyenne'
      },
      weak: {
        title: 'Connexion au réseau faible',
        description:
          "Nous sommes en train de réessayer de nous connecter. Vous pouvez également essayer de changer de réseau ou d'appareil si un meilleur choix est disponible."
      }
    }
  },
  // 网络错误提示
  networkError: {
    notice: [
      'Aucun réseau détecté',
      'Veuillez vérifier votre connexion réseau ou attendre la reconnexion'
    ]
  },
  // 课件组件
  courseware: {
    errorNotice: [
      'Échec du chargement du cours',
      'Échec du chargement du cours',
      `Cliquez sur "Recharger" et essayez à nouveau.`
    ]
  },
  // 截屏
  screenThumbnail: {
    successNotice: 'Enregistré sur le bureau'
  },
  // 媒体权限
  mediaSecurityAccess: {
    camera: `Veuillez activer l'autorisation de caméra pour participer aux interactions en cours.`,
    microphone: `Veuillez permettre à ThinkAcademy d'accéder à votre microphone`,
    deniedAccess: `Veuillez activer l'autorisation de caméra et de microphone pour participer aux interactions en cours.`,
    tip: `Veuillez redémarrer l'application une fois les autorisations activées.`,
    confirm: `Je suis d'accord`,
    cancel: 'Refuser'
  },
  // 踢出
  liveKickout: {
    notice:
      'Votre compte est actuellement connecté sur un autre appareil. Veuillez vous reconnecter.'
  },
  // 下课提示
  classEnded: {
    title: 'Cours terminé',
    content: "Ce cours est terminé. Veuillez retourner à la page d'accueil",
    okText: 'OK'
  },
  // 定向金币
  orientationCoins: {
    notice: ['Ces élèves ont gagné', 'pièces !'],
    Congrats: 'Obtenir des pièces',
    others: `et d'autres camarades de classe`,
    smallCongrats: `Félicitations!`
  },
  // 作业盒子
  assignmentBox: {
    modalTitle: 'Basic Modal',
    nodataNotice: 'Pas de pratique en classe pour le moment',
    checkBigPictures: {
      buttonName: 'Correction'
    },
    checkedExerciseToast: {
      notice: 'Votre tuteur a corrigé votre exercice, venez voir',
      buttonName: `Voir l'exercice`,
      loadingName: `Chargement de l'image`,
      pictureDesc: '{assisTeacher}a corrigé votre exercice, venez voir'
    },
    checkErrorTips: `C'est dommage, continuez à travailler dur`
  },
  // 小班涂鸦板
  smallClassGraffitiCorrect: {
    yellow: 'jaune',
    red: 'rouge',
    blue: 'bleu'
  },
  // 连续作答
  continuousCorrect: {
    rightLevelDesc:
      'Après avoir répondu correctement à une autre question, votre médaille sera mise à niveau en{nextTitle}',
    rightTopLevelDesc: 'Vous avez déjà atteint le niveau de médaille le plus élevé de cette leçon',
    wrongLevelDesc:
      'Après avoir répondu correctement à une autre question, votre médaille sera mise à niveau en{nextTitle}',
    encourageNotice: `Récompense en or`,
    noanswerNotice:
      'Participez aux questions interactives du cours pour mettre à niveau votre médaille',
    continuousText: 'Bonne réponse, très bien !',
    highestLevelText: 'Votre médaille est déjà au niveau maximum !',
    upgradedLevelText: 'Votre médaille a été mise à niveau en niveau{curLevel}',
    wrongTitleText: 'Oops, un erreur',
    wrongDescText: 'Essayez encore la prochaine fois !'
  },
  // 辅导连麦
  tutorVideoLink: {
    title: 'Discussion privée de tutorat',
    errorMsg: ['', 'Il y a eu un petit problème']
  },
  // 系统出错
  systemError: {
    message: 'Erreur système, veuillez réessayer',
    noPlaybackMessage: `La vidéo de lecture n'a pas été générée`,
    refreshButtonName: 'Actualiser',
    backButtonName: 'Retourner au cours'
  },
  // 老师上台
  teacherOnStage: {
    notice: ['Eh bien, le professeur reviendra bientôt', ''],
    privateChat: 'Discussion privée en vidéo'
  },
  // 课中考试
  ClassExam: {
    rankHeaderTitle: 'Palmarès'
  }
}
