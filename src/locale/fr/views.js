export default {
  // 积分商城
  coinsMall: {
    title: 'Boutique de Points'
  },
  // 设备拦截
  deviceIntercept: {
    interceptInfo: {
      title: 'Non compatible avec cet appareil',
      content: `Désolé, cet appareil n'est actuellement pas pris en charge. Veuillez exécuter l'application Think Academy sur un appareil conforme aux exigences minimales de fonctionnement`
    },
    recommendInfo: {
      title: 'Non recommandé pour cet appareil',
      content: `Désolé, votre appareil actuel peut ne pas exécuter l'application Think Academy en toute fluidité. Veuillez exécuter l'application Think Academy sur un appareil conforme aux exigences minimales de fonctionnement`
    },
    deviceInfo: {
      compatibility: `Système d'exploitation`,
      macTips: 'MacOS 10.12 ou version ultérieure',
      windowsTips: 'Windows 7 ou version ultérieure',
      configurationsTitle: 'Configuration matérielle minimale',
      configurationsInfo: 'CPU quadricœur, 4 Go de mémoire vive',
      deviceTitle: 'Dispositif',
      notAvailableDevice:
        'Les ordinateurs portables ChromeBook et la série Surface Book ne sont pas pris en charge'
    }
  },
  // 课前准备页
  prepareClass: {
    great: 'Normal',
    gotIt: 'accepter',
    title: `Préparation de cours`,
    audioAccess: 'Autorisation du microphone non activée',
    audioAvailable: 'Pas de microphone disponible',
    cameraAccess: 'Autorisation de la caméra non activée',
    cameraAvailable: 'Pas de caméra disponible',
    audioAccessWindow: 'Autorisation du microphone non activée',
    audioAccessContent: `Veuillez activer l'autorisation du microphone de Think Academy dans les paramètres système, sinon le professeur ne pourra pas entendre votre voix.`,
    cameraAccessWindow: 'Autorisation de la caméra non activée',
    cameraAccessContent: `Veuillez activer l'autorisation de la caméra de Think Academy dans les paramètres système, sinon le professeur ne pourra pas vous voir.`,
    networkWeek: 'Connexion réseau faible',
    networkGood: 'Réseau bon',
    networkGoodContent:
      'Votre connexion réseau est bonne et vous pouvez utiliser Think Academy en toute fluidité.',
    networkWeekContent:
      'Une connexion réseau médiocre peut entraîner des retards pendant la classe. Veuillez essayer de passer à un autre réseau ou, si vous utilisez une connexion Wi-Fi, redémarrez votre routeur et rapprochez-vous de votre routeur sans fil pour améliorer la qualité de la connexion réseau.',
    retestNetwork: 'Retester',
    testSpeed: 'Tester la vitesse du réseau',
    enterName: 'Entrez votre pseudo',
    setNickName: 'Définir un pseudo',
    setNickNameTip: `65 caractères ou moins, ne peut contenir de caractères spéciaux`,
    setNickNameError: `Échec de la modification du pseudo, veuillez réessayer`,
    enterBtn: 'Entrer le nom',
    hasNotEnter: `Vous n'avez pas encore saisi de pseudo`,
    enterClassroom: 'Entrer',
    enterClassroomError: `Impossible d'entrer dans la salle de classe, veuillez réessayer`,
    editNickname: 'Modifier le pseudo',
    validateInput: `Saisie invalide`,
    done: 'Téléchargé',
    redownload: 'Réessayer',
    coursewareDownload: 'Téléchargement des supports de cours terminé',
    onlineCourseware: 'Cours en ligne',
    onlineCoursewareContent: `Utilisez les supports de cours en ligne uniquement lorsque le téléchargement des supports hors ligne n'est pas possible, mais cette méthode n'est pas fiable.`,
    confirmUser: `Confirmer`,
    coursewareDownloading:
      'Téléchargement des supports de cours en cours, {speed}, reste {minutes}',
    coursewareDownloadingSlow: 'Le temps est supérieur à 60 minutes',
    coursewareDownloadWeek: 'Échec du téléchargement des supports de cours, veuillez réessayer',
    coursewareDownloadWeekTitle: 'Échec du téléchargement des supports de cours',
    coursewareDownloadWeekContent:
      'Veuillez vérifier votre réseau et essayer de télécharger à nouveau',
    playbackError: `Ce cours n'a pas de rediffusion ou la rediffusion n'a pas encore été générée`,
    minute: 'minute',
    second: 'seconde',
    waiting: 'Veuillez attendre la fin du téléchargement des supports de cours',
    networkBtn: {
      good: 'Bon',
      normal: 'Normal',
      retest: 'Refaire le test du réseau',
      weak: 'Faible'
    },
    startAgo: 'Le cours a commencé il y a {changeMinutes} minutes, entrez dans la salle de classe',
    willStart: ' Commencera dans {changeMinutes} minutes',
    missClass: 'Si vous avez manqué le cours en direct, veuillez regarder la rediffusion',
    joinClass: 'Rejoindre le cours',
    continue: 'Continuer',
    confirm: 'Confirmer',
    downloadTip: {
      coursewareDownloading: 'Téléchargement des supports de cours',
      downloadReson: `Télécharger les supports de cours à l'avance peut économiser du temps en classe`,
      incomplete: ' Le téléchargement des supports de cours est incomplet',
      downloadComplete: 'Téléchargement terminé',
      DownloadFailed: 'Échec du téléchargement'
      // subtitle: 'Télécharger les supports de cours à l'avance peut économiser du temps en classe'
    },
    checkIn: {
      onTime: `Arrivez à l'heure en classe`,
      title: 'Enregistrement',
      missTips: [
        'Le cours a déjà commencé，',
        `vous ne pouvez pas obtenir la récompense d'enregistrement:(`
      ],
      firstCheckInMessage: `Veuillez d'abord vous enregistrer`,
      checkInButtonName: 'Enregistrement',
      checkInSuccess: 'Enregistrement réussi',
      checkInFail: `Échec de l'enregistrement`
    }
  }
}
