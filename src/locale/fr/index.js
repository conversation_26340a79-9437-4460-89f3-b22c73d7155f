import common from './common'
import login from './login'
import courses from './courses'
import setting from './setting'
import account from './account'
import classroom from './classroom'
import today from './today'
import views from './views'
import growthHandbook from './growthHandbook.js'
import blindBox from './blindBox.js'
import myBackpack from './myBackpack.js'

export default {
  common,
  login,
  setting,
  courses,
  account,
  classroom,
  today,
  growthHandbook,
  blindBox,
  myBackpack,
  ...views,
  liveroomMicFirstTipAlertTitlePC:
    "Maintenez la barre d'espace ou le bouton du microphone enfoncé pour parler",
  liveroomMicFirstTipAlertDoneTitle: 'Compris',
  liveroomMicSpeakingHUDTitle: 'En train de parler',
  liveroomKeepMuteModeToast: 'Le professeur a coupé le microphone de tout le monde',
  liveroomPushToTalkModeToast: 'Le professeur a activé le microphone de tout le monde',
  liveroomKeepMicOnModeToast: 'Le professeur a réglé le microphone en mode Appuyez-pour-Parler',
  老师已将麦克风设置为自由发言模式: 'Le professeur a réglé le microphone en mode Micro ouvert',
  liveroomHoldDownMicBtnWhenSpeakingToastForPC:
    "Veuillez maintenir la barre d'espace ou le bouton du microphone enfoncé lorsque vous parlez",
  liveroomDisableOthersVideoMenu: 'Muet Vidéo Autres',
  磁盘可用空间不足: 'Espace disque insuffisant, veuillez nettoyer le disque'
}
