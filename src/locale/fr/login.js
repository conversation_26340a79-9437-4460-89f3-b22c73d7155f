// 登录页
export default {
  // 通用
  common: {
    email: 'E-mail',
    emailAddress: 'Adresse e-mail',
    phoneNumber: 'Numéro',
    phoneNumberOrEmail: 'Téléphone portable ou e-mail',
    verificationCodePlaceholder: 'Code de vérification',
    passwordPlaceholder: 'V<PERSON><PERSON><PERSON> entrer le mot de passe de connexion',
    studentId: `Num<PERSON>ro d'étudiant`,
    signIn: 'Connexion',
    inputInvalid: ' invalide.',
    incorrectId: `<PERSON><PERSON><PERSON><PERSON> d'étudiant ou mot de passe incorrect, veuillez réessayer`,
    loginAgain: 'Connexion à nouveau',
    emailReg: 'Veuillez entrer une adresse e-mail valide.',
    continue: 'Continuer avec',
    是否还没有账号: "Vous n'avez pas encore de compte?",
    立即注册: 'Inscription',
    已经注册过账号: 'Vous avez déjà un compte？',
    立即登录: 'Connexion',
    注册: 'Inscrivez-vous.',
    我们向您发送了一个验证码: 'Nous vous avons envoyé un code de vérification',
    请在下方输入以进行验证: 'Veuillez le saisir ci-dessous pour vérifier {phone}',
    您似乎还未注册: 'Il semble que vous ne vous soyez pas inscrit.',
    您已经注册过账号: 'Vous avez déjà un compte?',
    切换其他登录方式: 'Autres méthodes de connexion',
    找到X个可能是您的账号: 'Trouver le compte {num} qui pourraient être le vôtre',
    如果是您的账号:
      "S'il s'agit de votre compte, nous vous suggérons de vous connecter directement. Sinon, veuillez créer un nouveau compte",
    已存在的账号: 'Compte existant',
    继续创建新账号: 'Créer un nouveau compte',
    您是否要更新现有账户: 'Souhaitez-vous mettre à jour le compte existant？',
    将此注册信息更新到现有账户:
      "Mettre à jour ces informations d'inscription pour le compte existant",
    账号更新成功: 'réussi',
    手机: 'Téléphone portable',
    邮箱: 'E-mail'
  },
  // 验证码登录页
  verificationCodePage: {
    title: 'Connexion avec code de vérification',
    description: `Numéros non inscrits seront automatiquement inscrits`,
    emailDesc: `Un compte sera automatiquement créé pour l'adresse e-mail qui n'a pas été enregistrée après la connexion.
`,
    footerTitle: 'Connexion avec code de vérification',
    skip: 'Passer',
    send: 'Envoyer',
    reSend: 'Renvoyer',
    back: 'Annuler',
    verificationTips: {
      confirmPolicy: `Veuillez cocher la case pour accepter les conditions d'utilisation, la politique de confidentialité et la politique de confidentialité pour enfants ci-dessous avant de continuer`,
      enterAccount: 'Veuillez entrer votre compte',
      enterPhoneNumber: 'Veuillez entrer votre numéro de téléphone',
      phoneNumberIncorrect: 'Numéro de téléphone incorrect',
      enterVerificationCode: 'Code de vérification par SMS'
    }
  },
  // 密码登录页
  passwordPage: {
    title: 'Connexion avec mot de passe',
    mobileDescription: `Définissez un mot de passe pour se connecter`,
    studentIdDescription: `Une fois connecté, vous pourrez trouver le numéro d'étudiant de votre enfant dans "mon compte"`,
    footerTitle: 'Connexion avec mot de passe',
    forgotPassword: 'Mot de passe oublié?',
    confirmPolicyTip: `Veuillez cocher les accords d'utilisateur, la politique de confidentialité et la politique de confidentialité des enfants ci-dessous pour continuer`,
    otherLoginType: `Se connecter avec d'autres méthodes`
  },
  // 协议信息
  policyInfo: {
    desc: `Veuillez d'abord cocher nôtre`,
    and: 'et'
  },
  // 踢出提示
  kickoutNotice: {
    message: 'Pour la sécurité de votre compte, veuillez vous reconnecter'
  }
}
