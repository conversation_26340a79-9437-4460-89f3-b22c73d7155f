// 课程中心
export default {
  // 课程列表页
  list: {
    title: 'Mes cours',
    titleTip: `Vous êtes actuellement à l'ecole de Mars`,
    tabList: {
      all: 'Tout',
      current: 'En cours',
      completed: 'Cours terminé'
    },
    courseTabList: {
      live: 'Cours en direct',
      record: 'Cours enregistré'
    },
    noCourses: 'Pas de cours disponibles / Aucun cours dans cette catégorie',
    switchSingleAccount:
      'Votre sous-compte{account} a des cours disponibles，cliquez pour basculer',
    switchMultipleAccount: 'Vous avez des cours dans plusieurs sous-comptes, cliquez pour changer',
    switchMultipleSchool:
      'Vous avez des cours dans plusieurs écoles, vous pouvez cliquer pour changer',
    switchSingleSchool: `Il y a des cours sous l'école {school}, vous pouvez cliquer pour changer.`,
    switchAccountText: `Changer d'élève`,
    switchSchoolText: 'Changer de école ',
    planName: 'Leçon'
  },
  lessonType: {
    formal: 'Cours formel',
    playback: 'Rediffusion',
    audition: 'un cours en auditeur libre',
    temporary: 'leçon temporaire'
  },
  // 课程卡片
  courseCard: {
    classInSession: 'En cours',
    nextSession: 'Prochain cours',
    enter: 'Entrer',
    completed: 'Terminé',
    refunded: 'Remboursé',
    playback: 'rejouer',
    startAuditing: 'Assister à des cours',
    view: 'Voir',
    reportTips: 'Le rapport de fin de cours pour "{course}"     a été généré.',
    parentAudit: 'Pour les parents',
    alreadyStarted: `La session {courseNum} a déjà commencé`,
    nextLesson: 'prochaine leçon'
  },
  // 录播课
  recordCourse: {
    courseValidUntil: `Ce cours est valable jusqu'à :`,
    enter: 'Entrer',
    refunded: 'Remboursé',
    expired: 'Expiré',
    expiredTip: 'Ce cours est expiré',
    permanentTip: 'Ce cours est valable en permanence',
    goToTest: 'Répondre',
    report: `Rapport d'examen`,
    reload: 'Recharger',
    loadFailed: 'Échec du chargement',
    loading: 'Chargement en cours',
    saveShotScreen: 'Chargement en cours',
    switchChannel: 'Changer de ligne',
    playNextVideo: 'Jouer la vidéo suivante'
  },
  // 录播课播放器提示
  recordPlayerTips: {
    0: 'Jouer',
    '0-1': 'pauser',
    1: 'Avancez de 10 secondes',
    2: 'Reculer de 10 secondes',
    9: `Capture d'écran`
  },
  // 课程详情页
  detail: {
    title: 'Détails du cours',
    specialTitle: '{prefix}Détails',
    // 播放按钮
    playButton: {
      // 未开始状态
      startSoon: 'Pas encore commencé',
      playbackGenerating: 'Génération',
      playback: 'Regarder la rediffusion',
      playbackExpired: 'La rediffusion a expiré',
      unpaid: 'Non acheté',
      live: 'Entrer',
      finished: 'Terminé',
      startAuditing: `Faire de l'audition`
    },
    classRoom: 'salle de classe',
    homework: {
      status: [
        'En attente de publication',
        'En attente de soumission',
        'Corrigé',
        'Soumis',
        'Expiré',
        'Voir les réponses',
        'En attente de soumission'
      ],
      title: 'devoirs'
    },
    exam: {
      status: ['Non publié', `Aller à l'examen`, 'Répondu', 'Corrigé', 'Expiré']
    },
    examTip: `L'examen n'a pas encore été publié.`,
    resources: 'Données',
    playBackTip: 'Diffusion disponible 2 heures après la fin du cours en direct',
    reportBtnText: 'Corrigé',
    reportSubmitText: 'Soumis',
    reportExpiredText: 'Expiré',
    startTest: `Allez à l'examen.`,
    reportTips: `Le rapport sera disponible après {canViewTime}.`,
    lessonReport: 'Rapport de classe',
    expiredTips:
      'Le délai de soumission des devoirs est dépassé, veillez à soumettre vos devoirs à temps la prochaine fois.',
    file: 'fichier',
    files: 'fichiers',
    transferred: 'Changement de cours',
    classNotes: 'Tableau',
    learningMaterials: `Matériel d'étude`
  },
  switchAccount: {
    title: `Changer d'élève`,
    subTitle: 'Vous avez des cours sous les sous-comptes suivants, cliquez pour basculer.',
    cancel: 'annuler',
    confirm: 'Confirmer',
    switchSuccess: 'Changement réussi',
    switchFail: 'Échec de la commutation, veuillez réessayer plus tard',
    switchIng: 'En cours de commutation......',
    emptyTip: 'Veuillez sélectionner un compte étudiant',
    loginTitle: 'Cette famille compte plusieurs étudiants',
    loginSubTitle: 'Veuillez choisir quel étudiant voulez-vous vous connecter?'
  },
  switchSchool: {
    title: 'Changer de campus',
    subTitle: 'Vos succursales suivantes proposent des cours. Cliquez pour changer.'
  },
  // 面授课 - 如何上课
  faceToFace: {
    attendClass: 'Voir le mode de cours',
    finished: 'Terminé',
    location: 'Lieu',
    copy: 'Copier',
    zoomId: 'Zoom ID',
    zoomPassword: 'Mot de passe Zoom',
    zoomDownload: 'Télécharger Zoom',
    zoomDescribe: `Et comme mentionné précédemment, entrez l'identifiant Zoom et le mot de passe. Rejoignez la classe 10 minutes avant la réunion en direct sur Zoom et profitez de votre expérience ThinkAcademy.`,
    classinLink: 'Lien Classin',
    classinDownload: 'Télécharger Classin',
    classinDescribe: `Et inscrivez-vous à l'aide de votre compte lors de l'inscription au cours. Rejoignez la classe 10 minutes avant la réunion en direct sur Classin et profitez de votre expérience ThinkAcademy.`,
    classroomDescribe: `Veuillez arriver 10 minutes à l'avance dans la salle de classe pour profiter de votre expérience ThinkAcademy.`,
    ipadDescribe: `Veuillez utiliser l'application Thinkhub sur iPad pour les cours. Si vous avez des questions, veuillez appeler.`,
    confirm: 'confirmé'
  },
  // 课后作业
  assignment: {
    title: 'devoirs',
    earnedNotice: 'Vous avez obtenu',
    submissionNotice: 'Terminer en avance, récompense supplémentaire'
  },
  // 课前测
  previewQuestion: {
    title: 'Test préliminaire',
    deadlineName: 'Date limite',
    deadlineTime: 'temps limite',
    answerTimeName: 'Temps de réponse',
    deadlineBannerTips:
      'Vous avez dépassé la date limite de réponse, veuillez répondre à temps la prochaine fois !',
    problemDescriptors: [`Description de la question`, `(sur un total de {totalScore} points)`],
    confirmTitle: 'Êtes-vous sûr de vouloir commencer à répondre ?',
    confirmSubtitle: `Temps de réponse total de {duration}, allez-y!`,
    solutions: 'solutions',
    start: 'commencer',
    deadlineTitle: `Le test est terminé, vous n'avez pas participé à temps.`,
    deadlineDesc:
      'Oh là là, vous avez dépassé la date limite de réponse, veuillez répondre à temps la prochaine fois.'
  },
  // 学习资料
  studyResources: {
    title: `Matériel d'étude`,
    download: 'Télécharger',
    play: 'Lire'
  },
  // 课堂报告
  lessonReport: {
    title: 'Rapport en classe'
  },
  // 阶段考试
  exam: {
    title: `Remarques d'examen`,
    solutions: `Réponses`,
    report: 'Affichez le rapport',
    start: 'commencer',
    coinsTip: `Répondez et obtenez une récompense en pièces d'or.`,
    confirmTitle: 'Rappel de réponse',
    confirmSubtitle: `Temps de réponse total : {duration}, allez-y !`,
    confirmBtn: 'Confirmer',
    cancelBtn: 'canceller'
  },
  // 临时教室
  temporaryClassroom: {
    title: 'Salle de classe provisoire',
    noCourses: 'Aucun cours disponible',
    registrationCourses: 'Inscription au cours',
    noCoursewareNotice: `Ce cours n'est pas lié à un support de cours.`,
    statusName: {
      startSoon: 'Pas encore commencé',
      playbackGenerating: 'Génération',
      playback: 'Regarder la rediffusion',
      playbackExpired: 'La rediffusion a expiré',
      enter: 'Entrer'
    }
  },
  confirmModal: {
    title: 'Rappel de cours',
    content: `L'étudiant correspondant au compte actuel est déjà en cours en direct. si vous choisissez de rejoindre le cours, vous serez {warning} veuillez choisir si vous souhaitez continuer à rejoindre le cours ?`,
    warning: 'Expulser les comptes existants de la session de cours en direct，',
    enterBtn: 'Confirmation',
    backBtn: 'annulation',
    confirm: 'confirmer'
  },
  switchSchoolModal: {
    title: 'Rappel',
    content: 'Voulez-vous passer à cette école  ?'
  },
  resolutionRatioModal: {
    title: 'Rappel',
    content: `Votre résolution d'écran actuelle est inférieure à la résolution minimale requise par ThinkAcademy.\nVeuillez réduire l'échelle d'affichage dans les paramètres système - Affichage.`,
    confirm: 'confirmé'
  },
  playback: {
    courseInformationError: 'Échec de récupération des informations sur le cours',
    playbackInformationError: 'Échec de récupération des informations de  rediffusion',
    playbackInterfaceError: `Erreur d'accès à l'interface de rediffusion`,
    playbackSourceIsNull: `L'adresse de rediffusion est vide`,
    playbackStampError: `Échec de l'obtention des informations de pointage`
  },
  noCoursesWare: `Il n'y a pas de matériel de cours pour cette session.`,
  // 教师联系方式
  teacherContact: {
    tip: 'Si vous avez des questions, veuillez contacter votre professeur.',
    copy: 'Copier',
    addContact: 'Veuillez scanner le code QR pour ajouter le professeur sur WeChat.'
  },
  // 课堂评价
  nps: {
    feedback: 'Évaluation du cours',
    enjoyTip: 'As-tu apprécié cette leçon ?',
    contactTip: `Vous nous dites la raison peut aider ThinkAcademy à s'améliorer`,
    reason: 'Vous pouvez écrire ici les raisons détaillées.',
    submitBtn: 'Soumettre',
    submitSuccess: 'Soumis avec succès, merci pour vos commentaires'
  },
  wrongQuestionBook: {
    title: `Cahier d'erreurs`
  },
  expand_fold: 'voir moins',
  expand_more: 'Plus',
}
