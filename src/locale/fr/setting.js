// setting
export default {
  // 设置菜单
  menus: {
    settings: 'Paramètres',
    update: 'Mise à jour',
    cleanCache: 'Effacer le cache',
    aboutUs: 'À propos de nous',
    reportLogs: 'Télécharger le journal',
    feedback: `Feedback`,
    logout: 'Déconnexion',
    checkDevice: `Test de l'appareil`
  },
  // 关于我们
  aboutUs: {
    // 隐私协议
    policyTips: {
      temsOfUse: `les Conditions d'Utilisation`,
      privacyPolicy: 'la Politique de Confidentialité',
      internetSafetyPolicy: 'politique de sécurité sur internet',
      childPrivacy: 'politique de confidentialité pour les enfants'
    }
  },
  // 设置
  settings: {
    savePicture: `Emplacement de sauvegarde de la capture d'écran`,
    chooseCampus: 'Système éducatif',
    chooseLanguage: 'Choisir la langue',
    browse: 'Parcourir',
    cancel: 'Annuler',
    confirm: 'Confirmer',
    renderMode: 'Mode de rendu',
    oneRender: 'Rendu unique',
    twoRender: 'Rendu double',
    采集模式: 'Mode de collecte',
    通用: 'Universel',
    兼容: 'Compatible'
  },
  // 反馈
  feedback: {
    title: `Retour d'information`,
    problemDescriptorText: 'Description du problème',
    problemDescriptors: [
      'Fonctionnement anormal',
      'Suggestion de fonctionnalité',
      'Enseignement',
      'Expérience globale',
      'Cours en direct',
      'Autres problèmes'
    ],
    feedBackInput: 'Feedback (Required)',
    images: 'Images',
    optional: 'Optionnel',
    maximuImages: 'Maximum {num} images',
    maxFileSize: 'La taille du fichier dépasse 10 Mo, veuillez réessayer !',
    fileFormatIncorrect: 'Format de fichier incorrect, veuillez réessayer !',
    uploadFailTryAgain: 'Échec du téléchargement du fichier, veuillez réessayer !',
    submit: 'Soumettre',
    backToCourses: 'Retourner à mes cours',
    inputInvilidate: `Veuillez fournir des informations supplémentaires sur votre retour d'information ici`
  },
  // 版本相关
  version: {
    versionChecking: 'Vérification de la version...',
    version: 'Version',
    alreadyLastUpdated: 'Vous êtes déjà à jour',
    newVersion: 'Nouvelle version',
    nextTime: 'Prochaine mise à jour',
    updateNow: 'Mettre à jour maintenant',
    downloadFailed:
      'Téléchargement échoué, veuillez télécharger la dernière version depuis notre site officiel',
    byClickingHere: 'en cliquant ici',
    checkFailMessage: 'La vérification de la mise à jour a échoué, veuillez réessayer!',
    betaVersion: 'Version bêta',
    downloading: 'Téléchargement du fichier de mise à jour...',
    wait: `Installation de la nouvelle version en cours, veuillez patienter pendant 2-3 minutes...`,
    winWait: `Nouvelle version téléchargée, suivez les instructions pour l'installer.`
  },
  // 清除缓存
  cleanCache: {
    content: 'Êtes-vous sûr de vouloir effacer le cache du cours ?',
    successMessage: 'Cache vidé',
    failMessage: 'Échec de la purge, veuillez réessayer'
  },
  // 上报日志
  reportLogs: {
    content: 'Êtes-vous sûr de vouloir envoyer un rapport de log ?',
    logSubmitted: 'Log envoyé, merci pour votre remarque.',
    uploadfailed: `L'envoi a échoué, veuillez réessayer`
  },
  // 登出
  logout: {
    content: 'Êtes-vous sûr de vouloir vous déconnecter ?'
  },
  // 时区
  timezone: {
    current: 'Fuseau horaire actuel :',
    choose: 'Choisir un fuseau horaire',
    search: 'Rechercher un fuseau horaire',
    noResult: 'Aucun résultats trouvés.',
    invalidMsg: 'Fuseau horaire introuvable, le fuseau par défaut a été sélectionné :'
  },
  localsetting: {
    chooseTitle: 'Choisir un système éducatif',
    chooseDesc: 'Choisissez le système éducatif ou la région qui convient à votre enfant'
  }
}
