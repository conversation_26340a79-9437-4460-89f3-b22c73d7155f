export default {
  handBook: 'Growth Adventure',
  growthLevel: 'Growth Level',
  growthTask: 'Growth Task',
  guideTitle: 'How to enjoy Growth Adventure？',
  gotIt: 'Got it!',
  skip: 'Skip',
  weekTask: 'Weekly Task',
  challengeTask: 'Challenge',
  activeTask: 'Bonus Task',
  process: 'Progress',
  unfinished: 'In progress',
  drawDown: 'Collect',
  claimed: 'Collected',
  highestLevel: "You've reached the highest level",
  needExp: 'You need {exp} XP to reach next level',
  tasksCompleted: 'Complete {count} tasks',
  tasksUncompleted: 'In progress',
  congratulations: "You've earned the following items",
  accept: 'Got it!',
  startChallenge: 'Begin the challenge',
  countDownTextForDay: 'There are {days} day(s) and {hours} hour(s) left for this week',
  countDownTextForHour: 'There are {hours} hour(s) left for this week',
  countDownTextForMinute: 'There are {minutes} minute(s) left for this week',
  requestError: 'Request timed out',
  backHome: 'Back to home',
  rewardCollected: 'Reward collected',
  systemError: 'System error (Error code: {code})',
  getExpDesc: 'Complete tasks to earn experience points',
  upgradeDesc: 'Level up to unlock more fun rewards!',
  toClassDesc: 'Attending classes',
  doHomeworkDesc: 'Completing Homework',
  upgradeTitle: 'Level Up',
  initUpgradeTitle: `Well done! You've unlocked the starting level.`,
  exp: 'XP',
  coin: 'Coin',
  retry: 'Retry',
  networkErr: 'Network error. Please connect to the internet.',
  摄像头初始化: 'Camera is initializing, please retry in 3 seconds.'
}
