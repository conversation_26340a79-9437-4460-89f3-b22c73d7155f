// 通用配置
export default {
  // 公共文案配置
  // 请勿修改, 如正在使用的公共文案有变化, 请重新取名定义
  MasterTeacher: 'Tutor',
  AssistantTeacher: 'Tutor Assistant',
  loading: 'Loading',
  uploading: 'Uploading',
  me: 'Me',
  i: 'I',
  reload: 'Reload',
  cancel: 'Cancel',
  send: 'Send',
  yes: 'Yes',
  no: 'No',
  exit: 'Exit',
  confirm: 'Confirm',
  refresh: 'Refresh',
  back: 'Back',
  submit: 'Submit',
  done: 'Done',
  teacherWatching: 'Teacher checking',
  submiting: 'Submiting',
  coins: 'Coins',
  gotIt: 'Got it',
  retry: 'Retry',
  uploadFailed: 'Upload failed',
  sumbittedSuccessfully: 'Submit successfully',
  submissionFailed: 'Submission failed',
  submittedError: 'Submitted Error',
  savedSuccessfully: 'Saved successfully',
  saveFailedTryAgain: 'Save failed, Please try again',
  systemErrorTryAgain: 'System error, Please try again',
  copy: 'Copy',
  save: 'Save',
  preview: 'Preview',
  reUpload: 'Re-upload',
  redeemNow: 'Redeem Now',
  错题本: 'Mistakes',
  agree: 'Agree',
  deny: 'Deny',
  helpOthers: 'The teacher is helping others',
  speedyHand: "No one answered. Let's play together next time!",
  onStage: 'On Stage',
  noNetworkAndRetry: 'Unable to connect to the Internet, please check your network',
  cacheUpdate: 'Restart the application to update app data',
  updateTitle: 'Restart Application',
  restart: 'Restart',
  // 公共组件配置
  components: {
    // 错误状态组件
    errorStatus: {
      message: 'Sorry, service error, please refresh and try again'
    }
  },
  networkErr: 'Network Error'
}
