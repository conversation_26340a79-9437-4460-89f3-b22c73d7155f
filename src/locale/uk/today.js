// 首页
export default {
  today: 'Today',
  courses: 'Courses',
  homework: {
    task: 'Task',
    homework: 'Homework',
    exercise: 'Review Exercise',
    preview: 'Preview',
    exam: 'Exam',
    endTimeTips: '{remainTime} days left until the submission deadline',
    unSubmitTips: 'Submit by {submitTime}',
    exercise_not_start: 'Not started',
    exercise_in_progress: 'In progress',
    unSubmit: 'Unsubmitted',
    submitted: 'Submitted',
    reviewed: 'Reviewed',
    noHomeworkTip: 'No homework or tests need to be done right now'
  },
  lessonPlan: {
    title: 'Schedule',
    viewCourses: 'Go to all courses',
    noCourses: 'No courses for the next 30 days',
    reportTips: 'A Class Performance for you!',
    view: 'View',
    timeTextsMap: {
      RESCHEDULE: 'Rescheduled',
      AUDIT: 'Audit',
      COMPLETE: 'Completed'
    },
    lessonModal: {
      generating: 'Playback generating',
      expired: 'Playback expired',
      over: 'Class completed',
      notStart: 'Class not started',
      courseBegin: 'The course has already started',
      enter: 'Enter',
      playBack: 'Playback'
    },
    officeHour: 'OFFICE HOUR'
  },
  growthHandbook: {
    title: 'Growth Adventure',
    newTerm: 'New Term',
    new: 'New',
    detail: 'View'
  },
  coinsMall: 'Coins Mall',
  wrongQuestionBook: 'Notebook',
  classOverModal: {
    coins: 'Coins earned',
    home: 'Back to home',
    noPublishLesson: "Teacher didn't publish questions in this lesson.",
    gotIt: 'Got it'
  },
  viewed: "You've viewed this Class performance report!",
  assignment_15_64j : 'Assignment',
	log_out_15_vtt : 'Log out',
	home_page_15_6gc : 'Home',
	correction_book_15_875 : 'Correction Book',
	learn_plan_15_f4u : 'Learn Plan',
	last_class_15_i1j : 'Last Lesson',
	next_lesson_15_xv3 : 'Next Lesson',
	welcome_15_ztp : 'Welcome',
	in_progress_15_754 : 'In progress',
	starting_soon_15_la9 : 'Starting Soon',
	need_to_use_another_account_click_here_to_switch_15_9fg : 'Need to use another account? Click here to switch.',
	not_started_15_aqp : 'Not started',
	okay_15_bp5 : 'Got it',
	already_ended_15_p1g : 'Already ended',
	recent_lessons_15_s6n : 'Recent Lessons',
	completed_courses_15_0vt : 'Completed Courses',
	my_courses_15_b44 : 'My Courses',
	download_think_academy_app_15_i45 : 'Download Think Academy APP',
	enjoy_the_full_experience_15_0ml : 'Enjoy the full experience',
	please_select_student_account_15_6fb : 'Please select the student account',
	sorry_this_course_requires_think_academy_app_15_2dh : 'Sorry, this course requires the Think Academy app to join the live classroom. We are working on supporting access via the website in the future.',
	sorry_course_playback_only_available_think_academy_app_future_web_support_15_erc : 'Sorry, the playback for this course is currently only available on the Think Academy app. We are working on supporting playback via the website in the future.',
	session_expired_please_log_in_again_15_6qp : 'Your session has expired. Please log in again.',
	login_status_changed_please_refresh_15_gxu : 'Your login status has changed. Please refresh this page.',
	confirm_15_ifl : 'Confirm',
	ready_for_class_15_mye : 'Ready for Class',
	enable_camera_microphone_class_15_hkq : 'Turn on the camera and microphone to join the class interaction.',
	browser_not_supported_15_ypi : 'This browser is not supported',
	outdated_browser_update_15_boa : 'You are using an outdated or unsupported browser, which may not display this website correctly. For a better experience, please update your browser or switch to a modern one (such as Chrome, Edge, Safari, etc.).',
	camera_15_ov3 : 'Camera',
	audio_15_lbj : 'Audio',
	device_15_stl : 'Device',
	preview_15_o2m : 'Preview',
	microphone_15_prn : 'Microphone',
	speaker_15_oid : 'Speaker',
	detect_15_to4 : 'Detect',
	enter_class_15_ls6 : 'Enter Class',
  no_recent_upcoming_courses_15_vbl : 'No recent or upcoming courses.',
	no_courses_or_completed_15_l9h : 'No courses enrolled or courses completed',
	enroll_course_15_hj4 : 'Enroll in a course',
  view_15_7ot : 'View',
}
