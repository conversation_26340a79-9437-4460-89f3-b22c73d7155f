/**
 * 模块配置
 */
export default {
  // 导航
  header: {
    // 返回确认窗口
    backConfirm: {
      content: `The teacher is in class. Are you sure you want to quit the live broadcast of the class ?`,
      exitByCoursewareIdChange: `There's something wrong with the courseware, please re-enter the classroom.`
    }
  },
  // 反馈
  feedback: {
    headerName: 'Report an issue',
    optionNames: ['Study question', 'APP problem', 'Inappropriate behavior', 'Others'],
    placeholder: `1.Select the above label
2.Please describe your problem and assist the teacher to solve the problem quickly`,
    screenshotTips: 'I agree to send a screenshot of the page to the tech team.',
    sendSuccessNotice:
      'Thank you for your feedback, we will be in touch with you/your parents soon',
    defaultMsg: 'I send a feedback',
    playback: {
      header: 'Feedback',
      placeholder: 'Please describe your problem and assist us to solve it quickly.',
      screenshotTips: 'I agree to send screenshot and device information to the tech team.',
      cancel: 'Cancel',
      send: 'Send',
      success: 'Submit successfully',
      failed: 'Submit failed'
    }
  },
  // 设备检测
  deviceTest: {
    dialogTitle: 'Device testing',
    statusNames: {
      disabled: 'disabled',
      usable: 'usable',
      unauthorized: ' unauthorized'
    },
    cameraTitle: 'Camera detection: ',
    microphoneTitle: 'Microphone detection: ',
    audioTitle: 'Speaker detection: ',
    authorizeGuide: ['Go to authorize, ', 'Click on the link']
  },
  checkDevice: {
    network: 'Network',
    microphone: 'Microphone',
    camera: 'Camera',
    voice: 'Sound',
    网络满足上课需求: 'Your network meets the system requirements',
    网络不满足上课需求:
      'There seems to be something wrong with your network. Check the solution suggestions',
    你的设备满足上课需求: 'The current device meets the requirements for class',
    你的设备不满足上课需求:
      'It seems there is an issue with your internet connection. Click here for suggested solutions.',
    音视频测试: 'Live streaming service',
    课堂互动测试: 'Interaction service',
    服务器检测: 'Server connection speed',
    课件下载测试: 'Courseware download',
    此项测试大约需要花费一分钟: 'This may take 1 ~ 3 minutes',
    常见网络问题建议: 'Solutions to network problems',
    尝试重启路由器:
      'Try to restart your Wi-Fi router and get closer to your Wi-Fi router if use wireless network.',
    检查网络权限: 'Check and make sure your device allows Think Academy to connect to the internet',
    切换其它网络: 'Try switching to another network',
    重新测试: 'Retest',
    下一项: 'Next',
    没有麦克风权限: 'Microphone access not enable',
    在设置中打开麦克风权限:
      'Please allow Think Academy to access your microphone in the system settings',
    在设置中打开摄像头权限:
      'Please allow Think Academy to access your camera in the system settings',
    没有找到可用的麦克风: 'No microphone available',
    请检查你的麦克风或者佩戴耳机: 'Please check your  microphone settings or wear headphones',
    尝试你大声读出下面的文字: 'Try reading the words below to test your microphone',
    你好: 'Hello',
    你的麦克风似乎存在问题: 'There seems to be a problem with your microphone',
    我们没有听到你的声音:
      "We didn't hear your, check your microphone settings or try using headphones",
    麦克风检测完成: 'Microphone test complete',
    你的麦克风工作正常: 'Your microphone is working properly',
    去设置: 'Settings',
    没有摄像头权限: 'Camera access not enable',
    没有找到可用的摄像头: 'No camera available',
    请检查你的摄像头:
      'Please check whether the camera is installed correctly or switch another device',
    可以在上面看到自己吗: 'Can you see yourself in the video above ?',
    正在使用摄像头: 'Currently camera is working',
    你的摄像头似乎存在问题: 'There seems to be a problem with your camera',
    请检查你的摄像头是否正确安装:
      'Please check whether the camera is installed correctly or switch another device',
    摄像头检测完成: 'Camera test complete',
    你的摄像头工作正常: 'Your camera is working properly',
    当前音量: 'Volume: ',
    可以听到正在播放的音乐吗: 'Can you hear the sound being played？',
    请注意调整系统音量到适当大小: 'Please adjust the system volume to an appropriate level',
    能: 'Yes',
    不能: 'No',
    你的扬声器似乎存在问题: 'There may be a problem with your speakers',
    建议尝试佩戴耳机或者更换其它设备: 'Try using headphones or switch to another device',
    你的设备满足上课要求: 'This device meets the requirements of live class',
    你的设备不完全满足上课要求: 'This device does not meet the requirements of live class',
    继续使用此设备上课可能会降低你的体验: 'Using this device may compromise your class experience',
    知道了: 'Got it',
    你的网络是乎有些问题: 'There seems to be something wrong with your network.',
    解决方案: 'Check the solution suggestions'
  },
  // 网络状态提示
  networkStatus: {
    statusMap: {
      good: {
        title: 'Good internet connection'
      },
      normal: {
        title: 'Normal internet connection'
      },
      weak: {
        title: 'Weak internet connection',
        description: 'Trying to reconnect, or you can switch network or use another device.'
      }
    }
  },
  // 网络错误提示
  networkError: {
    notice: [
      'No network connection.',
      'Please check your internet connection or wait for the server reconnection'
    ]
  },
  // 课件组件
  courseware: {
    errorNotice: [
      'Courseware failed to load',
      'Your courseware failed to load.',
      'Please click "Reload" to try again'
    ]
  },
  // 截屏
  screenThumbnail: {
    successNotice: 'Saved to desktop'
  },
  // 媒体权限
  mediaSecurityAccess: {
    camera: `Allow Camera access to participate in class activities. Please confirm to allow access.`,
    microphone: `Please allow ThinkAcademy to access your microphone`,
    deniedAccess:
      'Allow Camera and Microphone access to interact with your teacher, and enter classroom activities. Please confirm to allow access.',
    tip: 'After allowing access, please restart the software',
    confirm: 'Allow',
    cancel: 'Deny'
  },
  // 踢出
  liveKickout: {
    notice: 'Your account has been logged in on another device. Please log in again'
  },
  // 下课提示
  classEnded: {
    title: 'Class Ended',
    content: 'This class has ended. Please return to the homepage',
    okText: 'OK'
  },
  // 定向金币
  orientationCoins: {
    notice: ['These students just got', 'coins!'],
    Congrats: 'Congrats!',
    others: 'and others',
    smallCongrats: 'Good job！'
  },
  // 作业盒子
  assignmentBox: {
    modalTitle: 'Basic Modal',
    nodataNotice: 'The exercise has not been initiated yet',
    checkBigPictures: {
      buttonName: 'Modify'
    },
    checkedExerciseToast: {
      notice: 'The VIP teacher has finished your exercise, come and take a look',
      buttonName: 'View Feedback',
      loadingName: 'picture loading',
      pictureDesc: `The {assisTeacher} has finished marking your work; let's review their feedback`
    },
    checkErrorTips: 'Not quite...Keep on trying!'
  },
  // 小班涂鸦板
  smallClassGraffitiCorrect: {
    yellow: 'yellow',
    red: 'red',
    blue: 'blue'
  },
  // 连续作答
  continuousCorrect: {
    rightLevelDesc: 'Get 1 more correct and advance to {nextTitle}',
    rightTopLevelDesc: `You've reached the highest level in this class!`,
    wrongLevelDesc: `Get 1 more correct and advance to {nextTitle}`,
    encourageNotice: 'Coins for your trying',
    noanswerNotice: 'Answer questions and get your badge level up!',
    continuousText: 'Correct, good job!',
    highestLevelText: 'Your badge has been upgraded to the highest level!',
    upgradedLevelText: 'Your badge has been upgraded to Lv {curLevel}',
    wrongTitleText: 'Oops',
    wrongDescText: 'Better luck next time～'
  },
  // 辅导连麦
  tutorVideoLink: {
    title: 'Private Video Call',
    errorMsg: ['Oops!', 'Something Wrong...']
  },
  // 系统出错
  systemError: {
    message: 'System error, please try again',
    noPlaybackMessage: 'Playback video not generated',
    refreshButtonName: 'Refresh',
    backButtonName: 'Back My Courses'
  },
  // 老师上台
  teacherOnStage: {
    notice: ['Humm…', 'your teacher will be back soon'],
    privateChat: 'Private Call'
  },
  // 课中考试
  ClassExam: {
    rankHeaderTitle: 'Leaderboard'
  }
}
