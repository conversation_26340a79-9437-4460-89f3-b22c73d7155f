/**
 * 国际化配置引入文件
 */

// import Vue from 'vue'
// import VueI18n from 'vue-i18n'
import { createI18n } from 'vue-i18n'
import us from './us'
import sg from './sg'
import uk from './uk'
import hk from './hk'
import cn from './cn'
import fr from './fr'
import ja from './ja'
import ko from './ko'
// import config from '@/utils/schoolMap.js'
// import { getLocalInfo } from 'utils/local'
// import { setLanguage, getLanguage } from '../utils/language'
// Vue.use(VueI18n)

// const localConfigs = {
//   en: us,
//   'zh-HK': hk,
//   'zh-TW': hk, // 'zh-TW' 语言包使用 'zh-HK' 语言包
//   'zh-CN': cn,
//   zh: cn,
//   'en-GB': uk,
//   'en-SG': sg,
//   'en-US': us,
//   ja: ja,
//   'ja-JP': ja,
//   'ko-KR': ko,
//   ko: ko,
//   fr: fr,
//   'fr-CA': fr,
//   'fr-CH': fr,
//   'fr-FR': fr
// }
const localConfigs111 = {
  uk: uk,
  sg: sg,
  us: us,
  hk: hk,
  fr: fr,
  kr: ko,
  jp: ja,
  cn: cn,
  ca: us,
  au: uk,
  my: uk,
  bmus: us,
  bmsg: sg,
  // todo 待确认不同分校对应的语言处理
  ae: hk,
  am: hk,
  ai: hk,
  tc: us,
  tmc: us
}
localConfigs111

const localConfigs = {
  en: us,
  'zh-HK': hk,
  'zh-TW': hk, // 'zh-TW' 语言包使用 'zh-HK' 语言包
  'zh-CN': cn,
  zh: cn,
  'en-GB': uk,
  'en-SG': sg,
  'en-US': us,
  ja: ja,
  'ja-JP': ja,
  'ko-KR': ko,
  kr: ko,
  fr: fr,
  'fr-CA': fr,
  'fr-CH': fr,
  'fr-FR': fr
}
const languageMap = {
  uk: 'en-GB',
  sg: 'en',
  us: 'en-US',
  hk: 'zh-HK',
  fr: 'fr-FR',
  jp: 'ja-JP',
  kr: 'ko-KR',
  cn: 'zh-CN',
  ca: 'en-CA',
  au: 'en-AU',
  my: 'ms-MY',
  bmus: 'en-US',
  bmsg: 'en',
  ae: 'en',
  am: 'en',
  tc: 'en',
  tmc: 'zh-CN'
}
const validateLanguage = [
  'en',
  'zh-HK',
  'zh-TW',
  'zh-CN',
  'zh',
  'en-GB',
  'en-SG',
  'en-US',
  'fr',
  'fr-FR',
  'fr-CA',
  'fr-CH',
  'ja',
  'ja-JP',
  'ko-KR',
  'ko'
]
// switch (locale) {
//   case 'uk':
//     localeConfig = await import('locales/uk')
//     break;
//   case 'sg':
//     localeConfig = await import('locales/sg')
//     break;
//   case 'us':
//     localeConfig = await import('locales/us')
//     break;
//   case 'hk':
//     localeConfig = await import('locales/hk')
//     moment.locale('zh-hk')
//     break;
//   case 'tmc':
//     localeConfig = await import('locales/tmc')
//     break;
//   case 'au':
//     localeConfig = await import('locales/au')
//     break;
//   case 'my':
//     localeConfig = await import('locales/my')
//     break;
//   case 'ca':
//     localeConfig = await import('locales/ca')
//     break;
//   case 'tc':
//     localeConfig = await import('locales/tc')
//     break;
//   case 'ai':
//     localeConfig = await import('locales/ai')
//     break;
//   case 'am':
//     localeConfig = await import('locales/am')
//     break;
//   case 'ae':
//     localeConfig = await import('locales/ae')
//     break;
//   case 'bmsg':
//     localeConfig = await import('locales/bmsg')
//     break;
//   case 'bmus':
//     localeConfig = await import('locales/bmus')
//     break;
//   case 'jp':
//     localeConfig = await import('locales/jp')
//     moment.locale('ja')
//     break;
//   case 'kr':
//     localeConfig = await import('locales/kr')
//     moment.locale('ko')
//     break;
//   case 'fr':
//     localeConfig = await import('locales/fr')
//     moment.locale('fr')
//     break;
//   default:
//     localeConfig = await import('locales/sg')
//     break;
// }
// const validateLanguage = [
//   'en',
//   'zh-HK',
//   'zh-TW',
//   'zh-CN',
//   'zh',
//   'en-GB',
//   'en-SG',
//   'en-US',
//   'fr',
//   'fr-FR',
//   'fr-CA',
//   'fr-CH',
//   'ja',
//   'ja-JP',
//   'ko-KR',
//   'ko'
// ]
const createFlexibleI18n = options => {
  const i18n = createI18n(options)

  // 支持多种插值语法：%{key}、{key}、${key} 等
  const INTERPOLATION_PATTERNS = [
    /%\{(\w+)\}/g, // %{key}
    /\{(\w+)\}/g, // {key}
    /\$\{(\w+)\}/g // ${key}
  ]

  const originalT = i18n.global.t
  i18n.global.t = function (key, params = {}) {
    const locale = this.locale
    const rawValue = key.split('.').reduce((o, k) => o?.[k], this.messages[locale])

    if (Array.isArray(rawValue)) {
      return rawValue.map(item => {
        if (typeof item === 'string') {
          let result = item
          // 尝试所有可能的插值语法
          INTERPOLATION_PATTERNS.forEach(regex => {
            result = result.replace(regex, (_, p1) => params[p1] ?? '')
          })
          return result
        }
        return item
      })
    }
    return originalT.call(this, key, params)
  }

  return i18n
}
const i18n = createFlexibleI18n({
  //legacy: false, // you must set `false`, to use Composition API
  messages: localConfigs,
  // locale: 'us',
  // fallbackLocale: config.fallbackLocale
  fallbackLocale: 'us',
  runtimeConfig: {
    replace: 'sprintf' // 启用 sprintf 插值支持 {key} 格式化
  }
})
const i18nLocaleUpdate = async function (localShort) {
  const lang = languageMap[localShort] || ''

  if (validateLanguage.includes(lang)) {
    i18n.global.locale = lang
  } else {
    i18n.global.locale = 'us'
  }
  console.info('打印系统分校简码', localShort, lang, validateLanguage.includes(lang), i18n) // 打印系统语言

  // const lang = await getLanguage()
  // i18n.locale = lang
  // todo 传过来的是分校，如何通过分校获得分校默认的语言呢
  // if (lang) {
  //   console.info('打印系统语言-有默认语言', lang)
  //   if (validateLanguage.includes(lang)) {
  //     console.info('打印系统语言-有默认语言-有效', lang)

  //     i18n.locale = lang
  //   } else {
  //     console.info('打印系统语言-有默认语言-无效', lang)
  //     i18n.locale = 'en'
  //   }
  // } else {
  //   console.info('打印系统语言-无默认语言', lang)
  //   // 通过electron 获取当前语言
  //   const locale = lang
  //   // await ipcRenderer.invoke('GET_LOCAL')
  //   console.info('打印系统语言', locale, lang) // 打印系统语言

  //   if (validateLanguage.includes(locale)) {
  //     console.info('打印系统语言-有当前语言', locale, lang)
  //     i18n.locale = locale
  //   } else {
  //     console.info('打印系统语言-无当前语言', validateLanguage, locale, lang)
  //     i18n.locale = 'en'
  //   }
  // }
  // console.info('打印系统语言-i18n-locale-update', i18n.locale)
  // setLanguage(i18n.locale)
}

// const getLocalConfig = async function() {
//   const localInfo = await getLocalInfo()
//   const language = await getLanguage()
//   return localConfigs[language || localInfo.schoolISOStandardlanguageCode]
// }

export { i18n, i18nLocaleUpdate }

// /* eslint-disable no-param-reassign */
// /**
//  * 检测设置分校标识插件
//  */

// import config from 'config/index';
// import localeConfig from 'config/localeConfig'

// export default function ({ req, app, store }) {
//   // 客户端环境不执行
//   if (process.client) return;

//   // 国家域名语言环境映射关系
//   const { domainsLocaleMap } = config;

//   // 主机名
//   const { hostname } = req.ctx;
//   console.log('hostname123', hostname)
//   // 默认语言环境
//   let locale = config.fallbackLocale;

//   // 根据主机名定位语言环境
//   Object.keys(domainsLocaleMap).forEach((key) => {
//     if (hostname.includes(key)) {
//       locale = domainsLocaleMap[key];
//     }
//   })

//   if (locale === 'hk' || locale === 'ai') {
//     app.head.htmlAttrs.lang = 'zh-Hant'
//   }

//   if (hostname.includes('bettermeedu')) {
//     store.commit('SET_BETTERME', 'bettermeedu');
//   } else {
//     store.commit('SET_BETTERME', 'none');
//   }

//   // 设置语言环境
//   store.commit('SET_LOCALE', locale);

//   // app.head.htmlAttrs.id = `style__${locale}`

//   // 设置分校配置
//   store.commit('SET_LOCALE_CONFIG', localeConfig[locale] || {});

//   // 设置国家编码
//   const countryCode = config.countryCodeMap[locale];
//   store.commit('SET_COUNTRY_CODE', countryCode);

//   // 设置分校编码
//   const schoolCode = config.schoolCodeMap[locale];
//   store.commit('SET_SCHOOL_CODE', schoolCode);
// }
