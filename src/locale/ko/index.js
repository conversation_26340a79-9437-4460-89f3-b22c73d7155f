import common from './common'
import login from './login'
import courses from './courses'
import setting from './setting'
import account from './account'
import classroom from './classroom'
import today from './today'
import views from './views'
import growthHandbook from './growthHandbook.js'
import blindBox from './blindBox.js'
import myBackpack from './myBackpack.js'

export default {
  common,
  login,
  setting,
  courses,
  account,
  classroom,
  today,
  growthHandbook,
  blindBox,
  myBackpack,
  ...views,

  liveroomMicFirstTipAlertTitlePC: '스페이스바 또는 마이크 버튼을 누른 상태로 발언해 주세요',
  liveroomMicFirstTipAlertDoneTitle: '알겠습니다',
  liveroomMicSpeakingHUDTitle: '발언 중',
  liveroomKeepMuteModeToast: '선생님이 모든 사람의 마이크를 음소거하셨습니다',
  liveroomPushToTalkModeToast: '선생님이 모든 사람의 마이크를 켜셨습니다',
  liveroomKeepMicOnModeToast: '선생님이 마이크를 푸시투토크 모드로 설정했습니다',
  老师已将麦克风设置为自由发言模式: '선생님이 마이크를 오픈 마이크 모드로 설정했습니다',
  liveroomHoldDownMicBtnWhenSpeakingToastForPC:
    '말씀하실 때 스페이스 바 또는 마이크 버튼을 누르고 계세요',
  liveroomDisableOthersVideoMenu: '타인 비디오 끄기',
  磁盘可用空间不足: '디스크의 사용 가능한 공간이 부족합니다. 디스크를 정리하십시오'
}
