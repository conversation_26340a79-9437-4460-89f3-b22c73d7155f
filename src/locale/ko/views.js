export default {
  // 积分商城
  coinsMall: {
    title: '포인트 몰'
  },
  // 设备拦截
  deviceIntercept: {
    interceptInfo: {
      title: '이 장치와 호환되지 않습니다',
      content: `죄송합니다. 이 장치에서 앱을 사용할 수 없습니다. 최소 작동 요구 사항을 충족하는 장치에서 Think Academy 앱을 실행해 주십시오.`
    },
    recommendInfo: {
      title: '본 장치를 이용하는 것을 권장하지 않습니다. ',
      content: `죄송합니다. 본 장치에서 앱을 원활하게 이용하지 못할 가능성이 있습니다. 최소 요구 사항을 충족하는 장치에서 Think Academy 앱을 실행해 주십시오.`
    },
    deviceInfo: {
      compatibility: '작동 시스템',
      macTips: 'MacOS 10.12  혹은 이상의 버전',
      windowsTips: 'Windows 7 혹은 이상의 버전',
      configurationsTitle: '최소 하드웨어 구성',
      configurationsInfo: '코어 CPU, 4GB 실행 메모리',
      deviceTitle: '장치',
      notAvailableDevice: 'ChromeBook 및 Surface Book 시리즈의 노트북은 지원되지 않습니다'
    }
  },
  // 课前准备页
  prepareClass: {
    great: '정상',
    gotIt: '접수',
    title: `수업 전 준비`,
    audioAccess: '마이크 권한이 켜져 있지 않습니다',
    audioAvailable: '사용 가능한 마이크 없습니다',
    cameraAccess: '마이크 권한이 켜져 있지 않습니다',
    cameraAvailable: '사용 가능한 카메라 없습니다',
    audioAccessWindow: '마이크 권한이 켜져 있지 않습니다',
    audioAccessContent:
      '시스템 설정에서 Think Academy의 마이크 권한을 열어 주십시오. 열지 않을 경우 선생님께서 학생의 목소리를 들을 수 없습니다.',
    cameraAccessWindow: '카메라 권한이 켜져 있지 않습니다',
    cameraAccessContent:
      '시스템 설정에서 Think Academy의 카메라 권한을 열어 주십시오. 열지 않을 경우 선생님께서  학생의 얼굴을 볼 수 없습니다.',
    networkWeek: '네트워크 연결 불량',
    networkGood: '네트워크 상태 양호',
    networkGoodContent:
      '네트워크 연결 상태가 양호하고 Think Academy 수업을 원활하게 이용할 수 있습니다.',
    networkWeekContent:
      '네트워크 상태가 좋지 않을 경우, 수업 중 동영상 끊김이 발생할 수 있습니다. 다른 네트워크로 전환하거나, Wi-Fi 를 사용하는 경우 라우터를 재부팅 한 후 라우터와 더 가까운 위치로 이동하여 네트워크 연결 퀄리티를 높여 보십시오.',
    retestNetwork: '다시 테스트하기',
    testSpeed: '네트워크 연결 속도 테스트',
    enterName: '닉네임을 입력하십시오',
    setNickName: '닉네임 설정',
    setNickNameTip: '65자 이하, 특수 문자를 포함할 수 없음',
    setNickNameError: '닉네임 변경에 실패하셨습니다. 다시 시도해주십시오.',
    enterBtn: '이름을 입력하기',
    hasNotEnter: `닉네임을 입력하지 않으셨습니다.`,
    enterClassroom: '수업 시작',
    enterClassroomError: '강의실 들어가기에 실패하셨습니다. 다시 시도해주십시오.',
    editNickname: '닉네임 수정',
    validateInput: `잘못된 입력`,
    done: '다운로드 완료',
    redownload: '다시 하기',
    coursewareDownload: '코스웨어 다운로드 완료',
    onlineCourseware: '온라인 코스웨어',
    onlineCoursewareContent:
      '온라인 코스웨어는 오프라인 코스웨어를 다운로드할 수 없는 경우에만 시도할 수 있습니다. 하지만 이 방법은 신뢰성이 없습니다.',
    confirmUser: '사용 확인',
    coursewareDownloading: '코스웨어 다운로드 중, {speed}，나머지{minutes}.',
    coursewareDownloadingSlow: '60분 이상',
    coursewareDownloadWeek: '코스웨어 다운로드에 실패하셨습니다. 다시 시도해주십시오.',
    coursewareDownloadWeekTitle: '코스웨어 다운로드에 실패했습니다.',
    coursewareDownloadWeekContent: '네트워크를 확인하시고 다시 다운로드해주십시오.',
    playbackError: '본 수업은 리플레이가 없거나 아직 생성되지 않았습니다.',
    minute: '분',
    second: '초',
    waiting: '코스웨어 다운로드가 완료될 때까지 기다려주십시오.',
    networkBtn: {
      good: '양호',
      normal: '보통',
      retest: '네트워크 연결 재시도',
      weak: '불량'
    },
    startAgo: '수업이 시작된 지 {changeMinutes}분이 지났습니다. 빨리 교실에 들어가십시오.',
    willStart: ' {changeMinutes}분 후에 수업이 시작됩니다.',
    missClass: '라이브 수업을 놓쳤다면 리플레이 수업을 시청하십시오!',
    joinClass: '수업 바로 가기',
    continue: '계속하기',
    confirm: '확인',
    downloadTip: {
      coursewareDownloading: '코스웨어 다운로드',
      downloadReson: '코스웨어를 미리 다운로드해두면 수업 시간을 절약할 수 있습니다.',
      incomplete: ' 다운로드한 코스웨어가 완전하지 않습니다.',
      downloadComplete: '다운로드 완료',
      DownloadFailed: '다운로드 실패'
      // subtitle: '코스웨어를 미리 다운로드해두면 수업 시간을 절약할 수 있습니다.'
    },
    checkIn: {
      onTime: '시간에 맞춰 수업에 도착하셨습니다',
      title: '출석 체크',
      missTips: ['수업이 이미 시작되었습니다，', `체크인 보상를 받을 수 없습니다:(`],
      firstCheckInMessage: '우선 체크인십시오.',
      checkInButtonName: '체크인',
      checkInSuccess: '체크인 성공',
      checkInFail: '체크인 실패'
    }
  }
}
