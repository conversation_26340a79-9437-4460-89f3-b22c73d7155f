/**
 * 模块配置
 */
export default {
  // 导航
  header: {
    // 返回确认窗口
    backConfirm: {
      content: `라이브 강의실을 나가시겠습니까?`,
      exitByCoursewareIdChange: '코스웨어가 비정상입니다. 강의실을 종료하고 다시 들어가십시오.'
    }
  },
  // 反馈
  feedback: {
    headerName: '문제 설명',
    optionNames: ['학습 관련', 'APP관련', '부적절한 행동', '기타'],
    placeholder: `1. 해당 피드백 유형 라벨을 선택하십시오.
2. 의문을 빨리 해결해드릴 수 있도록 자세히 설명해주십시오.`,
    screenshotTips: '현재 스크린샷을 교육팀에 보내는 데 동의합니다.',
    sendSuccessNotice: '의견을 보내주셔서 감사합니다. 곧 연락드리겠습니다.',
    defaultMsg: '피드백 보내기',
    playback: {
      header: '피드백',
      placeholder: '의문을 여기에 적어 주십시오.',
      screenshotTips: '현재 장치 정보와 스크린샷을 기술 지원팀에 제출하는 데 동의합니다.',
      cancel: '취소',
      send: '발송',
      success: '제출 성공',
      failed: '제출 실패'
    }
  },
  // 设备检测
  deviceTest: {
    dialogTitle: '장비 테스트',
    statusNames: {
      disabled: '사용 금지',
      usable: '사용',
      unauthorized: ' 권한 없습니다'
    },
    cameraTitle: '카메라 테스트: ',
    microphoneTitle: '마이크 테스트: ',
    audioTitle: '오디오 테스트: ',
    authorizeGuide: ['권한 부여하러 가기, ', '링크를 클릭하십시오']
  },
  checkDevice: {
    network: '네트워크',
    microphone: '마이크',
    camera: '카메라',
    voice: '스피커',
    网络满足上课需求: '네트워크는 학습 요구를 만족합니다.',
    网络不满足上课需求: '네트워크는 학습 요구를  만족시키지 못합니다',
    你的设备满足上课需求: '현재 장비는 학습 요구를 만족합니다.',
    你的设备不满足上课需求: '네트워크에 문제가 있습니다. 해결 제안을 보려면 여기를 클릭하십시오.',
    音视频测试: '오디오 및 비디오 서비스',
    课堂互动测试: '수업 활동 서비스',
    服务器检测: '서버 연결 속도 테스트',
    课件下载测试: '코스웨어 다운로드 테스트',
    此项测试大约需要花费一分钟: '본 과정은 약 1~3분 정도 소요됩니다.',
    常见网络问题建议: '네트워크 문제 해결을 위한 제안',
    尝试重启路由器:
      'Wi-Fi를 사용할 경우 라우터에 더 가까이 이동하거나 라우터를 다시 시작해보십시오.',
    检查网络权限: '본 장치가가 네트워크에 액세스할 수 있는지 확인하십시오',
    切换其它网络: '다른 네트워크로 전환하기',
    重新测试: '다시 테스트하기',
    下一项: '다음 항목',
    没有麦克风权限: 'Think Academy는 당신의 마이크를 사용할 수 없습니다.',
    没有找到可用的麦克风: '사용 가능한 마이크를 찾지 못했습니다.',
    请检查你的麦克风或者佩戴耳机: '마이크 설정을 확인하거나 헤드폰을 착용해 주십시오.',
    尝试你大声读出下面的文字: '다음 텍스트를 큰 소리로 읽어 보십시오.',
    你好: '안녕하세요',
    你的麦克风似乎存在问题: '마이크에 문제가 있는 것 같습니다.',
    我们没有听到你的声音:
      '당신의 목소리를 듣지 못했습니다, 장비를 변경하거나 또는 헤드폰 착용 후 다시 시도하십시오.',
    麦克风检测完成: '마이크 테스트 완료',
    你的麦克风工作正常: '마이크는 정상입니다.',
    去设置: '설정하러가기',
    没有摄像头权限: 'Think Academy는 당신의 카메라를 사용할 수 없습니다.',
    在设置中打开麦克风权限: '시스템 설정에서 Think Academy의 마이크 사용 권한을 열어주십시오',
    在设置中打开摄像头权限: '시스템 설정에서 Think Academy의 카메라 사용 권한을 열어주십시오',
    没有找到可用的摄像头: '사용 가능한 카메라를 찾지 못했습니다',
    请检查你的摄像头:
      '카메라가 제대로 설치되어 있는지 확인하시거나 다른 장비를 이용해 수업을 진행해주십시오.',
    可以在上面看到自己吗: '위의 비디오 프레임에서 자신의 모습을 볼 수 있습니까?',
    正在使用摄像头: '카메라는 현재 사용 중이며 정상상태입니다.',
    你的摄像头似乎存在问题: '카메라에 문제가 있는 것 같습니다.',
    请检查你的摄像头是否正确安装:
      '카메라가 제대로 설치되어 있는지 확인하시거나 다른 장비를 이용해 수업을 진행해주십시오.',
    摄像头检测完成: '카메라 테스트 완료',
    你的摄像头工作正常: '카메라는 정상입니다',
    当前音量: '현재 볼륨：',
    可以听到正在播放的音乐吗: '음악이 재생되고 있는것을 들을 수 있습니까?',
    请注意调整系统音量到适当大小: '시스템 볼륨을 적절한 크기로 조정하십시오.',
    能: '됩니다',
    不能: '안됩니다',
    你的扬声器似乎存在问题: '스피커에 문제가 있는 것 같습니다.',
    建议尝试佩戴耳机或者更换其它设备:
      '헤드폰을 착용하거나 다른 장비를 이용해 수업을 진행해주십시오.',
    你的设备满足上课要求: '장비는 학습 요구를 만족합니다.',
    你的设备不完全满足上课要求: '장비는 학습 요구를 만족시키지 못합니다',
    继续使用此设备上课可能会降低你的体验:
      '이 장비에서 계속 수업을 진행하실 경우 수업 체험이 저하될 수 있습니다.',
    知道了: '얼겠습니다',
    你的网络是乎有些问题: '네트워크에 문제가 있는 것 같습니다. 여기를 클릭하시고 확인하십시오 .',
    解决方案: '해결 방법'
  },
  // 网络状态提示
  networkStatus: {
    statusMap: {
      good: {
        title: '네트워크 연결이 량호합니다.'
      },
      normal: {
        title: '네트워크 연결이 보통입니다.'
      },
      weak: {
        title: '네트워크 연결이 불량합니다.',
        description:
          '다시 연결을 시도하는 중입니다. 더 나은 네트워크나 장비로 전환해 볼 수도 있습니다.'
      }
    }
  },
  // 网络错误提示
  networkError: {
    notice: ['네트워크가 없습니다', '네트워크 연결을 확인하거나 다시 연결될 때까지 기다려 주십시오']
  },
  // 课件组件
  courseware: {
    errorNotice: [
      '코스웨어 로딩 실패',
      '코스웨어 로딩 실패',
      `새로고침'을 클릭하시고 다시 시도해 보십시오.`
    ]
  },
  // 截屏
  screenThumbnail: {
    successNotice: '바탕 화면에 저장됨'
  },
  // 媒体权限
  mediaSecurityAccess: {
    camera: `수업 활동에 참여하려면 카메라 권한을 켜주셔야 합니다. 켜져 있는지 확인해주십시오.`,
    microphone: `ThinkAcademy가 귀하의 마이크에 접근하도록 허용해 주세요`,
    deniedAccess:
      '수업 활동에 참여하려면 마이크와 카메라 권한을 켜주셔야 합니다. 켜져 있는지 확인해주십시오',
    tip: '권한을 켜준 후 애플리케이션을 다시 시작해주십시오.',
    confirm: '동의',
    cancel: '거절'
  },
  // 踢出
  liveKickout: {
    notice: '계정이 다른 장비에 로그인되어 있습니다. 다시 로그인해 주십시오.'
  },
  // 下课提示
  classEnded: {
    title: '수업 종료',
    content: '이 수업이 종료되었습니다. 홈페이지로 돌아가 주세요',
    okText: '확인'
  },
  // 定向金币
  orientationCoins: {
    notice: ['이 학생들이 받았습니다', '금화！'],
    Congrats: '금화를 받았습니다',
    others: '다른 학생들과 함께',
    smallCongrats: '훌륭합니다！'
  },
  // 作业盒子
  assignmentBox: {
    modalTitle: 'Basic Modal',
    nodataNotice: '본 수업에는 연습문제가 게시되지 않았습니다.',
    checkBigPictures: {
      buttonName: '수정'
    },
    checkedExerciseToast: {
      notice: '선생님께서 학생의 연습을 채점해 주셨습니다. 확인해주십시오',
      buttonName: '연습 보기',
      loadingName: '이미지 로딩중',
      pictureDesc: '{assisTeacher}께서 학생의 연습을 채점해 주셨습니다. 확인해주십시오'
    },
    checkErrorTips: '정말 안타깝네요. 계속 노력 하세요.'
  },
  // 小班涂鸦板
  smallClassGraffitiCorrect: {
    yellow: '노란색',
    red: '빨간색',
    blue: '파란색'
  },
  // 连续作答
  continuousCorrect: {
    rightLevelDesc:
      '한 가지 질문에 더 정확하게 답하면 메달이{nextTitle}레벨로 업그레이드될 수 있습니다',
    rightTopLevelDesc: '본 수업에서 가장 높은 레벨의 메달을 획득하셨습니다.',
    wrongLevelDesc:
      '한 가지 질문에 더 정확하게 답하면 메달이{nextTitle}레벨로 업그레이드될 수 있습니다',
    encourageNotice: '금화 장려',
    noanswerNotice: '수업 중 활동에 참여하시고 메달을 업그레이드하십시오',
    continuousText: '정답입니다, 참 멋집니다！',
    highestLevelText: '메달은 이미 최고 레벨에 도달했습니다',
    upgradedLevelText: '메달이 Lv{curLevel} 로 업그레이드되었습니다.',
    wrongTitleText: '아이고 실수하셨습니다',
    wrongDescText: '계속 파이팅 하세요～'
  },
  // 辅导连麦
  tutorVideoLink: {
    title: '보조 강사 채팅',
    errorMsg: ['', '작은 문제가 발생했습니다.']
  },
  // 系统出错
  systemError: {
    message: '시스템 오류입니다. 다시 시도해 주십시오.',
    noPlaybackMessage: '재생 비디오가 생성되지 않았습니다',
    refreshButtonName: '새로 고치기',
    backButtonName: '수업으로 돌아가기'
  },
  // 老师上台
  teacherOnStage: {
    notice: ['어, 선생님은 잠시후 돌아올게요', ''],
    privateChat: '영상 단독 채팅'
  },
  // 课中考试
  ClassExam: {
    rankHeaderTitle: '순위표'
  }
}
