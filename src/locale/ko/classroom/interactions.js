/**
 * 互动配置
 */
export default {
  // 投票
  vote: {
    title: '투표',
    results: '결과',
    voteResult: '해답 결과',
    true: '정확합니다',
    false: '부정확합니다',
    correctTitle: '축하합니다',
    correctTips: `대단합니다`,
    rightTipsByPlayback: [`정답입니다`, `금화를 얻었습니다`, `너무 멋져요!`],
    rightTipsByLive: [`오답입니다`, `금화를 얻었습니다`, `계속 힘내세요!`],
    repeatSubmitMessage: `이미 제출하셨습니다.`,
    // 懂不懂
    isUnderstand: '오늘 강의는 이해하셨나요?',
    understand: '이해했어요',
    notUnderstand: '이해하지 못했어요',
    understandFeedback: '너무 멋져요，계속 힘내세요!',
    notUnderstandFeedback: '괜찮아요，계속 힘내세요!'
  },
  // 游戏互动
  gameCourseware: {},
  // 课间休息
  classRest: {
    title: '수업간 휴식',
    startTip: '휴식 시간',
    endTip: '시간이 다 됐습니다'
  },
  // 送礼物
  gift: {
    // 请选择礼物提示
    pleaseChooseGiftNotice: '선물을 골라서 선생님께 드리세요~',
    // 金币不足
    notEnoughCoin: '금화가 부족합니다.:(',
    // 可送礼物次数已到达
    notEnoughTimes: '감사합니다. 하지만 선물은 한 번에 최대 3개까지만 보낼 수 있습니다.:)'
  },
  // 答题板
  coursewareBoard: {
    true: '정확합니다',
    false: '부정확합니다',
    yourAnswer: '학생의 해답',
    rightAnswer: '정확한 해답',
    correctAnswer: '정확한 해답'
  },
  // 拍照上墙涂鸦
  graffiti: {
    yellow: '노란색',
    red: '빨간색',
    blue: '파란색'
  },
  // 拍照上墙
  photoWall: {
    graffitiBtnName: '그라피티 아트보드',
    graffitiDesc1: `* 사진이 잘 안 나와요?`,
    graffitiDesc2: `그라피티 아트보드를 사용하여 연습문제 제출하세요~`,
    cameraBtnName: '카메라를 사용하기',
    confirmNotice: [`제출하시겠습니까?`, `제출한후 다시 수정할 수 없습니다.`],
    shortCutTip: `팁: 스페이스바를 클릭하여 사진을 찍을 수도 있습니다.`,
    notjoinTip: `선생님께서 해답을 종료시켰습니다. 다음번에는 더 적극적으로 참여하시기 바랍니다~`,
    hasAccessNotice: [
      `교재나 메모지에 연습문제를 완성하십시오.`,
      `아래 버튼을 눌러 사진을 찍어 업로드해주십시오`,
      `제출 뒤 '연습'에서 수정 결과를 확인하실 수 있습니다.`
    ],
    notAccessNotice: [
      `카메라를 사용할 수 없습니다`,
      `그라피티 아트보드를 사용하여 연습문제를 제출하십시오.`
    ],
    endNotice: [
      `선생님께서 인터랙티브 문제를 종료하셨습니다. 지금 해답결과를 제출하시겠습니까?`,
      `5초 후에 수업으로 돌아갑니다`
    ],
    grading: '채점 중',
    correct: '정확합니다',
    incorrect: '오답',
    halfCorrect: '부분  정답',
    answerCorrect: '정답입니다'
  },
  // 随机连线
  randomCall: {
    title: '무작위 학생 선택',
    finishedNotice: {
      self: `축하합니다. 선택되셨습니다!`,
      other: '{name}학생이 선택되셨습니다!'
    }
  },
  // 红包
  redPacket: {
    received: 'Received', // 查看红包结题里显示的, 目前功能已隐藏, 暂不翻译
    unfortunatelyRedNotice: ['돈 봉투는 이미 다 수령되었습니다.', ''],
    oopsRedNotice: `돈 봉투를 받았습니다`,
    congrats: '축하합니다!'
  },
  // 视频连麦
  videoLink: {
    joinMessage: '손을 들어 선생님과의 영상 채팅에 참여하십시오!',
    joinButtonName: '손을 들기',
    waitMessage: '선생님 선택을 기다리기',
    cancelButton: '취소',
    raiseHandButton: '손을 들기',
    speaking: '발언중...'
  },
  // 集体发言
  collectiveSpeech: {
    permissionDeniedTips:
      '마이크 권한이 켜져 있지 않습니다. 시스템 설정에서 마이크 권한을 켜주십시오.'
  },
  redRain: {
    collect: '금화를 받기',
    toastTip: '선생님께서 인터랙티브 문제를 종료했습니다.',
    loading: '게임 로딩중..',
    NetworkFailTip:
      '서버 연결이 시간을 초과하였습니다. 네트워크 상태를 확인하신 후 다시 시도해 주십시오.',
    RetryBtn: '다시 시도하기',
    title: '돈봉투 떨어집니다'
  },
  // 填一填
  nonPersetFillBlank: {
    submitSuccessfully: '제출에 성공하셨습니다',
    submitFailed: '제출에 실패하셨습니다. 다시 시도해 주십시오.',
    inputPlaceholder: '답안을 입력해주세요.',
    clear: 'AC',
    submit: '제출하기',
    myAnswer: '나의 답안'
  },
  praiseList: {
    fileName: '클래스 표창 명단_{firstTitle}_강{lessonName}_{courseName}',
    saveText: '스크린샷이 다음곳에 저장되었습니다.',
    savePath: '데스크탑/Think-Academy-Screenshot',
    savingText: '저장중',
    emptyText: '본 클래스에는 아직 표창 명단에 오른 학생이 없습니다.'
  },
  schulteTable: {
    schulteTable: '슐테 그리드',
    naturalNumber: '자연수',
    primeNumber: '소수',
    normalMode: '일반 모드',
    shuffleMode: '랜덤 모드',
    nextNumber: '다음 숫자',
    duration: '{duration}초',
    gameInstructions: [
      '{start}부터 {end}까지의 자연수를 작은 수부터 차례로 클릭하십시오',
      '{start}부터 {end}까지의 소수를 작은 수부터 차례로 클릭하십시오'
    ],
    gameObjectives: [
      '작은 것부터 큰 것까지의 순서로 숫자를 클릭하십시오.',
      '빠르게 클릭할수록 더 많은 금화를 얻을 수 있습니다.'
    ],
    gameCompleted: '게임 완료',
    newRecord: '새로운 기록',
    firstChallenge: '첫 도전',
    yourCurrentTime: '사용된 시간',
    yourBestTime: '역사 최고',
    leaderboard: '통관 시간 랭킹',
    congratulations: '멋집니다! 성공적으로 랭킹에 오른 것을 축하합니다!',
    tryAgain: '오! 너무 아쉬워요. 계속 힘내세요!',
    gameOver: '게임 종료',
    leaderboardTip: [
      '선생님이 곧 랭킹표를 공개하실 거예요!',
      '과연 랭킹표에 올랐을까요？곧 공개됩니다!'
    ],
    submitFailTitle: '게임 결과 제출 실패',
    submitFailTip:
      '서버 연결이 시간을 초과하였습니다. 네트워크 상태를 확인하신 후 다시 시도해 주십시오',
    submitFailBtn: '다시 시도하기'
  }
}
