/**
 * 大班直播
 */
export default {
  // 导航区
  header: {
    groupVideoButtonName: '그룹 비디오',
    deviceTestButtonName: '장비 테스트',
    teacherMessageOnly: '선생님 메시지만 보기'
  },
  // 播放器区
  players: {
    mute: '무음 모드',
    unmute: '무음 모드 취소'
  },
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: '선생님께서 아직 교실에 들어오지 않았습니다. 잠시만 기다려주십시오.',
    gameConfig: {
      gameTip: '선생님 시연 중',
      gameToast: '선생님의 시연이 끝날 때까지 잠시 기다려 주십시오.'
    }
  },
  // 聊天
  chats: {
    newMessage: '새로운 메시지',
    teacherOnly: '선생님만 보기',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: '채팅이 닫혔습니다',
      2: '채팅이 닫혔습니다',
      3: '',
      4: '',
      5: '로딩중'
    },
    inputPlaceholder: '한마디 해주세요.',
    hotWordList: ['완성！', '잘 모르겠어요', '알아 들었어요', 'ㅋㅋㅋ'],
    msgTip: {
      CONNECT: '채팅 서비스가 연결되었습니다',
      DISCONNECT: '채팅 서비스가 중단되었습니다.',
      RECONNECT: '채팅 서비스가 다시 연결 중입니다',
      BAN_SPEECH: '선생님께서 학생을 금언시켰습니다', // 禁言
      RELIEVE_SPEECH: '선생님께서 학생을 금언 해제시켰습니다', // 解除禁言
      SPEECH_INTERVAL: '3초 후에 다시 발언해 주십시오.',
      SPEECH_MINE: '나',
      SPEECH_SYS: '시스템 정보',
      SPEECH_TEACHER: '선생님',
      SPEECH_EMPTY: '입력한 메시지가 불법입니다',
      REMOTE_LOGIN:
        '현재 위치는 일반적으로 사용되는 로그인 위치가 아닙니다. 본 페이지를 새로 고치십시오.',
      CHAT_DISCONNECT: '서버 연결이 중단되어 지금은 발언할 수 없습니다.',
      ENTER_ROOM: '교실에 들어왔습니다'
    },
    level: {
      wood: '브론즈',
      gold: '골드',
      silver: '실버',
      diamond: '다이아몬드'
    },
    reply: '답장',
    gotIt: '받았습니다'
  },
  // 视频回显
  videoGroup: {
    textConfig: {
      matching: '매칭 중...',
      noticeAccessDenied: '시스템 설정에서 Think Academy의 카메라 권한을 켜주십시오'
    },
    tabNames: ['비디오', '채팅'],
    closedNotice: '그룹 비디오가 닫혔습니다',
    localVideoAudition: {
      notice: '학생은 방청수업을 진행하고있습니다'
    }
  },
  // 金币显示
  coins: {
    title: '나의 금화'
  },
  // 举手
  raiseHand: {
    buttonName: '손을 들기'
  },
  // 回放
  playback: {
    notice: '재생 영상 보기'
  }
}
