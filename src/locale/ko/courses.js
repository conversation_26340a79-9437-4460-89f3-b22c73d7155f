// 课程中心
export default {
  // 课程列表页
  list: {
    title: '나의 수업',
    titleTip: '현재는 화성 캠퍼스에 계십니다.',
    tabList: {
      all: '전체',
      current: '수업 중입니다',
      completed: '수업이 모두 끝났습니다'
    },
    courseTabList: {
      live: '생방송 수업',
      record: '녹화 수업'
    },
    noCourses: '아직 수업이 없습니다. / 본 카테고리에는 수업이 없습니다',
    switchSingleAccount: '해당 하위 계정 {account}에 수업이 있습니다. 전환하려면 클릭하십시오.',
    switchMultipleAccount: '여러 하위 계정에 수업이 있습니다. 전환하려면 클릭하십시오.',
    switchMultipleSchool: '여러 분교에 수업이 있습니다. 전환하려면 클릭하십시오.',
    switchSingleSchool: '분교{school}에 수업이 있습니다. 전환하려면 클릭하십시오.',
    switchAccountText: '학생 교체하기',
    switchSchoolText: '분교 전환하기',
    planName: '수업'
  },
  lessonType: {
    formal: '정규 수업',
    playback: '리플레이 수업',
    audition: '방청 수업',
    temporary: '임시 수업'
  },
  // 课程卡片
  courseCard: {
    classInSession: '수업 중',
    nextSession: '다음 수업',
    enter: '교실로 들어가기',
    completed: '완료',
    refunded: '환불 완료',
    playback: '리플레이',
    startAuditing: '방청으러 가기',
    view: '보기',
    reportTips: '“{course}”의 수업 리포트가 생성되었습니다',
    parentAudit: '학부모 방청',
    alreadyStarted: `{courseNum}수업은 시작되였습니다.`,
    nextLesson: '다음 수업'
  },
  // 录播课
  recordCourse: {
    courseValidUntil: '본 수업의 유효기간은:：',
    enter: '들어가기',
    refunded: '환불 완료되었습니다',
    expired: '만료되었습니다.',
    expiredTip: '본 수업은 만료되었습니다.',
    permanentTip: '본 수업은  영구적 사용 가능합니다.',
    goToTest: '문제풀이 시작',
    report: '테스트 리포트',
    reload: '다시 불러오기',
    loadFailed: '로딩에 실패하셨습니다. ',
    loading: '로딩 중',
    saveShotScreen: '스크린샷이 저장되었습니다.',
    switchChannel: '선로 교체',
    playNextVideo: '다음 영상을 재생하기'
  },
  // 录播课播放器提示
  recordPlayerTips: {
    0: '재생',
    '0-1': '일시 중지',
    1: '10초 빨리 감기.',
    2: '10초 빨리 되감기.',
    9: '스크린샷'
  },
  // 课程详情页
  detail: {
    title: '수업 세부 사항',
    specialTitle: '{prefix} 세부 사항',
    // 播放按钮
    playButton: {
      // 未开始状态
      startSoon: '아직 시작 되지 않았습니다.',
      playbackGenerating: '리플레이 생성중입니다',
      playback: '리플레이 보기',
      playbackExpired: '리플레이 만료되었습니다',
      unpaid: '아직 구매하지 않았습니다.',
      live: '교실로 들어가기',
      finished: '끝났습니다',
      startAuditing: '방청으러 가기'
    },
    classRoom: '교실',
    homework: {
      status: [
        '발표되지 않았습니다',
        '제출되지 않았습니다',
        '채점 완료되었습니다',
        '제출 완료되였습니다',
        '만료되었습니다',
        '답안 보기',
        '제출되지 않았습니다'
      ],
      title: '숙제'
    },
    exam: {
      status: [
        '발표되지 않았습니다',
        '테스트 하러 가기',
        '문제풀이 완료되었습니다',
        '채점 완료되었습니다',
        '만료되었습니다'
      ]
    },
    examTip: '테스트가 발표되지 않았습니다',
    resources: '학습 자료',
    playBackTip: '생방송 수업 종료 후 2시간 후에 재생가능합니다.',
    reportBtnText: '채점 완료되었습니다.',
    reportSubmitText: '제출 완료되였습니다.',
    reportExpiredText: '만료되었습니다.',
    startTest: '테스트 하러 가기',
    reportTips: `{canViewTime}후에 보고서를 확인할 수 있습니다.`,
    lessonReport: '수업 리포트',
    expiredTips: '숙제 제출 기한이 지났습니다. 다음에는 제시간에 숙제를 제출하십시오!',
    file: '파일',
    files: '파일',
    transferred: '수업 조정',
    classNotes: '판서',
    learningMaterials: '학습 자료'
  },
  switchAccount: {
    title: '학생 교체하기',
    subTitle: '해당 하위 계정에 수업이 있습니다. 전환하려면 클릭하십시오.',
    cancel: '취소',
    confirm: '확인',
    switchSuccess: '교체 성공',
    switchFail: '교체에 실패하셨습니다.  잠시 후에 다시 시도하십시오.',
    switchIng: '교체 중......',
    emptyTip: '수강생 계정을 선택해 주십시오.',
    loginTitle: '본 가족 그룹에는 여러 명의 학생이 있습니다',
    loginSubTitle: '로그인할 수강생을 선택해 주십시오.'
  },
  switchSchool: {
    title: '분교 전환하기',
    subTitle: '아래 분교에 수업이 있습니다. 전환하려면 클릭하십시오.'
  },
  // 面授课 - 如何上课
  faceToFace: {
    attendClass: '수업 방식 확인',
    finished: '끝났습니다.',
    location: '장소',
    copy: '복사',
    zoomId: 'Zoom ID',
    zoomPassword: 'Zoom 비밀 번호',
    zoomDownload: 'Zoom 다운로드',
    zoomDescribe: `위에 설명된 대로 Zoom ID와 비밀번호를 입력하십시오.  Zoom 라이브 세션이 시작되기 10분 전에 강의실에 입장하여 적극적으로 사고해보십시오.`,
    classinLink: 'Classin 링크',
    classinDownload: 'Classin 다운로드',
    classinDescribe: `수업에 등록할 때 자신의 계정으로 등록하십시오. Classin 라이브 세션 시작 10분 전에 강의실에 입장하여 Think Academy를 즐기십시오.`,
    classroomDescribe: `수업 시작 10분 전에 강의실에 입장하여 Think Academy를 즐기십시오.`,
    ipadDescribe: `수업은 iPad 앱 Thinkhub를 이용해 주십시오. 문제가 있으시면 전화로 문의하십시오.`,
    confirm: '알겠습니다'
  },
  // 课后作业
  assignment: {
    title: '숙제',
    earnedNotice: '받았습니다.',
    submissionNotice: '미리 완성 하여 추가 선물을 드립니다.'
  },
  // 课前测
  previewQuestion: {
    title: '수업전 테스트',
    deadlineName: '마감 시간',
    deadlineTime: '마감 시간',
    answerTimeName: '테스트 시간',
    deadlineBannerTips: '마감 시간이 지났습니다. 다음에는 제시간에 제출하십시오！',
    problemDescriptors: [`문제 구성`, `（총 {totalScore} 점）`],
    confirmTitle: '문제 풀이 시작하시겠습니까?',
    confirmSubtitle: `문제 풀이 시간은 {duration}분 입니다. 힘내요!`,
    solutions: '해석',
    start: '문제 풀이 시작',
    deadlineTitle: '테스트가 끝났습니다. 제시간에 참석하지 못했습니다.',
    deadlineDesc: '아이고, 문제 풀이 마감시간이 지났네요, 다음부터는 시간 맞춰 완성해주십시오.'
  },
  // 学习资料
  studyResources: {
    title: '학습 자료',
    download: '다운로드',
    play: '재생'
  },
  // 课堂报告
  lessonReport: {
    title: '수업 리포트'
  },
  // 阶段考试
  exam: {
    title: '시험 주의 사항',
    solutions: '문제 해석 보기',
    report: '리포트 확인',
    start: '문제 풀이 시작',
    coinsTip: '문제 풀이를 완성하면 금화를 얻을 수 있습니다.',
    confirmTitle: '문제 풀이 알림',
    confirmSubtitle: `문제 풀이 시간은 {duration}분 입니다. 힘내세요!`,
    confirmBtn: '확인',
    cancelBtn: '취소'
  },
  // 临时教室
  temporaryClassroom: {
    title: '임시 교실',
    noCourses: '수업이 없습니다.',
    registrationCourses: '수강 신청',
    noCoursewareNotice: '본 수업에는 코스웨어가 바인딩되어 있지 않습니다.',
    statusName: {
      startSoon: '아직 시작 되지 않았습니다.',
      playbackGenerating: '리플레이 생성중입니다.',
      playback: '리플레이 보기',
      playbackExpired: '리플레이 만료되었습니다',
      enter: '교실로 들어가기'
    }
  },
  confirmModal: {
    title: '교실로 들어가기 알림',
    content:
      '현재 계정에 해당하는 학생은 이미 생방송 수업에 있으며, 수업에 들어가기를 선택하시면 {warning}. 수업으로 계속 들어가시겠습니까?',
    warning: '생방송 수업에서 기존 계정을 삭제합니다.',
    enterBtn: '확인',
    backBtn: '취소',
    confirm: '확인'
  },
  switchSchoolModal: {
    title: '알림',
    content: '이 분교로 전환하겠습니까?'
  },
  resolutionRatioModal: {
    title: '알림',
    content: `현재 화면 해상도가 ThinkAcademy에서 요구하는 최소 해상도보다 낮습니다. \n '시스템 설정 - 디스플레이'에서 디스플레이 확대 축소 비율을 낮춰주십시오.`,
    confirm: '알겠습니다.'
  },
  playback: {
    courseInformationError: '수업 정보 가져오기 실패',
    playbackInformationError: '리플레이 정보 가져오기 실패',
    playbackInterfaceError: '리플레이 인터페이스 액세스 오류가 발생했습니다.',
    playbackSourceIsNull: '리플레이 주소가 비어 있습니다.',
    playbackStampError: '리플레이 스탬프 오류가 발생했습니다'
  },
  noCoursesWare: '본 수업은 코스웨어가 없습니다.',
  // 教师联系方式
  teacherContact: {
    tip: '궁금한 점이 있으시면 선생님께 문의하십시오.',
    copy: '복사',
    addContact: 'QR 코드를 스캔하여 교사의 위챗을 추가하십시오.'
  },
  // 课堂评价
  nps: {
    feedback: '수업 평가',
    enjoyTip: '수업이 즐거웠나요?',
    contactTip: '이유를 말씀해주시면 Think Academy가 더 잘 나아갈 수 있도록 도와줄 수 있습니다',
    reason: '자세한 이유는 여기에 써주십시오.',
    submitBtn: '제출',
    submitSuccess: '제출에 성공하셨습니다. 피드백을 보내주셔서 감사합니다.'
  },
  wrongQuestionBook: {
    title: '오답 노트'
  },
  expand_fold: '거두기',
  expand_more: '더 보기',
}
