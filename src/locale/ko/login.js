// 登录页
export default {
  // 通用
  common: {
    email: '이메일',
    emailAddress: '이메일 주소',
    phoneNumber: '휴대폰 번호',
    phoneNumberOrEmail: '전화번호 혹은 이메일',
    verificationCodePlaceholder: '인증코드',
    passwordPlaceholder: '비밀번호를 입력하십시오.',
    studentId: '수강생 번호',
    signIn: '로그인',
    inputInvalid: '잘못된 입력',
    incorrectId: '수강생 번호 또는 비밀번호가 옳바르지 않습니다. 다시 시도해 주십시오.',
    loginAgain: '다시 로그인',
    emailReg: '유효한 이메일 주소를 입력하하십시오.',
    continue: '계속하기',
    是否还没有账号: '계정이 없으신가요?',
    立即注册: '계정 만들기',
    已经注册过账号: '이미 가입하셨나요? ',
    立即登录: '로그인',
    注册: '계정 만들기',
    我们向您发送了一个验证码: '인증코드가 발송되였습니다',
    请在下方输入以进行验证: '아래에 입력하여 인증을 하세요',
    您似乎还未注册: '가입하지 않으신 것 같습니다.',
    您已经注册过账号: '이미 가입하셨나요?',
    切换其他登录方式: '로그인 방식 바꾸기',
    找到X个可能是您的账号: '당신의 계정일 가능성이 있는 계정 {num}개를 찾았습니다',
    如果是您的账号:
      '고객님의 계정이라면 바로 로그인하시고, 아니면 새 계정을 만드시는 것을 추천드립니다.',
    已存在的账号: '이미 존재하는 계정',
    继续创建新账号: '새 계정 계속 만들기',
    您是否要更新现有账户: '기존 계정을 업데이트하시겠습니까?',
    将此注册信息更新到现有账户: '이 등록 정보를 기존 계정으로 업데이트하세요',
    账号更新成功: '계정 업데이트 성공',
    手机: '전화번호',
    邮箱: '이메일'
  },
  // 验证码登录页
  verificationCodePage: {
    title: '인증번호 로그인',
    description: `등록되지 않은 휴대폰번호는 자동으로 등록됩니다.`,
    emailDesc: '등록된 이메일 주소가 없을 경우 로그인 후 자동으로 계정이 생성됩니다.',
    footerTitle: '인증번호 로그인',
    skip: '뛰어넘기',
    send: '보내기',
    reSend: '다시 보내기',
    back: '돌아가기',
    verificationTips: {
      confirmPolicy:
        '계속하려면 아래의 사용자 협의, 개인 정보 보호 정책 및 아동 개인 정보 보호 정책에 동의함을 체크해 주십시오.',
      enterAccount: '계정 번호를 입력해 주십시오.',
      enterPhoneNumber: '전화번호를 입력하십시오.',
      phoneNumberIncorrect: '전화번호가 잘못되었습니다.',
      enterVerificationCode: '인증코드'
    }
  },
  // 密码登录页
  passwordPage: {
    title: '비밀번호 로그인',
    mobileDescription: `비밀번호 설정 후 비밀번호를 이용하여 로그인하실 수 있습니다.`,
    studentIdDescription: `로그인 후 "나의 계정"에서 수강생 번호를 찾으실 수 있습니다.`,
    footerTitle: '비밀 번호로 로그인',
    forgotPassword: '비밀번호를 잊으셨나요?',
    confirmPolicyTip:
      '계속하려면 아래의 사용자 협의, 개인 정보 보호 정책 및 아동 개인 정보 보호 정책에 동의함을 체크해 주십시오.',
    otherLoginType: '기타 로그인 방식'
  },
  // 协议信息
  policyInfo: {
    desc: '우선 동의함을 체크해 주십시오',
    and: '와'
  },
  // 踢出提示
  kickoutNotice: {
    message: '계정 안전을 위해 다시 로그인하십시오.'
  }
}
