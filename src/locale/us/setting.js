// setting
export default {
  // 设置菜单
  menus: {
    settings: 'Settings',
    update: 'Update',
    cleanCache: 'Clear Cache',
    aboutUs: 'About Us',
    feedback: 'Feedback',
    reportLogs: 'Report Logs',
    logout: 'Log Out',
    checkDevice: 'Device Test'
  },
  // 关于我们
  aboutUs: {
    // 隐私协议
    policyTips: {
      temsOfUse: 'Terms of Use',
      privacyPolicy: 'Privacy Policy',
      internetSafetyPolicy: 'Internet Safety Tips',
      childPrivacy: 'child Privacy'
    }
  },
  // 设置
  settings: {
    savePicture: 'Save location of screenshot picture',
    chooseCampus: 'Choose campus',
    chooseLanguage: 'Choose language',
    browse: 'Browse',
    cancel: 'Cancel',
    confirm: 'Confirm',
    renderMode: 'Render mode',
    oneRender: '1x',
    twoRender: '2x',
    采集模式: 'Collection mode',
    通用: 'Universal',
    兼容: 'Compatible'
  },
  // 反馈
  feedback: {
    title: 'FeedBack',
    problemDescriptorText: 'Report an issue',
    problemDescriptors: [
      'Features Malfunction',
      'Features Suggestions',
      'Teaching',
      'Overall Experience',
      'In-Class Experience',
      'Other Problems'
    ],
    feedBackInput: 'Description of issue',
    images: 'Images',
    optional: 'Optional',
    maximuImages: 'Maximum {num} images',
    maxFileSize: 'File size exceeds 10M, please try again!',
    fileFormatIncorrect: 'File format is incorrect, please try again!',
    uploadFailTryAgain: 'File upload fail, please try again!',
    submit: 'Submit',
    backToCourses: 'Back to My Courses',
    inputInvilidate: 'Please provide additional information of your feedback here'
  },
  // 版本相关
  version: {
    versionChecking: 'Version checking...',
    version: 'Version',
    alreadyLastUpdated: 'is already the latest version.',
    newVersion: 'New version',
    nextTime: 'Not Now',
    updateNow: 'Update Now',
    downloadFailed: 'Download failed, please download the update from our website',
    byClickingHere: 'by clicking here',
    checkFailMessage: 'Check for updates failed, please try again!',
    betaVersion: 'Beta Version',
    downloading: 'Downloading upgrade files...',
    wait: `Installing...it may take 2-3 minutes and the software will restart once it's completed.`,
    winWait: `Download completed, please install according to the instructions`
  },
  // 清除缓存
  cleanCache: {
    content: 'Are you sure clean up courseware cache?',
    successMessage: 'Clear completed!',
    failMessage: 'Clear failed, please try again.'
  },
  // 上报日志
  reportLogs: {
    content: ' Are you sure you want to report exceptions log? ',
    logSubmitted: 'Log submitted, thank you for the feedback.',
    uploadfailed: 'Upload failed, please try again.'
  },
  // 登出
  logout: {
    content: 'Are you sure you want to exit the current account?'
  },
  // 时区
  timezone: {
    current: 'Current time zone is',
    choose: 'Choose Time Zone',
    search: 'Search time zone',
    noResult: 'No Result',
    invalidMsg: 'The time zone has been switched to '
  },
  localsetting: {
    chooseTitle: 'Choose an education system',
    chooseDesc: 'Please choose the education system that you want your child to follow.'
  }
}
