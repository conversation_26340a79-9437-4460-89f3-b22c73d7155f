/**
 * 互动配置
 */
export default {
  // 投票
  vote: {
    title: 'Vote',
    results: 'Results',
    voteResult: 'Result',
    true: 'TRUE',
    false: 'FALSE',
    correctTitle: 'Congratulations',
    correctTips: `You‘ve got coins`,
    rightTipsByPlayback: [`Congratulations`, `You‘ve got coins`, `Great Job!`],
    rightTipsByLive: [`Unfortunately`, `You‘ve got coins`, `Better luck next time！`],
    repeatSubmitMessage: `You've already voted!`,
    // 懂不懂
    isUnderstand: 'Do you understand?',
    understand: 'Yes, I understood',
    notUnderstand: "No, I didn't'",
    understandFeedback: 'Awesome job! Keep it up!',
    notUnderstandFeedback: "Don't worry, you will get this next time!"
  },
  // 游戏互动
  gameCourseware: {},
  // 课间休息
  classRest: {
    title: 'Break Time',
    startTip: 'Break Time',
    endTip: "Time's Up"
  },
  // 送礼物
  gift: {
    // 请选择礼物提示
    pleaseChooseGiftNotice: 'Please Choose a gift ~',
    // 金币不足
    notEnoughCoin: 'Your coins are not enough:(',
    // 可送礼物次数已到达
    notEnoughTimes: 'Thank you! Only three gifts each time :)'
  },
  // 答题板
  coursewareBoard: {
    true: 'TRUE',
    false: 'FALSE',
    yourAnswer: 'Your answer',
    rightAnswer: 'Right answer',
    correctAnswer: 'Correct answer'
  },
  // 拍照上墙涂鸦
  graffiti: {
    yellow: 'yellow',
    red: 'red',
    blue: 'blue'
  },
  // 拍照上墙
  photoWall: {
    graffitiBtnName: 'Doodle Board',
    graffitiDesc1: `* Is the photo blurry?`,
    graffitiDesc2: `Use the doodle board to submit!`,
    cameraBtnName: 'Use Camera',
    confirmNotice: [`Confirm submission?`, `After submitting, you cannot modify your answer`],
    shortCutTip: `Tips: Tap this space bar to take photos`,
    notjoinTip: `The teacher has closed the question Look forward to your participation next time!`,
    hasAccessNotice: [
      `Complete the exercise on your handout or scratch paper`,
      `Click below to take a photo and upload it`,
      `After submitting successfully, you may view the grading results in the box in the upper right corner`
    ],
    notAccessNotice: [
      `Hmm, your camera isn't working...`,
      `Use the doodle board to finish the exercise.`
    ],
    endNotice: [
      `The teacher has ended the interaction, do you wish to submit your answer?`,
      `You'll return to class after 5 seconds`
    ],
    grading: 'Grading',
    correct: 'Correct',
    incorrect: 'Incorrect',
    halfCorrect: 'Partially correct',
    answerCorrect: 'Answer is correct'
  },
  // 随机连线
  randomCall: {
    title: 'Lucky Draw',
    finishedNotice: {
      self: `Congratulations! You're the WINNER!`,
      other: 'The lucky student is {name}'
    }
  },
  // 随机连麦
  randomVideoLink: {},
  // 红包
  redPacket: {
    received: 'Received',
    unfortunatelyRedNotice: [`Not this time buddy…`, `gotta work harder next time~`],
    oopsRedNotice: `You've received the red packet already`,
    congrats: 'Congrats!'
  },
  // 视频连麦
  videoLink: {
    joinMessage: 'Come and join the video chat!',
    joinButtonName: 'Raise your hand',
    waitMessage: 'Waiting for the teacher to select',
    cancelButton: 'Cancel',
    raiseHandButton: 'Raise hand',
    speaking: 'Speaking...'
  },
  // 集体发言
  collectiveSpeech: {
    permissionDeniedTips: 'Microphone permission is denied. Enable microphone in settings.'
  },
  redRain: {
    collect: 'Collect',
    toastTip: 'Teacher just ended the game',
    loading: 'Loading..',
    NetworkFailTip:
      'Server connection timeout. Please check the internet connection and try again.',
    RetryBtn: 'Try again',
    title: 'Candy Bomb'
  },
  // 填一填
  nonPersetFillBlank: {
    submitSuccessfully: 'Submit successfully',
    submitFailed: 'Submit failed, please try again',
    inputPlaceholder: 'Type your answer here',
    clear: 'Clear',
    submit: 'Submit',
    myAnswer: 'My Answer'
  },
  praiseList: {
    fileName: 'Honor Roll_{firstTitle}_{lessonName}_{courseName}',
    saveText: 'Screenshot saved',
    savePath: 'Desktop/Think-Academy-Screenshot',
    savingText: 'Saving...',
    emptyText: 'No one on the merit list of this class'
  },
  schulteTable: {
    schulteTable: 'Schulte Table',
    naturalNumber: 'Natural Number',
    primeNumber: 'Prime Number',
    normalMode: 'Normal Mode',
    shuffleMode: 'Shuffle Mode',
    nextNumber: 'Next Number',
    duration: '{duration}s',
    gameInstructions: [
      'Click on natural numbers from {start} to {end} in ascending order.',
      'Click on prime numbers from {start} to {end} in ascending order.'
    ],
    gameObjectives: [
      'Click on the numbers in order from smallest to largest,',
      'the faster you click the more coins you get！'
    ],
    gameCompleted: 'Game Completed',
    newRecord: 'New Record',
    firstChallenge: 'First Challenge',
    yourCurrentTime: 'Your current time',
    yourBestTime: 'Your best time',
    leaderboard: 'Leaderboard',
    congratulations: 'Congratulations！',
    tryAgain: 'Almost there! Try it next time！',
    gameOver: 'Game time is up',
    leaderboardTip: ['Leaderboard will be published soon!', 'Are you ready to check it out?'],
    submitFailTitle: 'Submission Failed',
    submitFailTip: 'Server connection timeout. Please check the internet connection and try again.',
    submitFailBtn: 'Try again'
  }
}
