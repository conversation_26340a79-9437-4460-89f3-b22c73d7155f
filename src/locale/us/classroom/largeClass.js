/**
 * 大班直播
 */
export default {
  // 导航区
  header: {
    groupVideoButtonName: 'Group video',
    deviceTestButtonName: 'Device testing',
    teacherMessageOnly: 'Teacher Message Only'
  },
  // 播放器区
  players: {
    mute: 'Mute',
    unmute: 'Unmute'
  },
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: 'Your teacher will be here soon',
    gameConfig: {
      gameTip: 'Teacher demonstrating',
      gameToast: 'You cannot play games yet while the teacher is demonstrating.'
    }
  },
  // 聊天
  chats: {
    newMessage: 'New Message',
    teacherOnly: 'Teacher only',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: 'Chatbox Closed',
      2: 'Chatbox Closed',
      3: '',
      4: '',
      5: 'Connecting…'
    },
    inputPlaceholder: 'Say something',
    hotWordList: ['Done!', 'I don’t understand', 'I understand!', 'hahaha'],
    msgTip: {
      CONNECT: 'Chat server connected.',
      DISCONNECT: 'Chat server disconnected.',
      RECONNECT: 'Chat server reconnecting...',
      BAN_SPEECH: 'Your VIP Teacher just closed your chatbox.', // 禁言
      RELIEVE_SPEECH: 'Your VIP Teacher just opened your chatbox.', // 解除禁言
      SPEECH_INTERVAL: 'Please speak in 3 seconds.',
      SPEECH_MINE: 'Me',
      SPEECH_SYS: 'System Message',
      SPEECH_TEACHER: 'Teacher',
      SPEECH_EMPTY: 'Please enter valid information!',
      REMOTE_LOGIN: 'Remote login, please refresh the page!',
      CHAT_DISCONNECT: 'Server connection lost, currently unable to speak.',
      ENTER_ROOM: 'entered classroom'
    },
    level: {
      wood: 'Wood',
      gold: 'Gold',
      silver: 'Silver',
      diamond: 'Diamond'
    },
    reply: 'Reply',
    gotIt: 'Got it'
  },
  // 视频回显
  videoGroup: {
    textConfig: {
      matching: 'Matching...',
      noticeAccessDenied:
        'Enable camera access for Think Academy under Security&Privacy in System Perferences'
    },
    tabNames: ['Video', 'Chat'],
    closedNotice: 'Group video closed',
    localVideoAudition: {
      notice: 'Class auditing'
    }
  },
  // 金币显示
  coins: {
    title: 'My coins'
  },
  // 举手
  raiseHand: {
    buttonName: 'Raise hand'
  },
  // 回放
  playback: {
    notice: 'Watching Playback'
  }
}
