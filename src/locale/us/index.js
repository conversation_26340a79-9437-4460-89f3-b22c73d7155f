import common from './common'
import login from './login'
import courses from './courses'
import setting from './setting'
import account from './account'
import classroom from './classroom'
import today from './today'
import views from './views'
import growthHandbook from './growthHandbook.js'
import blindBox from './blindBox.js'
import myBackpack from './myBackpack.js'

export default {
  common,
  login,
  setting,
  courses,
  account,
  classroom,
  today,
  growthHandbook,
  blindBox,
  myBackpack,
  ...views,
  liveroomMicFirstTipAlertTitlePC: 'Hold down the space bar or microphone button to speak',
  liveroomMicFirstTipAlertDoneTitle: 'Got it',
  liveroomMicSpeakingHUDTitle: 'Speaking',
  liveroomKeepMuteModeToast: 'The teacher has muted everyone’s microphone',
  liveroomPushToTalkModeToast: 'The teacher has unmuted everyone’s microphone',
  liveroomKeepMicOnModeToast: 'The teacher has set the microphone to Push-to-Talk mode',
  老师已将麦克风设置为自由发言模式: 'The teacher has set the microphone to Open Mic mode',
  liveroomHoldDownMicBtnWhenSpeakingToastForPC:
    'Please hold down the space bar or microphone button when speaking',
  liveroomDisableOthersVideoMenu: "Mute Others' Video",
  磁盘可用空间不足: 'Insufficient disk space available, please clean up the disk.'
}
