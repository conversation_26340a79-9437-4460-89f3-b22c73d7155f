export default {
  // 积分商城
  coinsMall: {
    title: 'Think Coins Mall'
  },
  // 设备拦截
  deviceIntercept: {
    interceptInfo: {
      title: 'Device Not Compatible',
      content: `Sorry, your device is not supported. Please use the software on devices that meet the minimum requirements.`
    },
    recommendInfo: {
      title: 'Device Not Recommend',
      content: `Sorry, your device is not recommended for an ultimate class experience. Please use the software on devices that meet the minimum requirements.`
    },
    deviceInfo: {
      compatibility: 'Compatibility',
      macTips: 'MacOS 10.12 and above',
      windowsTips: 'Windows 7 and above',
      configurationsTitle: 'configurations',
      configurationsInfo: 'CPU 4 Cores, 4GB RAM',
      deviceTitle: 'Device',
      notAvailableDevice: 'Chromebook and Surface Book are NOT recommended'
    }
  },
  // 课前准备页
  prepareClass: {
    great: 'Great',
    gotIt: 'Got it',
    title: `Just a moment, we're preparing the class...`,
    audioAccess: 'Microphone access required',
    audioAvailable: 'No microphones available',
    cameraAccess: 'Camera access required',
    cameraAvailable: 'No cameras available',
    audioAccessWindow: 'Microphone access required',
    audioAccessContent:
      'Please allow <PERSON> Academy to access your microphone in the system Settings, otherwise the teacher will not be able to hear you.',
    cameraAccessWindow: 'Camera access required',
    cameraAccessContent:
      'Please allow Think Academy to access your camera in system Settings, otherwise the teacher will not be able to see you.',
    networkWeek: 'Poor network connection',
    networkGood: 'Good network connection',
    networkGoodContent:
      'Your internet connection is good and meets the requirements of the Think Academy class.',
    networkWeekContent:
      'Poor network connection may cause a delay during class. Please switch to another network or try restarting your router and moving closer to your wireless router to improve your network connection if you are using a Wi-Fi network.',
    retestNetwork: 'Re-test',
    testSpeed: 'Testing internet speed',
    enterName: 'Enter your nickname',
    setNickName: 'Set Nickname',
    setNickNameTip: '65 characters or less, cannot contain special characters',
    setNickNameError: 'Failed to set nickname, please try again',
    enterBtn: 'Enter name',
    hasNotEnter: `You haven't entered your nickname`,
    enterClassroom: 'Enter',
    enterClassroomError: 'Failed to enter classroom, please try again',
    done: 'Done',
    redownload: 'Redownload',
    coursewareDownload: 'Courseware download complete',
    onlineCourseware: 'Online courseware',
    onlineCoursewareContent:
      'Try using online courseware when unable to download offline courseware, but this way is not reliable.',
    confirmUser: 'Confirm',
    coursewareDownloading: 'Downloading courseware, {speed}, {minutes} left.',
    coursewareDownloadingSlow: 'more than 60 minutes',
    coursewareDownloadWeek: 'Courseware download failed, please try again',
    coursewareDownloadWeekTitle: 'Courseware download failed',
    coursewareDownloadWeekContent: 'Please check your network connection and try downloading again',
    playbackError: 'This course does not support playback or playback has not been generated',
    minute: 'min',
    second: 'sec',
    waiting: 'Please wait until the file is downloaded',
    networkBtn: {
      good: 'Good',
      normal: 'Normal',
      retest: 'Retest',
      weak: 'Weak'
    },
    editNickname: 'Edit Nickname',
    validateInput: 'This input is invalid.',
    startAgo: 'Class started {changeMinutes} minutes ago, come and join us!',
    willStart: 'Class starts in {changeMinutes} minutes',
    missClass: 'Watch playback if you missed the live classes',
    joinClass: 'Join class',
    continue: 'Continue',
    confirm: 'Confirm',
    downloadTip: {
      coursewareDownloading: 'Courseware downloading',
      downloadReson: 'Downloading courseware in advance saves time in class',
      incomplete: 'Courseware download incomplete',
      downloadComplete: 'Download completed',
      DownloadFailed: 'Download failed'
      // subtitle: 'Downloading courseware in advance saves time in class'
    },
    checkIn: {
      onTime: 'Get to class on time',
      title: 'Check in',
      missTips: ['The class has started...', `You've missed the check-in coins:(`],
      firstCheckInMessage: 'Please check in first',
      checkInButtonName: 'Check in',
      checkInSuccess: 'Sign in successfully',
      checkInFail: 'Sign in failed'
    }
  }
}
