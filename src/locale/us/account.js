// 账户相关
export default {
  // 菜单
  menus: {
    editProfile: 'Edit Profile',
    myAccount: 'My Account',
    temporaryClassroom: 'Temporary Classroom',
    accountInfo: 'Account Info'
  },
  // 个人信息
  personalInformation: {
    title: 'Personal Information',
    displayName: 'Nickname',
    displayNamePlaceholder: 'Please enter nickname',
    displayNameUse: '65 characters or less, cannot contain special characters',
    studentFirstName: 'Student First Name',
    studentLastName: 'Student Last Name',
    enterFirstName: 'Enter First Name',
    enterLastName: 'Enter Last Name',
    select: 'Please Select',
    email: 'Email',
    enterEmail: 'Enter Email',
    backToCourses: 'Back to My Courses',
    save: 'Save',
    gradeTitle: `Your Child's Grade`,
    uploadAvatarTitle: 'Replace Profile Picture',
    line: 'Line',
    weChat: 'WeChat',
    whatsApp: 'WhatsApp',
    enterLine: 'Enter Line',
    enterWeChat: 'Enter WeChat',
    enterWhatsApp: 'Enter WhatsApp',
    description:
      "WeChat / WhatsApp / Line will be used for teachers to communicate with parents about students' learning."
  },
  validateInput: {
    inputRequire: 'This input is required.',
    maxLength: 'Maximun 32 letters. Letters supported.',
    invalid: 'This input is invalid.'
  },
  modifyPassword: {
    noEmailOrPhoneLinked:
      'The password cannot be changed. Because no mobile phone or email address linked',
    changePasswordTips:
      'After changing the password, all other logged-in devices will be logged out. So please ensure your child is currently not in a live class',
    cancel: 'Cancel',
    confirmChange: 'Change Password',
    confirm: 'Confirm',
    title: 'Password Settings',
    subTitle:
      'After setting the password, you can log in via [mobile number + password], or [email + password], or [student number + password]',
    modifyText: [
      'This password is shared by all student accounts in your family.',
      `Please enter 6-20 characters, contains at least one letter and one number.Only letters and
      numbers are accepted.`
    ],
    placeholder: ['New Password', 'Confirm Password'],
    lengthError: 'Password must be between 6-20 characters',
    notMatch: ' Passwords do not match',
    notFormat: 'Password format is not accepted',
    successMessage: 'Password set successfully',
    failMessage: 'Password set failed'
  },
  accountVerification: {
    title: 'Account Verification',
    subTitle: 'Please help us confirm your identity and secure your account',
    nextStep: 'Next Step',
    confirmPolicy: 'Please confirm the policies to continue',
    clickToResetPw: ['Click here', `to reset your family's login password`],
    copy: 'Copy',
    current: 'Current',
    addStudent: 'Add a Student',
    otherStudents: 'Other Students',
    note: 'A maximum of 6 students can be associated',
    maxAccount: 'Max of 6 has Reached',
    switchedSuccessfully: 'Switched Successfully',
    switchedFailed: 'Switched Failed',
    copiedFailed: 'Copied Failed',
    copiedSuccessfully: 'Copied Successfully',
    backToAccount: 'Back To My Account',
    done: 'Done',
    noAccountAssociated: 'No account associated',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    validationNotices: [
      'Please enter your account information',
      'Please enter your phone number',
      'Phone number is incorrect',
      'Please enter the verification code'
    ],
    validationEmailNotices: [
      'Please enter your account information',
      'Please enter your email adress',
      'Email adress is incorrect',
      'Please enter the verification code'
    ],
    sendFailedTryAgain: 'Failed to send. Please try again'
  },
  myAccount: {
    title: 'My Account',
    addFailedMessage: 'Failed to add student, please try again',
    addSuccessedMessage: 'Student added successfully'
  },
  accountInfo: {
    title: 'Account Info',
    tip: 'Nickname will be used during class,comments,ect.',
    注册信息: 'Registration Information',
    手机号: 'Mobile',
    邮箱: 'Email',
    已绑定: 'Bound',
    未绑定: 'Unbound account',
    你即将离开当前页面: 'You are about to leave Think Academy and jump to another page',
    下一步: 'Next',
    取消: 'Cancel',
    解绑X账号: 'Unlink {type} account',
    解绑后: 'After unbinding, you will no longer be able to log in with your {type} account',
    账号绑定成功: 'Account bound successfully'
  },
  change: {
    title: 'Change mobile number',
    账号安全: 'Account Security: Verify Your Identity Information',
    请输入验证码进行验证: 'Please enter the verification code {phone}',
    绑定手机号: 'Bind your mobile number',
    请输入变更后的手机号: 'Please enter the new mobile number',
    绑定邮箱: 'Bind email',
    请输入变更后的邮箱: 'Please enter the updated email'
  }
}
