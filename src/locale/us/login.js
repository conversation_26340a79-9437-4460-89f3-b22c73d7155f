// 登录页
export default {
  // 通用
  common: {
    phoneNumber: 'Phone Number',
    email: 'Email',
    phoneNumberOrEmail: 'Mobile number or Email',
    emailAddress: 'Email Address',
    verificationCodePlaceholder: 'Verification Code',
    passwordPlaceholder: 'Please enter your password',
    studentId: 'Student ID',
    signIn: 'Sign in',
    inputInvalid: 'This input is invalid',
    incorrectId: 'Incorrect student ID or password, please try again',
    loginAgain: 'Login again',
    emailReg: 'Please enter a valid Email address',
    continue: 'Continue with',
    是否还没有账号: "Don't have an account？",
    立即注册: 'sign up now',
    已经注册过账号: 'Already have an account？',
    立即登录: 'sign in now',
    注册: 'sign up',
    我们向您发送了一个验证码: 'We sent you a code',
    请在下方输入以进行验证: 'Please enter the verification code {phone}',
    您似乎还未注册: "It seems you haven't registered.",
    您已经注册过账号: 'It seems you haved registered.',
    切换其他登录方式: 'Other login methods',
    找到X个可能是您的账号: 'Found {num} possible accounts that might be yours',
    如果是您的账号:
      'If it is your account, we suggest you log in directly. If not, please create a new account',
    已存在的账号: 'Exsiting account',
    继续创建新账号: 'Create a new account',
    您是否要更新现有账户: 'Would you like to update the existing account？',
    将此注册信息更新到现有账户: 'Update this registration information to the existing account',
    账号更新成功: 'successful',
    手机: 'Mobile',
    邮箱: 'Email'
  },
  // 验证码登录页
  verificationCodePage: {
    title: 'OTP Login',
    description: `Don't have an account? Enter your phone number to create one.`,
    emailDesc: 'Don’t have an account? Enter your email address to create one',
    footerTitle: 'OTP Login',
    skip: 'Skip',
    send: 'Send',
    reSend: 'Resend',
    back: 'Back',
    verificationTips: {
      confirmPolicy: 'Please confirm the policies to continue',
      enterAccount: 'Please enter your account information',
      enterPhoneNumber: 'Please enter your phone number',
      phoneNumberIncorrect: 'Phone number is incorrect',
      enterVerificationCode: 'Please enter the verification code'
    }
  },
  // 密码登录页
  passwordPage: {
    title: 'Password Login',
    mobileDescription: `You can sign in with password if you have set it for your account.`,
    studentIdDescription: `You can find your child's Student ID in 'My Account' after login.`,
    footerTitle: 'Password Login',
    forgotPassword: 'Forgot Password?',
    confirmPolicyTip: 'Please confirm the policies to continue',
    otherLoginType: 'or password login with'
  },
  // 协议信息
  policyInfo: {
    desc: 'As parent/ legal guardian, I agree to Think Academy’s',
    and: 'and'
  },
  // 踢出提示
  kickoutNotice: {
    message: 'For the safety of your account, please login again'
  }
}
