// 课程中心
export default {
  // 课程列表页
  list: {
    title: 'My Courses',
    titleTip: 'Your current location is Mars school',
    tabList: {
      all: 'All',
      current: 'Current',
      completed: 'Completed'
    },
    courseTabList: {
      live: 'Live Lessons',
      record: 'Recorded Lessons'
    },
    noCourses: 'No courses under this category',
    switchSingleAccount:
      'There are courses under your sub-account "{account}", click to switch accounts',
    switchMultipleAccount: 'Your have courses in multiple student accounts, click to switch',
    switchMultipleSchool: 'Your have courses in multiple schools, click to switch',
    switchSingleSchool: 'There are courses under your school "{school}", click to switch school',
    switchAccountText: 'Switch student account',
    switchSchoolText: 'Switch school',
    planName: 'Lesson'
  },
  lessonType: {
    formal: 'Purchased class',
    playback: 'Playback only',
    audition: 'Audit class',
    temporary: 'Office hour'
  },
  // 课程卡片
  courseCard: {
    classInSession: 'Class in Session',
    nextSession: 'Next Lesson',
    enter: 'Enter',
    completed: 'Completed',
    refunded: 'Refunded',
    playback: 'Playback only',
    startAuditing: 'Start auditing',
    view: 'View',
    reportTips: 'Your lesson report for "{course}" is ready.',
    parentAudit: 'Parent audit',
    alreadyStarted: `Lesson {courseNum} has been already started`,
    nextLesson: 'Next lesson'
  },
  // 录播课
  recordCourse: {
    courseValidUntil: 'Expiration date: ',
    enter: 'Enter',
    expired: 'Expired',
    refunded: 'Refunded',
    expiredTip: 'This course is expired',
    permanentTip: 'Course valid permanently',
    goToTest: 'Go to Test',
    report: 'Report',
    reload: 'Reload',
    loadFailed: 'Failed to load',
    loading: 'Loading',
    saveShotScreen: 'The screenshot is saved',
    switchChannel: 'Switch channel',
    playNextVideo: 'Play next video'
  },
  // 录播课播放器提示
  recordPlayerTips: {
    0: 'Play',
    '0-1': 'Pause',
    1: 'Rewind 10s',
    2: 'Fast forward 10s',
    9: 'Screenshot'
  },
  // 课程详情页
  detail: {
    title: 'Course Details',
    specialTitle: '{prefix} Details',
    // 播放按钮
    playButton: {
      // 未开始状态
      startSoon: 'To Start',
      playbackGenerating: 'Playback Generating',
      playback: 'Playback',
      playbackExpired: 'Playback Expired',
      unpaid: 'Unpaid',
      finished: 'Finished',
      live: 'Enter',
      startAuditing: 'Start auditing'
    },
    classRoom: 'ClassRoom',
    homework: {
      status: [
        'Unpublished',
        'Do Homework',
        'Reviewed',
        'Submitted',
        'Expired',
        'View Solution',
        'Start Test'
      ],
      title: 'HomeWork'
    },
    exam: {
      status: ['Unpublished', 'Go to Test', 'Answer Submitted', 'Test Reviewed', 'Test Expired']
    },
    examTip: 'The exam has not been released yet',
    resources: 'Learning Materials',
    lessonReport: 'Lesson Report',
    playBackTip: 'Playback will be available 2 hours after the live class ends.',
    reportBtnText: 'Reviewed',
    reportSubmitText: 'Submitted',
    reportExpiredText: 'Expired',
    startTest: 'Go to Test',
    reportTips: `Report available from {canViewTime}`,
    expiredTips:
      ' The deadline for submission has passed. Please remember to turn in your assignmenton time',
    file: 'File',
    files: 'Files',
    transferred: 'Rescheduled',
    classNotes: 'Class notes',
    learningMaterials: 'Learning materials'
  },
  switchAccount: {
    title: 'Switch student account',
    subTitle: 'You have courses under the following sub-accounts, click to switch',
    cancel: 'Cancel',
    confirm: 'Confirm',
    switchSuccess: 'Switched Successfully',
    switchFail: 'Failed to switch. Please try again later',
    switchIng: 'Switching...',
    emptyTip: 'Please select a student account',
    loginTitle: 'Multiple students in your family',
    loginSubTitle: 'Please select which student you want to log in to.'
  },
  switchSchool: {
    title: 'Switch school',
    subTitle: 'You have courses under the following schools, click to switch'
  },
  // 面授课 - 如何上课
  faceToFace: {
    attendClass: 'How to Attend',
    finished: 'Finished',
    location: 'Location',
    copy: 'Copy',
    zoomId: 'Zoom ID',
    zoomPassword: 'Zoom Password',
    zoomDownload: 'Download Zoom',
    zoomDescribe: ` and enter the Zoom ID and password as above.Enter into the classroom 10 minutes before the live session on Zoom and enjoy your Think Academy experience.`,
    classinLink: 'Classin Link',
    classinDownload: 'Download Classin',
    classinDescribe: ` and register with the account you when you enrolled into the course. Enter into the classroom 10 minutes before the live session on Classin and enjoy your Think Academy experience.`,
    classroomDescribe: `Please arrive at the classroom 10 minutes before and enjoy your Think Academy experience.`,
    ipadDescribe: `Please attend the class using iPad App Thinkhub. If you have any questions, please call`,
    confirm: 'Got it'
  },
  // 课后作业
  assignment: {
    title: 'Homework',
    earnedNotice: 'You have earned',
    submissionNotice: 'coins for your early submission'
  },
  // 课前测
  previewQuestion: {
    title: 'Preview question',
    deadlineName: 'Deadline',
    deadlineTime: 'Deadline',
    answerTimeName: 'Answer time',
    deadlineBannerTips: 'Oops, this preview is past due. Please do your homework on time',
    problemDescriptors: [`Problem Descriptors`, `Total {totalScore} points`],
    confirmTitle: 'Do you want to start the preview now?',
    confirmSubtitle: `Time limit is {duration},good luck!`,
    solutions: 'Solutions',
    start: 'Start',
    deadlineTitle: 'You missed the deadline.',
    deadlineDesc: 'Oops, this exam is past due. Please do your homework on time next time!'
  },
  // 学习资料
  studyResources: {
    title: 'Learning Materials',
    download: 'Download',
    play: 'Play'
  },
  // 课堂报告
  lessonReport: {
    title: 'Lesson Report'
  },
  // 阶段考试
  exam: {
    title: 'Note',
    solutions: 'Check Solutions',
    report: 'Check results',
    start: 'Start Test',
    coinsTip: 'Coins will be rewarded after completion',
    confirmTitle: 'Reminder',
    confirmSubtitle: `You have {duration} in total. Good Luck!`,
    confirmBtn: 'Determine',
    cancelBtn: 'Cancel'
  },
  // 临时教室
  temporaryClassroom: {
    title: 'Temporary Classroom',
    noCourses: 'No courses yet',
    registrationCourses: 'Registration course',
    noCoursewareNotice: 'The course is not associated with courseware',
    statusName: {
      startSoon: 'To Start',
      playbackGenerating: 'Producing',
      playback: 'Playback',
      playbackExpired: 'Expired',
      enter: 'Enter'
    }
  },
  // 进课验证弹框
  confirmModal: {
    title: 'Attention',
    content:
      'Your child is in this live classroom on another device. When entering the classroom,\n {warning}',
    warning: 'your child will be forced out.',
    enterBtn: 'Enter',
    backBtn: 'Back',
    confirm: 'Confirm'
  },
  switchSchoolModal: {
    title: 'Reminder',
    content: 'Do you want to switch?'
  },
  resolutionRatioModal: {
    title: 'Reminder',
    content:
      'The resolution of your computer is lower than needed. \nPlease go to “Settings - Display” to lower the scaling factor.',
    confirm: 'Got it'
  },
  playback: {
    courseInformationError: 'Course information error',
    playbackInformationError: 'Playback information error',
    playbackInterfaceError: 'Playback interface error',
    playbackSourceIsNull: 'Playback source is null',
    playbackStampError: 'Playback stamp error'
  },
  noCoursesWare: 'No courseware in this course',
  teacherContact: {
    tip: 'If you have any questions, please contact your teacher',
    copy: 'Copy',
    addContact: `Please scan the QR code to add the teacher's WeChat`
  },
  // 课堂评价
  nps: {
    feedback: 'Feedback',
    enjoyTip: 'Did you enjoy this class?',
    contactTip: 'Tell us why to help Think Academy do better',
    reason: 'You can write the detailed reason here',
    submitBtn: 'Submit',
    submitSuccess: 'Submit successfully, thank you for your feedback'
  },
  expand_fold: 'Fold',
  expand_more: 'More',
}
