// 成长手册
export default {
  handBook: '成长手册',
  guideTitle: '如何玩转成长手册？',
  gotIt: '我明白了！',
  skip: '跳过',
  growthLevel: '成长段位',
  growthTask: '成长任务',
  weekTask: '本周任务',
  challengeTask: '挑战任务',
  activeTask: '活跃任务',
  process: '进度',
  unfinished: '进行中',
  drawDown: '领取',
  claimed: '已领取',
  highestLevel: '你已达到最高等级',
  needExp: '距离下一等级还需要{exp}经验值',
  tasksCompleted: '需完成任务数：{count}',
  tasksUncompleted: '待完成',
  congratulations: '恭喜获得以下物品',
  accept: '收下',
  startChallenge: '开始挑战',
  countDownTextForDay: '本周还剩{days}天{hours}小时',
  countDownTextForHour: '本周还剩{hours}小时',
  countDownTextForMinute: '本周还剩{minutes}分钟',
  requestError: '请求超时',
  backHome: '返回主页',
  rewardCollected: '奖励已领取',
  systemError: '系统错误（错误代码：{code}）',
  getExpDesc: '完成学习任务，获得经验值',
  upgradeDesc: '升级段位，解锁丰富有趣的奖励',
  toClassDesc: '去上课',
  doHomeworkDesc: '做作业',
  upgradeTitle: '段位升级',
  initUpgradeTitle: '恭喜获得初始段位！',
  exp: '经验值',
  coin: '金币',
  retry: '重试',
  networkErr: '网络异常，请连接网络后重试',
  摄像头初始化: '摄像头正在初始化，请3秒后重试'
}
