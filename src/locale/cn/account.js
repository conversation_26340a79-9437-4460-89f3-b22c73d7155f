// 账户相关
export default {
  // 菜单
  menus: {
    editProfile: '编辑个人信息',
    myAccount: '我的账号',
    temporaryClassroom: '临时教室',
    accountInfo: '账号信息'
  },
  // 个人信息
  personalInformation: {
    title: '个人信息',
    displayName: '昵称',
    displayNamePlaceholder: '请输入昵称',
    displayNameUse: '不超过 65 个字符，不能包含特殊字符',
    studentFirstName: '名字',
    studentLastName: '姓氏',
    enterFirstName: '请填写中文名字',
    enterLastName: '请填写中文姓氏',
    select: '选择年级',
    email: '邮箱',
    enterEmail: '编辑邮箱',
    backToCourses: '回到我的课程',
    save: '保存',
    gradeTitle: '年级',
    uploadAvatarTitle: '上传头像',
    line: 'Line',
    weChat: '微信',
    whatsApp: 'WhatsApp',
    enterLine: '编辑Line',
    enterWeChat: '编辑微信',
    enterWhatsApp: '编辑WhatsApp',
    description: '微信/WhatsApp/Line将用于教师与家长沟通学生的学习情况。'
  },
  validateInput: {
    inputRequire: '此输入为必填项',
    maxLength: '最大支持32个字符',
    invalid: '输入不符合要求'
  },
  // 修改密码
  modifyPassword: {
    noEmailOrPhoneLinked: '未绑定注册手机号和注册邮箱，暂时无法修改密码',
    changePasswordTips:
      '更改密码后，所有其他已登录的设备都将登出。因此，请确保您的孩子目前不在直播课堂。',
    cancel: '取消',
    confirmChange: '修改密码',
    confirm: '确认',
    title: ' 密码设置',
    subTitle: ' 设置密码后，可使用[手机号+密码]或[邮箱+密码]或[学员编号+密码]的方式登录',
    modifyText: [
      '此密码为您家庭中的所有学生帐户共享。',
      `请输入6-20个字符，至少包含一个字母和一个数字。仅包含字母和数字
      数字是可以接受的。`
    ],
    placeholder: ['新密码', '确认密码'],
    lengthError: '密码必须爲6-20个字符之间',
    notMatch: '密码不匹配',
    notFormat: '密码格式错误',
    successMessage: '密码设置成功',
    failMessage: '密码设置失败'
  },
  accountVerification: {
    title: '帐户验证',
    subTitle: '请确认您的身份以保护您的帐户安全',
    nextStep: '下一步',
    confirmPolicy: '请勾选同意下面的使用协议和隐私保护政策才能继续登录',
    clickToResetPw: ['点击此处', `重新设置您的登录密码`],
    copy: '复制',
    current: 'Current',
    addStudent: '添加一名学生',
    otherStudents: '其他学生',
    note: '最多可关联6名学生',
    maxAccount: '已满6个账号',
    switchedSuccessfully: '切换成功',
    switchedFailed: '切换失败',
    copiedFailed: '复制失败',
    copiedSuccessfully: '复制成功',
    backToAccount: '回到我的账户',
    done: '完成',
    noAccountAssociated: '没有关联的账户',
    newPassword: '新密码',
    confirmPassword: '确认密码',
    validationNotices: [
      '请输入您的帐户信息',
      '请输入您的手机号码',
      '手机号码不正确',
      '请输入短信验证码'
    ],
    validationEmailNotices: [
      '请输入您的帐户信息',
      '请输入您的电邮箱地址',
      '邮箱地址不正确',
      '请输入验证码'
    ],
    sendFailedTryAgain: '发送失败, 请重试'
  },
  myAccount: {
    title: '我的账号',
    addFailedMessage: '添加学生失败，请重试！',
    addSuccessedMessage: '添加学生成功'
  },
  accountInfo: {
    title: '账号信息',
    tip: '昵称会用于您在课堂的名称展示，评论等',
    注册信息: '注册信息',
    手机号: '手机号',
    邮箱: '邮箱',
    已绑定: '已绑定',
    未绑定: '未绑定',
    你即将离开当前页面: '你即将离开当前页面，并跳转到第三方页面',
    下一步: '下一步',
    取消: '取消',
    解绑X账号: '解绑 {type} 账号',
    解绑后: '解绑后，您将不能使用 {type} 进行登录',
    账号绑定成功: '账号绑定成功'
  },
  change: {
    title: '变更手机号',
    账号安全: '账号安全：验证您的身份信息',
    请输入验证码进行验证: '请输入验证码进行验证 {phone}',
    绑定手机号: '绑定手机号',
    请输入变更后的手机号: '请输入变更后的手机号',
    绑定邮箱: '绑定邮箱',
    请输入变更后的邮箱: '请输入变更后的邮箱'
  }
}
