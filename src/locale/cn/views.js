export default {
  // 积分商城
  coinsMall: {
    title: '积分商城'
  },
  // 设备拦截
  deviceIntercept: {
    interceptInfo: {
      title: '不兼容此设备',
      content: `抱歉，暂不支持当前设备，请在符合最低运行要求的设备上运行Think Academy应用`
    },
    recommendInfo: {
      title: '不建议使用此设备',
      content: `抱歉，您当前的设备可能无法流畅运行Think Academy应用，请在符合最低运行要求的设备上运行Think Academy应用`
    },
    deviceInfo: {
      compatibility: '操作系统',
      macTips: 'MacOS 10.12或者更高版本',
      windowsTips: 'Windows 7或者更高版本',
      configurationsTitle: '最低硬件配置',
      configurationsInfo: '4核心CPU，4GB运行内存',
      deviceTitle: '设备',
      notAvailableDevice: '不支持ChromeBook和Surface Book系列笔记本电脑'
    }
  },
  // 课前准备页
  prepareClass: {
    great: '正常',
    gotIt: '收下',
    title: `课前准备`,
    audioAccess: '麦克风权限未打开',
    audioAvailable: '没有可用的麦克风',
    cameraAccess: '摄像头权限未打开',
    cameraAvailable: '没有可用的摄像头',
    audioAccessWindow: '未开启麦克风权限',
    audioAccessContent: '请在系统设置中打开Think Academy的麦克风权限，否则老师将无法听到你的发言。',
    cameraAccessWindow: '未开启摄像头权限',
    cameraAccessContent: '请在系统设置中打开Think Academy的摄像头权限，否则老师将无法看到你。',
    networkWeek: '网络连接较差',
    networkGood: '网络良好',
    networkGoodContent: '您的网络连接良好，可以流畅使用Think Academy课堂。',
    networkWeekContent:
      '网络不佳可能会导致您上课时出现卡顿，请尝试切换至其它网络或者如果您使用Wi-Fi网络接入，请尝试重新启动路由器并靠近您的无线路由器以改善网络连接质量。',
    retestNetwork: '重新测试',
    testSpeed: '测试网络速度',
    enterName: '输入您的昵称',
    setNickName: '设置昵称',
    setNickNameTip: '不超过 65 个字符，不能包含特殊字符',
    setNickNameError: '昵称修改失败，请重试',
    enterBtn: '输入名称',
    hasNotEnter: `您还没有输入昵称`,
    enterClassroom: '进入教室',
    enterClassroomError: '进入教室失败，请重试',
    editNickname: '编辑昵称',
    validateInput: `输入无效`,
    done: '已下载',
    redownload: '重试下载',
    coursewareDownload: '课件下载完成',
    onlineCourseware: '使用在线课件',
    onlineCoursewareContent: '仅当无法下载离线课件时可尝试使用在线课件，但此方式并不可靠。',
    confirmUser: '确认使用',
    coursewareDownloading: '正在下载课件，{speed}，剩余{minutes}',
    coursewareDownloadingSlow: '时间大于60分钟',
    coursewareDownloadWeek: '课件下载失败，请重试',
    coursewareDownloadWeekTitle: '课件下载失败',
    coursewareDownloadWeekContent: '请检查您的网络并尝试重新下载',
    playbackError: '该课程没有回放或者回放还未生成',
    minute: '分钟',
    second: '秒',
    waiting: '请等待课件下载完成',
    networkBtn: {
      good: '良好',
      normal: '一般',
      retest: '重试网络',
      weak: '差'
    },
    startAgo: '课程已开始{changeMinutes}分钟，快进入教室吧',
    willStart: ' {changeMinutes}分钟后开始上课',
    missClass: '如果你错过了现场课程，请观看回放',
    joinClass: '上课',
    continue: '继续',
    confirm: '确定',
    downloadTip: {
      coursewareDownloading: '课件下载',
      downloadReson: '提前下载课件可以节省课堂时间',
      incomplete: ' 课件下载不完整',
      downloadComplete: '下载完成',
      DownloadFailed: '下载失败'
      // subtitle: '提前下载课件可以节省课堂时间'
    },
    checkIn: {
      onTime: '准时到达教室',
      title: '签到',
      missTips: ['课程已开始，', `无法获得签到奖励啦:(`],
      firstCheckInMessage: '请先签到',
      checkInButtonName: '签到',
      checkInSuccess: '签到成功',
      checkInFail: '签到失败'
    }
  }
}
