import common from './common'
import login from './login'
import courses from './courses'
import setting from './setting'
import account from './account'
import classroom from './classroom'
import today from './today'
import views from './views'
import growthHandbook from './growthHandbook.js'
import blindBox from './blindBox.js'
import myBackpack from './myBackpack.js'

export default {
  common,
  login,
  setting,
  courses,
  account,
  classroom,
  today,
  growthHandbook,
  blindBox,
  myBackpack,
  ...views,
  liveroomMicFirstTipAlertTitlePC: '按住空格键或麦克风按钮进行发言',
  liveroomMicFirstTipAlertDoneTitle: '我知道了',
  liveroomMicSpeakingHUDTitle: '正在发言',
  liveroomKeepMuteModeToast: '老师已关闭所有人的麦克风',
  liveroomPushToTalkModeToast: '老师已开启所有人的麦克风',
  liveroomKeepMicOnModeToast: '老师已将麦克风设置为按住发言模式',
  老师已将麦克风设置为自由发言模式: '老师已将麦克风设置为自由发言模式',
  liveroomHoldDownMicBtnWhenSpeakingToastForPC: '发言时请按住空格键或麦克风按钮',
  liveroomDisableOthersVideoMenu: '关闭他人视频',
  磁盘可用空间不足: '磁盘可用空间不足，请清理磁盘'
}
