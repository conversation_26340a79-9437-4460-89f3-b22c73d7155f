// 课程中心
export default {
  // 课程列表页
  list: {
    title: '我的课程',
    titleTip: '您当前是在火星分校',
    tabList: {
      all: '全部',
      current: '上课中',
      completed: '已结课'
    },
    courseTabList: {
      live: '直播课',
      record: '录播课'
    },
    noCourses: '还没有课程 / 此类别下没有课程',
    switchSingleAccount: '你的子帐号{account}下有课程，可点击切换',
    switchMultipleAccount: '你在其他多个子帐号里有课程，可点击切换',
    switchMultipleSchool: '你在其他多个分校里有课程，可点击切换',
    switchSingleSchool: '你的分校{school}下有课程，可点击切换',
    switchAccountText: '切换学员',
    switchSchoolText: '切换分校',
    planName: '课次'
  },
  lessonType: {
    formal: '正式课',
    playback: '回放课',
    audition: '旁听课',
    temporary: '临时课'
  },
  // 课程卡片
  courseCard: {
    classInSession: '上课中',
    nextSession: '下一节课',
    enter: '进入教室',
    completed: '已完成',
    refunded: '已退费',
    playback: '回放',
    startAuditing: '去旁听',
    view: '查看',
    reportTips: '“{course}”的课后报告已生成。',
    parentAudit: '家长旁听',
    alreadyStarted: `课次{courseNum}已开始`,
    nextLesson: '下一课次'
  },
  // 录播课
  recordCourse: {
    courseValidUntil: '此课程有效期至：',
    enter: '进入',
    refunded: '已退费',
    expired: '已过期',
    expiredTip: '此课程已失效',
    permanentTip: '此课程永久有效',
    goToTest: '去作答',
    report: '考试报告',
    reload: '重新加载',
    loadFailed: '加载失败',
    loading: '加载中',
    saveShotScreen: '截图已保存',
    switchChannel: '切换线路',
    playNextVideo: '播放下一视频'
  },
  // 录播课播放器提示
  recordPlayerTips: {
    0: '播放',
    '0-1': '暂停',
    1: '快进10秒',
    2: '快退10秒',
    9: '截图'
  },
  // 课程详情页
  detail: {
    title: '课程详情',
    specialTitle: '{prefix}详情',
    // 播放按钮
    playButton: {
      // 未开始状态
      startSoon: '暂未开始',
      playbackGenerating: '回放生成中',
      playback: '看回放',
      playbackExpired: '回放已过期',
      unpaid: '未购买',
      live: '进入教室',
      finished: '已结束',
      startAuditing: '去旁听'
    },
    classRoom: '教室',
    homework: {
      status: ['待发布', '待提交', '已批改', '已提交', '已过期', '查看答案', '待提交'],
      title: '作业'
    },
    exam: {
      status: ['未发佈', '去考试', '已作答', '已批改', '已过期']
    },
    examTip: '考试尚未发佈哦',
    resources: '资料',
    playBackTip: '直播课结束2小时后可播放',
    reportBtnText: '已批改',
    reportSubmitText: '已提交',
    reportExpiredText: '已过期',
    startTest: '去考试',
    reportTips: `{canViewTime}之后可查看报告`,
    lessonReport: '课堂报告',
    expiredTips: '超过作业提交的截止时间了，下次要准时提交作业呀',
    file: '文件',
    files: '文件',
    transferred: '调课',
    classNotes: '板书',
    learningMaterials: '学习资料'
  },
  switchAccount: {
    title: '切换学员',
    subTitle: '你的以下子帐号下有课程，可点击切换',
    cancel: '取消',
    confirm: '确定',
    switchSuccess: '切换成功',
    switchFail: '切换失败，请稍后再试',
    switchIng: '切换中......',
    emptyTip: '请选择一个学生账号',
    loginTitle: '此家庭组内有多个学员',
    loginSubTitle: '请选择你想登录哪个学员？'
  },
  switchSchool: {
    title: '切换分校',
    subTitle: '你的以下分校有课程，可点击切换'
  },
  // 面授课 - 如何上课
  faceToFace: {
    attendClass: '查看上课方式',
    finished: '已结束',
    location: '地点',
    copy: '复制',
    zoomId: 'Zoom ID',
    zoomPassword: 'Zoom 密码',
    zoomDownload: '下载Zoom',
    zoomDescribe: `并如上所述输入Zoom ID和密码。在Zoom现场会议前10分钟进入课堂，尽情思考学院经验。`,
    classinLink: 'Classin 链接',
    classinDownload: '下载Classin',
    classinDescribe: `并在注册课程时使用您的帐户注册。在Classin现场会议前10分钟进入课堂，享受你的Think Academy体验。`,
    classroomDescribe: `请提前10分钟到达教室，享受您的Think Academy体验。`,
    ipadDescribe: `请使用iPad应用程序Thinkhub上课。如果您有任何问题，请致电`,
    confirm: '知道了'
  },
  // 课后作业
  assignment: {
    title: '作业',
    earnedNotice: '你获得了',
    submissionNotice: '提前完成，额外奖励'
  },
  // 课前测
  previewQuestion: {
    title: '课前测',
    deadlineName: '截止日期',
    deadlineTime: '截止时间',
    answerTimeName: '作答时间',
    deadlineBannerTips: '超过作答截止时间了，下次要准时作答哦！',
    problemDescriptors: [`题目描述`, '（共 {totalScore}分）'],
    confirmTitle: '确定开始作答？',
    confirmSubtitle: '作答时间共{duration}，加油！',
    solutions: '解析',
    start: '开始作答',
    deadlineTitle: '测试已结束，未按时参加',
    deadlineDesc: '哎呀，超过作答截止时间了，下次要准时作答哦'
  },
  // 学习资料
  studyResources: {
    title: '学习资料',
    download: '下载',
    play: '播放'
  },
  // 课堂报告
  lessonReport: {
    title: '课堂报告'
  },
  // 阶段考试
  exam: {
    title: '考试须知',
    solutions: '查看题目解析',
    report: '查看报告',
    start: '开始作答',
    coinsTip: '作答完成有金币奖励哦',
    confirmTitle: '作答提醒',
    confirmSubtitle: `作答时间共{duration}，加油哦！`,
    confirmBtn: '确定',
    cancelBtn: '取消'
  },
  // 临时教室
  temporaryClassroom: {
    title: '临时教室',
    noCourses: '暂无课程',
    registrationCourses: '报名课程',
    noCoursewareNotice: '本课程未绑定课件',
    statusName: {
      startSoon: '暂未开始',
      playbackGenerating: '回放生成中',
      playback: '看回放',
      playbackExpired: '回放已过期',
      enter: '进入教室'
    }
  },
  confirmModal: {
    title: '进课提醒',
    content: '当前账号对应学生已在直播课程中，若选择进课，则会{warning}请选择是否继续进课？',
    warning: '将现存账号踢出直播课程，',
    enterBtn: '确认',
    backBtn: '取消',
    confirm: '确认'
  },
  switchSchoolModal: {
    title: '提醒',
    content: '您是否切换为此分校？'
  },
  resolutionRatioModal: {
    title: '提醒',
    content:
      '您当前的屏幕分辨率低于ThinkAcademy需要的最低分辨率，\n请在“系统设置 - 显示”中降低显示缩放比。',
    confirm: '知道了'
  },
  playback: {
    courseInformationError: '课程信息获取失败',
    playbackInformationError: '回放信息获取失败',
    playbackInterfaceError: '回放接口访问错误',
    playbackSourceIsNull: '回放地址为空',
    playbackStampError: '打点信息获取失败'
  },
  noCoursesWare: '当前课次没有课件',
  // 教师联系方式
  teacherContact: {
    tip: '如您有任何问题，请联系您的老师',
    copy: '复制',
    addContact: '请扫描二维码添加老师微信'
  },
  // 课堂评价
  nps: {
    feedback: '课程评价',
    enjoyTip: '这节课学的开心吗？',
    contactTip: '告诉我们原因可以帮助Think Academy做的更好',
    reason: '可以在这里写下详细原因',
    submitBtn: '提交',
    submitSuccess: '提交成功，感谢你的反馈'
  },
  wrongQuestionBook: {
    title: '错题本'
  },
  expand_fold: '收起',
  expand_more: '更多',
}
