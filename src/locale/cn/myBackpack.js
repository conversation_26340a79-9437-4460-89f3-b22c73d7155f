// 我的背包
export default {
  backpack: '我的背包',
  effect: '形象',
  decoration: '装饰',
  suit: '套装',
  head: '头部',
  face: '脸部',
  hand: '手部',
  avatarFrame: '头像框',
  messageBox: '消息气泡',
  stageFrame: '上台视频框',
  suitEmptyTips: '集齐的形象套装在这里展示哦',
  emptyTips: '当前分类下还没有物品，快去抽盒吧',
  openCamera: '打开摄像头',
  openCameraTips: '打开摄像头预览形象效果哦',
  selectedDefaultTips: '选择物品即可预览',
  selectedGoodsTips: '打开摄像头才能预览形象效果哦',
  effectSceneTips: '当老师在课堂中开启「允许展示形象」时，就可以在视频框看到你的形象啦',
  avatarFrameSceneTips: '选择一个头像框，你的头像都会佩戴上哦',
  messageBoxSceneTips: '课堂中，在聊天区发言就可以看到你独特的消息气泡咯',
  stageFrameSceneTips: '课堂中，上台发言时就可以看到你独特的上台视频框咯',
  requireCamera: 'Think Academy Classroom请求获取摄像头权限',
  requireCameraDesc: '打开摄像头即可预览形象贴纸效果',
  allow: '好的',
  notAllow: '不允许',
  previewTitle: '奖品预览',
  previewEffectTips: '你的形象效果会在课堂视频区展示，同学们都可以看到哦',
  previewAvatarFrameTips: '头像框会佩戴在你的头像上哦',
  previewMessageBoxTips: '消息气泡会在课堂聊天区展示，同学们都可以看到哦',
  previewStageFrameTips: '上台视频框会在你课堂上台时展示，同学们都可以看到哦',
  legendary: '传奇',
  epic: '史诗',
  rare: '稀有',
  uncommon: '高级',
  common: '基础',
  ruleTitle: '说明',
  rules1:
    '1. 背包内的虚拟物品仅支持在线小班课堂内展示，在在线大班课堂、面授课堂、旁听课等课堂内暂无法展示。',
  rules2: '2. 在在线小班课堂中，主讲老师可控制是否允许课堂内学生展示其形象（含头部、面部）。',
  rules3:
    '3. 当主讲老师在教师端打开「允许展示学生虚拟形象」开关时，同学们就可以看到你的虚拟形象贴纸了。',
  rules4: '4. 装饰类物品及部分形象类物品（如举手动效），主讲老师不作限制，佩戴上即可在课堂中展示。',
  gotIt: '我知道了',
  widdowRequireCamera: '请前往「设置」允许Think Academy访问摄像头'
}
