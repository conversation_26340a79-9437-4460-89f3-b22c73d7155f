/**
 * 互动配置
 */
export default {
  // 聊天
  chats: {
    newMessage: '新消息',
    teacherOnly: '仅看老师',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: '聊天已关闭',
      2: '聊天已关闭',
      3: '',
      4: '',
      5: '加载中'
    },
    inputPlaceholder: '说点什么吧',
    hotWordList: ['完成！', '我不懂', '我听懂了', '哈哈哈'],
    msgTip: {
      CONNECT: '聊天服务已连接',
      DISCONNECT: '聊天服务已断开',
      RECONNECT: '聊天服务重新连接中',
      BAN_SPEECH: '辅导老师已将你禁言', // 禁言
      RELIEVE_SPEECH: '辅导老师已将你解除禁言', // 解除禁言
      SPEECH_INTERVAL: '请3秒后再发言',
      SPEECH_MINE: '我',
      SPEECH_SYS: '系统消息',
      SPEECH_TEACHER: '老师',
      SPEECH_EMPTY: '输入的消息不合法',
      REMOTE_LOGIN: '当前地点不是常用登录地点，请刷新此页面',
      CHAT_DISCONNECT: '服务器连接已断开，当前不能发言',
      ENTER_ROOM: '进入了教室'
    },
    level: {
      wood: '青木',
      gold: '黄金',
      silver: '白银',
      diamond: '星钻'
    },
    chatbox: '班级聊天',
    frequently: '消息发送过于频繁',
    onlyteacher: '老师关闭了群聊，仅允许发送消息给老师。',
    all: '老师打开了群聊，允许发送消息给所有人。',
    teacherOff: '老师关闭了聊天',
    teacherOn: '老师开启了聊天',
    chatboxClosed: '班级聊天已关闭',
    connecting: '连接中...',
    sendTo: '发送给',
    sendToAll: '所有人',
    sendToTeacher: '老师',
    privateMessage: '私聊',
    To: '发给',
    mutedByTeacher: '你已被老师禁言',
    unmutedByTeacher: '你的禁言状态已被解除',
    underMute: '禁言中'
  }
}
