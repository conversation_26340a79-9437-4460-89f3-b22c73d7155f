/**
 * 互动配置
 */
export default {
  // 投票
  vote: {
    title: '投票',
    results: '结果',
    voteResult: '答题结果',
    true: '正确',
    false: '错误',
    correctTitle: '恭喜',
    correctTips: `你真棒`,
    rightTipsByPlayback: [`回答正确`, `你获得了金币`, `非常棒！`],
    rightTipsByLive: [`不对哦`, `你获得了金币`, `下次继续努力！`],
    repeatSubmitMessage: `你已经提交过啦`,
    // 懂不懂
    isUnderstand: '听懂老师讲的内容了吗？',
    understand: '听懂了',
    notUnderstand: '没听懂',
    understandFeedback: '非常棒，继续保持哦!',
    notUnderstandFeedback: '没关系，继续加油哦!'
  },
  // 游戏互动
  gameCourseware: {},
  // 课间休息
  classRest: {
    title: '课间休息',
    startTip: '课间休息',
    endTip: '休息结束'
  },
  // 送礼物
  gift: {
    // 请选择礼物提示
    pleaseChooseGiftNotice: '选个礼物送给老师吧~',
    // 金币不足
    notEnoughCoin: '你的金币不够哦 :(',
    // 可送礼物次数已到达
    notEnoughTimes: '谢谢，但每次最多只能送3个礼物哦:)'
  },
  // 答题板
  coursewareBoard: {
    true: '正确',
    false: '错误',
    yourAnswer: '你的答案',
    rightAnswer: '正确答案',
    correctAnswer: '正确答案'
  },
  // 拍照上墙涂鸦
  graffiti: {
    yellow: '黄',
    red: '红',
    blue: '蓝'
  },
  // 拍照上墙
  photoWall: {
    graffitiBtnName: '涂鸦画板',
    graffitiDesc1: `* 拍照不清楚？`,
    graffitiDesc2: `使用涂鸦画板提交练习吧!`,

    cameraBtnName: '使用相机',
    confirmNotice: [`确认提交吗？`, `提交后将不可再次修改`],
    shortCutTip: `提示：点击空格键也可以拍照`,
    notjoinTip: `老师已经结束了作答，期待下次积极参与哦!`,
    hasAccessNotice: [
      `在讲义或者草稿纸上完成练习`,
      `点击下方的按钮拍照上传`,
      `提交后可在‘练习’中查看批改结果`
    ],
    notAccessNotice: [`相机不可用`, `使用涂鸦画板提交练习吧`],
    endNotice: [`老师已经结束了互动，现在提交你的答案吗？`, `5秒后返回课堂`],
    grading: '批改中',
    correct: '正确',
    incorrect: '错误',
    halfCorrect: '部分正确',
    answerCorrect: '回答正确'
  },
  // 随机连线
  randomCall: {
    title: '随机点名',
    finishedNotice: {
      self: `恭喜，你被选中了！`,
      other: '{name}被选中了'
    }
  },
  // 红包
  redPacket: {
    received: 'Received', // 查看红包结题里显示的, 目前功能已隐藏, 暂不翻译
    unfortunatelyRedNotice: ['红包已被领取完', ''],
    oopsRedNotice: `你已经收到了这个红包`,
    congrats: '恭喜!'
  },
  // 视频连麦
  videoLink: {
    joinMessage: '举手加入视频连麦吧!',
    joinButtonName: '举手',
    waitMessage: '等待老师选择',
    cancelButton: '取消',
    raiseHandButton: '举手',
    speaking: '发言中...'
  },
  // 集体发言
  collectiveSpeech: {
    permissionDeniedTips: '麦克风权限未开启，请在系统设置中开启麦克风权限'
  },
  redRain: {
    collect: '收下金币',
    toastTip: '老师结束了互动',
    loading: '游戏加载中..',
    NetworkFailTip: '服务器连接超时，请查看网络情况后重试',
    RetryBtn: '重试',
    title: '红包雨'
  },
  // 填一填
  nonPersetFillBlank: {
    submitSuccessfully: '提交成功',
    submitFailed: '提交失败，请重试',
    inputPlaceholder: '请输入答案',
    clear: '清空',
    submit: '提交',
    myAnswer: '我的答案'
  },
  praiseList: {
    fileName: '课堂表扬榜_{firstTitle}_课次{lessonName}_{courseName}',
    saveText: '截图已保存至',
    savePath: '桌面/Think-Academy-Screenshot',
    savingText: '保存中',
    emptyText: '本班暂时无人上榜'
  },
  schulteTable: {
    schulteTable: '舒尔特方格',
    naturalNumber: '自然数',
    primeNumber: '质数',
    normalMode: '普通模式',
    shuffleMode: '随机模式',
    nextNumber: '下一个数字',
    duration: '{duration}秒',
    gameInstructions: [
      '从小到大按顺序从{start}到{end}点击下方自然数',
      '从小到大按顺序从{start}到{end}点击下方质数'
    ],
    gameObjectives: ['从小到大按顺序点击数字，', '点击越快获得的金币越多'],
    gameCompleted: '游戏完成',
    newRecord: '新纪录',
    firstChallenge: '首次挑战',
    yourCurrentTime: '本次用时',
    yourBestTime: '历史最佳',
    leaderboard: '通关时间榜',
    congratulations: '太棒了! 恭喜你成功上榜！',
    tryAgain: '啊哦！只差一点点，下次继续加油呀！',
    gameOver: '游戏时间到',
    leaderboardTip: ['老师即将发布排行榜！', '你上榜了吗？马上揭晓！'],
    submitFailTitle: '游戏结果提交失败',
    submitFailTip: '服务器连接超时，请查看网络情况后重试',
    submitFailBtn: '重试'
  }
}
