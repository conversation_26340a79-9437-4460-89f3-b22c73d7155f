/**
 * 模块配置
 */
export default {
  // 导航
  header: {
    // 返回确认窗口
    backConfirm: {
      content: `确认退出直播教室吗？`,
      exitByCoursewareIdChange: '课件异常，请退出后重新进入教室'
    }
  },
  // 反馈
  feedback: {
    headerName: '问题描述',
    optionNames: ['学习相关', 'APP相关', '不当的行为', '其它'],
    placeholder: `1.请选择对应的反馈类型标签
2.请详细描述你的问题以便老师能快速解决问题`,
    screenshotTips: '我同意将当前屏幕截图发送给教学团队',
    sendSuccessNotice: '谢谢你的反馈，我们将很快与你或者你的父母取得联繫',
    defaultMsg: '发送反馈',
    playback: {
      header: '问题反馈',
      placeholder: '请在此描述你遇到的问题',
      screenshotTips: '我同意提交当前设备信息和屏幕截图给技术支持团队',
      cancel: '取消',
      send: '发送',
      success: '提交成功',
      failed: '提交失败'
    }
  },
  // 设备检测
  deviceTest: {
    dialogTitle: '设备检测',
    statusNames: {
      disabled: '禁用',
      usable: '启用',
      unauthorized: ' 未授权'
    },
    cameraTitle: '摄像头检测: ',
    microphoneTitle: '麦克风检测: ',
    audioTitle: '音频检测: ',
    authorizeGuide: ['去授权, ', '点击链接']
  },
  checkDevice: {
    network: '网络',
    microphone: '麦克风',
    camera: '摄像头',
    voice: '扬声器',
    网络满足上课需求: '网络满足上课需求',
    网络不满足上课需求: '网络不满足上课需求',
    你的设备满足上课需求: '当前设备符合上课要求',
    你的设备不满足上课需求: '您的网络似乎存在问题， 点击这裡查看解决建议',
    音视频测试: '音视频服务',
    课堂互动测试: '课堂互动服务',
    服务器检测: '服务器连接速度测试',
    课件下载测试: '课件下载测试',
    此项测试大约需要花费一分钟: '此过程大概花费1~3分钟',
    常见网络问题建议: '常见网络问题解决建议',
    尝试重启路由器: '如果你使用Wi-Fi无线网络，请尝试距离路由器更近一点以及尝试重新启动路由器',
    检查网络权限: '请检查并确保您的设备允许我们访问网络',
    切换其它网络: '尝试切换使用其它的网络',
    重新测试: '重新测试',
    下一项: '下一项',
    没有麦克风权限: 'Think Academy无法使用你的麦克风',
    没有找到可用的麦克风: '没有找到可用的麦克风',
    请检查你的麦克风或者佩戴耳机: '请检查你的麦克风设置或者尝试佩戴耳机',
    尝试你大声读出下面的文字: '尝试大声读出下面的文字',
    你好: '你好',
    你的麦克风似乎存在问题: '你的麦克风似乎存在问题',
    我们没有听到你的声音: '我们没有听到你的声音，请尝试更换设备后或者佩戴耳机后再次尝试',
    麦克风检测完成: '麦克风检测完成',
    你的麦克风工作正常: '你的麦克风工作正常',
    去设置: '去设置',
    没有摄像头权限: 'Think Academy无法使用你的摄像头',
    在设置中打开麦克风权限: '请在系统设置中打开Think Academy的麦克风使用权限',
    在设置中打开摄像头权限: '请在系统设置中打开Think Academy的摄像头使用权限',
    没有找到可用的摄像头: '没有可用的摄像头设备',
    请检查你的摄像头: '请检查您的摄像头是否正确安装或者使用其它设备上课',
    可以在上面看到自己吗: '可以在上面的视频框中看到自己吗？',
    正在使用摄像头: '当前正在使用摄像头，工作正常',
    你的摄像头似乎存在问题: '你的摄像头似乎存在问题',
    请检查你的摄像头是否正确安装: '请检查您的摄像头是否正确安装或者使用其它设备上课',
    摄像头检测完成: '摄像头检测完成',
    你的摄像头工作正常: '你的摄像头工作正常',
    当前音量: '当前音量：',
    可以听到正在播放的音乐吗: '可以听到正在播放的音乐吗？',
    请注意调整系统音量到适当大小: '请注意调整系统音量到适当大小',
    能: '能',
    不能: '不能',
    你的扬声器似乎存在问题: '你的扬声器似乎存在问题',
    建议尝试佩戴耳机或者更换其它设备: '建议尝试佩戴耳机或者更换其它设备上课',
    你的设备满足上课要求: '你的设备满足上课要求',
    你的设备不完全满足上课要求: '你的设备不完全满足上课要求',
    继续使用此设备上课可能会降低你的体验: '继续在此设备上课可能会降低您的上课体验',
    知道了: '知道了',
    你的网络是乎有些问题: '你的网络是乎有些问题，点击这裡查看',
    解决方案: '解决方案'
  },
  // 网络状态提示
  networkStatus: {
    statusMap: {
      good: {
        title: '网络连接良好'
      },
      normal: {
        title: '网络连接一般'
      },
      weak: {
        title: '网络连接较差',
        description: '正在尝试重新连接，你也可以尝试切换其它更好的网络或者设备'
      }
    }
  },
  // 网络错误提示
  networkError: {
    notice: ['没有网络', '请检查网络连接，或者等待重新连接']
  },
  // 课件组件
  courseware: {
    errorNotice: ['课件加载失败', '课件加载失败', '点击‘重新加载’再试一次']
  },
  // 截屏
  screenThumbnail: {
    successNotice: '已保存到桌面'
  },
  // 媒体权限
  mediaSecurityAccess: {
    camera: `开启摄像头权限才能参与课中互动，请确认开启`,
    microphone: `请允许ThinkAcademy访问你的麦克风`,
    deniedAccess: '开启摄像头和麦克风权限才能参与课中互动，请确认开启',
    tip: '开启权限后，请重新启动应用',
    confirm: '同意',
    cancel: '拒绝'
  },
  // 踢出
  liveKickout: {
    notice: '你的账号已在其他设备登录，请重新登录'
  },
  // 下课提示
  classEnded: {
    title: '课程已结束',
    content: '本节课程已经结束，请返回首页',
    okText: '确定'
  },
  // 定向金币
  orientationCoins: {
    notice: ['这些学员获得了', '金币！'],
    Congrats: '获得金币',
    others: '和其它同学',
    smallCongrats: '非常棒！'
  },
  // 作业盒子
  assignmentBox: {
    modalTitle: 'Basic Modal',
    nodataNotice: '暂无课中练习',
    checkBigPictures: {
      buttonName: '订正'
    },
    checkedExerciseToast: {
      notice: '辅导老师已经批改了你的练习，快来看看吧',
      buttonName: '查看练习',
      loadingName: '图片加载中',
      pictureDesc: '{assisTeacher}已经批改了你的练习，快来看看吧'
    },
    checkErrorTips: '好遗憾，继续努力'
  },
  // 小班涂鸦板
  smallClassGraffitiCorrect: {
    yellow: '黄色',
    red: '红色',
    blue: '蓝色'
  },
  // 连续作答
  continuousCorrect: {
    rightLevelDesc: '再答对一题后勋章可升级为{nextTitle}',
    rightTopLevelDesc: '你已经获得本节课最高等级勋章啦',
    wrongLevelDesc: '再答对一题后勋章可升级为{nextTitle}',
    encourageNotice: '金币奖励',
    noanswerNotice: '参与课中互动题来升级你的勋章吧',
    continuousText: '回答正确，非常棒！',
    highestLevelText: '你的徽章已经到达最高等级！',
    upgradedLevelText: '你的徽章已升级到Lv {curLevel}',
    wrongTitleText: '哎呀失误了',
    wrongDescText: '下次加油哦～'
  },
  // 辅导连麦
  tutorVideoLink: {
    title: '辅导私聊',
    errorMsg: ['', '出了点小问题']
  },
  // 系统出错
  systemError: {
    message: '系统错误，请重试',
    noPlaybackMessage: '视频回放未生成',
    refreshButtonName: '刷新',
    backButtonName: '返回到课程'
  },
  // 老师上台
  teacherOnStage: {
    notice: ['额，老师待会回来呦', ''],
    privateChat: '视频私聊'
  },

  // 课中考试
  ClassExam: {
    rankHeaderTitle: '考试排行榜'
  }
}
