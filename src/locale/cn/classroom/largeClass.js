/**
 * 大班直播
 */
export default {
  // 导航区
  header: {
    groupVideoButtonName: '小组视频',
    deviceTestButtonName: '设备检测',
    teacherMessageOnly: '仅看老师消息'
  },
  // 播放器区
  players: {
    mute: '静音',
    unmute: '取消静音'
  },
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: '老师暂未在教室，请稍等',
    gameConfig: {
      gameTip: '老师演示中',
      gameToast: '请稍等老师演示结束'
    }
  },
  // 聊天
  chats: {
    newMessage: '新消息',
    teacherOnly: '仅看老师',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: '聊天已关闭',
      2: '聊天已关闭',
      3: '',
      4: '',
      5: '加载中'
    },
    inputPlaceholder: '说点什么吧',
    hotWordList: ['完成！', '我不懂', '我听懂了', '哈哈哈'],
    msgTip: {
      CONNECT: '聊天服务已连接',
      DISCONNECT: '聊天服务已断开',
      RECONNECT: '聊天服务重新连接中',
      BAN_SPEECH: '辅导老师已将你禁言', // 禁言
      RELIEVE_SPEECH: '辅导老师已将你解除禁言', // 解除禁言
      SPEECH_INTERVAL: '请3秒后再发言',
      SPEECH_MINE: '我',
      SPEECH_SYS: '系统消息',
      SPEECH_TEACHER: '老师',
      SPEECH_EMPTY: '输入的消息不合法',
      REMOTE_LOGIN: '当前地点不是常用登录地点，请刷新此页面',
      CHAT_DISCONNECT: '服务器连接已断开，当前不能发言',
      ENTER_ROOM: '进入了教室'
    },
    level: {
      wood: '青木',
      gold: '黄金',
      silver: '白银',
      diamond: '星钻'
    },
    reply: '回复',
    gotIt: '收到'
  },
  // 视频回显
  videoGroup: {
    textConfig: {
      matching: '匹配中...',
      noticeAccessDenied: '请在系统设置中打开Think Academy的摄像头权限'
    },
    tabNames: ['视频', '聊天'],
    closedNotice: '小组视频已关闭',
    localVideoAudition: {
      notice: '你正在上旁听课'
    }
  },
  // 金币显示
  coins: {
    title: '我的金币'
  },
  // 举手
  raiseHand: {
    buttonName: '举手'
  },
  // 回放
  playback: {
    notice: '观看回放'
  }
}
