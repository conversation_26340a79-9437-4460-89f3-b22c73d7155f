// 登录页
export default {
  // 通用
  common: {
    email: '邮箱',
    emailAddress: '邮箱地址',
    phoneNumber: '手机号码',
    phoneNumberOrEmail: '手机号或邮箱',
    verificationCodePlaceholder: '请输入验证码',
    passwordPlaceholder: '请输入您的密码',
    studentId: '学员编号',
    signIn: '登录',
    inputInvalid: '输入无效',
    incorrectId: '学员编号或密码不正确，请重试',
    loginAgain: '重新登录',
    emailReg: '请输入有效的邮箱地址',
    continue: '继续使用',
    是否还没有账号: '是否还没有账号？',
    立即注册: '立即注册',
    已经注册过账号: '已经注册过账号？',
    立即登录: '立即登录',
    注册: '注册',
    我们向您发送了一个验证码: '我们向您发送了一个验证码',
    请在下方输入以进行验证: '请在下方输入以进行验证 {phone}',
    您似乎还未注册: '您似乎还未注册？',
    您已经注册过账号: '您已经注册过账号？',
    切换其他登录方式: '切换其他登录方式',
    找到X个可能是您的账号: '找到{num}个可能是您的账号',
    如果是您的账号: '如果是您的账号，建议您可以直接登录，若不是则创建新的账号',
    已存在的账号: '已存在的账号',
    继续创建新账号: '继续创建新账号',
    您是否要更新现有账户: '您是否要更新现有账户？',
    将此注册信息更新到现有账户: '将此注册信息更新到现有账户',
    账号更新成功: '账号更新成功',
    手机: '手机',
    邮箱: '邮箱'
  },
  // 验证码登录页
  verificationCodePage: {
    title: '验证码登录',
    description: `未注册的手机号在登录后将自动为您创建账号。`,
    emailDesc: '若您没有注册邮箱，会在登录后自动为您创建帐号',
    footerTitle: '验证码登录',
    skip: '跳过',
    send: '发送',
    reSend: '重新发送',
    back: '返回',
    verificationTips: {
      confirmPolicy: '请勾选同意下面的用户协议、隐私政策和儿童隐私政策才能继续',
      enterAccount: '请输入你的账号',
      enterPhoneNumber: '请输入你的手机号码',
      phoneNumberIncorrect: '手机号码不正确',
      enterVerificationCode: '请输入短信验证码'
    }
  },
  // 密码登录页
  passwordPage: {
    title: '密码登录',
    mobileDescription: `如果您已为帐户设置了密码，则可以使用密码登录。`,
    studentIdDescription: `登录后，您可以在“我的帐户”中找到学员编号。`,
    footerTitle: '密码登录',
    forgotPassword: '忘记密码？',
    confirmPolicyTip: '请勾选同意下面的用户协议、隐私政策和儿童隐私政策才能继续',
    otherLoginType: '或使用其他方式密码登录'
  },
  // 协议信息
  policyInfo: {
    desc: '请先勾选同意我们的',
    and: '和'
  },
  // 踢出提示
  kickoutNotice: {
    message: '为了您的账户安全，请重新登录'
  }
}
