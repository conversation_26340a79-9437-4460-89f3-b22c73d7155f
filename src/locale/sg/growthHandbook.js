export default {
  handBook: 'Season Pass',
  growthLevel: 'Season Rank',
  growthTask: 'Season Quest',
  guideTitle: 'Ready for your adventure?',
  gotIt: 'Got it!',
  skip: 'Skip',
  weekTask: 'Weekly Quest',
  challengeTask: 'Bonus Quest',
  activeTask: 'Side Quest',
  process: 'Progress',
  unfinished: 'In progress',
  drawDown: 'Claim',
  claimed: 'Claimed',
  highestLevel: "You've reached the highest rank",
  needExp: 'You need {exp} XP to reach next rank',
  tasksCompleted: 'Complete {count} quests',
  tasksUncompleted: 'In progress',
  congratulations: "You've earned the following items",
  accept: 'Claim!',
  startChallenge: 'Start!',
  countDownTextForDay: 'There are {days} day(s) and {hours} hour(s) left for this week',
  countDownTextForHour: 'There are {hours} hour(s) left for this week',
  countDownTextForMinute: 'There are {minutes} minute(s) left for this week',
  requestError: 'Request timed out',
  backHome: 'Back to home',
  rewardCollected: 'Reward claimed',
  systemError: 'System error (Error code: {code})',
  getExpDesc: 'Complete quests to earn experience points',
  upgradeDesc: 'Promote to higher rank to unlock the reward!',
  toClassDesc: 'Go to class',
  doHomeworkDesc: 'Do homework',
  upgradeTitle: `Congrats! You've unlocked a new rank!`,
  initUpgradeTitle: `Congrats! You've unlocked Wood I rank!`,
  exp: 'XP',
  coin: 'Coin',
  retry: 'Retry',
  networkErr: 'Network error. Please connect to the internet.',
  摄像头初始化: 'Camera is initializing, please retry in 3 seconds.'
}
