/**
 * 互动配置
 */
export default {
  // 聊天
  chats: {
    newMessage: 'New Message',
    teacherOnly: 'Teacher only',
    // 聊天区input提示文案 1:老师关闭2:禁言 3:老师开启聊天 4:5s内不能再次发送消息 5未开启
    inputStatusMap: {
      1: 'Chatbox Closed',
      2: 'Chatbox Closed',
      3: '',
      4: '',
      5: 'Loading'
    },
    inputPlaceholder: 'Say something',
    hotWordList: ['Done!', 'I don’t understand', 'I understand!', 'hahaha'],
    msgTip: {
      CONNECT: 'Chat server connected.',
      DISCONNECT: 'Chat server disconnected.',
      RECONNECT: 'Chat server reconnecting...',
      BAN_SPEECH: 'Your VIP Teacher just closed your chatbox.', // 禁言
      RELIEVE_SPEECH: 'Your VIP Teacher just opened your chatbox.', // 解除禁言
      SPEECH_INTERVAL: 'Please speak in 3 seconds.',
      SPEECH_MINE: 'Me',
      SPEECH_SYS: 'System Message',
      SPEECH_TEACHER: 'Teacher',
      SPEECH_EMPTY: 'Please enter valid information!',
      REMOTE_LOGIN: 'Remote login, please refresh the page!',
      CHAT_DISCONNECT: 'Server connection lost, currently unable to speak.',
      ENTER_ROOM: 'entered classroom'
    },
    level: {
      wood: 'Wood',
      gold: 'Gold',
      silver: 'Silver',
      diamond: 'Diamond'
    },
    chatbox: 'Chatbox',
    frequently: 'Speaking too frequently',
    onlyteacher:
      'The teacher closed the group chat, you can still send private messages to the teacher.',
    all: 'The teacher opened the group chat.',
    teacherOff: 'Teacher just closed the chatbox',
    teacherOn: 'Teacher just opened the chatbox',
    chatboxClosed: 'Chatbox closed',
    connecting: 'Connecting...',
    sendTo: 'Send to',
    sendToAll: 'All',
    sendToTeacher: 'Teacher',
    privateMessage: 'Private message',
    To: 'To',
    mutedByTeacher: 'You have been muted by the teacher',
    unmutedByTeacher: 'You have been unmuted by the teacher',
    underMute: 'Under mute'
  }
}
