/**
 * 小班直播
 */
export default {
  // 视频回显区
  videoGroup: {
    hide: 'Hide Video',
    show: 'Show Video',
    localVideoAudition: {
      notice: 'Class auditing',
      offline: 'Offline'
    }
  },
  speaking: 'Speaking: ',
  douhao: ',',
  // 课件白板区
  coursewareBoard: {
    classSoonNotice: 'Your teacher will be here soon',
    gameConfig: {
      gameTip: 'Teacher demonstrating',
      gameToast: 'You cannot play games yet while the teacher is demonstrating.'
    }
  },
  // 金币
  coins: {
    buttonName: 'Detail',
    currentLessonCoinsTips: 'This Lesson',
    totalCoinsTips: 'Coins in total',
    levelNameList: ['Level 1', 'Level 2', 'Level 3', 'Level 4', 'Level 5', 'Level 6', 'Level 7'], // 等级名称列表
    levelNotice_0: `Answer questions and get your badge level up!`, // 0级别
    levelNotice_7: `You've reached the highest level in this class!`, // 7级别
    levelNotice_other: `Get 1 more correct and advance to {levelName}` // 其它级别
  },
  // 麦克风
  microphone: {
    buttonName: ['Mute', 'Unmute']
  },
  // 举手
  raiseHand: {
    buttonName: ['Raise hand', 'Wait a moment']
  },
  // 发送表情
  sendEmoji: {
    buttonName: ['Emoji', 'Emoji unavailable'],
    sendTooFastNotice: 'Take a break and try again later',
    forbiddenSendMessage: 'Emoji closed',
    sendTooFast: 'Too many sends, retry in {num}s.'
  },
  // 动态表情过期
  dynamicEmoji: ['Emojis expired.', 'Buy it again in the coins shop.'],
  // 更多设置
  moreSettings: {
    buttonName: 'More'
  },
  // 反馈
  feedback: {
    buttonName: 'Feedback'
  },
  screenShot: {
    buttonName: 'Screenshot'
  },
  // 摄像头
  camera: {
    buttonName: 'Camera'
  },
  visualEffect: {
    buttonName: 'Video effect',
    unableOpen: 'Unable to activate video effects',
    teacherClose: 'The teacher has disabled video effects',
    openTip: 'Video effects enabled, you can activate them here',
    failLoadEffect: 'Failed to activate video effects',
    confirm: 'Got it'
  },
  // 课后作业
  homework: {
    buttonName: 'Exercise'
  },
  // 考试报告
  examReport: {
    buttonName: 'Exam'
  },
  // 查看考试报告提示文案
  examReportTips: {
    msg: 'The report will be available after the exam.',
    confirm: 'Confirm'
  },
  // 摄像头授权确认窗口
  cameraAllowConfirm: {
    notice: 'Teacher reminds you to turn the camera on'
  },
  // 麦克风授权确认窗口
  microphoneAllowConfirm: {
    notice: 'Teacher reminds you to turn the microphone on'
  },
  // 老师禁用麦克风后开启麦克风时提示
  teacherDisabledOnMicrophone: {
    notice: `The teacher doesn't allow you to turn the microphone on`
  },
  // 老师禁用麦克风后关闭麦克风时提示
  teacherDisabledOffMicrophone: {
    notice: `The teacher doesn't allow you to turn the microphone off`
  },
  // 旁听状态面版
  auditionPanel: {
    notice: 'Class auditing'
  },
  // 回放
  playback: {
    notice: 'Watching Playback'
  },
  courseEnd: 'The current course has ended',
  视频采集失败: 'Failed to capture video',
  没有摄像头权限: 'Failed to capture video,no permission to use camera',
  找不到可用摄像头设备: 'Failed to capture video,no camera available',
  摄像头被其他应用占用: 'Failed to capture video,the camera is occupied by other applications',
  请检查摄像头设备: 'Failed to capture video,check the camera please',
  音频采集失败: 'Audio capture failed',
  没有麦克风权限: 'Audio capture failed, no permission to use microphone',
  麦克风被其他应用占用: 'Audio capture failed, the microphone is occupied by other applications',
  请检查麦克风设备: 'Audio capture failed, check your microphone please',
  没有可用的麦克风: 'Audio capture failed, no microphone available',
  没有可用的音频播放设备: 'Audio capture failed, no speaker device available',
  说出你的答案吧: 'Speak out your answer',
  正确答案: 'Correct answer: ',
  请注意专心听讲哦: 'Please pay attention and focus',
  我知道了: 'Got it'
}
