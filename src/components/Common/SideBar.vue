<template>
  <div class="sideBar">
    <div class="wrapper">
      <section>
        <div
          class="tab"
          :class="{ 'active-page': isActiveTab('/home/<USER>') }"
          @click="handleChange('/home/<USER>')"
        >
          <div class="icon-box">
            <i class="iconfont icon-home"></i>
          </div>
          {{ $t('today.home_page_15_6gc') }}
        </div>
        <div
          class="tab"
          :class="{ 'active-page': isActiveTab('/home/<USER>') }"
          @click="handleChange('/home/<USER>')"
        >
          <div class="icon-box">
            <i class="iconfont icon-assignment"></i>
          </div>
          {{ $t('today.assignment_15_64j') }}
        </div>
        <div
          class="tab"
          :class="{ 'active-page': isActiveTab('/home/<USER>') }"
          @click="handleChange('/home/<USER>')"
        >
          <div class="icon-box">
            <i class="iconfont icon-a-CorrectionBook"></i>
          </div>
          {{ $t('today.correction_book_15_875') }}
        </div>
        <div
          v-if="isShowStudyPlan"
          class="tab"
          :class="{ 'active-page': isActiveTab('/home/<USER>') }"
          @click="handleChange('/home/<USER>')"
        >
          <div class="icon-box">
            <i class="iconfont icon-a-LearnPlan"></i>
          </div>
          {{ $t('today.learn_plan_15_f4u') }}
        </div>
      </section>
    </div>
    <div class="download" @click="toDownLoadApp">
      <img src="@/assets/images/home/<USER>" class="img-logo" alt="" />
      <div class="text-content">
        <h4>{{ $t('today.enjoy_the_full_experience_15_0ml') }}</h4>
        <p>{{ $t('today.download_think_academy_app_15_i45') }}</p>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue'
import {
  getMenuRelateData
  // getStudyPlanUrl
} from '@/api/home/<USER>'
import { emitter } from '@/hooks/useEventBus.js'
import { sensorEvent } from '@/utils/sensorEvent'

const route = useRoute()
const router = useRouter()
const activePath = ref(route.path)
const fromRoutePath = ref('home')
const isShowStudyPlan = ref(false)
watch(
  () => route.path,
  (newPath, oldPath) => {
    activePath.value = newPath
    fromRoutePath.value = oldPath
  },
  { immediate: true } // 立即执行一次
)
const isActiveTab = computed(() => {
  return tab => {
    return activePath.value === tab
  }
})
const handleChange = function (page) {
  router.push({
    path: page
  })
}
const getStudyPlan = () => {
  getMenuRelateData({})
    .then(res => {
      if (res.data.learnSchedule) {
        isShowStudyPlan.value = !res.data.learnSchedule.hide
      }
    })
    .catch(err => {
      console.log('报错了 getMenuRelateData', err)
    })

  // getStudyPlanUrl({}).then((res) => {
  //   if(res.code === 0 && res?.data?.url) {
  //     isShowStudyPlan.value = true
  //   }
  // }).catch((err) => {
  //   console.log('报错了 getStudyPlanUrl', err)
  // })
}
const toDownLoadApp = () => {
  sensorEvent('osta_web_study_download_app')
  window.open(`${window.location.origin}/download`, '_blank')
}
onMounted(() => {
  console.log('路由', route, router)
  getStudyPlan()
  // 监听全局刷新事件，重置是否需要展示学习计划
  emitter.on('globalReFresh', getStudyPlan)
})
onBeforeUnmount(() => {
  emitter.off('globalReFresh', getStudyPlan)
})
</script>
<style lang="scss" scoped>
.sideBar {
  position: relative;
  background: #fff;
  width: 342px;
  height: calc(100vh - 170px);
  min-height: 400px;
  border-radius: 12px;
  .wrapper {
    padding: 24px;
  }
  .tab {
    display: flex;
    align-items: center;
    cursor: pointer;
    height: 32px;
    &:not(.active-page):hover {
      //background-color: rgba(0, 0, 0, 0.04);
    }
    font-size: 16px;
    font-family: Montserrat-Regular, Montserrat;
    font-weight: 400;
    color: #222222;
    line-height: 16px;
  }
  .tab:not(:first-child) {
    margin-top: 16px;
  }
  .icon-box {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    width: 32px;
    height: 32px;
    background: #fff5e7;
    border-radius: 32px;
    color: #ff9f0a;
  }
  .active-page {
    color: #ff9f0a;
    font-weight: 600;
    .icon-box {
      background: #ff9f0a;
      color: #fff;
    }
  }
  .download {
    cursor: pointer;
    width: calc(100% - 40px);
    position: absolute;
    bottom: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
    padding: 20px;
    background: #f9f9f9;
    border-radius: 12px;
    img {
      object-fit: cover;
    }
    .text-content {
      flex: 1;
      padding-left: 12px;
      color: #ff9f0a;
      h4 {
        font-size: 16px;
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
      }
      p {
        margin-top: 3px;
        font-size: 12px;
        font-family: Montserrat-Medium, Montserrat;
        font-weight: 500;
        color: #FF9F0A;
      }
    }
    .img-logo {
      width: 50px;
      height: 50px;
    }
  }
}
</style>
