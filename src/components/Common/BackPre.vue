<template>
  <div class="page-back">
    <i class="iconfont icon-fanhui back" @click="goBack"></i>
    {{ title }}
  </div>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { ref } from 'vue'
const props = defineProps({
  title: {
    type: String, // 类型
    default: '',  // 默认值
  },
  // 页面回退地址
  backPath: {
    type: String, // 类型
    default: '',  // 默认值
  },
})
const title = ref(props.title)
const router = useRouter()
const goBack = () => {
  // 需要考虑一下刷新页面的问题
  // 判断页面是否由 classroom 过来，若是，则通过其退出教室跳转到课程详情或者home页， 但是再如课程详情的back的时候，不能直接回退
  // 当前页面,若是这样那么只会记得页面，不会包含参数了
  if (props.backPath) {
    router.push(props.backPath)
  } else {
    // router.back()
    navigateBack()
  }
}
const navigateBack = (fallback = "/home/<USER>") => {
  if (window.history.length > 1) {
    // 当前页面刷新不会改变使其不能返回，有一个问题比如在某一个页面的基础上，直接粘贴这个某个考试页面场景，点击考试页返回, 若没有当前路由的back则返回首页
    if (window.history?.state?.back) {
      router.back();
    } else {
      router.push(fallback);
    }
  } else {
    // 直接通过链接打开的，回退的话是直接跳转跟随浏览器的路由
    router.push(fallback); // 跳转到固定页面
  }
}
</script>
<style lang="less" scoped>
.page-back {
  //width: 878px;
  height: 44px;
  margin-bottom: 20px;
  line-height: 44px;
  background: #FFFFFF;
  border-radius: 12px;
  font-size: 16px;
  font-family: Montserrat-SemiBold, Montserrat;
  font-weight: 600;
  color: #222222;
  .back {
    padding-left: 20px;
    cursor: pointer;
  }
}
</style>
