<template>
  <div class="wave-animation" :style="containerStyle" :class="speedClass">
    <div class="bar bar1"></div>
    <div class="bar bar2"></div>
    <div class="bar bar3"></div>
    <div class="bar bar4"></div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  width: {
    type: [String, Number],
    default: '100%'
  },
  height: {
    type: [String, Number],
    default: '100%'
  },
  color: {
    type: String,
    default: '#ffaa0a'
  },
  speed: {
    type: String,
    default: 'normal',
    validator: value => ['slow', 'normal', 'fast'].includes(value)
  },
  paused: {
    type: Boolean,
    default: false
  }
})

const containerStyle = computed(() => {
  const style = {}

  if (typeof props.width === 'number') {
    style.width = `${props.width}px`
  } else {
    style.width = props.width
  }

  if (typeof props.height === 'number') {
    style.height = `${props.height}px`
  } else {
    style.height = props.height
  }

  style['--wave-color'] = props.color

  return style
})

const speedClass = computed(() => {
  const classes = []
  if (props.speed !== 'normal') {
    classes.push(props.speed)
  }
  if (props.paused) {
    classes.push('paused')
  }
  return classes
})
</script>

<style lang="scss" scoped>
.wave-animation {
  width: 100%;
  height: 100%;

  --wave-color: #ffaa0a;
  --bar-width: 14%;
  --border-radius: 4.17%;

  --bar1-height-min: 33.33%;
  --bar1-height-max: 66.67%;

  --bar2-height-min: 25%;
  --bar2-height-max: 83.33%;

  --bar3-height-min: 16.67%;
  --bar3-height-max: 75%;

  --bar4-height-min: 41.67%;
  --bar4-height-max: 91.67%;

  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  background: transparent;
  position: relative;

  min-width: 8px;
  min-height: 8px;
}

.bar {
  width: var(--bar-width);
  background-color: var(--wave-color);
  border-radius: var(--border-radius);
  animation-duration: 1.2s;
  animation-iteration-count: infinite;
  animation-timing-function: ease-in-out;
  transform-origin: bottom;
  flex-shrink: 0;
}

.bar1 {
  height: var(--bar1-height-min);
  animation-name: wave1-responsive;
  animation-delay: 0s;
}

.bar2 {
  height: var(--bar2-height-min);
  animation-name: wave2-responsive;
  animation-delay: 0.1s;
}

.bar3 {
  height: var(--bar3-height-max);
  animation-name: wave3-responsive;
  animation-delay: 0.2s;
}

.bar4 {
  height: var(--bar4-height-min);
  animation-name: wave4-responsive;
  animation-delay: 0.3s;
}

.wave-animation.slow .bar {
  animation-duration: 2s;
}

.wave-animation.fast .bar {
  animation-duration: 0.8s;
}

.wave-animation.paused .bar {
  animation-play-state: paused;
}

@keyframes wave1-responsive {
  0% {
    height: var(--bar1-height-min);
  }
  50% {
    height: var(--bar1-height-max);
  }
  100% {
    height: var(--bar1-height-min);
  }
}

@keyframes wave2-responsive {
  0% {
    height: var(--bar2-height-min);
  }
  50% {
    height: var(--bar2-height-max);
  }
  100% {
    height: var(--bar2-height-min);
  }
}

@keyframes wave3-responsive {
  0% {
    height: var(--bar3-height-max);
  }
  50% {
    height: var(--bar3-height-min);
  }
  100% {
    height: var(--bar3-height-max);
  }
}

@keyframes wave4-responsive {
  0% {
    height: var(--bar4-height-min);
  }
  50% {
    height: var(--bar4-height-max);
  }
  100% {
    height: var(--bar4-height-min);
  }
}
</style>
