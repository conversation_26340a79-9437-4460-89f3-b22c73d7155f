<template>
  <a-modal
    v-model:open="showModal"
    :width="380"
    :footer="null"
    :centered="true"
    :closable="false"
    :keyboard="false"
    :maskClosable="false"
    :cancelText="$t('courses.confirmModal.enterBtn')"
    dialogClass="modal-simple no-apu-header"
    @ok="OK"
  >
    <div class="confirm-con is-keep-all">
      <p>{{ isRefresh ? $t('today.login_status_changed_please_refresh_15_gxu') : $t('today.session_expired_please_log_in_again_15_6qp') }}</p>
    </div>
    <div class="button-wrapper">
      <a-button class="primary" size="large" type="primary" shape="round" block @click="OK">
        {{ isRefresh ? $t('common.refresh') : $t('login.common.signIn') }}
      </a-button>
    </div>
  </a-modal>
</template>
<script>
import { emitter } from '@/hooks/useEventBus'
export default {
  data() {
    return {
      showModal: false
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // title: {
    //   type: String,
    //   default: ''
    // },
    // btnTitle: {
    //   type: String,
    //   default: ''
    // },
    isRefresh: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    visible(val) {
      this.showModal = val
    },
  },
  methods: {
    OK() {
      if (this.isRefresh) {
        this.showModal = false
        emitter.emit('globalReFresh')
        console.log('看看刷新情况怎么请求那么多遍呢 触发了 modal')
        this.$emit('closeModal')
      } else {
      // let url = window.location.href
      // if (url.includes())
      // 去登录页，并回退当前路径，包括参数
        window.open(`${window.location.origin}/passport/login?notReturnTempToken=1&retUrl=${window.location.origin}/study`, '_self');
        this.showModal = false
        this.$emit('closeModal')
      }
    },
  }
}
</script>
<style lang="scss" scoped>
.confirm-con {
  text-align: center;
  font-size: 20px;
  font-family: Montserrat-SemiBold, Montserrat;
  font-weight: 600;
  color: #222222;
  line-height: 34px;
}
.is-keep-all {
  word-break: keep-all;
  white-space: pre-line;
}
.button-wrapper {
  display: flex;
  margin-top: 20px;
  justify-content: center;
  > button.primary {
    width: 210px;
    background: #FFAA0A !important;
    color: #FFFFFF !important;
  }
  .primary {
    font-size: 14px !important;
    font-family: Montserrat-SemiBold, Montserrat !important;
    font-weight: 600 !important;
  }
}
</style>
