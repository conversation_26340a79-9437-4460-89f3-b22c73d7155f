<template>
<a-modal
  :open="visible"
  :width="340"
  :closable="false"
  :footer="null"
  @cancel="handleCancel"
>
  <div class="content">
    <span class="title">
      {{ $t('setting.logout.content') }}
    </span>
    <div class="btn">
      <div @click="handleCancel" class="no">
        {{ $t('common.cancel') }}
      </div>
      <div @click="handleConfirm" class="yes">
        {{ $t('common.confirm') }}
      </div>
    </div>
  </div>
</a-modal>
</template>
<script setup>
import { removeCookies } from '@/utils/util.js';
// import { logout } from '@/api/user'
// import Cookies from 'js-cookie'
defineProps({
  visible: {
    type: Boolean,
    required: true
  }
});

const emit = defineEmits(['update:visible']);

const handleCancel = () => {
  emit('update:visible', false); // 通知父组件关闭弹窗
};
const handleConfirm = () => {
  removeCookies('_official_token')
  // domain.thethinkacademy.com
  emit('update:visible', false);
  // 推出登录并跳转到对应的官网首页
  window.open(window.location.origin, '_self');
  //  })
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  .title-icon {
    width: 183px;
    height: 97px;
    position: absolute;
    top: -68px;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100%;
    background-image: url('../assets/images/courses/previewClass/title.png');
  }
  .title {
    font-size: 16px;
    font-weight: 600;
    color: rgba(23, 43, 77, 1);
    display: block;
    margin-top: 35px;
  }
  .sub-title {
    font-size: 14px;
    color: rgba(162, 170, 184, 1);
    margin-top: 4px;
  }
  .btn {
    display: flex;
    margin-top: 40px;
    width: 100%;
    justify-content: space-between;
    .no {
      width: 147px;
      height: 48px;
      background: rgba(255, 243, 220, 1);
      border-radius: 24px;
      color: #ffaa0a;
      font-size: 16px;
      line-height: 48px;
      text-align: center;
      cursor: pointer;
      transition:
        color 0.15s ease-in-out,
        background-color 0.15s ease-in-out,
        border-color 0.15s ease-in-out,
        box-shadow 0.15s ease-in-out;
    }
    .yes {
      margin-left: 12px;
      width: 147px;
      height: 48px;
      background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
      border-radius: 24px;
      font-size: 16px;
      line-height: 48px;
      color: #fff;
      text-align: center;
      cursor: pointer;
    }
  }
}
</style>
