<template>
  <div :class="['error-wrapper', bgWhiteAuth ? 'bg-white' : '']">
    <div class="center">
      <div class="error-status"></div>
      <div class="message">
        <div class="message-main">{{ message || $t('common.components.errorStatus.message') }}</div>
        <p v-if="scene" class="message-sub">
          {{ `Scene: ${scene}.` }}
          {{ subMessage }}
        </p>
      </div>
      <div class="button-wrapper">
        <a-button
          v-if="showRefresh"
          :class="showBack || isClassLiveOrPlayback ? 'mr40' : ''"
          type="primary"
          shape="round"
          size="large"
          @click="handleRefresh"
        >
          {{ $t('common.refresh') }}
        </a-button>
        <!-- 普通情况是返回上一級，只有回放及课堂情况是返回首页课堂列表（可能返回多级） -->
        <a-button
          v-if="showBack || isClassLiveOrPlayback"
          class="color-orange"
          @click="handleBack"
          shape="round"
          size="large"
        >
          {{
            isClassLiveOrPlayback
              ? $t('classroom.modules.systemError.backButtonName')
              : $t('common.back')
          }}
        </a-button>
      </div>
    </div>
  </div>
</template>

<script>
// import { route_href } from 'utils/routeUtil'

export default {
  name: 'ErrorStatus',
  props: {
    message: {
      type: String,
      default() {
        return ''
      }
    },
    isClassLiveOrPlayback: {
      type: Boolean,
      default: false
    },
    scene: {
      type: String,
      default: ''
    },
    subMessage: {
      type: String,
      default: ''
    },
    showRefresh: {
      type: Boolean,
      default: true
    },
    showBack: {
      type: Boolean,
      default: false
    },
    backUrl: {
      type: String,
      default: ''
    }
  },
  computed: {
    bgWhiteAuth() {
      return ['ClassLiving', 'PlaybackReadyClass', 'LivingReadyClass', 'PlayBack'].includes(
        this.scene
      )
    }
  },
  methods: {
    handleRefresh() {
      if (this.isClassLiveOrPlayback) {
        window.location.reload()
      } else {
        this.$emit('click-refresh')
      }
    },
    handleBack() {
      if (this.isClassLiveOrPlayback) {
        // todo刷新逻辑处理
        // route_href('/#/home')
        this.$router.push({
          path: this.$route.query.backUrl
        })
      } else {
        this.$router.back()
        // this.$router.push({
        //   path: this.backUrl
        // })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.error-wrapper {
  width: 100%;
  height: 100%;
  // position: relative;
}
.bg-white {
  background: #fff;
}
.center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.error-status {
  width: 158px;
  height: 158px;
  margin: 0 auto;
  background: url('@/assets/images/bg-error-new.png') no-repeat;
  background-size: 100%;
}
.message {
  text-align: center;
  &-main {
    font-size: 18px;
    font-weight: 500;
    color: #172b4d;
    line-height: 21px;
    margin-top: 10px;
  }
  &-sub {
    margin-top: 10px;
    font-size: 12px;
    font-weight: 500;
    color: #a2aab8;
    line-height: 17px;
  }
}
.button-wrapper {
  margin-top: 30px;
  text-align: center;
}
.button-wrapper > button {
  min-width: 130px;
  //height: 34px;
}
.mr40 {
  margin-right: 40px;
}

.color-orange {
  background: #fff3dc;
  border-color: #fff3dc;
  color: #ffaa0a;
}
</style>
