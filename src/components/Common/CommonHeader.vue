<template>
  <div class="header">
    <div class="content">
      <div class="logo" @click="toWebsiteApp">
        <img src="@/assets/images/home/<USER>" alt="">
      </div>
      <div class="operate">
        <Dropdown trigger="click" placement="bottomRight" :arrow="false" overlayClassName="popover-header-set">
          <template #overlay>
            <Menu class="header-expand-menu">
              <menu-item v-for="item in [curUser, ...otherUsers]" class="user-account" :key="item.uid" @click="handleSwitchAccount(item)">
                <div  :class="['user', curSelectUser.uid === item.uid ? 'cur-user' : '']">
                  <img :src="item.avatar" alt="">
                  <div class="title">
                    <div class="nick-name">
                      {{ item.nickName }}
                    </div>
                    <div class="card">
                      {{ item.card }}
                    </div>
                  </div>
                </div>
              </menu-item>
              <menu-item class="addLine">
                <div class="log-out" @click="LogOut">

                  <div class="text">
                    {{ $t('today.log_out_15_vtt') }}
                  </div>
                </div>
              </menu-item>
            </Menu>
          </template>
          <div id="targetTourElement" class="account-wrap">
            <div  :class="{
              'account': true,
              'switchActive': false,
              'tourActive': isShowTour,
            }">
              <img :src="curUser?.avatar || defaultAvatar" alt="">
              <div class="name">{{ curUser.nickName }}</div>
            </div>
          </div>
        </Dropdown>
      </div>
    </div>
    <!-- 还有切换的逻辑当前选中-->
    <Modal v-model:open="isShowGuideModal"
      :footer="null"
      :centered="true"
      :closable="false"
      :keyboard="false"
      :maskClosable="false"
      :width="500"
      dialogClass="modal-simple no-apu-header"
      @ok="handleSwitchAccount(curSelectUser)">
      <div class="switch-modal">
        <div class="title-main">{{ $t('today.welcome_15_ztp') }}</div>
        <div class="sub-title">{{ $t('today.please_select_student_account_15_6fb') }}</div>
        <div class="header-modal-switch-account">
          <div v-for="item in [curUser, ...otherUsers]" :key="item.uid" :class="['user', curSelectUser.uid === item.uid ? 'cur-user' : '']" @click="handleSelectedUser(item)">
            <img :src="item.avatar" alt="">
            <div class="title">
              <div class="nick-name">
                {{ item.nickName }}
              </div>
              <div class="card">
                {{ item.card }}
              </div>
            </div>
          </div>
        </div>
        <div class="button-wrapper">
          <a-button class="primary" size="large" type="primary" shape="round" block @click="handleSwitchAccount(curSelectUser)">
            {{ $t('common.confirm') }}
          </a-button>
        </div>
      </div>
    </Modal>
  </div>
</template>
<script setup>
import { setCookies, removeCookies } from '@/utils/util.js';
import { useBaseData } from '@/stores/baseData'
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { queryAllAccount, switchAccount, existAction, saveAction, logout } from '@/api/user'
import _debounce from 'lodash/debounce'
import { Modal, Dropdown, Menu } from 'ant-design-vue'
import { driver } from "driver.js";
import "driver.js/dist/driver.css";
import { useI18n } from 'vue-i18n'
import { emitter } from '@/hooks/useEventBus'
import defaultAvatar from '@/assets/images/home/<USER>'; // 用 ES Modules 导入图片
// 获取国际化实例
const { t } = useI18n()
import { useRequestInfo } from '@/hooks/useRequestInfo'
const { getSchoolCode, getTimezone, setFirstToken, getToken } = useRequestInfo()
const MenuItem = Menu.Item
const expandShow = ref(false)
const store = useBaseData()
const curUser = ref({})
const curSelectUser = ref({})
const otherUsers = ref([])
const isShowGuideModal = ref(false)
const isShowTour = ref(false)
onMounted(() => {
  getUserInfo()
  emitter.on('globalReFresh', getUserInfo)
})
onBeforeUnmount(() => {
  emitter.off('globalReFresh', getUserInfo)
})
const handleTour = () => {
  saveAction({
    type: 2
  }).then((res) => {
    if(res.code === 0) {
      isShowTour.value = false
      console.log('哈哈哈哈')
    }
  }).catch((err) => {
    console.log('报错了 saveAction', err)
  })
}
const openExpand = _debounce(() => {
  expandShow.value = !expandShow.value
}, 200)
const handleSelectedUser = (item) => {
  curSelectUser.value = item
}
// todo 文案
const initTourGuide = () => {
  nextTick(() => {
    isShowTour.value = true
    const driverObj = driver({
      popoverClass: 'driverjs-theme',
      doneBtnText: t('today.okay_15_bp5'),
      overlayOpacity: 0.5,
      overlayColor: '#000',
      showButtons: [
        'next',
        // 'previous',
        'close'
      ],
      showProgress: false,
      // allowClose: true,
      onNextClick:() => {
        console.log('Next Button Clicked');
        // Implement your own functionality here
        driverObj.moveNext();
      },
      onPrevClick:() => {
        console.log('Previous Button Clicked');
        // Implement your own functionality here
        driverObj.movePrevious();
      },
      onCloseClick:() => {
        console.log('Close Button Clicked');
        handleTour()
        // Implement your own functionality here
        driverObj.destroy();
      },
      // showProgress: true,
      // allowClose: false,
      onDestroyStarted: () => {
          handleTour()
          console.log('引导完成！');
          driverObj.destroy();
      },
      steps: [
        {
          element: '#targetTourElement',
          popover: {
            title: t('today.welcome_15_ztp'),
            description: t('today.need_to_use_another_account_click_here_to_switch_15_9fg'),
            side: "bottom",
            // position: 'right',
            align: 'end'
          },
        }
      ]
    });
    driverObj.drive();
  })
}

const getUserInfo = async (flag = true) => {
  try {
    // 查询所有账户信息
    const res = await queryAllAccount();
    if(res.code === 0) {
      curUser.value = res?.data?.currentAccount
      curSelectUser.value = res?.data?.currentAccount
      otherUsers.value = res?.data?.associatedAccount || []
    }
    if (flag) {
      existAction({type: 2}).then((res) => {
        if(res.code === 0) {
          if (otherUsers.value.length > 0) {
            isShowGuideModal.value = !res.data.familyExist;
          } else {
            console.log("otherUsers.length 为 0，不显示引导弹窗");
          }
        }
      }).catch((err) => {
        console.log('报错了 existAction', err)
      })
    }
  } catch (err) {
    console.log("报错了:", err);
  }
}
const isSwitching = ref(false)
const handleSwitchAccount = _debounce(async (stuInfo) => {
  if (isSwitching.value) return
  isSwitching.value = true
  console.log('看下uid::', stuInfo)
  const params = { targetUid: stuInfo.uid }
  const res = await switchAccount(params)

  if (!res || res.code != 0) {
    // todo 看下统一的提示相关的
    //this.$Message.warn(res.msg || this.$t('account.accountVerification.switchedFailed'), 1.5)
    isSwitching.value = false
    return
  }
  const resData = res.data || {}

  // this.$Message.success(this.$t('account.accountVerification.switchedSuccessfully'), 1.5)
  store.setUserInfo({
    uid: resData.uid, // 用户ID,
    card: resData.card, // 学员卡编号
    avatar: resData.avatar, // 头像地址
    email: resData.email, // 邮箱
    nickName: resData.nickName, // 用户昵称
    countryCallingCode: resData.countryCallingCode, // 区号
    phone: resData.phone, // 手机号
    unifiedAccessToken: resData.unifiedAccessToken // Token
  })

  try {
    // const hostname = window.location.hostname; // 例如 local-mars.thethinkacademy.com
    // // 按 "." 分割域名
    // const parts = hostname.split('.');
    // // 如果域名是多部分（含子域名），提取最后两部分
    // const domain = parts.slice(1).join('.'); // 获取主域名 thethinkacademy.com

    // // todo 还是不对呢，这个接口不会自动进行cookie设置么
    // // Cookies.remove todo 可以通过先移除再设置方式试一试 Cookies.remove('_official_token')
    // Cookies.set('_official_token', resData.unifiedAccessToken, {
    //   expires: 365,
    //   domain: domain
    // })
    setCookies('_official_token', resData.unifiedAccessToken)
    // 可以生效呢
    setFirstToken(resData.unifiedAccessToken)
    console.log('这下先看看 getToken 还能不能对', getToken() === resData.unifiedAccessToken)
    // let timer = setTimeout(() => {
    //   clearTimeout(timer)
      // 通过cookie进行设置，但是存在个问题 存储cookie 后立刻存取，取到的仍旧是上一个怎办呢
    emitter.emit('globalReFresh', false)
    console.log('看看刷新情况怎么请求那么多遍呢 触发了 switch')
  } catch (error) {
    console.log('看看刷新情况怎么请求那么多遍呢 触发 报错', error)
  }

    // router.push('/home/<USER>')
    // bus.emit('page-refresh')
    // this.$bus.$emit('update-userinfo')
    // this.$bus.$emit('update-coin')

    // getUserInfo(false)

    if (isShowGuideModal.value) {
      isShowGuideModal.value = false
      // 展示气泡切换提示
      // isShowTour.value = true
      initTourGuide()
    } else {
      expandShow.value = false
    }
    isSwitching.value = false
  // }, 500)
}, 200)
const toWebsiteApp = () => {
  window.open(`${window.location.origin}`, '_blank');
}
// const isShowLogOut = ref(false)
const LogOut = () => {
  // isShowLogOut.value = true
  // todo 待后端排查为啥不给返回体
  // // 退出登录，成功后，原页面跳转到官网首页
  logout({
    data: {},
    header: {
      "timezone": getTimezone() ,
      "appName":"Official",
      "appVersion":"1.0.0",
      "platform":"Web",
      "platformVersion":"",
      // "origin":"",
      "attribution":"event_type=0&event_id=0&source_type=0&source_id=0",
      schoolCode: getSchoolCode()
    }
  }).then((res) => {
    if (res.code === 0) {
      removeCookies('_official_token')
      window.open(`${window.location.origin}`, '_self');
    }
  }).catch((err) => {
    console.log('报错了 logout', err)
  })
}
</script>
<style lang="scss">
.driver-popover {
  border-radius: 10px;
}
.driver-popover-arrow-side-bottom.driver-popover-arrow-align-end {
    right: 30px;
    border-color: #FF9F0A;
    border-left-color: transparent;
    border-top-color: transparent;
    border-right-color: transparent;
}
.driverjs-theme {
  background: #FF9F0A;
  color: #FFFFFF;
  .driver-popover-close-btn {
    color: #FFFFFF;
  }
  .driver-popover-title {

  }
  .driver-popover-next-btn {
    padding: 0 30px;
    height: 36px;
    background: #FFFFFF;
    border-radius: 18px;
    font-size: 14px;
    font-family: Montserrat-SemiBold, Montserrat;
    font-weight: 600;
    color: #FF9F0A;
    line-height: 36px;
  }
  .driver-popover-footer button:hover, .driver-popover-footer button:focus {
    background-color: #fff;
  }
}
.user {
  display: flex;
  margin: 5px 10px;
  padding: 10px;
  align-items: center;
  cursor: pointer;
  border: 1px solid transparent;
  img {
    width: 40px;
    height: 40px;
    border-radius: 40px;
  }
  .title {
    margin-left: 10px;
    .nick-name {
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 24px;
      font-size: 16px;
      font-family: Montserrat-SemiBold, Montserrat;
      font-weight: 600;
      color: #222222;
      line-height: 24px;
    }
    .card {
      margin-top: 4px;
      height: 22px;
      font-size: 14px;
      font-family: Montserrat-Regular, Montserrat;
      font-weight: 400;
      color: #7A7A7A;
      line-height: 22px;
    }
  }
}
.cur-user {
  position: relative;
  background: #FFF5E6;
  border-radius: 8px;
  border: 1px solid #FF9F0A;
  box-sizing: border-box;
  margin-top: 0px;
  &::after {
    position: absolute;
    bottom: -1px;
    right: 0;
    content: ""; /* 必须要有 content 属性 */
    display: inline-block;
    width: 24px; /* 设置图像宽度 */
    height: 24px; /* 设置图像高度 */
    background-image: url('../../assets/images/home/<USER>');
    background-size: contain; /* 或 cover，根据需求调整 */
    background-repeat: no-repeat;
  }
}
.ant-dropdown .ant-dropdown-menu {
  padding: 10px 0px !important;
}
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item {
  padding: 0px !important;
}
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover {
  background: none !important;
}
.ant-popover .ant-popover-inner {
  padding: 0px !important;
}
.ant-popover .ant-popover-inner-content {
  padding: 10px 0px !important;
}
.switch-modal {
  display: flex;
  flex-direction: column;
  align-items: center;
  .title-main {
    height: 24px;
    font-size: 24px;
    font-family: Montserrat-Bold, Montserrat;
    font-weight: bold;
    color: #222222;
    line-height: 24px;
  }
  .sub-title {
    margin: 12px;
    font-size: 16px;
    font-family: Montserrat-Medium, Montserrat;
    font-weight: 500;
    color: #7A7A7A;
    line-height: 24px;
  }
  .button-wrapper {
    display: flex;
    margin-top: 20px;
    > button.primary {
      min-width: 210px;
      background: #FFAA0A !important;
      color: #FFFFFF !important;
    }
    .primary {
      font-size: 14px !important;
      font-family: Montserrat-SemiBold, Montserrat !important;
      font-weight: 600 !important;
    }
  }
}
.header-modal-switch-account {
  background: #F9F9F9;
  border-radius: 12px;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  .user {
    width: 220px;
    margin: 0px;
    background: #FFFFFF;
    border-radius: 8px;
  }
}
.header-expand-menu {
  width: 312px;
  border-radius: 12px;
  .user-account {
    cursor: pointer;
    //border-bottom: 1px solid #F2F2F2;
  }
  .addLine {
    border-top: 1px solid #F2F2F2;
  }
  .log-out {
    margin: 10px 20px 0px 10px;
    //border-top: 1px solid #F2F2F2;
    padding: 9px 29px;
    border-radius: 8px;
    display: flex;
    .text {
      padding-left: 25px;
      font-size: 16px;
      font-family: Montserrat-SemiBold, Montserrat;
      font-weight: 600;
      color: #222222;
    }
  }
  .log-out::before {
    content: ""; /* 必须要有 content 属性 */
    display: inline-block;
    width: 26px; /* 设置图像宽度 */
    height: 26px; /* 设置图像高度 */
    background-image: url('../../assets/images/home/<USER>');
    background-size: contain; /* 或 cover，根据需求调整 */
    background-repeat: no-repeat;
  }
  .log-out:hover {
    background: #FFF5E6;
  }
}
.ant-tour {
  width: 321px !important;
}
</style>
<style lang="scss" scoped>
.header {
  width: 100%;
  min-width: 1240px;
  background: #FFFFFF;
  .content {
    width: 1240px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    height: 90px;
    .logo {
      margin: 28px 0;
      cursor: pointer;
      img {
        width: 168px;
        height: 34px;
      }
    }
    .operate {
      display: flex;
      align-items: center;
      .account-wrap {
        height: 50px;
        display: flex;
        align-items: center;
        &:hover {
          background: #F2F2F2;
          border-radius: 12px;
        }
      }
      .account {
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        padding: 0 8px;
        img {
          width: 40px;
          height: 40px;
          border-radius: 40px;
        }
        .name {
          padding: 0 8px;
          height: 28px;
          font-size: 16px;
          font-family: Montserrat-SemiBold, Montserrat;
          font-weight: 600;
          color: #222222;
          line-height: 28px;
          max-width: 202px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &::after {
          content: ""; /* 必须要有 content 属性 */
          display: inline-block;
          width: 18px; /* 设置图像宽度 */
          height: 18px; /* 设置图像高度 */
          background-image: url('../../assets/images/home/<USER>');
          background-size: contain; /* 或 cover，根据需求调整 */
          background-repeat: no-repeat;
        }
      }
      .switchActive{
        padding: 9px 12px;
        background: #F2F2F2;
        border-radius: 12px;
      }
      .tourActive {
        padding: 9px 12px;
        background: #F2F2F2;
        border-radius: 15px;
        border: 1px solid #FF9F0A;
      }
    }
  }
}
</style>
