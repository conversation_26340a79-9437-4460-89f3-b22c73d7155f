<template>
  <div class="countdown">
    <slot
      v-if="$slots.default"
      :days="timeData.days"
      :hours="timeData.hours"
      :minutes="timeData.minutes"
      :seconds="timeData.seconds"
      :milliseconds="timeData.milliseconds"
    />
    <template v-else>{{ formattedTime }}</template>
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount, onActivated, onDeactivated } from 'vue'
import { isSameSecond, parseTimeData, parseFormat, raf, cancelRaf } from './utils'

defineOptions({
  name: 'NeCountdown'
})

const props = defineProps({
  millisecond: Boolean,
  time: {
    type: Number,
    default: 0
  },
  format: {
    type: String,
    default: 'HH:mm:ss'
  },
  autoStart: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['complete', 'remain'])

// 响应式状态
const remain = ref(0)
const counting = ref(false)
const keepAlivePaused = ref(false)
let endTime = 0
let rafId = null

// 计算属性
const timeData = computed(() => parseTimeData(remain.value))
const formattedTime = computed(() => parseFormat(props.format, timeData.value))

// 方法
const getRemain = () => {
  return Math.max(endTime - Date.now(), 0)
}

const setRemain = value => {
  remain.value = value

  if (value === 0) {
    pause()
    emit('complete')
  } else {
    emit('remain', parseInt(value / 1000, 10))
  }
}

const microTick = () => {
  rafId = raf(() => {
    setRemain(getRemain())

    if (remain.value !== 0) {
      microTick()
    }
  })
}

const macroTick = () => {
  rafId = raf(() => {
    const remainValue = getRemain()

    if (!isSameSecond(remainValue, remain.value) || remainValue === 0) {
      setRemain(remainValue)
    }

    if (remain.value !== 0) {
      macroTick()
    }
  })
}

const tick = () => {
  if (props.millisecond) {
    microTick()
  } else {
    macroTick()
  }
}

const start = () => {
  if (counting.value) {
    return
  }

  counting.value = true
  endTime = Date.now() + remain.value
  tick()
}

const pause = () => {
  counting.value = false
  cancelRaf(rafId)
}

const reset = () => {
  pause()
  remain.value = props.time

  if (props.autoStart) {
    start()
  }
}

// 监听属性变化
watch(
  () => props.time,
  () => reset(),
  { immediate: true }
)

// 生命周期钩子
onActivated(() => {
  if (keepAlivePaused.value) {
    counting.value = true
    keepAlivePaused.value = false
    tick()
  }
})

onDeactivated(() => {
  if (counting.value) {
    pause()
    keepAlivePaused.value = true
  }
})

onBeforeUnmount(() => {
  pause()
})

// 暴露方法给父组件
defineExpose({
  start,
  pause,
  reset
})
</script>
<style>
.countdown {
  color: #dee2e7;
  display: flex;
  align-items: center;
  margin-left: 5px;
}
</style>
