<template>
  <div
    class="custom-select"
    :class="{ open: isOpen, disabled }"
    @click="toggleDropdown"
    @blur="isOpen = false"
    tabindex="0"
  >
    <div class="selected-value">
      <span>{{ selectedLabel }}</span>
      <img :src="isOpen ? upIcon : downIcon" class="select-arrow" />
    </div>
    <ul v-if="isOpen" class="dropdown-list">
      <li
        v-for="option in options"
        :key="option.value"
        :class="{ selected: option.value === modelValue }"
        @click.stop="selectOption(option)"
      >
        {{ option.label }}
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import downIcon from '@/assets/icon/select-down.png'
import upIcon from '@/assets/icon/select-up.png'

const props = defineProps({
  options: {
    type: Array,
    default: () => []
  },
  modelValue: [String, Number],
  disabled: Boolean
})
const emit = defineEmits(['update:modelValue'])

const isOpen = ref(false)
const selectedLabel = computed(
  () => props.options.find(o => o.value === props.modelValue)?.label || '请选择'
)

function toggleDropdown() {
  if (!props.disabled) isOpen.value = !isOpen.value
}
function selectOption(option) {
  emit('update:modelValue', option.value)
  isOpen.value = false
}

// 监听外部modelValue变化时自动关闭下拉
watch(
  () => props.modelValue,
  () => {
    isOpen.value = false
  }
)
</script>

<style scoped>
.custom-select {
  position: relative;
  width: 100%;
  cursor: pointer;
  user-select: none;
}
.selected-value {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-radius: 8px;
  height: 2.6vw; /* 50px / 1920 * 100 = 2.6vw */
  min-height: 40px; /* 最小高度确保可用性 */
  padding: 0 0.83vw; /* 16px / 1920 * 100 = 0.83vw */
  font-size: 0.94vw; /* 18px / 1920 * 100 = 0.94vw */
  color: #222;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
  white-space: nowrap;
  overflow: hidden;
}
.selected-value span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  max-width: 90%;
}
.select-arrow {
  width: 1.25vw; /* 24px / 1920 * 100 = 1.25vw */
  height: 1.25vw;
  min-width: 16px; /* 最小尺寸 */
  min-height: 16px;
  margin-left: 0.42vw; /* 8px / 1920 * 100 = 0.42vw */
}
.dropdown-list {
  position: absolute;
  left: 0;
  right: 0;
  top: 100%;
  background: #fff;
  border-radius: 8px;
  margin-top: 4px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  z-index: 10;
  max-height: 220px;
  overflow-y: auto;
  list-style: none;
  padding: 0;
}
.dropdown-list li {
  padding: 0.63vw 0.83vw; /* 12px 16px / 1920 * 100 = 0.63vw 0.83vw */
  font-size: 0.94vw; /* 18px / 1920 * 100 = 0.94vw */
  color: #222;
  cursor: pointer;
  transition: background 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.dropdown-list li.selected,
.dropdown-list li:hover {
  background: #fff9ec;
  color: #ff9f0a;
}
.custom-select.disabled {
  opacity: 0.5;
  pointer-events: none;
}
</style>
