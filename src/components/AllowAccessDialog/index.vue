<template>
  <div v-if="sVisible" class="dialog-wrapper" :class="[dialogClass]">
    <div class="dialog-content">
      <slot></slot>
    </div>
    <div class="dialog-footer" v-if="showOkButton || showCancelButton">
      <div v-if="showCancelButton" class="dialog-button button-cancel" @click="handleCancel">
        {{ cancelValue || t('common.deny') }}
      </div>
      <div v-if="showOkButton" class="dialog-button button-ok" @click="handleOk">
        {{ okValue || t('common.agree') }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
defineOptions({
  name: 'AllowAccessDialog'
})
// 获取国际化实例
const { t } = useI18n()

// 定义 props
const props = defineProps({
  dialogClass: {
    type: String,
    default: ''
  },
  theme: {
    type: String,
    default: 'green'
  },
  modelValue: {
    type: Boolean,
    default: false
  },
  okValue: {
    type: String,
    default: ''
  },
  cancelValue: {
    type: String,
    default: ''
  },
  showOkButton: {
    type: Boolean,
    default: true
  },
  showCancelButton: {
    type: Boolean,
    default: true
  }
})

// 定义 emit
const emit = defineEmits(['update:modelValue', 'cancel', 'ok'])

// 响应式状态
const sVisible = ref(!!props.modelValue)

// 监听 visible 变化
watch(
  () => props.modelValue,
  val => {
    sVisible.value = val
  }
)

// 方法
const handleCancel = e => {
  emit('cancel', e)
  emit('update:modelValue', false)
}

const handleOk = e => {
  emit('ok', e)
  emit('update:modelValue', false)
}
</script>

<style scoped lang="scss">
.dialog-wrapper {
  width: 253px;
  background: rgba(15, 25, 42, 0.9);
  border-radius: 10px;
  overflow: hidden;
}

.dialog-content {
  padding: 18px 16px 24px 16px;
  color: #fff;
  font-size: 12px;
}

.dialog-footer {
  position: relative;
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.dialog-button {
  padding: 0 8px;
  height: 26px;
  background: #2f384b;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
  text-align: center;
  line-height: 26px;
}

.button-cancel {
  color: #a2aab8;
  margin-right: 24px;
}

.button-ok {
  background: #02ca8a;
}
</style>
