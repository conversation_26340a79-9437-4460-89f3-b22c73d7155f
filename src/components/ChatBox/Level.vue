<template>
  <div class="msg-level" :class="outClass">
    <div class="msg-level-content" :class="contentClass">
      <img :src="levelImg" />
      <span>{{ levelText }}</span>
      <span v-if="levelId != 4" class="ml4">{{ subLevelText }}</span>
    </div>
  </div>
</template>
<script>
// import chatConfig from './config.js'
export default {
  name: 'LevelDegree',
  data() {
    return {
      // levelImg: '',
      levelTextMap: {
        1: '青木',
        3: '黄金',
        2: '白银',
        4: '星钻',
      },
      subLevelTextMap: {
        1: 'Ⅰ',
        2: 'Ⅱ',
        3: 'Ⅲ',
        4: 'Ⅳ',
      },
      outClassMap: {
        1: 'level-wood-out',
        2: 'level-silver-out',
        3: 'level-gold-out',
        4: 'level-diamond-out',
      },
      contentClassMap: {
        1: 'level-wood-content',
        2: 'level-silver-content',
        3: 'level-gold-content',
        4: 'level-diamond-content',
      },
    }
  },
  props: {
    levelId: {
      type: Number,
      default: 0,
    },
    subLevel: {
      type: Number,
      default: 0,
    },
  },
  computed: {
    levelText() {
      // @log-ignore
      return this.levelTextMap[this.levelId]
    },
    subLevelText() {
      // @log-ignore
      return this.subLevelTextMap[this.subLevel]
    },
    outClass() {
      // @log-ignore
      return this.outClassMap[this.levelId]
    },
    contentClass() {
      // @log-ignore
      return this.contentClassMap[this.levelId]
    },
    levelImg() {
      if (this.levelId == 1) {
        return new URL(`./imgs/level/wood-${this.subLevel}.png`, import.meta.url).href
      } else if (this.levelId == 2) {
        return new URL(`./imgs/level/silver-${this.subLevel}.png`, import.meta.url).href
      } else if (this.levelId == 3) {
        return new URL(`./imgs/level/gold-${this.subLevel}.png`, import.meta.url).href
      } else if (this.levelId == 4) {
        return new URL(`./imgs/level/diamond.png`, import.meta.url).href
      } else {
        return new URL(`./imgs/level/wood-1.png`, import.meta.url).href
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.ml4 {
  margin-left: 4px;
}
.msg-level {
  position: relative;
  border-radius: 0px 2px 2px 0px;
  padding: 1px;
  .msg-level-content {
    border-radius: 0px 2px 2px 0px;
    padding: 0px 3px 1px 8px;
    font-size: 12px;
    line-height: 12px;
  }
  img {
    width: 16px;
    height: 16px;
    left: -7px;
    position: absolute;
    transform: translateY(-50%);
    top: 50%;
  }
}
.level-wood-out {
  background: #c88e57;
  .level-wood-content {
    background: #e5ae7c;
    color: #936133;
  }
}
.level-silver-out {
  background: #96c6ff;
  .level-silver-content {
    background: #5296dc;
    color: #fff;
  }
}
.level-gold-out {
  background: #fec701;
  .level-gold-content {
    background: #fee333;
    color: #f89701;
  }
}
.level-diamond-out {
  background: linear-gradient(140deg, rgba(218, 155, 255, 1), rgba(184, 93, 238, 1));
  .level-diamond-content {
    background: linear-gradient(159deg, #ad34d3 0%, #e125df 100%);
    color: #fff;
  }
}
</style>
