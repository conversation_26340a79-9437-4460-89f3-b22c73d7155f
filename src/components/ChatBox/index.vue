<template>
  <div
    v-show="chatBox.isShowBox"
    class="chat-box-container"
    :class="{
      playBack: isPlayBack,
      'chat-box-show': chatBox.isShowBox
    }"
    v-drag="{
      dragWrapSelector: '.full-float-container',
      dragSelector: '.chat-box-head',
      draggable: !isWholeOnsatge
    }"
  >
    <div class="chat-box-head" v-if="showHeader">
      <div class="title">{{ $t('classroom.chats.chats.chatbox') }}</div>
      <div class="close" @click="handleIsShowBox">
        <img src="./imgs/icon_close.png" alt="" />
      </div>
    </div>
    <div class="chat-box-content" id="chat-list">
      <ul class="chat-ul">
        <template v-for="(item, index) in chatList">
          <li v-if="item.role === 't' || item.role === 'f'" class="left-li" :key="index">
            <div class="avatarBox">
              <div
                v-if="item.userInfo.avatarFrame"
                class="avatar-box"
                :style="'background: url(' + item.userInfo.avatarFrame + ') no-repeat center'"
              ></div>
              <img :src="item.content.path" />
            </div>
            <div class="msgBox">
              <div class="msg-header">
                <div class="nickname">{{ item.content.name }}</div>
                <div v-if="item.content.to_uid" class="msg-private">
                  【{{ $t('classroom.chats.chats.privateMessage') }}】
                </div>
              </div>
              <div class="msg-box msg-box-teacher" :class="{ 'msg-box-f': item.role === 'f' }">
                {{ item.content.msg }}
              </div>
            </div>
          </li>

          <li
            v-if="item.role === 's' && item.userId != baseData.stuInfo.id"
            class="left-li"
            :key="index"
          >
            <div class="avatarBox">
              <div
                v-if="item.userInfo.avatarFrame"
                class="avatar-box"
                :style="'background: url(' + item.userInfo.avatarFrame + ') no-repeat center'"
              ></div>
              <img :src="item.content.path" />
            </div>
            <div class="msgBox">
              <div class="msg-header">
                <LevelDegree
                  v-if="item?.userInfo?.levelId"
                  :levelId="item.userInfo.levelId"
                  :subLevel="item.userInfo.subLevel"
                  class="ml-12"
                ></LevelDegree>
                <div class="nickname">
                  {{ item.content.name }}
                </div>
              </div>
              <div
                v-if="!item.content.isNewEmoji"
                class="msg-box"
                :class="{ 'box-back': item.userInfo.messageBoxFrame }"
                :style="
                  item.userInfo.messageBoxFrame
                    ? `border-image-source: url(${item.userInfo.messageBoxFrame});border-image-width: 20px 29px`
                    : ''
                "
              >
                {{ item.content.msg }}
              </div>
              <EmoticonMessage
                v-else
                :willAutoClear="false"
                :name="item.content.msg"
                :type="item.content.emojiType"
                :emojiId="item.content.emojiId"
                :width="40"
                :height="40"
                :lottieUrl="item.content.lottieUrl"
                :loopLottie="true"
                style="margin-left: 5px"
              />
            </div>
          </li>
          <li v-if="item.userId == baseData.stuInfo.id" class="right-li" :key="index">
            <div class="msgBox">
              <div class="msg-header">
                <div
                  v-if="item.content.to_uid && item.content.to_uid === baseData.teacherInfo.id"
                  class="msg-private"
                >
                  【{{ $t('classroom.chats.chats.To') }}
                  {{ $t('classroom.chats.chats.sendToTeacher') }}】
                </div>
                <LevelDegree
                  v-if="item?.userInfo?.levelId"
                  :levelId="item.userInfo.levelId"
                  :subLevel="item.userInfo.subLevel"
                ></LevelDegree>
                <div class="nickname">{{ item.content.name }}</div>
              </div>
              <div class="msg-wrap">
                <div @click="handleReSend(item)" v-if="item.status === 2" class="msg-error">
                  <img src="./imgs/icon_error.png" />
                </div>
                <div
                  v-if="!item.content.isNewEmoji"
                  class="msg-box msg-box-me"
                  :class="{ 'box-back': item.userInfo.messageBoxFrame }"
                  :style="
                    item.userInfo.messageBoxFrame
                      ? `border-image-source: url(${item.userInfo.messageBoxFrame});border-image-width: 20px 29px`
                      : ''
                  "
                >
                  {{ item.content.msg }}
                </div>
                <EmoticonMessage
                  v-else
                  :willAutoClear="false"
                  :name="item.content.msg"
                  :type="item.content.emojiType"
                  :emojiId="item.content.emojiId"
                  :width="40"
                  :height="40"
                  :lottieUrl="item.content.lottieUrl"
                  :loopLottie="true"
                />
              </div>
            </div>
            <div class="avatarBox">
              <div
                v-if="item.userInfo.avatarFrame"
                class="avatar-box"
                :style="'background: url(' + item.userInfo.avatarFrame + ') no-repeat center'"
              ></div>
              <img :src="item.content.path" />
            </div>
          </li>
          <li v-if="item.role === 'tip'" class="tip" :key="index">
            {{ $t(item.msg) }}
          </li>
          <li v-if="item.role === 'enterTip'" class="enterTipBox" :key="index">
            <div class="enterTip">
              <LevelDegree
                class="tipLevel"
                v-if="item.userInfo.levelId"
                :levelId="item.userInfo.levelId"
                :subLevel="item.userInfo.subLevel"
              ></LevelDegree>
              <span class="tipName">{{ item.userInfo.name }}&nbsp;</span>
              {{ $t(item.msg) }}
            </div>
          </li>
        </template>
      </ul>
    </div>
    <div class="chat-box-foot">
      <div class="new-meg-tip" v-if="isShowNewMsgTip" @click="handleScrollBottom">
        <i class="icon-more-bottom"></i>
      </div>
      <div v-if="isFrequentlyShow" class="too-frequently">
        {{ $t('classroom.chats.chats.frequently') }}
      </div>
      <div id="memberList" class="memberList" v-show="sendToReactive.isOpen">
        <ul>
          <li
            class="member-li"
            v-for="item in sendToList"
            :key="item.id"
            @click="changeSendToValue(item)"
            :data-log="`选中发送对象${$t(item.name)}`"
          >
            <span
              class="member-name"
              :class="{ 'member-changed': sendToReactive.id === item.id }"
              >{{ $t(item.name) }}</span
            >
            <span class="member-icon" v-if="sendToReactive.id === item.id"></span>
          </li>
        </ul>
      </div>
      <div class="send-to" v-if="isShowFoot">
        {{ $t('classroom.chats.chats.sendTo') }}:
        <div
          class="send-to-click"
          @click="toggleMemberPopup"
          :class="{
            'un-send-to-click':
              isConnecting || !chatBox.isOpenChat || chatBox.isOnlyTeacher || chatBox.isMuted
          }"
        >
          <span
            class="send-to-name"
            :class="{
              'un-send-to-name':
                isConnecting || !chatBox.isOpenChat || chatBox.isOnlyTeacher || chatBox.isMuted
            }"
            >{{ $t(sendToReactive.name) }}</span
          >
          <div
            v-if="chatBox.isOpenChat && !chatBox.isOnlyTeacher && !chatBox.isMuted"
            class="send-to-icon"
          ></div>
        </div>
      </div>
      <div
        v-if="isShowFoot && chatBox.isOpenChat && !isConnecting && !chatBox.isMuted"
        class="input-box"
      >
        <!-- <input
          type="textarea"
          v-model="messageText"
          style="width: 240px; height: 32px"
          :placeholder="$t('classroom.chats.chats.inputPlaceholder')"
          @keyup.enter="handleSendMsg"
        /> -->
        <Textarea
          class="text-area"
          v-model:value="messageText"
          :maxLength="200"
          :auto-size="{ minRows: 1, maxRows: 3 }"
          :placeholder="$t('classroom.chats.chats.inputPlaceholder')"
          @pressEnter="handleSendMsg"
        />
        <!-- <el-input
          class="text-area"
          type="textarea"
          v-model="messageText"
          :auto-size="{ minRows: 1, maxRows: 3 }"
          :maxLength="200"
          :placeholder="$t('classroom.chats.chats.inputPlaceholder')"
          @keyup.enter="handleSendMsg"
        /> -->
        <div v-if="isCanSend" class="send-btn" @click="handleSendMsg">
          <img src="./imgs/icon_send.png" />
        </div>
        <div v-else class="send-btn no-send">
          <img src="./imgs/icon_send_forbidden.png" />
        </div>
      </div>
      <div v-if="!chatBox.isOpenChat" class="input-close">
        <div class="input-close-content">
          {{ $t('classroom.chats.chats.chatboxClosed') }}
        </div>
      </div>
      <div v-else-if="chatBox.isMuted" class="input-close">
        <div class="input-close-content">
          {{ $t('classroom.chats.chats.underMute') }}
        </div>
      </div>
      <div v-if="isShowFoot && isConnecting" class="input-close">
        <div class="input-close-content">
          {{ $t('classroom.chats.chats.connecting') }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, computed, watch, onMounted, onBeforeUnmount, nextTick } from 'vue'
import EmoticonMessage from '@/components/EmoticonMessage/index.vue'
import LevelDegree from './Level.vue'
import { useClassData } from '@/stores/classroom'
import { emitter } from '@/hooks/useEventBus.js'
import defaultAvatar from './imgs/avatar_default.png'
import dragDirective from './dragDirective.js'
import { getLocalStorageConfig } from '@/utils/initConfig'
import { Textarea } from 'ant-design-vue'
import { useIrcService } from '@/hooks/useIrcService'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { chatLog } from '@/utils/web-log/HWLogDefine'

const { getIrcService } = useIrcService()

const MAX_CHATLIST_LENGTH = 300 // 聊天消息最大展示条数

// 发送消息的状态
const SEND_MSG_STATUS = {
  PENDING: 0,
  SUCCESS: 1,
  ERROR: 2
}
const props = defineProps({
  showHeader: {
    type: Boolean,
    default: true
  },
  isWholeOnsatge: {
    type: Boolean,
    default: false
  },
  isShowFoot: {
    type: Boolean,
    default: true
  },
  isPlayBack: {
    type: Boolean,
    default: false
  }
})

const isShowNewMsgTip = ref(false)
const messageText = ref('')
const chatList = ref([])
const isFrequently = ref(false)
const isFrequentlyShow = ref(false)
// const isConnecting = ref(true)
const isConnecting = ref(false)
const chatMsgPriority = ref(99)
const sendToReactive = reactive({
  id: '',
  name: 'classroom.chats.chats.sendToAll',
  isOpen: false
})
// const dragEvent = ref(null)
const singleChat = ref([])
const limitNum = 5 // 消息频率 5s 5条
const limitTime = 5000
const waitTime = 3000

const store = useClassData()

const baseData = computed(() => store.baseData)
const chatBox = computed(() => store.chatBox)
const isShowBox = computed(() => store.chatBox.isShowBox)
const ircStatus = computed(() => store.ircStatus)
const studentList = computed(() => store.studentList)
// 定义 isCanSend 计算属性
const isCanSend = computed(() => {
  return messageText.value.trim()
})

// 定义 sendToList 计算属性
const sendToList = computed(() => {
  console.log('baseData', baseData.value)
  return [
    { id: '', name: 'classroom.chats.chats.sendToAll' },
    { id: baseData.value.teacherInfo?.id, name: 'classroom.chats.chats.sendToTeacher' }
  ]
})
// 监听 ircStatus 的变化
watch(
  ircStatus,
  value => {
    if (value) {
      isConnecting.value = false
    }
  },
  { immediate: true }
)

// 监听 isShowBox 的变化
watch(isShowBox, value => {
  if (value) {
    handleScrollBottom()
    store.updateChatBoxInfo({
      hasNewMsg: false // 是否显示小红点
    })
  }
})

onMounted(() => {
  if (!props.isPlayBack) {
    // 网络情况
    emitter.on('chatboxNetStatusChanged', onNetStatusChanged)
    // 发出消息回调
    emitter.on('chatGroupMsgRes', onSendRoomMessageResponse)
    // 获取历史消息
    emitter.on('chatHistoryMsg', onGetRoomHistoryMessageResponse)

    // 监听实时消息
    emitter.on('chatboxMsg', onRecvRoomMessage)
    // 监听kv
    const IrcService = getIrcService()
    IrcService.on('onJoinRoomNotice', onJoinRoomNotice)
    emitter.on('openchat_chatbox', onOpenChatHandle)
    emitter.on('chat_msg_control_chatbox', onChatMsgControlHandle)
    emitter.on('private_mute_chat_chatbox', onPrivateMutChatHandle)
  }
  document.addEventListener('click', handleDocumentClick)

  // 获取云控配置信息-聊天频率
  const cloudConfig = getLocalStorageConfig()
  if (cloudConfig && cloudConfig.configs) {
    for (let i = 0; i < cloudConfig.configs.length; i++) {
      const element = cloudConfig.configs[i]
      if (element.configKey === 'frequentlyObj') {
        const frequentlyArr = element.configDefaultValue.split(',')
        limitNum.value = frequentlyArr[0] || 5
        limitTime.value = frequentlyArr[1] || 5000
        waitTime.value = frequentlyArr[2] || 3000
        break
      }
    }
  }
  console.log('cloudConfig cloudConfig', limitNum, limitTime, waitTime, cloudConfig)
  if (chatBox.value.historyList.length) {
    // 获取历史消息
    chatList.value = [...chatBox.value.historyList]
    handleScrollBottom()
  }
})

onBeforeUnmount(() => {
  emitter.off('chatboxNetStatusChanged', onNetStatusChanged)
  emitter.off('chatGroupMsgRes', onSendRoomMessageResponse)
  emitter.off('chatHistoryMsg', onGetRoomHistoryMessageResponse)
  emitter.off('chatboxMsg', onRecvRoomMessage)
  const IrcService = getIrcService()
  IrcService.off('onJoinRoomNotice', onJoinRoomNotice)
  emitter.off('openchat_chatbox', onOpenChatHandle)
  emitter.off('chat_msg_control_chatbox', onChatMsgControlHandle)
  emitter.off('private_mute_chat_chatbox', onPrivateMutChatHandle)
  document.removeEventListener('click', handleDocumentClick)
})

// 方法定义
function handleIsShowBox() {
  if (props.isPlayBack) {
    store.updateChatBoxInfo({
      isShowBox: true // 是否显示
    })
    return
  }
  store.updateChatBoxInfo({
    isShowBox: !chatBox.value.isShowBox // 是否显示
  })
  messageText.value = ''
  if (chatBox.value.isShowBox) {
    handleScrollBottom()
    store.updateChatBoxInfo({
      hasNewMsg: false // 是否显示小红点
    })
  }
}

function handleSendMsg(e) {
  e.preventDefault()
  if (!messageText.value.trim()) return
  if (isFrequently.value) {
    isFrequentlyShow.value = true
    classLiveSensor.osta_cb_input_cooling()
    return
  } else {
    isFrequentlyShow.value = false
  }

  const isFrequentlyCheck = handleFrequentlyFn()
  if (isFrequentlyCheck) {
    isFrequently.value = true
    isFrequentlyShow.value = true
    classLiveSensor.osta_cb_input_cooling()
    setTimeout(() => {
      isFrequently.value = false
      isFrequentlyShow.value = false
    }, waitTime.value)
    return
  }

  const content = {
    type: '130',
    from: 'flv',
    name: baseData.value.stuInfo.nickName,
    msg: messageText.value,
    path: baseData.value.stuInfo.avatar
  }

  if (sendToReactive.id) {
    content.type = '139'
    content.to_uid = sendToReactive.id
  }
  if (chatBox.value.isOnlyTeacher) {
    content.type = '139'
    content.to_uid = baseData.value.teacherInfo.id
  }
  const IrcService = getIrcService()
  const res = IrcService.sendRoomMessageWithPreMsgId({
    content: content,
    chatMsgPriority: chatMsgPriority.value
  })

  // console.log('1111111', res, chatList.value)

  if (res.code === 0) {
    chatList.value.push({
      role: 's',
      content: content,
      status: 'PENDING', // SEND_MSG_STATUS.PENDING 的替代值
      preMsgId: res.preMsgId,
      userId: baseData.value.stuInfo.id,
      userInfo: {
        levelId: baseData.value.stuInfo?.prop?.levelId,
        subLevel: baseData.value.stuInfo?.prop?.subLevel,
        name: baseData.value.stuInfo.nickName,
        avatarFrame: baseData.value.stuInfo?.prop?.avatarFrame?.resourceUrl,
        messageBoxFrame: baseData.value.stuInfo?.prop?.messageBoxFrame?.resourceUrl
      }
    })
    // console.log('1111111 发送', res, chatList.value.lenght)

    singleChat.value.push(+new Date())
    messageText.value = ''
    handleScrollBottom()
    sendLogger('学生发送chatbox消息', { ...content })
    classLiveSensor.osta_cb_send_msg(content)
  } else {
    sendLogger('学生发送chatbox消息失败', { ...res }, 'error')
  }
}

// handleFrequentlyFn 方法
const handleFrequentlyFn = () => {
  if (
    singleChat.value.length >= limitNum.value &&
    +new Date() - singleChat.value[singleChat.value.length - limitNum.value] < limitTime.value
  ) {
    return true
  } else {
    return false
  }
}

// onSendRoomMessageResponse 方法
const onSendRoomMessageResponse = res => {
  // @log-ignore
  if (res.code !== 0) {
    sendLogger(
      '学生发送聊天消息回调失败',
      {
        res
      },
      'error'
    )
  }

  for (let i = chatList.value.length - 1; i > 0; i--) {
    const element = chatList.value[i]
    // console.log('11111111 查看', res, element, i)
    if (element.preMsgId && element.preMsgId === res.preMsgId) {
      const status = res.code === 0 ? SEND_MSG_STATUS.SUCCESS : SEND_MSG_STATUS.ERROR
      element.status = status

      if (res.code === 0) {
        // 成功之后这里更新store里的historyList
        const list = [...chatBox.value.historyList, element]
        store.updateChatBoxInfo({
          historyList: list // 消息备份
        })
      }
      return
    }
  }
}

function handleReSend(item) {
  const { content } = item
  const IrcService = getIrcService()
  const res = IrcService.sendRoomMessageWithPreMsgId({
    content,
    chatMsgPriority: chatMsgPriority.value
  })
  if (res.code === 0) {
    for (let i = chatList.value.length - 1; i > 0; i--) {
      const element = chatList.value[i]
      if (element.preMsgId && element.preMsgId === item.preMsgId) {
        element.status = SEND_MSG_STATUS.PENDING
        element.preMsgId = res.preMsgId
        return
      }
    }
  }
}

function onGetRoomHistoryMessageResponse(res) {
  if (res.code === 0) {
    // Clear chat list after reconnection
    chatList.value = []
    res.content.forEach(e => {
      try {
        e.content = JSON.parse(e.text)
      } catch (error) {
        console.error('[chatbox]监听获取历史消息回调解析错误', error)
      }
      if (
        (e.content.type && Number(e.content.type) === 130) ||
        (Number(e.content.type) === 139 && e.content.to_uid === baseData.value.stuInfo.id) ||
        (Number(e.content.type) === 139 &&
          e.content.to_uid === baseData.value.teacherInfo.id &&
          Number(e.sender.split('_').pop()) === baseData.value.stuInfo.id)
      ) {
        e.status = SEND_MSG_STATUS.SUCCESS
        if (e.sender.startsWith('t')) {
          e.role = 't'
        } else if (e.sender.startsWith('s')) {
          e.role = 's'
        } else if (e.sender.startsWith('f')) {
          e.role = 'f'
        }
        if (!e.content.path) {
          e.content.path = defaultAvatar
        }
        e.userId = e.sender.split('_').pop()
        e.userInfo = {}
        const userInfo = studentList.value.find(item => item.userId === e.userId)
        if (userInfo) {
          e.content.name = userInfo.nickName
          e.content.path = userInfo?.avatar || defaultAvatar
          e.userInfo = {
            levelId: userInfo?.prop?.levelId,
            subLevel: userInfo?.prop?.subLevel,
            name: userInfo.nickName,
            avatarFrame: userInfo?.prop?.avatarFrame?.resourceUrl,
            messageBoxFrame: userInfo?.prop?.messageBoxFrame?.resourceUrl
          }
        }
        if (
          chatBox.value.isOnlyTeacher &&
          e.role === 's' &&
          +e.userId !== baseData.value.stuInfo.id
        ) {
          return
        }
        chatList.value.unshift(e)
      }
    })
    // Keep only the last 300 messages
    if (chatList.value.length > MAX_CHATLIST_LENGTH) {
      const splicStartNum = chatList.value.length - MAX_CHATLIST_LENGTH
      chatList.value = chatList.value.splice(splicStartNum)
    }
    chatList.value.push({
      role: 'enterTip',
      userInfo: {
        levelId: baseData.value.stuInfo?.prop?.levelId,
        subLevel: baseData.value.stuInfo?.prop?.subLevel,
        name: baseData.value.stuInfo.nickName
      },
      msg: 'classroom.chats.chats.msgTip.ENTER_ROOM'
    })
    // Update store historyList
    store.updateChatBoxInfo({
      historyList: [...chatList.value] // 消息备份
    })
    handleScrollBottom()
  }
}

// 老师开启、关闭聊天功能
function onOpenChatHandle(res) {
  const { noticeContent } = res
  const bool = noticeContent
  store.updateChatBoxInfo({
    isOpenChat: bool // 开启聊天 默认为true
  })
  handleChangeIsStopChat(bool)
  sendLogger(`老师${bool ? '开启' : '关闭'} 聊天功能`)
}

// 老师设置学员仅可看到老师消息
function onChatMsgControlHandle(res) {
  const { noticeContent } = res
  let isOnlyTeacher = noticeContent.status === 'teacher'
  store.updateChatBoxInfo({
    isOnlyTeacher: isOnlyTeacher // 只看老师
  })
  handleChangeIsOnlyReadTeacher(isOnlyTeacher)
  sendLogger(`老师设置学员仅可看到老师消息 ${isOnlyTeacher}`)
  if (isOnlyTeacher) {
    changeSendToValue({
      id: baseData.value.teacherInfo.id,
      name: 'classroom.chats.chats.sendToTeacher'
    })
  }
}

// 老师开启、关闭单独禁言
function onPrivateMutChatHandle(res) {
  const { noticeContent } = res
  const stopChat = noticeContent.ids.find(id => id == baseData.value.stuInfo.id)

  if (stopChat) {
    if (!chatBox.value.isMuted) {
      store.updateChatBoxInfo({
        isMuted: true //单独禁言
      })
      handleChangeIsMutedByTeacher(true)
      sendLogger(`老师开启单独禁言`)
    }
  } else {
    if (chatBox.value.isMuted) {
      store.updateChatBoxInfo({
        isMuted: false //单独禁言
      })
      handleChangeIsMutedByTeacher(false)
      sendLogger(`老师关闭单独禁言`)
    }
  }
}

// 监听网络状态变化函数
function onNetStatusChanged(res) {
  if (res.netStatus === 4) {
    chatList.value.push({
      role: 'tip',
      msg: 'classroom.chats.chats.msgTip.CONNECT'
    })
  } else if (res.netStatus === 1 || res.netStatus === 2 || res.netStatus === 5) {
    chatList.value.push({
      role: 'tip',
      msg: 'classroom.chats.chats.msgTip.DISCONNECT'
    })
  }
  handleConditionScrollBottom()
}

// 监听他人加入房间回调函数
function onJoinRoomNotice(res) {
  const item = studentList.value.find(item => item.stuIrcId === res.userInfo.nickname)
  if (item) {
    chatList.value.forEach(data => {
      if (data.userId === item.userId && item?.prop) {
        data.content.name = item.nickName
        data.content.path = item?.avatar || defaultAvatar
        data.userInfo.messageBoxFrame = item?.prop?.messageBoxFrame?.resourceUrl
        data.userInfo.avatarFrame = item?.prop?.avatarFrame?.resourceUrl
        data.userInfo.levelId = item?.prop?.levelId
        data.userInfo.subLevel = item?.prop?.subLevel
      }
    })
    chatList.value.push({
      role: 'enterTip',
      userInfo: {
        levelId: item?.prop?.levelId,
        subLevel: item?.prop?.subLevel,
        name: item.nickName
      },
      msg: 'classroom.chats.chats.msgTip.ENTER_ROOM'
    })
    handleConditionScrollBottom()
  }
}

function onRecvRoomMessage(res) {
  const { content, fromUserInfo } = res
  if (
    (content && content.type && Number(content.type) === 130) ||
    (Number(content.type) === 139 && content.to_uid === baseData.value.stuInfo.id)
  ) {
    let role = ''
    if (fromUserInfo.nickname.startsWith('t')) {
      role = 't'
    } else if (fromUserInfo.nickname.startsWith('s')) {
      role = 's'
    } else if (fromUserInfo.nickname.startsWith('f')) {
      role = 'f'
    }

    if (!content.path) {
      content.path = defaultAvatar
    }

    if (chatList.value.length >= MAX_CHATLIST_LENGTH) {
      chatList.value.shift()
    }
    if (chatBox.value.isOnlyTeacher && role === 's') {
      return
    }

    const userId = fromUserInfo.nickname.split('_').pop()
    const info = studentList.value.find(item => item.userId === userId)
    let userInfo = {}
    if (info) {
      userInfo = {
        levelId: info?.prop?.levelId,
        subLevel: info?.prop?.subLevel,
        name: info.nickName,
        avatarFrame: info?.prop?.avatarFrame?.resourceUrl,
        messageBoxFrame: info?.prop?.messageBoxFrame?.resourceUrl
      }
    }

    handleSaveList({
      role: role,
      content: content,
      userId,
      userInfo
    })

    const chatListDom = document.getElementById('chat-list')
    if (chatListDom) {
      const scrollHeight = Math.round(chatListDom.scrollHeight)
      const scrollTop = Math.round(chatListDom.scrollTop)
      const clientHeight = Math.round(chatListDom.clientHeight)

      // If the chat list is at the bottom, scroll to bottom when a new message arrives
      if (scrollHeight <= scrollTop + clientHeight + 1) {
        handleScrollBottom()
      } else {
        chatListDom.addEventListener('scroll', handleOnScroll)
        isShowNewMsgTip.value = true
      }
    }

    if (!chatBox.value.isShowBox) {
      store.updateChatBoxInfo({
        hasNewMsg: true // 是否显示小红点
      })
    }
  }
}
function handleChangeIsStopChat(bool) {
  let tip = ''
  if (bool) {
    tip = {
      role: 'tip',
      msg: 'classroom.chats.chats.teacherOn'
    }
  } else {
    tip = {
      role: 'tip',
      msg: 'classroom.chats.chats.teacherOff'
    }
  }
  handleSaveList(tip)
  handleConditionScrollBottom()
}

// 是否单独禁言
function handleChangeIsMutedByTeacher(bool) {
  let tip = ''
  if (bool) {
    tip = {
      role: 'tip',
      msg: 'classroom.chats.chats.mutedByTeacher'
    }
  } else {
    tip = {
      role: 'tip',
      msg: 'classroom.chats.chats.unmutedByTeacher'
    }
  }

  handleSaveList(tip)
  handleConditionScrollBottom()
}

// 是否只看老师
function handleChangeIsOnlyReadTeacher(bool) {
  let tip = ''
  if (bool) {
    tip = {
      role: 'tip',
      msg: 'classroom.chats.chats.onlyteacher'
    }
  } else {
    tip = {
      role: 'tip',
      msg: 'classroom.chats.chats.all'
    }
  }
  handleSaveList(tip)
  handleConditionScrollBottom()
}

// 同步 chatList和historyList
function handleSaveList(item) {
  chatList.value.push(item)
  store.updateChatBoxInfo({
    historyList: [...chatList.value] // 消息备份
  })
}

// 滚动到底部
function handleScrollBottom() {
  // @log-ignore
  isShowNewMsgTip.value = false
  nextTick(() => {
    const chatListDom = document.getElementById('chat-list')
    if (chatListDom) {
      const scrollHeight = Math.round(chatListDom.scrollHeight)
      const clientHeight = Math.round(chatListDom.clientHeight)
      chatListDom.scrollTop = scrollHeight - clientHeight + 1
      chatListDom.removeEventListener('scroll', handleOnScroll)
    }
  })
}

// 如果在底部就滚动,不在底部不滚动
function handleConditionScrollBottom() {
  // @log-ignore
  const chatListDom = document.getElementById('chat-list')
  if (chatListDom) {
    const scrollHeight = Math.round(chatListDom.scrollHeight)
    const scrollTop = Math.round(chatListDom.scrollTop)
    const clientHeight = Math.round(chatListDom.clientHeight)

    if (scrollHeight <= scrollTop + clientHeight + 1) {
      handleScrollBottom()
    }
  }
}

// 监听滚动事件
function handleOnScroll() {
  // @log-ignore
  const chatListDom = document.getElementById('chat-list')
  if (chatListDom) {
    const scrollHeight = Math.round(chatListDom.scrollHeight)
    const scrollTop = Math.round(chatListDom.scrollTop)
    const clientHeight = Math.round(chatListDom.clientHeight)
    if (scrollHeight <= scrollTop + clientHeight + 1) {
      isShowNewMsgTip.value = false
      chatListDom.removeEventListener('scroll', handleOnScroll)
    }
  }
}

function playBackList(e) {
  try {
    e.content = JSON.parse(e.text)
  } catch (error) {
    console.error(error)
  }

  if (e.sender.startsWith('t')) {
    e.role = 't'
  } else if (e.sender.startsWith('s')) {
    e.role = 's'
  } else if (e.sender.startsWith('f')) {
    e.role = 'f'
  }

  if (!e.content.path) {
    e.content.path = defaultAvatar
  }

  e.userId = e.sender.split('_').pop()

  if (
    (e.content.type && Number(e.content.type) === 130) ||
    (Number(e.content.type) === 139 && e.content.to_uid === baseData.value.stuInfo.id) ||
    (Number(e.content.type) === 139 &&
      e.content.to_uid == baseData.value.teacherInfo.id &&
      e.sender.indexOf(baseData.value.stuInfo.id) > -1)
  ) {
    e.status = SEND_MSG_STATUS.SUCCESS
    e.userInfo = {}
    chatList.value.push(e)
  } else {
    let messageParams = {}
    const commonOptions = {
      name: e.content.from?.username,
      isMe: false,
      msg: e.content.data?.name,
      isNewEmoji: true,
      path: e.content.from?.path || ''
    }

    if (e.content.ircType == 'send_emoji' && e.content.data.type == 1) {
      messageParams = commonOptions
      chatList.value.push({
        role: 's',
        content: messageParams,
        userId: e.userId,
        userInfo: {}
      })
    } else if (
      e.content.ircType == 'animation_emoji' &&
      (e.content.data.type == 2 || e.content.data.type == 3)
    ) {
      messageParams = {
        ...commonOptions,
        lottieUrl: e.content.data.resource?.lottieUrl || '',
        emojiId: e.content.data.resource?.emojiId || 0,
        emojiType: e.content.data.type
      }
      chatList.value.push({
        role: 's',
        content: messageParams,
        userId: e.userId,
        userInfo: {}
      })
    }
  }

  const chatListDom = document.getElementById('chat-list')
  const scrollHeight = Math.round(chatListDom.scrollHeight)
  const scrollTop = Math.round(chatListDom.scrollTop)
  const clientHeight = Math.round(chatListDom.clientHeight)

  if (scrollHeight <= scrollTop + clientHeight + 1) {
    handleScrollBottom()
  } else {
    chatListDom.addEventListener('scroll', handleOnScroll)
    isShowNewMsgTip.value = true
  }
}

function changeSendToValue(item) {
  sendToReactive.id = item.id
  sendToReactive.name = item.name
  sendToReactive.isOpen = false
}

function toggleMemberPopup(e) {
  e.stopPropagation()

  if (
    !chatBox.value.isOpenChat ||
    chatBox.value.isOnlyTeacher ||
    isConnecting.value ||
    chatBox.value.isMuted
  ) {
    sendToReactive.isOpen = false
    return
  }
  sendToReactive.isOpen = !sendToReactive.isOpen
}

function handleDocumentClick(e) {
  if (!sendToReactive.isOpen) return
  const memberListDom = document.getElementById('memberList')
  if (memberListDom && !memberListDom.contains(e.target)) {
    sendToReactive.isOpen = false
  }
}
/**
 * 日志上报
 */
function sendLogger(msg, params, level = 'info') {
  chatLog[level === 'error' ? 'error' : 'info'](msg, params)
}

defineOptions({
  directives: {
    drag: dragDirective
  }
})
</script>
<style lang="scss" scoped>
.chat-box-container {
  width: 305px;
  height: 572px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  cursor: default;
  pointer-events: auto;
  position: absolute;
  bottom: 36px;
  border-radius: 10px;
  .chat-box-head {
    width: 100%;
    height: 40px;
    background: #0f192a;
    text-align: center;
    position: relative;

    .title {
      font-size: 14px;
      font-weight: 600;
      color: #dee2e7;
      line-height: 16px;
      position: absolute;
      top: 12px;
      right: 0;
      bottom: 0;
      left: 0;
      margin: auto;
    }

    .close {
      width: 24px;
      height: 24px;
      overflow: hidden;
      border-radius: 50%;
      cursor: pointer;
      position: absolute;
      top: 8px;
      right: 8px;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .chat-box-content {
    width: 100%;
    flex: 1;
    background: rgba(15, 25, 42, 0.9);
    overflow-y: auto;
    padding: 8px;

    &::-webkit-scrollbar {
      display: none;
    }

    .chat-ul {
      margin: 0;
      padding: 0;

      .left-li,
      .right-li {
        .avatarBox {
          width: 48px;
          height: 48px;
          display: flex;
          justify-content: center;
          align-items: center;
          position: relative;
          img {
            width: 31px;
            height: 31px;
            border-radius: 50%;
            background: #fff;
          }
          .avatar-box {
            width: 48px;
            height: 48px;
            background-size: 48px 48px !important;
            position: absolute;
            left: 0;
            top: 0;
          }
        }
        .msgBox {
          padding-top: 9px;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
        }
        margin-top: 8px;
        position: relative;

        .msg-header {
          display: flex;
          align-items: center;

          .nickname {
            font-size: 12px;
            font-weight: 500;
            color: #ffffff;
            line-height: 14px;
            margin: 5px;
            max-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .msg-private {
            font-size: 10px;
            font-family: 'PingFangSC-Medium', 'PingFang SC';
            font-weight: 500;
            flex-shrink: 0;
          }
        }

        .msg-box {
          max-width: 243px;
          background: #fff;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          color: #172b4d;
          line-height: 14px;
          padding: 8px;
          word-break: break-word; // 一堆字母或者一个很长的英文单词时,强制换行,防止浏览器不知道如何换行
        }
      }

      .left-li {
        display: flex;
        align-items: flex-start;
        .msgBox {
          align-items: flex-start;
          .ml-12 {
            margin-left: 12px;
          }
        }
        .msg-header {
          .msg-private {
            height: 14px;
            color: #10e2e0;
          }
        }

        .msg-box {
          margin-left: 5px;
        }

        .msg-box-teacher {
          background: #3370ff;
          color: #ffffff;
        }

        .msg-box-f {
          background: #02ca8a;
          color: #ffffff;
        }
      }

      .right-li {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: flex-start;

        .msg-header {
          .msg-private {
            color: #10e2e0;
            margin-right: 4px;
          }
        }

        .msg-wrap {
          display: flex;
          margin-right: 5px;
          .msg-error {
            margin-right: 7px;
            width: 20px;
            height: 20px;
            cursor: pointer;
            flex-shrink: 0;

            img {
              width: 100%;
              height: 100%;
            }
          }

          .msg-box-me {
            max-width: 217px;
            background: #ff9f0a;
            color: #fff;
          }
        }
      }

      .tip {
        margin: 30px auto 22px;
        background: rgba(255, 255, 255, 0.12);
        border-radius: 4px;
        padding: 3px 5px;
        font-size: 11px;
        font-weight: 500;
        color: #dee2e7;
        line-height: 13px;
        width: fit-content;
      }
      .enterTipBox {
        text-align: center;
        margin: 10px 0;
        .enterTip {
          font-size: 12px;
          padding: 5px 8px;
          display: inline-flex;
          height: 22px;
          background: rgba(0, 0, 0, 0.4);
          border-radius: 11px;
          align-items: center;
          line-height: 12px;
          color: #fff;
        }
        .tipLevel {
          margin: 0 4px 0 8px;
        }
        .tipName {
          max-width: 80px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .box-back {
        border-image-slice: 40 58 40 58 fill;
        border-style: solid;
        border-image-repeat: stretch;
        border-width: 1px;
        background: transparent !important;
        color: #172b4d !important;
      }
    }
  }

  .chat-box-foot {
    width: 100%;
    min-height: 50px;
    background: #0f192a;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    position: relative;

    // padding: 0px 10px;
    .input-box {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
      position: relative;
      width: 100%;
      padding: 10px;

      // border-top: 1px solid rgba(255, 243, 220, 0.5);
      .text-area {
        border: none;
        caret-color: #3370ffff;
        width: 231px;
        height: 34px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #dee2e7;
        line-height: 16px;
        flex: 1;

        &::-webkit-scrollbar {
          display: none;
        }

        &::placeholder {
          font-size: 14px;
          font-weight: 500;
          color: #a2aab8;
          line-height: 16px;
        }
      }

      :deep(.ant-input) {
        resize: none !important;
        height: 34px;
        padding: 8px 9px;
      }

      .ant-input:hover {
        border: none;
        border-right-width: 0;
      }

      .ant-input:focus {
        border: none;
        box-shadow: none !important;
      }

      .send-btn {
        width: 45px;
        height: 34px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 6px;
        margin-left: 8px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        img {
          width: 12px;
          height: 14px;
        }
      }

      .no-send {
        cursor: no-drop;
      }
    }

    .new-msg {
      position: absolute;
      top: -49px;
      right: 16px;
      display: flex;
      align-items: center;
      cursor: pointer;

      img {
        width: 28px;
        height: 31px;
      }
    }

    .new-meg-tip {
      position: absolute;
      top: -30px;
      width: 40px;
      height: 28px;
      line-height: 18px;
      text-align: center;
      font-size: 12px;
      background: rgba(15, 25, 42, 0.95);
      border-radius: 16px;
      color: #ffaa0a;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;

      .icon-more-bottom {
        display: inline-block;
        width: 10px;
        height: 10px;
        background: url('./imgs/icon-more-bottom.png') no-repeat;
        background-size: cover;
      }
    }

    .too-frequently {
      position: absolute;
      top: -28px;
      height: 23px;
      background: #fff3dc;
      border-radius: 17px;
      font-size: 11px;
      padding: 5px 8px;
      font-weight: 500;
      color: #ffaa0a;
      line-height: 13px;
    }

    .input-close {
      width: 100%;
      box-sizing: border-box;
      height: 54px;
      font-size: 12px;
      font-weight: 600;
      line-height: 14px;
      color: #ffaa0a;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;

      .input-close-content {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.08);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
      }
    }

    .send-to {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      font-size: 12px;
      font-family: 'PingFangSC-Semibold', 'PingFang SC';
      font-weight: 500;
      color: rgba(255, 243, 220, 0.5);
      line-height: 17px;
      padding: 10px;
      padding-bottom: 0px;
      width: 100%;

      .send-to-click {
        cursor: pointer;

        &.un-send-to-click {
          cursor: not-allowed;
        }
      }

      .send-to-name {
        margin: 0 10px;
        font-size: 12px;
        font-family: 'PingFangSC-Semibold', 'PingFang SC';
        font-weight: 500;
        color: #10e2e0;
        line-height: 17px;

        &.un-send-to-name {
          color: #a2aab8;
        }
      }

      .send-to-icon {
        width: 8px;
        height: 8px;
        display: inline-block;
        background-image: url('./imgs/open_icon.png');
        background-repeat: no-repeat;
        background-size: 100%;
      }
    }

    .memberList {
      position: absolute;
      width: 143px;
      height: 68px;
      background: #0f192a;
      border-radius: 6px;
      top: -78px;
      left: 10px;
      overflow: hidden;
      padding: 0px 14px;

      .member-name {
        font-size: 12px;
        font-family: 'SFProRounded-Medium', 'SFProRounded';
        font-weight: 500;
        color: rgba(255, 255, 255, 0.5);
        line-height: 14px;

        &.member-changed {
          font-weight: 600;
          color: #10e2e0;
        }
      }

      .member-li {
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid rgba(255, 243, 220, 0.5);
        padding: 10px 0px;

        &:last-child {
          border-bottom: none;
        }
      }

      .member-icon {
        width: 10px;
        height: 10px;
        background-image: url('./imgs/member_changed_icon_green.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }
    }
  }
}

.playBack {
  margin-top: 2px;
  background: #1a1a1a;
  width: 100%;
  height: 100%;

  .chat-box-content {
    background: #1a1a1a;

    &::-webkit-scrollbar {
      width: 5px;
    }

    .chat-ul {
      margin: 0;
      padding: 0;

      .left-li,
      .right-li {
        .msg-header {
          .nickname {
            color: #ffffff;
          }

          // .msg-private {
          //   color: #10e2e0;
          // }
        }

        .msg-box {
          max-width: none;
        }
      }

      .left-li {
        .msg-box {
          color: #172b4d;
          background: #dee2e7;
        }

        .msg-box-teacher {
          background: #3370ff;
          color: #ffffff;
        }

        .msg-box-f {
          background: #02ca8a;
          color: #ffffff;
        }

        .msg-private {
          color: #10e2e0;
        }
      }

      .right-li {
        .msg-header {
          .msg-private {
            color: #10e2e0;
          }
        }

        .msg-wrap {
          .msg-box-me {
            background: #ffaa0a;
            color: #172b4d;
          }
        }
      }
    }
  }

  .chat-box-foot {
    background: #1a1a1a;
    min-height: 0px;
    height: 0px;

    .new-meg-tip {
      top: -28px;
    }
  }
}
.chat-box-show {
  position: absolute;
  bottom: 16px;
  z-index: 999 !important;
}
</style>
