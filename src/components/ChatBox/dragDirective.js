// dragDirective.js
import { nextTick } from 'vue'

const isMobile = () => {
  return /Android|webOS|iPhone|iPod|BlackBerry|symbianos|windows phone/i.test(navigator.userAgent)
}

const pointerFuncName = {
  start: 'pointerdown',
  move: 'pointermove',
  end: 'pointerup'
}

const mobileFuncName = {
  start: 'touchstart',
  move: 'touchmove',
  end: 'touchend'
}

const pcFuncName = {
  start: 'mousedown',
  move: 'mousemove',
  end: 'mouseup'
}

const funcName = window.PointerEvent ? pointerFuncName : isMobile() ? mobileFuncName : pcFuncName

export default {
  // Vue3中bind变为mounted
  mounted(el, binding) {
    nextTick(() => {
      const { dragWrapSelector, dragSelector, draggable } = binding.value || {}
      if (draggable === false) return

      // 可拖拽默认是绑定在元素本身上，如果传了参数就绑定在参数上
      let dragNode = dragSelector ? el.querySelector(dragSelector) : el

      let dragWrapNode = document.querySelector(dragWrapSelector || 'body')
      let {
        top: dragWrapTop,
        left: dragWrapLeft,
        width: dragWrapWidth,
        height: dragWrapHeight
      } = dragWrapNode.getBoundingClientRect()

      dragNode.style.cursor = 'move'

      // 窗口resize时重新计算位置
      let initWindowWidth = window.innerWidth
      let initWindowHeight = window.innerHeight

      const windowResizeFunc = () => {
        if (el && el.style.display === 'none') return
        const curWindowWidth = window.innerWidth
        const curWindowHeight = window.innerHeight
        let { left: bindNodeLeft, top: bindNodeTop } = el.getBoundingClientRect()
        el.style.position = 'fixed'
        el.style.left = bindNodeLeft + 'px'
        el.style.top = bindNodeTop + 'px'
        el.style.transform = 'none'

        if (el.offsetLeft && el.offsetTop && initWindowWidth) {
          el.style.left = el.offsetLeft / (initWindowWidth / curWindowWidth) + 'px'
          el.style.top = el.offsetTop / (initWindowHeight / curWindowHeight) + 'px'
        }

        initWindowWidth = curWindowWidth
        initWindowHeight = curWindowHeight

        const { top, left, width, height } = dragWrapNode.getBoundingClientRect()
        dragWrapTop = top
        dragWrapLeft = left
        dragWrapWidth = width
        dragWrapHeight = height
      }

      // 使用ResizeObserver监听容器大小变化
      const ResizeObserver =
        window.ResizeObserver || window.WebKitMutationObserver || window.MozMutationObserver

      if (ResizeObserver) {
        const observer = new ResizeObserver(windowResizeFunc)
        observer.observe(dragWrapNode)

        // 保存observer以便在unmounted时清理
        el._dragResizeObserver = observer
      }

      const handleDragStart = e => {
        let {
          left: bindNodeLeft,
          top: bindNodeTop,
          width: bindNodeWidth,
          height: bindNodeHeight
        } = el.getBoundingClientRect()

        el.style.position = 'fixed'
        el.style.left = bindNodeLeft + 'px'
        el.style.top = bindNodeTop + 'px'
        el.style.transform = 'none'

        // 鼠标相对元素的位置
        let cur = e.touches ? e.touches[0] : e
        let disX = cur.clientX - el.offsetLeft
        let disY = cur.clientY - el.offsetTop

        const preventFunc = e => {
          e.preventDefault()
          e.stopPropagation()
        }

        const moveFunc = e => {
          let cur = e.touches ? e.touches[0] : e
          let left = cur.clientX - disX
          let top = cur.clientY - disY
          let maxLeft =
            left > dragWrapWidth - bindNodeWidth + dragWrapLeft
              ? dragWrapWidth - bindNodeWidth + dragWrapLeft
              : left
          let maxTop =
            top > dragWrapHeight - bindNodeHeight + dragWrapTop
              ? dragWrapHeight - bindNodeHeight + dragWrapTop
              : top
          el.style.left = (left < dragWrapLeft ? dragWrapLeft : maxLeft) + 'px'
          el.style.top = (top < dragWrapTop ? dragWrapTop : maxTop) + 'px'
          el.style.right = 'auto'
          el.style.bottom = 'auto'
          preventFunc(e)
        }

        const endFunc = () => {
          window.removeEventListener(funcName.move, moveFunc, { passive: false })
        }

        window.addEventListener(funcName.move, moveFunc, { passive: false })
        window.addEventListener(funcName.end, endFunc, { once: true })
      }

      dragNode.addEventListener(funcName.start, handleDragStart, false)

      // 保存事件处理函数以便在unmounted时清理
      el._dragStartHandler = handleDragStart
      el._dragNode = dragNode
    })
  },

  // Vue3中新增的更新钩子
  updated(el, binding) {
    // 如果draggable状态改变，需要重新设置
    if (binding.value?.draggable !== binding.oldValue?.draggable) {
      // 先移除旧的事件监听
      if (el._dragNode && el._dragStartHandler) {
        el._dragNode.removeEventListener(funcName.start, el._dragStartHandler, false)
      }

      // 如果现在是可拖动的，重新挂载
      if (binding.value?.draggable !== false) {
        const dragSelector = binding.value?.dragSelector
        el._dragNode = dragSelector ? el.querySelector(dragSelector) : el
        el._dragNode.addEventListener(funcName.start, el._dragStartHandler, false)
      }
    }
  },

  // Vue3中unbind变为unmounted
  unmounted(el) {
    // 清理事件监听
    if (el._dragNode && el._dragStartHandler) {
      el._dragNode.removeEventListener(funcName.start, el._dragStartHandler, false)
    }

    // 清理ResizeObserver
    if (el._dragResizeObserver) {
      el._dragResizeObserver.disconnect()
      delete el._dragResizeObserver
    }

    // 清理引用
    delete el._dragStartHandler
    delete el._dragNode
  }
}
