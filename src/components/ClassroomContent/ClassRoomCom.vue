<template>
  <div class="class-room">
    <!-- <div class="class-header"> -->
    <Header class="class-header"></Header>
    <!-- </div> -->
    <div class="class-body">
      <div class="class-container">
        <!-- 视频回显区域 -->
        <VideoGroup></VideoGroup>
        <!-- 主区域内容， 16/9的尺寸 -->
        <div class="mainContaine" ref="resizableDiv">
          <Interactions class="class-interaction-layer" isCourseware4b3="false" />
          <!-- 根据课件比例动态计算尺寸 -->
          <div ref="coursewareContainer" class="aspect-ratio-container">
            <Courseware id="class-board-layer"></Courseware>
            <WhiteBoard id="class-whiteboard-layer" class="classWhiteBoard"></WhiteBoard>
            <OnStageLayer></OnStageLayer>
            <div v-if="teacherOffline" class="wait-teacher">
              <div class="wait-dialog">
                <div class="notice-content">
                  {{ $t('classroom.largeClass.coursewareBoard.classSoonNotice') }}
                </div>
              </div>
            </div>
          </div>
          <!-- 容器悬浮层  16/9-->
          <FloatLayer class="class-float-layer"></FloatLayer>
        </div>
      </div>
      <ContinuousCorrect />
      <FullFloatLayer class="class-full-float-layer"></FullFloatLayer>
    </div>
  </div>
</template>
<script setup>
import { onMounted, onBeforeUnmount, computed } from 'vue'
import Header from '@/components/ClassHead/Header/index.vue'
import VideoGroup from '@/components/VideoGroup/index.vue'
import Courseware from '@/components/Courseware/index.vue'
import { ref } from 'vue'
import { useClassData } from '@/stores/classroom'
import {
  quitroomPushStudentDeviceInfoHandle,
  enterroomPushStudentDeviceInfoHandle
} from '@/hooks/useReportDeviceInfo'
import Interactions from '@/interactions/index.vue'
import WhiteBoard from '@/components/WhiteBoard/index.vue'
import FloatLayer from '@/components/FloatLayer/index.vue'
import OnStageLayer from '@/components/LayerOnStage/index.vue'
import FullFloatLayer from '@/components/LayerFullFloat/index.vue'

import { coursewareLog } from '@/utils/web-log/HWLogDefine'
import ContinuousCorrect from '@/interactions/ContinuousCorrect/index.vue'

import logManager from '@/utils/web-log/LogUploadManager.js'

// 1. 创建一个 ref 以便引用 DOM 元素
const coursewareContainer = ref(null)
// 定义一个响应式引用来存储目标元素
const resizableDiv = ref(null)
const title = ref('')
const store = useClassData()
const init = async () => {
  const baseData = store.baseData
  console.log(
    `进入小班课直播页面 planId: ${baseData?.planInfo?.id}, stuId: ${baseData?.stuInfo?.id}`
  )

  title.value = (baseData.planInfo && baseData.planInfo.name) || ''

  initCoursewareAndBoard()
  enterroomPushStudentDeviceInfoHandle(store.baseData)
  logManager.uploadWithTenDays()
}
onMounted(() => {
  init()
})
const teacherOffline = computed(() => {
  return !store.teacherInfo.inClass
})

// 设置 ResizeObserver 并在组件挂载时开始观察
function initCoursewareAndBoard() {
  setTimeout(() => {
    // 2. 确保 DOM 元素已经被渲染
    resizeCoursewareAndBorad()
    if (resizableDiv.value) {
      const resizeObserver = new ResizeObserver(() => {
        resizeCoursewareAndBorad()
      })
      resizeObserver.observe(resizableDiv.value)
      // 存储到实例上以便在卸载时销毁观察器
      resizableDiv.value._resizeObserver = resizeObserver
    }
  }, 500)
}

function resizeCoursewareAndBorad() {
  if (coursewareContainer.value) {
    const coursewareInfo = store.coursewareInfo
    const resizableDiv = document.querySelector('.mainContaine')
    const height = resizableDiv.clientHeight
    coursewareContainer.value.style.height = `${height}px`
    const rate = coursewareInfo.rate === 2 ? 16 / 9 : 4 / 3
    coursewareContainer.value.style.width = `${height * rate}px`
    coursewareLog.info('课件rate', coursewareInfo.rate)

    // 同时限制 VideoGroup 的最大宽度为 mainContainer 的宽度
    const videoGroup = document.querySelector('.video-list')
    if (videoGroup && resizableDiv) {
      const mainContainerWidth = resizableDiv.clientWidth
      videoGroup.style.width = `${mainContainerWidth}px`
    }
  }
}

// 确保在组件卸载前断开 ResizeObserver
onBeforeUnmount(() => {
  if (resizableDiv.value && resizableDiv.value._resizeObserver) {
    resizableDiv.value._resizeObserver.disconnect()
  }
  quitroomPushStudentDeviceInfoHandle(store.baseData)
})
</script>
<style lang="scss" scoped>
.class-room {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .class-header {
    height: 44px;
    background: #1a1a1a;
    // flex-shrink: 0;
    width: 100%;
    display: flex;
    position: relative;
    z-index: 1000;
  }
  .class-body {
    flex-shrink: 0;
    height: calc(100vh - 44px);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .class-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: flex-start;
      gap: 2px;
      /* 限制容器宽度，防止被子元素撑开 */
      max-width: 100%;
      overflow: hidden;
      .mainContaine {
        aspect-ratio: 16/9;
        height: 85%;
        position: relative;
        //background-image: url('@/assets/images/live/classroom-bg.png');
        //background-repeat: repeat;
        .class-float-layer {
          position: absolute;
          width: 100%;
          height: 100%;
          z-index: 101;
          pointer-events: none;
          top: 0;
        }
      }
    }
    .class-full-float-layer {
      width: 100%;
      height: calc(100vh - 44px);
      position: absolute;
      z-index: 2000;
      pointer-events: none;
    }
  }
}

#class-board-layer {
  position: absolute;
}

#tools-chatbox {
  margin: 30px 20px;
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 30px;
  cursor: pointer;
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 100;
}
.class-interaction-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  /* 如果改小层级，需要同步修改激励弹窗ContinuousCorrect层级 */
  z-index: 99;
  pointer-events: none;
}
.aspect-ratio-container {
  top: 0px;
  bottom: 0px;
  position: relative;
  width: 100%;
  height: 100%;
  // background-color: rgb(22, 37, 207);
  .classWhiteBoard {
    // background-color: #a80d0d !important;
    // z-index: 10;
    position: absolute; /* 确保它相对于父容器定位 */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
  .wait-teacher {
    position: absolute;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
    z-index: 99;
    background: url('@/assets/images/wait-bg.jpg');
    background-size: cover;

    .wait-dialog {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 310px;
      height: 205px;
      padding: 30px 30px 40px 30px;
      position: absolute;
      top: 50%;
      left: 50%;
      margin-top: -150px;
      margin-left: -155px;
      background: url('@/assets/images/wait-dialog-bg.png');
      background-size: cover;
    }

    .notice-content {
      font-size: 18px;
      color: #ff850a;
    }
  }
}
</style>
