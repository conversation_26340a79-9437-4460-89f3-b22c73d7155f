<template>
  <div class="setup-container">
    <!-- 添加背景几何图形 -->
    <div class="background-shapes">
      <div
        v-for="shape in shapes"
        :key="shape.id"
        :class="['shape', shape.type]"
        :style="getShapeStyle(shape)"
      ></div>
    </div>

    <div class="main-content">
      <!-- 左侧：用户信息和课程信息 -->
      <div class="left-panel">
        <div class="avatar">
          <img v-if="lessonInfo.avatar" :src="lessonInfo.avatar" alt="用户头像" />
        </div>
        <h2 class="user-name">{{ lessonInfo.name }}</h2>
        <div class="divider"></div>
        <h3 class="class-title">
          {{ lessonInfo.lessonName }}
        </h3>
        <p v-if="formattedLessonTime" class="class-time">
          <img src="@/assets/icon/time.png" alt="时间" class="time-icon" />
          {{ formattedLessonTime }}
        </p>
      </div>

      <!-- 右侧：设备检查 -->
      <div class="right-panel">
        <!-- 将标题和提示文字移到设备检测部分内部 -->
        <div class="right-card">
          <h1 class="title">{{ $t('today.ready_for_class_15_mye') }}</h1>
          <p class="global-instruction">
            {{ $t('today.enable_camera_microphone_class_15_hkq') }}
          </p>

          <div class="device-check-content">
            <div class="tabs-vertical">
              <button
                class="tab-button"
                :class="{ active: activeTab === 'camera' }"
                @click="toggleDeviceType('camera')"
              >
                <!-- 摄像头检查 -->
                {{ $t('today.camera_15_ov3') }}
              </button>
              <button
                class="tab-button"
                :class="{ active: activeTab === 'audio' }"
                @click="toggleDeviceType('audio')"
              >
                <!-- 音频检查 -->
                {{ $t('today.audio_15_lbj') }}
              </button>
            </div>

            <div class="tab-content">
              <!-- Camera Tab -->
              <div v-show="activeTab === 'camera'" class="camera-content">
                <div class="device-select-container">
                  <label> {{ $t('today.device_15_stl') }} </label>
                  <CustomSelect
                    v-model="selectedVideoDevice"
                    :options="videoDevices.map(d => ({ label: d.label, value: d.deviceId }))"
                    :disabled="!hasVideoDevices"
                  />
                </div>
                <div class="device-preview-container">
                  <label>{{ $t('today.preview_15_o2m') }}</label>
                  <div class="camera-preview">
                    <div v-if="cameraError || !hasVideoDevices" class="camera-error">
                      <div class="error-icon">
                        <!-- /Users/<USER>/Code/work/student-web/src/assets/icon/no-video.png -->
                        <img src="@/assets/icon/no-video.png" alt="摄像头错误图标" />
                      </div>
                      <!-- <p>{{ !hasVideoDevices ? '未检测到摄像头设备' : '摄像头画面未显示' }}</p>
                      <p>{{ !hasVideoDevices ? '请连接摄像头后刷新页面' : '请检查设备并重试' }}</p> -->
                    </div>
                    <video
                      v-else
                      ref="videoElement"
                      autoplay
                      muted
                      disablePictureInPicture
                      playsinline
                      webkit-playsinline
                    ></video>
                  </div>
                </div>
              </div>

              <!-- Audio Tab -->
              <div v-if="activeTab === 'audio'" class="audio-content">
                <div class="device-select-container">
                  <label>{{ $t('today.microphone_15_prn') }}</label>
                  <CustomSelect
                    v-model="selectedAudioDevice"
                    :options="audioDevices.map(d => ({ label: d.label, value: d.deviceId }))"
                    :disabled="!hasAudioDevices"
                  />
                  <!-- 音量变化指示条 -->
                  <div class="audio-meter-container" :class="{ disabled: !hasAudioDevices }">
                    <!-- 添加当前音量数值显示 -->
                    <div class="volume-bar">
                      <!-- 使用v-for创建10个音量块 -->
                      <div
                        v-for="i in 10"
                        :key="i"
                        class="volume-segment"
                        :class="{
                          active: hasAudioDevices && audioLevel >= i * 10 - 5,
                          low: i <= 3,
                          medium: i > 3 && i <= 7,
                          high: i > 7
                        }"
                      ></div>
                    </div>
                  </div>
                </div>

                <div class="device-select-container">
                  <label>{{ $t('today.speaker_15_oid') }}</label>
                  <div class="speaker-list">
                    <CustomSelect
                      v-model="selectedSpeakerDevice"
                      :options="speakerDevices.map(d => ({ label: d.label, value: d.deviceId }))"
                      :disabled="!hasSpeakerDevices"
                      class="speakers-device-select"
                    />
                    <button
                      class="test-button"
                      @click="isTestingAudio ? stopAudioTest() : testSpeaker()"
                      :class="{ testing: isTestingAudio, disabled: !hasSpeakerDevices }"
                      :disabled="!hasSpeakerDevices"
                    >
                      <div v-show="isTestingAudio" class="audio-playing-animation">
                        <div class="bar bar1"></div>
                        <div class="bar bar2"></div>
                        <div class="bar bar3"></div>
                        <div class="bar bar4"></div>
                      </div>
                      <span v-show="!isTestingAudio"> {{ $t('today.detect_15_to4') }}</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 保留页面底部的"进入课堂"按钮 -->
        <button class="enter-button" @click="enterClass">
          {{ $t('today.enter_class_15_ls6') }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, watch, computed } from 'vue'
import { throttle } from '@/utils/util'
import testAudioSrc from '@/assets/mp3/test.mp3'
import CustomSelect from '@/components/CustomSelect.vue'
import { getLessonBasicInfo } from '@/api/classroom/pre'
import { useRoute } from 'vue-router'
import { useClassData } from '@/stores/classroom'
import { useBaseData } from '@/stores/baseData'
import { useRtcService } from '@/hooks/useRtcService'
import { RTCLog } from '@/utils/web-log/HWLogDefine'

const route = useRoute()
const activeTab = ref('camera')
const videoDevices = ref([])
const audioDevices = ref([])
const speakerDevices = ref([])
const selectedVideoDevice = ref('')
const selectedAudioDevice = ref('')
const selectedSpeakerDevice = ref('')
const audioLevel = ref(0)
const rawAudioLevel = ref(0) // 保存原始音频值
const isSpeaking = ref(false) // 判断是否正在说话
const cameraError = ref(false)
const shapes = ref([])
const animationId = ref(null)
const videoElement = ref(null)
const isTestingAudio = ref(false) // 控制音频测试状态
let audioElement = null // 存储当前播放的音频元素
let audioTrack = null // 存储当前Agora音频轨道

// 设备可用性状态
const hasVideoDevices = computed(() => videoDevices.value.length > 0)
const hasAudioDevices = computed(() => audioDevices.value.length > 0)
const hasSpeakerDevices = computed(() => speakerDevices.value.length > 0)

const lessonInfo = ref({
  avatar: '',
  name: '',
  lessonName: '',
  orderNum: '',
  startTimestamp: 0,
  endTimestamp: 0
})
const { getRtcService } = useRtcService()

const formattedLessonTime = computed(() => {
  if (!lessonInfo.value.startTimestamp || !lessonInfo.value.endTimestamp) return ''
  try {
    const start = new Date(lessonInfo.value.startTimestamp)
    const end = new Date(lessonInfo.value.endTimestamp)
    // e.g. Fri, May 1:00 PM - 2:30 PM
    // day: 'numeric'
    const options = { weekday: 'short', month: 'short', day: 'numeric' }
    const startDateStr = start.toLocaleDateString(baseStore.locale, options)
    const startTimeStr = start.toLocaleTimeString(baseStore.locale, {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
    const endTimeStr = end.toLocaleTimeString(baseStore.locale, {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
    return `${startDateStr} ${startTimeStr} - ${endTimeStr}`
  } catch {
    return ''
  }
})

const store = useClassData()
const baseStore = useBaseData()

// 生命周期钩子
onMounted(async () => {
  initializeDevices()
  initShapes()
  startAnimation()

  // 获取课程基础信息
  try {
    const params = {
      header: {
        schoolCode: baseStore.schoolCode
      },
      data: {
        planId: Number(route.query.planId) || 1,
        classId: Number(route.query.classId) || 1,
        lessonType: route.query.lessonType
      }
    }
    const res = await getLessonBasicInfo(params)
    if (res.code === 0 && res.data) {
      lessonInfo.value = {
        avatar: res.data.teachers[0]?.avatar || '',
        name: res.data.teachers[0]?.name || '',
        lessonName: res.data.lessonName || '',
        orderNum: res.data.orderNum,
        startTimestamp: res.data.startTimestamp || 0,
        endTimestamp: res.data.endTimestamp || 0
      }
    }
  } catch (e) {
    console.error('获取课程基础信息失败:', e)
  }
})
const toggleDeviceType = async deviceType => {
  activeTab.value = deviceType
  // 停止当前的音量检测循环（如果存在）
  if (window.audioVolumeInterval) {
    console.log('停止当前的音量检测循环')
    clearInterval(window.audioVolumeInterval)
    window.audioVolumeInterval = null
  }
  if (isTestingAudio.value) {
    stopAudioTest()
  }
  if (deviceType === 'audio') {
    // 切换到音频检查时初始化音频分析
    setupAudioAnalysis()
  } else {
    // await initializeCamera()
  }
}
onBeforeUnmount(() => {
  const RtcService = getRtcService()
  RtcService.destroyLocalVideo()
  stopAudioTest()

  // 清理动画
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
    animationId.value = null
  }

  // 清理音频相关资源
  if (window.audioVolumeInterval) {
    clearInterval(window.audioVolumeInterval)
    window.audioVolumeInterval = null
  }

  // 清理 Web Audio API 相关资源
  if (window.audioSource) {
    window.audioSource.disconnect()
    window.audioSource = null
  }

  if (window.audioAnalyser) {
    window.audioAnalyser.disconnect()
    window.audioAnalyser = null
  }

  if (window.audioContext) {
    window.audioContext.close()
    window.audioContext = null
  }

  // 清理麦克风流
  if (window.microphoneStream) {
    window.microphoneStream.getTracks().forEach(track => track.stop())
    window.microphoneStream = null
  }

  // 清理视频元素
  if (videoElement.value) {
    videoElement.value.srcObject = null
    videoElement.value = null
  }
})

// 初始化设备
const initializeDevices = async () => {
  try {
    await getDevices()
    await initializeCamera()
    await initializeMicrophone()
  } catch (error) {
    console.error('Error initializing devices:', error)
  }
}
const initializeMicrophone = async () => {
  try {
    // 如果没有麦克风设备，直接返回
    if (!hasAudioDevices.value) {
      console.warn('没有可用的麦克风设备，跳过音频分析初始化')
      return
    }
    const rtcService = getRtcService()
    // 创建本地音频轨道
    await rtcService.initAudioTracks(selectedAudioDevice.value)
  } catch (error) {
    console.error('Error initializing microphone:', error)
  }
}

// 获取设备列表
const getDevices = async () => {
  const RtcService = getRtcService()
  audioDevices.value = await RtcService.getAudioRecordingDevices()
  videoDevices.value = await RtcService.getVideoDevices()
  speakerDevices.value = await RtcService.getSpeakerList()

  // 读取store中的设备id
  let videoId = store.deviceInfo.videoDeviceId
  let audioId = store.deviceInfo.audioDeviceId
  let speakerId = store.deviceInfo.speakerDeviceId

  // 校验设备是否存在
  if (!videoDevices.value.find(d => d.deviceId === videoId)) {
    videoId = videoDevices.value[0]?.deviceId || ''
    store.changeDeviceInfo({ videoDeviceId: videoId })
  }
  if (!audioDevices.value.find(d => d.deviceId === audioId)) {
    audioId = audioDevices.value[0]?.deviceId || ''
    store.changeDeviceInfo({ audioDeviceId: audioId })
  }
  if (!speakerDevices.value.find(d => d.deviceId === speakerId)) {
    speakerId = speakerDevices.value[0]?.deviceId || ''
    store.changeDeviceInfo({ speakerDeviceId: speakerId })
  }

  selectedVideoDevice.value = videoId
  selectedAudioDevice.value = audioId
  selectedSpeakerDevice.value = speakerId

  console.log('设备检测结果:', {
    摄像头设备数: videoDevices.value.length,
    麦克风设备数: audioDevices.value.length,
    扬声器设备数: speakerDevices.value.length
  })

  // 如果没有任何设备，显示提示
  if (!hasVideoDevices.value && !hasAudioDevices.value) {
    setTimeout(() => {
      showNoDeviceAlert()
    }, 500)
  }
}

// 显示无设备提示
const showNoDeviceAlert = () => {
  // alert('未检测到任何音视频设备，请确保您的设备已正确连接，然后刷新页面重试。这可能影响您的课堂体验。');
}

// 初始化摄像头
const initializeCamera = async () => {
  try {
    // 如果没有摄像头设备，直接返回
    if (!hasVideoDevices.value) {
      RTCLog.warning('没有可用的摄像头设备，跳过摄像头初始化')
      cameraError.value = true
      return
    }
    // 创建新摄像头轨道
    const RtcService = getRtcService()
    await RtcService.initVideoTracks(selectedVideoDevice.value)

    // 在视频元素中播放轨道
    if (videoElement.value) {
      RtcService.createLocalVideo(videoElement.value)
    }

    cameraError.value = false
  } catch (error) {
    console.error('Error accessing camera:', error)
    cameraError.value = true
  }
}

// 设置音频分析 - 使用 Agora 音频轨道的 getVolumeLevel 方法
const setupAudioAnalysis = async () => {
  try {
    console.log('开始初始化 Agora 麦克风分析')

    // 获取 RTC 服务
    const { getRtcService } = useRtcService()
    const rtcService = getRtcService()

    if (!rtcService) {
      console.error('RTC Service 或 client 不可用')
      return
    }

    console.log('Agora 音频轨道创建成功')

    // 使用节流函数来创建更新UI的函数
    const updateVolumeUI = throttle(volume => {
      // 平滑处理，使音量变化更平滑
      rawAudioLevel.value = rawAudioLevel.value * 0.7 + volume * 0.3
      audioLevel.value = Math.min(100, rawAudioLevel.value * 100)

      // 判断是否正在说话
      isSpeaking.value = rawAudioLevel.value > 0.15

      // 调试输出
      if (Math.abs(volume - rawAudioLevel.value) > 0.05) {
        console.log('音量更新:', (volume * 100).toFixed(1), '%')
      }
    }, 100)

    // 开始音量监测循环
    console.log('开始音量监测循环')
    window.audioVolumeInterval = setInterval(() => {
      // 获取音量级别，范围是 0-1
      const volumeLevel = rtcService.getVolumeLevel()

      // 使用节流函数更新UI
      updateVolumeUI(volumeLevel)
    }, 100) // 每100ms检测一次
  } catch (error) {
    console.error('Agora 麦克风初始化失败:', error)
    rawAudioLevel.value = 0
    audioLevel.value = 0
    isSpeaking.value = false
  }
}

// 添加设备切换监听
watch(selectedVideoDevice, newVal => {
  store.changeDeviceInfo({ videoDeviceId: newVal })
  const RtcService = getRtcService()
  RtcService.switchVideoDevice(newVal)
})

watch(selectedAudioDevice, newVal => {
  store.changeDeviceInfo({ audioDeviceId: newVal })
  const RtcService = getRtcService()
  RtcService.switchAudioDevice(newVal)
})

watch(selectedSpeakerDevice, newVal => {
  store.changeDeviceInfo({ speakerDeviceId: newVal })
  if (isTestingAudio.value) {
    stopAudioTest()
  }
})

// 测试扬声器
const testSpeaker = async () => {
  try {
    // 如果没有扬声器设备，直接返回
    if (!hasSpeakerDevices.value) {
      console.warn('没有可用的扬声器设备，无法进行测试')
      alert('未检测到扬声器设备，请连接扬声器后重试。')
      return
    }

    // 如果已经在测试中，先停止
    if (isTestingAudio.value) {
      await stopAudioTest()
      return
    }

    console.log('开始测试扬声器...')
    isTestingAudio.value = true

    // 优先使用原生音频API
    audioElement = new Audio(testAudioSrc)

    // 设置音频结束时的处理
    audioElement.onended = () => {
      console.log('音频播放完成')
      stopAudioTest()
    }

    // 错误处理
    audioElement.onerror = e => {
      console.error('音频播放错误:', e)
      stopAudioTest()
    }

    // 尝试使用原生 setSinkId 方法设置音频输出设备（如果浏览器支持）
    if (selectedSpeakerDevice.value && audioElement.setSinkId) {
      try {
        await audioElement.setSinkId(selectedSpeakerDevice.value)
        console.log('成功设置音频输出设备:', selectedSpeakerDevice.value)
      } catch (e) {
        console.warn('设置音频输出设备失败，使用默认设备:', e)
      }
    } else {
      console.log('浏览器不支持选择音频输出设备，使用系统默认设备')
    }

    // 播放音频
    const playPromise = audioElement.play()

    // 处理自动播放限制
    if (playPromise !== undefined) {
      playPromise.catch(error => {
        console.warn('自动播放受限，需要用户交互:', error)

        // 显示提示，告诉用户需要点击页面
        alert('请点击页面以允许音频播放')

        // 添加一次性点击事件监听器，用户点击后播放音频
        const playOnClick = () => {
          audioElement.play().catch(e => {
            console.error('播放失败:', e)
            stopAudioTest()
          })
          document.removeEventListener('click', playOnClick)
        }
        document.addEventListener('click', playOnClick)

        // 如果无法自动播放，恢复按钮状态
        stopAudioTest()
      })
    }

    return
  } catch (err) {
    console.warn('原生音频API测试失败，尝试使用Agora API:', err)
    const RtcService = getRtcService()
    // 原生API失败，尝试使用Agora API
    try {
      // 使用项目中的测试音频文件
      audioTrack = await RtcService.createBufferSourceAudioTrack({
        source: testAudioSrc
      })

      // 仅在浏览器支持时设置播放设备
      if (selectedSpeakerDevice.value) {
        try {
          // 先检查浏览器是否支持
          await RtcService.checkAudioOutputDeviceSupport()
          await audioTrack.setPlaybackDevice(selectedSpeakerDevice.value)
          console.log('已设置Agora音频输出设备:', selectedSpeakerDevice.value)
        } catch (e) {
          console.warn('Agora设置音频输出设备失败，使用默认设备:', e)
          // 继续播放，使用默认设备
        }
      }

      // 播放结束事件
      audioTrack.on('ended', () => {
        console.log('Agora音频播放结束')
        stopAudioTest()
      })

      // 无论如何播放音频
      await audioTrack.play()
      console.log('正在使用Agora API播放音频测试')
    } catch (agoraErr) {
      console.error('Agora API也无法播放音频:', agoraErr)
      alert('您的浏览器不支持音频测试功能，请检查系统音频设置')
      stopAudioTest()
    }
  }
}

// 停止音频测试
const stopAudioTest = () => {
  console.log('停止音频测试')

  // 停止原生音频播放
  if (audioElement) {
    audioElement.pause()
    audioElement.currentTime = 0
    audioElement.onended = null
    audioElement.onerror = null
    audioElement = null
  }

  // 停止Agora音频轨道
  if (audioTrack) {
    try {
      audioTrack.stop()
      audioTrack.close()
    } catch (e) {
      console.warn('关闭Agora音频轨道时出错:', e)
    }
    audioTrack = null
  }

  // 重置状态
  isTestingAudio.value = false
}
const emit = defineEmits(['enter-classroom'])
const enterClass = async () => {
  emit('enter-classroom')
}

// 初始化几何图形
const initShapes = () => {
  // 清空已有的图形
  shapes.value = []

  // 创建8个图形: 圆形、三角形、正方形、长方形各两个
  const shapeTypes = [
    'circle',
    'circle',
    'triangle',
    'triangle',
    'square',
    'square',
    'rectangle',
    'rectangle'
  ]

  // 为每个形状添加随机颜色、大小和位置
  shapeTypes.forEach((type, index) => {
    // 随机大小 (范围根据形状类型调整)
    let size
    if (type === 'circle' || type === 'square') {
      // 圆形和正方形使用单一尺寸
      size = Math.floor(Math.random() * 80) + 40 // 40-120px
    } else if (type === 'rectangle') {
      // 长方形使用宽高
      size = {
        width: Math.floor(Math.random() * 100) + 80, // 80-180px
        height: Math.floor(Math.random() * 60) + 40 // 40-100px
      }
    } else if (type === 'triangle') {
      // 三角形使用边长
      size = Math.floor(Math.random() * 80) + 60 // 60-140px
    }

    // 窗口尺寸计算
    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight

    // 形状位置 (确保完全在视窗内)
    const actualSize = type === 'rectangle' ? Math.max(size.width, size.height) : size
    const maxX = windowWidth - actualSize
    const maxY = windowHeight - actualSize

    const left = Math.floor(Math.random() * maxX)
    const top = Math.floor(Math.random() * maxY)

    // 随机运动方向和速度
    const speedFactor = Math.random() * 0.5 + 0.5 // 0.5-1.0的速度系数
    const speed = {
      x: (Math.random() > 0.5 ? 1 : -1) * (Math.random() * 2 + 1) * speedFactor, // 随机方向，1-3px每帧
      y: (Math.random() > 0.5 ? 1 : -1) * (Math.random() * 2 + 1) * speedFactor
    }

    // 创建形状对象
    shapes.value.push({
      id: index,
      type,
      size,
      left,
      top,
      speed,
      // 随机旋转角度和旋转速度 (只应用于三角形)
      rotation: type === 'triangle' ? Math.random() * 360 : 0,
      rotationSpeed: type === 'triangle' ? (Math.random() > 0.5 ? 1 : -1) * Math.random() : 0,
      // 根据形状类型决定是否使用三角形特殊样式
      isTriangle: type === 'triangle'
    })
  })
}

// 开始动画循环
const startAnimation = () => {
  // 确保没有多个动画循环
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }

  // 窗口尺寸
  const windowWidth = window.innerWidth
  const windowHeight = window.innerHeight

  // 计算两个图形之间的距离
  const getDistance = (shape1, shape2) => {
    // 计算每个形状的中心点
    let center1 = getCenterPoint(shape1)
    let center2 = getCenterPoint(shape2)

    // 计算两点之间的欧几里得距离
    return Math.sqrt(Math.pow(center1.x - center2.x, 2) + Math.pow(center1.y - center2.y, 2))
  }

  // 获取形状的中心点
  const getCenterPoint = shape => {
    let width, height

    if (shape.type === 'rectangle') {
      width = shape.size.width
      height = shape.size.height
    } else if (shape.type === 'triangle') {
      width = shape.size
      height = shape.size
    } else {
      width = shape.size
      height = shape.size
    }

    const x = shape.left + width / 2
    const y = shape.top + height / 2

    return { x, y }
  }

  // 获取形状的半径（用于碰撞检测）
  const getRadius = shape => {
    if (shape.type === 'rectangle') {
      // 使用对角线长度的一半作为近似半径
      return Math.sqrt(Math.pow(shape.size.width, 2) + Math.pow(shape.size.height, 2)) / 2
    } else if (shape.type === 'triangle') {
      // 三角形的近似半径
      return shape.size / 2
    } else {
      // 圆形和正方形使用一致的半径
      return shape.size / 2
    }
  }

  // 检查形状之间的碰撞
  const checkCollision = (shape1, shape2) => {
    const distance = getDistance(shape1, shape2)
    const minDistance = getRadius(shape1) + getRadius(shape2)

    // 如果距离小于两者半径之和，则发生碰撞
    return distance < minDistance * 0.8 // 添加0.8系数使碰撞更自然
  }

  // 处理碰撞后的响应
  const handleCollision = (shape1, shape2) => {
    // 获取中心点
    const center1 = getCenterPoint(shape1)
    const center2 = getCenterPoint(shape2)

    // 计算碰撞角度
    const angle = Math.atan2(center2.y - center1.y, center2.x - center1.x)

    // 交换速度方向
    const temp = {
      x: shape1.speed.x,
      y: shape1.speed.y
    }

    shape1.speed.x = shape2.speed.x
    shape1.speed.y = shape2.speed.y
    shape2.speed.x = temp.x
    shape2.speed.y = temp.y

    // 添加一点随机性，使动画更有趣
    shape1.speed.x += (Math.random() - 0.5) * 0.5
    shape1.speed.y += (Math.random() - 0.5) * 0.5
    shape2.speed.x += (Math.random() - 0.5) * 0.5
    shape2.speed.y += (Math.random() - 0.5) * 0.5

    // 防止粘连：稍微分开两个形状
    const overlap = getRadius(shape1) + getRadius(shape2) - getDistance(shape1, shape2)
    if (overlap > 0) {
      // 将形状沿碰撞方向推离一小段距离
      const pushFactor = overlap / 2 + 1 // 额外增加1像素，确保分离
      shape1.left -= Math.cos(angle) * pushFactor
      shape1.top -= Math.sin(angle) * pushFactor
      shape2.left += Math.cos(angle) * pushFactor
      shape2.top += Math.sin(angle) * pushFactor
    }

    // 碰撞后如果是三角形，改变旋转方向
    if (shape1.isTriangle) {
      shape1.rotationSpeed *= -1.2 // 反向并加快旋转
      // 限制最大旋转速度
      shape1.rotationSpeed = Math.min(Math.max(shape1.rotationSpeed, -2), 2)
    }
    if (shape2.isTriangle) {
      shape2.rotationSpeed *= -1.2
      shape2.rotationSpeed = Math.min(Math.max(shape2.rotationSpeed, -2), 2)
    }
  }

  // 创建已检查碰撞的形状对的集合，避免重复检查
  const checkedPairs = new Set()

  // 动画更新函数
  const animate = () => {
    // 重置已检查的形状对
    checkedPairs.clear()

    // 更新每个形状的位置
    shapes.value.forEach(shape => {
      // 计算形状的实际大小
      let width, height

      if (shape.type === 'rectangle') {
        width = shape.size.width
        height = shape.size.height
      } else if (shape.type === 'triangle') {
        // 三角形的边界盒计算 (近似为一个正方形)
        width = shape.size
        height = shape.size
      } else {
        // 圆形和正方形使用相同的宽高
        width = shape.size
        height = shape.size
      }

      // 更新位置
      shape.left += shape.speed.x
      shape.top += shape.speed.y

      // 边界检测和碰撞反弹
      if (shape.left <= 0) {
        shape.left = 0
        shape.speed.x *= -1
      } else if (shape.left + width >= windowWidth) {
        shape.left = windowWidth - width
        shape.speed.x *= -1
      }

      if (shape.top <= 0) {
        shape.top = 0
        shape.speed.y *= -1
      } else if (shape.top + height >= windowHeight) {
        shape.top = windowHeight - height
        shape.speed.y *= -1
      }

      // 更新三角形旋转角度
      if (shape.isTriangle) {
        shape.rotation += shape.rotationSpeed
        // 保持角度在0-360之间
        if (shape.rotation >= 360) {
          shape.rotation -= 360
        } else if (shape.rotation < 0) {
          shape.rotation += 360
        }
      }
    })

    // 检查图形之间的碰撞
    for (let i = 0; i < shapes.value.length; i++) {
      for (let j = i + 1; j < shapes.value.length; j++) {
        const shape1 = shapes.value[i]
        const shape2 = shapes.value[j]

        // 创建唯一的形状对ID
        const pairId = `${i}-${j}`

        // 检查是否已经处理过这一对形状
        if (checkedPairs.has(pairId)) continue

        // 标记这对形状已被检查
        checkedPairs.add(pairId)

        // 检查碰撞
        if (checkCollision(shape1, shape2)) {
          // 处理碰撞
          handleCollision(shape1, shape2)
        }
      }
    }

    // 请求下一帧
    animationId.value = requestAnimationFrame(animate)
  }

  // 开始动画循环
  animate()
}

// 生成形状样式
const getShapeStyle = shape => {
  const style = {
    left: `${shape.left}px`,
    top: `${shape.top}px`
  }

  // 根据形状类型添加特定样式
  if (shape.type === 'circle' || shape.type === 'square') {
    style.width = `${shape.size}px`
    style.height = `${shape.size}px`
  } else if (shape.type === 'rectangle') {
    style.width = `${shape.size.width}px`
    style.height = `${shape.size.height}px`
  } else if (shape.type === 'triangle') {
    // 三角形使用CSS边框制作，所以width和height设为0
    style.width = '0px'
    style.height = '0px'

    const halfSize = shape.size / 2

    // 根据三角形旋转角度调整边框
    style.borderWidth = `0 ${halfSize}px ${shape.size}px ${halfSize}px`
    style.borderColor = `transparent transparent rgba(255, 255, 255, 0.12) transparent`
    style.transform = `rotate(${shape.rotation}deg)`
    style.transformOrigin = 'center bottom'
  }

  return style
}
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
}
button,
input,
select {
  border: none;
  outline: none;
  /* 同时移除焦点时的轮廓线，提升视觉效果 */
}

.setup-container {
  width: 100%;
  min-height: 100vh;
  margin: 0;
  padding: 20px 0; /* 添加垂直内边距 */
  font-family: Arial, sans-serif;
  position: relative;
  background-color: #ff9f0a;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 保持水平不滚动 */
  color: #fff;
  box-sizing: border-box;
  display: flex;
  align-items: center; /* 改为顶部对齐，避免居中时内容被裁剪 */
  justify-content: center;
}

/* 确保背景铺满全屏 */
html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

.background-shapes {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.shape {
  position: absolute;
  opacity: 0.12;
  transition:
    left 0.05s linear,
    top 0.05s linear;
}

.circle,
.square,
.rectangle {
  background: #ffffff;
}

.circle {
  border-radius: 50%;
}

.triangle {
  background: transparent !important;
  width: 0 !important;
  height: 0 !important;
  border-style: solid !important;
  opacity: 1 !important;
  /* 确保三角形不受全局透明度影响 */
}

.title {
  font-size: 1.88vw; /* 36px / 1920 * 100 = 1.88vw */
  font-family: Montserrat-Bold, Montserrat;
  font-weight: bold;
  color: #222222;
  line-height: 2.29vw; /* 44px / 1920 * 100 = 2.29vw */
  margin-bottom: 0.42vw; /* 8px / 1920 * 100 = 0.42vw */
}

.global-instruction {
  font-size: 1.04vw; /* 20px / 1920 * 100 = 1.04vw */
  font-family: Montserrat-SemiBold, Montserrat;
  font-weight: 600;
  color: #7a7a7a;
  line-height: 1.25vw; /* 24px / 1920 * 100 = 1.25vw */
  margin-bottom: 1.56vw; /* 30px / 1920 * 100 = 1.56vw */
}

.main-content {
  display: flex;
  gap: 2.6vw;
  /* 50px / 1920 * 100 = 2.6vw */
  position: relative;
  z-index: 1;
  justify-content: center;
}

/* 左侧面板样式 */
.left-panel {
  width: 41.67vw; /* 800px / 1920 * 100 = 41.67vw */
  border-radius: 0.83vw; /* 16px / 1920 * 100 = 0.83vw */
  padding: 1.04vw; /* 20px / 1920 * 100 = 1.04vw */
  // box-shadow: 0 0.1vw 0.26vw rgba(0, 0, 0, 0.1); /* 0 2px 5px */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  z-index: 1;

  .avatar {
    width: 8.33vw; /* 160px / 1920 * 100 = 8.33vw */
    height: 8.33vw;
    border-radius: 50%;
    overflow: hidden;
    margin-top: 1.56vw; /* 30px / 1920 * 100 = 1.56vw */
    margin-bottom: 1.04vw; /* 20px / 1920 * 100 = 1.04vw */
  }

  .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .user-name {
    font-size: 1.88vw; /* 36px / 1920 * 100 = 1.88vw */
    font-family: Montserrat-Bold, Montserrat;
    font-weight: bold;
    color: #ffffff;
    line-height: 2.29vw; /* 44px / 1920 * 100 = 2.29vw */
    text-align: center;
    margin-bottom: 1.56vw; /* 30px / 1920 * 100 = 1.56vw */
  }

  .divider {
    width: 100%;
    height: 0.05vw; /* 1px / 1920 * 100 = 0.05vw */
    background: #ffffff;
    margin-bottom: 1.82vw; /* 35px / 1920 * 100 = 1.82vw */
  }

  .class-title {
    font-size: 2.6vw; /* 50px / 1920 * 100 = 2.6vw */
    font-family: Montserrat-Bold, Montserrat;
    font-weight: bold;
    color: #ffffff;
    line-height: 3.13vw; /* 60px / 1920 * 100 = 3.13vw */
    margin-bottom: 1.3vw; /* 25px / 1920 * 100 = 1.3vw */
    text-align: center;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .class-time {
    font-size: 1.04vw; /* 20px / 1920 * 100 = 1.04vw */
    font-family: Montserrat-Medium, Montserrat;
    font-weight: 500;
    display: flex;
    align-items: center;

    .time-icon {
      width: 1.04vw; /* 20px / 1920 * 100 = 1.04vw */
      height: 1.04vw;
      margin-right: 0.42vw; /* 8px / 1920 * 100 = 0.42vw */
    }
  }
}

/* 右侧面板样式 */
.right-panel {
  display: flex;
  flex-direction: column;
}
.right-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding: 3.13vw 5.21vw; /* 60px 100px / 1920 * 100 = 3.13vw 5.21vw */
  width: 46.88vw; /* 900px / 1920 * 100 = 46.88vw */
  background: #ffffff;
  border-radius: 1.56vw; /* 30px / 1920 * 100 = 1.56vw */
  z-index: 1;
}

.device-check-content {
  display: flex;
  width: 36.46vw; /* 700px / 1920 * 100 = 36.46vw */
  background: #f0f1f5;
  border-radius: 0.63vw; /* 12px / 1920 * 100 = 0.63vw */
  padding: 0.21vw; /* 4px / 1920 * 100 = 0.21vw */
}

/* 标签页样式 */
.tabs-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 10.42vw; /* 200px / 1920 * 100 = 10.42vw */
  background: #ffffff;
  gap: 0.63vw; /* 12px / 1920 * 100 = 0.63vw */
  padding: 0.94vw 1.04vw; /* 18px 20px / 1920 * 100 = 0.94vw 1.04vw */
  border-radius: 0.63vw 0px 0px 0.63vw; /* 12px 0 0 12px */
}

.tabs-vertical .tab-button {
  width: 8.33vw; /* 160px / 1920 * 100 = 8.33vw */
  height: 2.29vw; /* 44px / 1920 * 100 = 2.29vw */
  background: #ffffff;
  border-radius: 0.42vw; /* 8px / 1920 * 100 = 0.42vw */
  border: none;
  font-size: 0.83vw; /* 16px / 1920 * 100 = 0.83vw */
  font-family:
    PingFangSC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: #7a7a7a;
  line-height: 0.94vw; /* 18px / 1920 * 100 = 0.94vw */
  cursor: pointer;
}

.tabs-vertical .tab-button.active {
  background: #ff9f0a;
  color: #ffffff;
}

.tab-content {
  padding: 1.56vw; /* 30px / 1920 * 100 = 1.56vw */
  flex: 1 1 auto; // 拉伸
  min-width: 0; // 允许收缩到比内容更小
  height: 24vw;
}

.device-preview-container {
  height: 14.06vw; /* 270px / 1920 * 100 = 14.06vw */
}

.device-select-container,
.device-preview-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 1.04vw;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    font-size: 1.04vw;
    /* 20px / 1920 * 100 = 1.04vw */
    font-family:
      PingFangSC-Semibold,
      PingFang SC;
    font-weight: 600;
    color: #222222;
    line-height: 1.46vw;
    /* 28px / 1920 * 100 = 1.46vw */
    margin-bottom: 0.42vw;
    /* 8px / 1920 * 100 = 0.42vw */
  }

  .speaker-list {
    width: 100%;
    display: flex;
    align-items: center; // 垂直居中
    justify-content: flex-start; // 改为左对齐，防止居中溢出
    gap: 0.42vw;
    .speakers-device-select {
      flex: 1 1 auto; // 根据内容拉伸，也可收缩
      min-width: 0; // 防止因内容过宽撑出父容器
      height: 2.6vw;
      background: #ffffff;
      border-radius: 0.42vw;
      border: none;
      cursor: pointer;
    }

    .test-button {
      flex: 0 0 3.28vw; // 固定宽度，不伸缩
      width: 3.28vw;
      min-width: 3.28vw;
      height: 2.6vw;
      background: #ffffff;
      border-radius: 0.42vw;
      // border: 0.05vw solid #ff9f0a;
      cursor: pointer;
      transition: all 0.3s ease;
      white-space: nowrap;

      &:hover {
        background: #ff9f0a;
        color: #ffffff;
      }

      &.testing {
        background: #ff9f0a;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 3.28vw;

        .audio-playing-animation {
          width: 3.28vw;
          height: 3.28vw;
          display: flex;
          align-items: flex-end;
          justify-content: center;
          gap: 0.25vw;
          padding-bottom: 1vw;

          .bar {
            width: 0.25vw;
            background-color: #ffffff;
            border-radius: 0.1vw;
            animation-duration: 0.8s;
            animation-iteration-count: infinite;
            animation-timing-function: ease-in-out;
            transform-origin: bottom;
          }

          .bar1 {
            height: 0.73vw;
            animation-name: audioWave1;
            animation-delay: 0s;
          }

          .bar2 {
            height: 0.63vw;
            animation-name: audioWave2;
            animation-delay: 0.1s;
          }

          .bar3 {
            height: 0.83vw;
            animation-name: audioWave3;
            animation-delay: 0.2s;
          }

          .bar4 {
            height: 0.68vw;
            animation-name: audioWave4;
            animation-delay: 0.3s;
          }
        }
      }

      &.disabled {
        background: #e0e0e0;
        color: #999999;
        border-color: #e0e0e0;
        cursor: not-allowed;

        &:hover {
          background: #e0e0e0;
          color: #999999;
        }
      }
    }
  }
}

.camera-content,
.audio-content {
  display: flex;
  flex-direction: column;
}

.camera-preview {
  width: 100%;
  border-radius: 0.52vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  position: relative;
  box-sizing: border-box;

  video {
    width: 100%;
    aspect-ratio: 16 / 9;
    object-fit: cover;
    border-radius: 0.52vw;
    box-sizing: border-box;
  }
}

.camera-error {
  box-sizing: border-box;
  color: white;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.error-icon {
  width: 100%;
  aspect-ratio: 16 / 9;
  background: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.7) 100%);
  border-radius: 0.94vw;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.04vw;

  /* 20px / 1920 * 100 = 1.04vw */
  img {
    width: 2.08vw;
    /* 40px / 1920 * 100 = 2.08vw */
    height: 2.4vw;
    /* 46px / 1920 * 100 = 2.4vw */
    object-fit: cover;
  }
}

.device-select {
  width: 23.33vw;
  /* 448px / 1920 * 100 = 23.33vw */
  height: 2.6vw;
  /* 50px / 1920 * 100 = 2.6vw */
  background: #ffffff;
  border-radius: 0.42vw;
  /* 8px / 1920 * 100 = 0.42vw */
  font-size: 0.94vw;
  /* 18px / 1920 * 100 = 0.94vw */
  font-family: Montserrat-Medium, Montserrat;
  font-weight: 500;
  color: #222222;
  border: none;
  cursor: pointer;
}

.audio-meter-container {
  margin-top: 0.52vw;
  width: 100%;
  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.volume-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.31vw;
  /* 6px / 1920 * 100 = 0.31vw */
  color: #222222;
  font-size: 0.83vw;
  /* 16px / 1920 * 100 = 0.83vw */
  font-family: Montserrat-Medium, Montserrat;
}

.mic-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25vw;
  /* 24px / 1920 * 100 = 1.25vw */
  height: 1.25vw;
  /* 24px / 1920 * 100 = 1.25vw */
  border-radius: 50%;
  background-color: #e0e0e0;
  transition: background-color 0.2s ease;

  &.active {
    background-color: #ff9f0a;
  }

  .microphone-icon {
    display: inline-block;
    width: 0.73vw;
    /* 14px / 1920 * 100 = 0.73vw */
    height: 0.73vw;
    /* 14px / 1920 * 100 = 0.73vw */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='https://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath fill='white' d='M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z'/%3E%3Cpath fill='white' d='M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
  }
}

.volume-bar {
  width: 100%;
  height: 1.25vw;
  /* 24px / 1920 * 100 = 1.25vw */
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.42vw 0.63vw;
  /* 8px 12px -> 0.42vw 0.63vw */
  background: #ffffff;
  border-radius: 0.42vw;
  /* 8px / 1920 * 100 = 0.42vw */
  box-shadow: 0 0.1vw 0.21vw rgba(0, 0, 0, 0.05);
  /* 0 2px 4px -> 0 0.1vw 0.21vw */
}

.volume-segment {
  width: 1.56vw;
  /* 30px / 1920 * 100 = 1.56vw */
  height: 0.63vw;
  /* 12px / 1920 * 100 = 0.63vw */
  background: #e0e0e0;
  border-radius: 0.31vw;
  /* 6px / 1920 * 100 = 0.31vw */
  transition:
    background-color 0.15s ease-in-out,
    transform 0.15s ease;

  &.active {
    transform: scaleY(1.2);

    &.low {
      background: #ff9f0a;
    }

    &.medium {
      background: #ff9f0a;
    }

    &.high {
      background: #ff9f0a;
      animation: pulse 0.5s infinite alternate;
    }
  }
}

@keyframes pulse {
  from {
    transform: scaleY(1.2);
  }

  to {
    transform: scaleY(1.4);
  }
}

@keyframes audioWave1 {
  0% {
    height: 0.73vw;
  }
  50% {
    height: 1.04vw;
  }
  100% {
    height: 0.73vw;
  }
}

@keyframes audioWave2 {
  0% {
    height: 0.63vw;
  }
  50% {
    height: 1.15vw;
  }
  100% {
    height: 0.63vw;
  }
}

@keyframes audioWave3 {
  0% {
    height: 0.83vw;
  }
  50% {
    height: 0.52vw;
  }
  100% {
    height: 0.83vw;
  }
}

@keyframes audioWave4 {
  0% {
    height: 0.68vw;
  }
  50% {
    height: 1.25vw;
  }
  100% {
    height: 0.68vw;
  }
}

.enter-button {
  display: block;
  width: 20.83vw;
  /* 400px / 1920 * 100 = 20.83vw */
  min-width: 200px; /* 确保最小宽度 */
  height: 4.17vw;
  /* 80px / 1920 * 100 = 4.17vw */
  min-height: 50px; /* 确保最小高度 */
  background: #ffffff;
  border-radius: 2.08vw;
  /* 40px / 1920 * 100 = 2.08vw */
  font-size: 1.46vw;
  /* 28px / 1920 * 100 = 1.46vw */
  font-family: Montserrat-Bold, Montserrat;
  font-weight: bold;
  color: #ff9f0a;
  line-height: 1.67vw;
  /* 32px / 1920 * 100 = 1.67vw */
  border: none;
  cursor: pointer;
  transition: background 0.2s;
  margin-top: 4.56vw; /* 30px / 1920 * 100 = 1.56vw */
  align-self: center;
  z-index: 1;
}

.enter-button:hover {
  background: #fff9ec;
}
</style>
