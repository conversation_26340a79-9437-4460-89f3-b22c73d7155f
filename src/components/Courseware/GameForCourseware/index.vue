<template>
  <div class="gamecourseware-container">
    <a-spin v-if="!showGame" class="loading">
      <template v-slot:indicator>
        <a-icon type="loading" spin />
      </template>
    </a-spin>
    <Gamecourseware
      v-else
      :gameUrl="gameUrl"
      :nickName="nickName"
      :role="role"
      :isAnswer="isAnswer"
      :lang="lang"
      :showResultToast="showResultToast"
      :rightCoin="rightCoin"
      :gameCoursewareId="gameCoursewareId"
      :resourceUrl="resourceUrl"
      :isNewGame="isNewGame"
      @submit="submitAnswer"
      @exit="destroy"
      @loaded="gameLoaded"
      @reload="gameReload"
      @fail="gameFail"
    />
  </div>
</template>

<script>
import { emitter } from '@/hooks/useEventBus'
import { nativeApi } from 'utils/electronIpc'
import { getCoursewareInfo } from 'api/h5courseware'
import { checkCourseWare } from 'utils/courses'
import { getUrlParam } from 'utils/util'
import Game from './game'
// import logger from 'utils/logger'
import { dataURLtoBlob } from 'utils/util'
// import Upload from 'utils/upload'
import { sensorEvent } from '@/utils/sensorEvent'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import Gamecourseware from '@/components/game-courseware/src/index.vue'
export default {
  name: 'GameCourseware',
  components: {
    Gamecourseware
  },
  props: {
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      gameUrl: null, // 游戏课件地址
      nickName: '',
      role: 'student',
      lang: 'en',
      showGame: false, // 是否显示游戏
      isAnswer: 2, // 是否已做答 1:正常作答  2:没有作答
      userCoin: null, // 游戏内用户金币
      interactStatus: 2, // 当前互动状态  1:正常   2:主动关闭
      showResultToast: false,
      rightCoin: 0,
      isSubmiting: false,
      resourceUrl: '',
      gameCoursewareId: '',
      isNewGame: false
    }
  },
  async created() {
    this.game = new Game({
      ...this.options
    })
    this.game.interactReport('open') // 上报开启游戏
    const { code, data } = await this.game.getGameStatus().catch(err => {
      this.sendLogger(
        {
          msg: `接口报错:开启游戏:`,
          response: err
        },
        'error'
      )
      return err
    }) // 异常恢复数据
    if (code !== 0 || data.studentInteractStatus * 1 === 2) {
      this.$emit('closeGame')
      console.error('主动关闭，不恢复游戏')
      return
    } // 主动关闭游戏后，将无法不再打开
    const CW_SERVER = await this.getGameServe() // 获取课件域名(可能是本地，也可能是远程)
    this.interactStatus = data.studentInteractStatus * 1 // 当前游戏状态
    this.nickName = this.options.roomMessage.roomInfo.stuInfo.nickName
    this.isNewGame = this.options.ircMsg.isNewGame
    if (this.options.ircMsg.isNewGame) {
      this.gameCoursewareId = this.options.ircMsg.gameCoursewareId
      const hasStaticAssets = this.options.ircMsg.hasStaticAssets
      const supportKeepAndRestart = this.options.ircMsg.supportKeepAndRestart
      this.resourceUrl = `${CW_SERVER}/resource/iframes`
      const isLocal = CW_SERVER.includes('localhost') || CW_SERVER.includes('127.0.0.1')
      console.info('process.env.VUE_APP_MODE', process.env.VUE_APP_MODE)
      this.gameUrl =
        `${CW_SERVER}/${this.options.ircMsg.interactiveTemplatePath}` +
        `?userId=${this.$store.state.smallClass.baseData.stuInfo.id + ''}` + //如果能取到真实 Id 则传真实 id，否则传 1
        '&app=hw' +
        `&env=${
          process.env.VUE_APP_MODE === 'beta' || process.env.VUE_APP_MODE === 'development'
            ? 'test'
            : 'online'
        }` + //线上环境：online    测试环境：test
        '&channel=hw' +
        '&browser=Chromium' +
        '&bPreload=1' + //是否为预加载模式
        `&coursewareId=${this.gameCoursewareId}` +
        '&kjSceneCode=jiaoyanxitong' +
        '&isSync=0' + //是否为同步模式
        `${isLocal ? '&isOffline=1' : '&isOffline=0'}` + //是否为离线模式
        '&role=student' + //教师：teacher  学生：student
        `${supportKeepAndRestart ? '&supportKeepAndRestart=true' : ''}` + //是否支持接着玩、重新玩   true/false
        `${isLocal && hasStaticAssets ? '&hasStaticAssets=1' : '&hasStaticAssets=0'}` + //是否有外部依赖的静态资源   1/0
        `&lang=${this.lang}`
    } else {
      this.isNewGame = false
      this.gameUrl = `${CW_SERVER}/${this.options.ircMsg.interactiveTemplatePath}` // 拼接游戏地址
    }
    console.info('开始加载互动游戏地址 gameUrl', this.gameUrl)
    // this.sendLogger({
    //   msg: `收到互动, 加载互动游戏地址: ${JSON.stringify(this.gameUrl)}`,
    //   stage: 'start'
    // })
    this.showGame = true
    // window.thinkApi.ipc.send('test:url', this.gameUrl)
  },
  methods: {
    /**
     * 获取游戏课件域名
     */
    async getGameServe() {
      let coursewareInfo = {}
      const planId = getUrlParam('planId')
      // 获取课件本地SERVER信息
      const { CW_SERVER_ADDRESS, CW_WEBROOT } = await nativeApi.getClientInfo()
      const courseware = await getCoursewareInfo(planId * 1)
      if (courseware.code !== 0 || courseware.data.list.length === 0) {
        this.$Message.info('COURSESWARE ERROR')
        return
      }
      courseware.data.list.forEach(coursewareItem => {
        if (coursewareItem.id == 16) {
          coursewareInfo = coursewareItem
        }
      })
      let CW_SERVER = `${CW_SERVER_ADDRESS}${coursewareInfo.detail.catalog}`
      this.sendLogger({
        msg: `游戏本地服务域名: ${JSON.stringify(CW_SERVER_ADDRESS)}${JSON.stringify(
          coursewareInfo.detail.catalog
        )}`
      })
      try {
        const checkCourseWareResult = await checkCourseWare(
          coursewareInfo.detail.baseZipUrl,
          coursewareInfo.detail.baseZipMd5,
          CW_WEBROOT
        )
        if (!checkCourseWareResult) {
          const remoteUrl = coursewareInfo.detail.compressIndexUrl.split(
            coursewareInfo.detail.catalog
          )
          CW_SERVER = `${remoteUrl[0]}${coursewareInfo.detail.catalog}`
          this.sendLogger({
            msg: `使用游戏远端服务域名: ${JSON.stringify(remoteUrl[0])}${JSON.stringify(
              coursewareInfo.detail.catalog
            )}`
          })
        }
      } catch (error) {
        this.sendLogger(
          {
            msg: `游戏检查压缩包失败,可能会导致游戏黑屏`,
            stage: 'error',
            params: {
              error: error
            }
          },
          'error'
        )
      }
      // 检查本地课件是否存在

      return CW_SERVER
    },
    /**
     * 提交答案
     * 1. 提交子题数据
     * 2. 上传图片
     */
    async submitAnswer(answerData) {
      // @log-ignore
      // if (this.isSubmiting) return
      // this.isSubmiting = true
      // const picPath = []
      // const picBlob = []
      // const { answerPics, judge, rightRate } = answerData
      // this.isAnswer = 1
      // // 提交游戏数据
      // const { params, response } = await this.game
      //   .submitAnswer(rightRate, this.isAnswer)
      //   .catch((err) => {
      //     this.sendLogger(
      //       {
      //         msg: `接口报错:提交游戏数据:`,
      //         response: err,
      //       },
      //       'error',
      //     )
      //     return err
      //   })
      // this.sendLogger({
      //   msg: '提交答案结果',
      //   params: params,
      //   response: response,
      // })
      // const { code, data } = response
      // if (code == 0 && data) {
      //   this.rightCoin = data.rightCoin
      //   const isRight = rightRate == 100 ? 1 : this.isAnswer == 2 ? 3 : 2 // 对错，1 - 正确，2 - 错误, 3 - 未答
      //   const continuousCorrectData = { ...data, isRight, isGameQuestion: true }
      //   // 触发连对激励
      //   // this.$bus.$emit('continuousCorrect', continuousCorrectData)
      //   this.$bus.$emit('onlyContinuousCorrect', continuousCorrectData)
      //   // this.$bus.$emit('addCoin', true, this.rightCoin, true)
      //   classLiveSensor.osta_ia_base_interaction_submit(this.options?.ircMsg, '5', isRight, true)
      // }
      // // this.showResultToast = true
      // if (answerPics.length === 0) {
      //   this.game
      //     .submitPics({
      //       gameImages: [],
      //       userAnswerResult: judge,
      //     })
      //     .then(() => {
      //       console.info('接口同步后端游戏截图成功')
      //       this.isSubmiting = false
      //     })
      //     .catch(() => {
      //       this.isSubmiting = false
      //       console.error('接口同步后端游戏截图失败')
      //     })
      //   return
      // }
      // // 拼装上传图片的数据
      // answerPics.forEach((item, index) => {
      //   picBlob.push(dataURLtoBlob(item))
      //   picPath.push(
      //     `${this.options.ircMsg.interactId}_${this.options.roomMessage.roomInfo.stuInfo.id}_${index}.jpg`,
      //   )
      // })
      // // 实例化上传类
      // const upload = new Upload({
      //   scene: 'game',
      // })
      // // 批量上传文件
      // try {
      //   // 这个写法有些怪异,Promise + 回调,搞复杂了
      //   upload.putFiles({
      //     filePaths: picPath,
      //     files: picBlob,
      //     success: async (res) => {
      //       await this.game.submitPics({
      //         gameImages: res.filePaths,
      //         userAnswerResult: judge,
      //       })
      //       this.isSubmiting = false
      //     },
      //     fail: () => {
      //       this.sendLogger({
      //         msg: '上传游戏截图失败',
      //       })
      //     },
      //   })
      // } catch (error) {
      //   console.error('catch上传游戏截图失败', error)
      // }
    },

    /**
     * 游戏加载成功
     */
    gameLoaded() {
      this.sendLogger({
        msg: '游戏加载成功'
      })
    },

    /**
     * 重新加载游戏
     */
    gameReload() {
      this.sendLogger({
        msg: '重新加载游戏'
      })
    },

    /**
     * 游戏加载失败
     */
    gameFail() {
      this.sendLogger({
        msg: '游戏加载失败'
      })
    },

    /**
     * 销毁组件
     */
    destroy() {
      sensorEvent(
        'hw_classroom_interact_game_quit',
        this.options.roomMessage.roomInfo.commonOption,
        {
          interact_id: this.options.ircMsg.interactId
        }
      )
      this.$emit('closeGame')
    },

    /**
     * 日志上报
     */
    sendLogger({ msg = '', stage = '', params = {}, response = {} }, level = 'info') {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg,
          interactType: 'Game',
          interactId: this.options.ircMsg.interactId,
          interactStage: stage,
          params: JSON.stringify(params),
          response: JSON.stringify(response)
        },
        level
      })
    }
  },
  beforeUnmount() {
    // 收到关闭消息后，如果用户未作答，则需要主动提交一条数据
    if (this.isAnswer === 2 && this.interactStatus !== 2) {
      // 未作答
      const continuousCorrectData = { isRight: 3, isGameQuestion: true, rightCoin: 0 }
      // 触发连对激励
      this.$bus.$emit('continuousCorrect', continuousCorrectData)
      // 上报未作答
      this.game.submitAnswer(0, 2)
    } else {
      // 关闭金币连对
      this.$bus.$emit('continuousCorrect', false)
    }

    // 上报关闭游戏
    this.game.interactReport('close')
    this.sendLogger({
      msg: '结束互动',
      stage: 'end'
    })
    this.showGame = false
    if (this.rightCoin > 0) {
      emitter.emit('addCoin', true, this.rightCoin, true)
      // this.$bus.$emit('addCoin', true, this.rightCoin, true)
    }
    this.rightCoin = 0
  }
}
</script>
<style scoped lang="less">
.gamecourseware-container {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: auto;
  background: #000;
  .loading {
    color: #fff;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    i {
      font-size: 30px;
      color: #fff;
    }
  }
}
</style>
