// 业务逻辑
// import InteractionBase from '@thinkacademy/live-framework/components/interactions/interaction-base'
import {
  submitGameResult,
  submitPics,
  interactReport,
  getGameStatus,
} from '@/api/interaction/gameCourseware'
export default class Game {
  constructor(opts = {}) {
    // @log-ignore
    this.app = app
    this.vm = null
    this.options = opts
  }

  /*
   * 创建vue组件实例，并执行createPropsData，把相关参数传递给视图
   */
  initVmApp() {
    if (!this.dom) throw Error('互动的dom容器不存在')
    if (this.dom.children.length > 0) {
      this.dom.innerHTML = ''
    }
    this.vm = this.initVm(this.app)
    this.render(this.dom, this.vm)
  }

  /**
   *
   * @param {*} app
   * @description 初始化vm
   */
  initVm(app) {
    const Constructor = Vue.extend(app)
    const propsData = this.createPropsData()
    const vm = new Constructor({
      propsData: propsData,
    })
    vm.$mount()
    return vm
  }

  /*
   * 将组件渲染到指定dom中
   */
  render(dom, vm) {
    dom.appendChild(vm.$el)
  }

  /**
   * @description 销毁
   */
  destroy(opts = {}) {
    const { submit } = opts
    if (!this.vm) return
    if (submit) {
      this.vm.submit()
    }
    this.vm.$destroy()
    this.dom.innerHTML = ''
    this.vm = null
    this.app = null
  }

  /**
   *
   * @param {*} moduleInfo
   * @param {*} moduleId
   * @description 获取项目配置
   */
  getProperties(moduleInfo, moduleId) {
    return moduleInfo[`${moduleId}`].properties
  }

  /**
   * @description 处理互动处理模块发来的中间态消息
   */
  handleMsg(params = {}) {}

  /**
   * 提交图片
   * @param {*} gameImages
   */
  async submitPics(data = {}) {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次
      interactId: this.options.ircMsg.interactId, // 互动ID
      gameImages: [...data.gameImages], // 图片
      userAnswerResult: [...data.userAnswerResult], // 判题结果
    }
    await submitPics(params)
  }

  /**
   * 提交数据
   * @param {*} userAnswerResult
   * @param {*} rightRate
   * @returns
   */
  async submitAnswer(rightRate, isAnswer) {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次ID
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId,
      interactId: this.options.ircMsg.interactId, // 互动ID
      rightRate,
      isAnswer,
    }
    const response = await submitGameResult(params)
    return {
      params,
      response,
    }
  }

  /**
   * 互动状态上报
   * @param {*} type
   */
  interactReport(type) {
    const params = {
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId * 1,
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1,
      interactId: this.options.ircMsg.interactId,
    }
    // 上报开启游戏
    interactReport(type, params)
  }

  /**
   * 异常恢复
   * @returns
   */
  async getGameStatus() {
    const params = {
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId * 1,
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1,
      interactId: this.options.ircMsg.interactId,
    }
    return await getGameStatus(params)
  }
}
