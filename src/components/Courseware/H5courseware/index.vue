<template>
  <div
    id="h5coursewareContainer"
    class="h5coursewareContainer"
    :class="{ topZindex: coursewareState === 'error' }"
  >
    <!-- 课件外层loading -->
    <NeLoading :visible="coursewareState === 'loading'" />
    <!-- <NeDialog :visible="coursewareState === 'error'"> -->
    <!-- <h3>
        {{ $t('classroom.modules.courseware.errorNotice')[0] }}
      </h3>
      <p>
        {{ $t('classroom.modules.courseware.errorNotice')[1] }}<br />
        {{ $t('classroom.modules.courseware.errorNotice')[2] }}
      </p> -->
    <!-- <div slot="footer"> -->
    <!-- <button class="button" @click="reloadCourseWare">
          {{ 'common.reload' }}
        </button> -->
    <!-- </div> -->
    <!-- </NeDialog> -->
    <!--直播： 屏幕共享 -->
    <div v-show="screenShareStatus" :id="screenDomID" class="screen-share-class"></div>
    <!-- 黑板 -->
    <img v-if="blackBoardImg" class="blackBoardImg" :src="blackBoardImg" />
    <!-- 图片上墙 -->
    <ShowPhotoWall
      :photoWallImg="photoWallImg"
      v-show="photoWallImg"
      likeContainer="interactionController"
    />
    <!-- 课件 -->
    <div
      ref="h5courseware"
      tabindex="0"
      id="h5CoursewareContent"
      :class="['h5courseware', coursewareState === 'noCourseware' ? 'coursewareBg' : '']"
    >
      <div class="tip" v-if="coursewareState === 'noCourseware'">
        {{ $t('courses.noCoursesWare') }}
      </div>
    </div>
    <!-- <div v-if="showTip" id="tipWarp" class="tipwarp" v-html="tipBody"></div> -->
  </div>
</template>

<script>
import CoursewarePlayerFactory from '@thinkacademy/courseware-player'
import { emitter } from '@/hooks/useEventBus'
import NeLoading from './loading/index.vue'
import ShowPhotoWall from './showPhotoWall.vue'
import { coursewareLog, screenShareLog, gameLog } from '@/utils/web-log/HWLogDefine.js'
import { useClassData } from '@/stores/classroom'
import { useRtcService } from '@/hooks/useRtcService'
import Game from '../GameForCourseware/game'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import Upload from '@/utils/upload'
import { dataURLtoBlob } from '@/utils/util'
import { getLocalStorageConfig } from '@/utils/initConfig'

const { getRtcService } = useRtcService()
export default {
  data() {
    return {
      coursewarePlayer: null,
      coursewareState: 'loading',
      blackBoardImg: null, // 黑板背景图
      photoWallImg: null, // 图片上墙的图片地址
      localUrl: null,
      pageId: 0, // 当前课件页面
      currentCourseWareData: {}, // 课件数据
      screenShareStatus: false, //屏幕共享是否显示
      screenShareBeginTime: 0, //屏幕共享开始时间
      screenVideoShow: false,
      game: null,
      options: null,
      isSubmiting: false,
      isAnswer: 2,
      rightCoin: 0,
      cacheGame: null,
      currentIndex: -1,
      cacheKvContent: {}, // 缓存每一页的未消费kv消息
      store: null
    }
  },
  components: { NeLoading, ShowPhotoWall },
  computed: {
    studentId() {
      return this.store?.baseData?.stuInfo?.id
    }
  },

  async created() {
    this.store = useClassData()
    this.teacherShareUid = this.store.baseData.configs.rtcConfig.teacherShareUid
    this.screenDomID = `screenShare-${this.teacherShareUid}`

    /** 👇👇👇👇👇👇👇👇👇👇旧代码*/
    // const planId = store.baseData.planId

    // console.log('课件组件内获取store.baseData：', store.baseData)
    // console.log('课件组件内获取store.baseData.planInfo', store.baseData.planInfo)
    // 获取课件本地SERVER信息
    // const { CW_SERVER_ADDRESS, CW_WEBROOT } = await nativeApi.getClientInfo()

    /** 👆👆👆👆👆👆👆👆👆👆旧代码*/

    /** -------------------------*/

    /** 👇👇👇👇👇👇👇👇👇新代码 - 使用 store 中的课件信息*/
    coursewareLog.info('课件组件从store获取课件信息', this.store.coursewareInfo)

    // 检查 store 中是否有课件信息
    if (!this.store.coursewareInfo || !this.store.coursewareInfo.coursewareId) {
      this.coursewareState = 'noCourseware'
      this.sendLogger(`store中课件信息为空`, 'error')
      this.$emit('courseWareReady', true)
      return
    }

    // 从 store 中获取课件信息
    const coursewareInfo = this.store.coursewareInfo

    this.localUrl = coursewareInfo.compressIndexUrlList[0]

    /** 👆👆👆👆👆👆👆👆👆👆新代码*/

    let game_config = {}
    const cloudConfig = getLocalStorageConfig()
    if (cloudConfig && cloudConfig.configs) {
      for (let i = 0; i < cloudConfig.configs.length; i++) {
        const element = cloudConfig.configs[i]
        if (element.configKey === 'hw_game_config') {
          game_config = JSON.parse(element.configValue)
          break
        }
      }
    }
    coursewareLog.info('游戏配置云控', game_config)
    const envStr = import.meta.env.VITE_APP_MODE
    // 初始化课件
    this.initCoursewareParams = {
      role: 'student',
      screenWidth: '100%', // 屏幕宽度
      screenHeight: '100%', // 屏幕高度
      itsId: coursewareInfo.coursewareId, // 课件ID
      localUrl: this.localUrl,
      remoteUrl: coursewareInfo.compressIndexUrlList,
      onEvent: this.onEvent.bind(this),
      showPagePercent: 30,
      pageResourceList: coursewareInfo.pageResourceList,
      rate: coursewareInfo.rate,
      gameTip: this.$t('classroom.smallClass.coursewareBoard.gameConfig.gameTip'),
      gameToast: this.$t('classroom.smallClass.coursewareBoard.gameConfig.gameToast'),
      getLatestItsMessage: async cb => {
        let data
        try {
          data = data && JSON.parse(data)
          cb(data)
        } catch (e) {
          console.error('error latest message', data, e)
        }
      },
      loadGame: game_config.closeLoadGame === 1 ? '0' : '1', // 默认传0
      gamePreload: game_config.closeGamePreload === 1 ? '0' : '1', // 是否开启交互游戏预加载，授课端传0，学生端云控

      // env:
      //   process.env.VUE_APP_MODE === 'beta' || process.env.VUE_APP_MODE === 'development'
      //     ? 'test'
      //     : 'online',

      env: envStr === 'beta' || envStr === 'development' ? 'test' : 'online',

      tipGameStart: this.$t('classroom.game.tipGameStart'),
      tipGameEnd: this.$t('classroom.game.tipGameEnd'),
      client: 'pcs',
      userId: this.studentId
    }
    this.$nextTick(() => {
      this.coursewarePlayer = CoursewarePlayerFactory.getPlayer('student', this.$refs.h5courseware)
      this.sendLogger('开始初始化课件')
      this.coursewarePlayer.init(this.initCoursewareParams)
    })
  },
  mounted() {
    // 老师离开关闭屏幕共享
    emitter.on('teachrLiveCloseScreenShare', this.handleTeachrLiveCloseScreenShare)
    emitter.on('canvas_switch_courseware', this.handleSwitchCourseware)
    emitter.on('gameInteract', this.handlePlayGame)
    emitter.on('beginTest', this.beginAnswerInCourseware)
    emitter.on('endTest', this.endAnswerInCourseware)
  },
  beforeUnmount() {
    this.coursewarePlayer && this.coursewarePlayer.destroy()
    // 注销老师离开关闭屏幕共享
    emitter.off('teachrLiveCloseScreenShare', this.handleTeachrLiveCloseScreenShare)
    emitter.off('canvas_switch_courseware', this.handleSwitchCourseware)
    emitter.off('gameInteract', this.handlePlayGame)
    emitter.off('beginTest', this.beginAnswerInCourseware)
    emitter.off('endTest', this.endAnswerInCourseware)
  },
  methods: {
    handleTeachrLiveCloseScreenShare() {
      screenShareLog.info('[屏幕共享] 收到关闭事件', this.screenShareStatus)
      if (this.screenShareStatus) {
        screenShareLog.info('关闭屏幕共享')
        this.screenShareStateChange(false)
      }
    },
    /**
     * 监听课件事件
     */
    onEvent(event, data) {
      // @log-ignore
      switch (event) {
        case 'error':
          this.handleError(data)
          break
        case 'loadingProgress': // 课件加载状态
          this.handleLoadingProgress(data)
          break
        case 'statusChange': // loading, loaded
          this.handleStatusChange(data)
          break
        case 'pageChangeToTeacher':
          this.handlePageChangeToTeacher(data)
          break
        case 'game_over':
          this.handleGameOver(data)
          break
        case 'statistic':
          this.handleStatistic(data)
          break
        case 'sendTestResult':
          this.handleSendTestResult(data)
          break
        default:
          this.handleDefault(event, data)
          break
      }
    },
    handleStatistic(data) {
      console.info('课件统计', data)
      try {
        classLiveSensor.osta_ia_game_loading(data.eventName, data.params)
      } catch (error) {
        console.error('课件统计错误', error)
      }
    },
    handleSendTestResult(data) {
      console.info('课件内答题结果', data)
      emitter.emit('sendTestResultToVote', data)
    },

    handleDefault(event, data) {
      // @log-ignore
      if (event === 'catalogueChange') {
        this.sendLogger(
          '课件event',
          {
            event: event,
            data: data.pages.length
          },
          'info'
        )
      } else {
        this.sendLogger(
          '课件event',
          {
            event: event,
            data: data
          },
          'info'
        )
      }
    },
    // 加载课件错误
    handleError(data) {
      this.coursewareState = 'error'
      coursewareLog.error('课件加载失败', data)
      // this.sendLogger('iframe 加载失败', { data }, 'error')
    },
    async handleGameOver(answerData) {
      console.info('游戏结束111', { answerData })
      if (!this.game) return
      this.isSubmiting = true
      const picPath = []
      const picBlob = []
      const { answerPics, judge, rightRate } = answerData
      this.isAnswer = 1
      // 提交游戏数据
      const { params, response } = await this.game
        .submitAnswer(rightRate, this.isAnswer)
        .catch(err => {
          this.sendLogger(
            {
              msg: `接口报错:提交游戏数据:`,
              response: err
            },
            'error'
          )
          return err
        })
      this.sendLogger({
        msg: '提交答案结果',
        params: params,
        response: response
      })
      const { code, data } = response

      if (code == 0 && data) {
        this.rightCoin = data.rightCoin
        const isRight = rightRate == 100 ? 1 : this.isAnswer == 2 ? 3 : 2 // 对错，1 - 正确，2 - 错误, 3 - 未答
        const continuousCorrectData = { ...data, isRight, isGameQuestion: true }
        // 触发连对激励
        emitter.emit('onlyContinuousCorrect', continuousCorrectData)
        emitter.emit('addCoin', true, this.rightCoin, true)
        classLiveSensor.osta_ia_base_interaction_submit(this.options?.ircMsg, '5', isRight, true)
      }

      // this.showResultToast = true
      if (answerPics.length === 0) {
        this.game
          .submitPics({
            gameImages: [],
            userAnswerResult: judge
          })
          .then(() => {
            console.info('接口同步后端游戏截图成功')
            this.isSubmiting = false
          })
          .catch(() => {
            this.isSubmiting = false
            console.error('接口同步后端游戏截图失败')
          })
        return
      }
      // 拼装上传图片的数据
      answerPics.forEach((item, index) => {
        picBlob.push(dataURLtoBlob(item))
        picPath.push(
          `${this.options.ircMsg.interactId}_${this.options.roomMessage.roomInfo.stuInfo.id}_${index}.jpg`
        )
      })
      // 实例化上传类
      const upload = new Upload({
        scene: 'game'
      })
      // 批量上传文件
      try {
        // 这个写法有些怪异,Promise + 回调,搞复杂了
        upload.putFiles({
          filePaths: picPath,
          files: picBlob,
          success: async res => {
            gameLog.success('上传游戏截图成功', res)
            await this.game.submitPics({
              gameImages: res.filePaths,
              userAnswerResult: judge
            })
            this.isSubmiting = false
          },
          fail: () => {
            gameLog.error({
              msg: '上传游戏截图失败'
            })
          }
        })
      } catch (error) {
        gameLog.error('catch上传游戏截图失败', error)
      }
    },

    // 课件加载进度监听(课件加载出来的页数达到某个值，就发出显示课件，不等课件全部加载完成。)
    handleLoadingProgress(data) {
      this.sendLogger('【游戏指令】课件加载中', { data })
      if ((data.loaded / data.total) * 100 >= this.initCoursewareParams.showPagePercent) {
        this.coursewareState = 'loaded'
      } else {
        this.coursewareState = 'loading'
      }
    },

    // 课件加载状态变化
    handleStatusChange(data) {
      gameLog.info('【游戏指令】课件加载状态改变', { data })
      // this.$bus.$emit('corewareLoadStatus', data)
      if (data.status === 'loaded') {
        this.sendLogger('【游戏指令】课件加载成功', { data }, 'success')
      }
    },
    handlePageChangeToTeacher(data) {
      gameLog.info('【游戏指令】课件翻页完成', data.pageIndex)
      this.currentIndex = data.pageIndex
      if (
        this.cacheGame &&
        this.currentIndex === JSON.parse(this.cacheGame.jsString).currentIndex
      ) {
        this.handlePlayGame(this.cacheGame, '缓存游戏指令')
        this.cacheGame = null
      } else {
        gameLog.info('【游戏指令】检查【无】待执行的游戏指令')
      }
      // 翻页时判断是否有未消费的课件内答题消息
      if (this.cacheKvContent[data.pageIndex]) {
        this.beginAnswerInCourseware(this.cacheKvContent[data.pageIndex])
        this.cacheKvContent = {}
      }
    },

    /**
     * 执行课件翻页操作
     * blackBoardType  0: 正常切换课件   1: 黑板   3: 图片上墙
     */
    handleSwitchCourseware(data) {
      // Logan.log('课件翻页', data)
      // @log-ignore
      if (!data || !data.currentCourseWare) return
      this.currentCourseWareData = data.currentCourseWare
      const { blackBoardType, imgUrl, jsString, photoWallImageArray, pageId } =
        data.currentCourseWare
      // this.$bus.$emit('photoWallShow', false)

      // 先清空所有的静态图片(黑板背景图、上墙图片)
      this.blackBoardImg = this.photoWallImg = ''
      // 显示黑板
      if (blackBoardType === 1 && imgUrl) {
        this.blackBoardImg = imgUrl
      }
      // 图片上墙
      if (blackBoardType === 3 && photoWallImageArray) {
        this.photoWallImg = photoWallImageArray[0]
        // this.$bus.$emit('photoWallShow', true) // 告诉作业盒子，现在正在上墙
      }
      // 屏幕共享
      if (blackBoardType === 9) {
        //显示屏幕共
        //直播时开启rtc流
        this.screenShareStateChange(true)
        this.beginScreeShare()
      }

      // 隐藏屏幕共享
      if (blackBoardType === 0 && this.screenShareStatus) {
        //直播时关闭rtc流
        this.screenShareStateChange(false)
      }
      // 正常切换课件
      if (blackBoardType === 0 && jsString) {
        const jsStringData = JSON.parse(jsString)
        this.coursewarePlayer && this.coursewarePlayer.handleRoomItsMessage(jsStringData)
      }

      this.pageId = pageId // 更改当前页码
      // console.log('涂鸦发送通知', this.currentCourseWareData)
      emitter.emit('changePageId', {
        id: pageId,
        currentCourseWareData: this.currentCourseWareData
      }) // 告诉涂鸦层，页码改变了，绘制涂鸦
      // this.$emit('changePageId', pageId, this.currentCourseWareData) // 告诉涂鸦层，页码改变了，绘制涂鸦
    },
    // 判断当前课件页和kv消息里的页码是否相同，不相同先缓存起来
    judgeKvContent(kvContent) {
      if (kvContent.currentIndex !== this.currentIndex) {
        console.info(
          '课件内答题信令页码和当前不一致，先缓存',
          kvContent.currentIndex,
          '当前所在页:',
          this.currentIndex
        )
        this.cacheKvContent[kvContent.currentIndex] = kvContent
        return false
      }
      return true
    },
    // 开启课件内作答
    beginAnswerInCourseware(kvContent) {
      console.info('课件内答题前判断', kvContent)
      const isSamePage = this.judgeKvContent(kvContent)
      if (!isSamePage) return
      const jsObject = JSON.parse(kvContent.jsString)
      jsObject.answerTip = this.$t('classroom.smallClass.正确答案')
      if (this.coursewarePlayer) {
        console.info('开始作答消息传入课件')
        this.coursewarePlayer.handleMessageToCourseware(jsObject)
        document.getElementById('class-whiteboard-layer').style.zIndex = -1
        this.store.updateSliderVisible(false)
        document.querySelector('.h5coursewareContainer').classList.add('canClick')
        console.info('开启答题 关闭按下说话')
        emitter.emit('close-speaking')
      } else {
        console.error('开始时课件播放器未初始化')
      }
    },
    endAnswerInCourseware(kvContent) {
      console.info('课件内答题结束', kvContent)
      //  this.cacheKvContent 不为{} 时
      if (Object.keys(this.cacheKvContent).length) {
        this.cacheKvContent = {}
        console.error('课件内答题结束时，删除未消费的kv消息，主动关闭互动')
        emitter.emit('cousewareEnd')
        return
      }

      this.store.updateSliderVisible(true)
      document.getElementById('class-whiteboard-layer').style.zIndex = ''
      document.querySelector('.h5coursewareContainer').classList.remove('canClick')
      document.getElementById('h5CoursewareContent').focus()
      if (this.coursewarePlayer) {
        console.info('停止作答消息传入课件')
        this.coursewarePlayer.handleMessageToCourseware(JSON.parse(kvContent.jsString))
      } else {
        console.error('结束时课件播放器未初始化')
      }
    },
    ///游戏指令
    async handlePlayGame(options1, description) {
      gameLog.info('【游戏指令】收到课件内玩游戏的指令', options1, '来自：', description)

      ///如果没有options1为undefined 直接返回
      if (!options1) {
        gameLog.info('【游戏指令】游戏指令为空')
        return
      }

      gameLog.info('【游戏指令】游戏指令不为空', options1)
      const store = useClassData()
      let options = {
        roomMessage: {
          roomInfo: {
            ...store.baseData
          }
        },
        ircMsg: options1
      }
      gameLog.info('【游戏指令】拼装数据模型', options)
      gameLog.info('【游戏指令】课件内玩开启【上】', options)
      this.options = options
      gameLog.info('游戏指令--课件内玩开启【下】', options)

      if (options.ircMsg.pub) {
        this.game = new Game({
          ...options
        })
        gameLog.info('【游戏指令】options.ircMsg.jsString', options.ircMsg.jsString)

        gameLog.info('【游戏指令】游戏指令jsstring转模型', JSON.parse(options.ircMsg.jsString))

        const { currentIndex: jsStingPage } = JSON.parse(options.ircMsg.jsString)

        if (jsStingPage !== this.currentIndex) {
          gameLog.info(
            '【游戏指令】',
            '游戏开始信令中页码不一致,将改次指令存起来，等到翻页到对应页码再执行，',
            '指令要求的课件所在页:',
            jsStingPage,
            '课件当前所在的页:',
            this.currentIndex
          )
          this.cacheGame = options1
          return
        }
        gameLog.info('【游戏指令】当前游戏要求的页码和课件所在页码一致，直接执行')

        this.game.interactReport('open') // 上报开启游戏
        if (!options.ircMsg.jsString) return
        this.coursewarePlayer &&
          this.coursewarePlayer.handleRoomItsMessage(JSON.parse(options.ircMsg.jsString))

        const ele = document.getElementById('class-board-layer')
        console.log('【游戏指令】操作dom，调整层级，dom对象为', ele)
        ele.style.zIndex = 9999999
        // document.getElementById('side-tools').style.display = 'none'
      } else {
        gameLog.info('【游戏指令】课件内玩游戏结束')
        // 收到关闭消息后，如果用户未作答，则需要主动提交一条数据
        if (!this.game) {
          gameLog.info('【游戏指令】结束信令没有对应的开始信令')
          return
        }
        gameLog.info('【游戏指令】课件内玩结束options.ircMsg', options.ircMsg)

        gameLog.info('【游戏指令】课件内玩结束options.ircMsg.jsString', options.ircMsg.jsString)

        if (JSON.parse(options.ircMsg.jsString).currentIndex !== this.currentIndex) {
          gameLog.info(
            '【游戏指令】',
            '游戏结束信令中页码不一致',
            options.ircMsg.jsString,
            '当前所在页:',
            this.currentIndex
          )
        }
        if (options.ircMsg.jsString) {
          gameLog.info('【游戏指令】课件内玩结束handleRoomItsMessage', options.ircMsg.jsString)
          this.cacheGame = null
          this.coursewarePlayer &&
            this.coursewarePlayer.handleRoomItsMessage(JSON.parse(options.ircMsg.jsString))
        }
        // document.getElementById('side-tools').style.display = 'block'
        document.getElementById('class-board-layer').style.zIndex = ''
        if (this.isAnswer === 2) {
          // 未作答
          const continuousCorrectData = { isRight: 3, isGameQuestion: true, rightCoin: 0 }
          // 触发连对激励
          // emitter.emit('continuousCorrect', continuousCorrectData)
          // 上报未作答
          this.game.submitAnswer(0, 2)
        } else {
          // 关闭金币连对
          // emitter.emit('continuousCorrect', false)
        }

        // 上报关闭游戏
        this.game.interactReport('close')
        gameLog.info({
          msg: '结束互动',
          stage: 'end'
        })
        // if (this.rightCoin > 0) {
        //   emitter.emit('addCoin', true, this.rightCoin, true)
        // }
        this.rightCoin = 0
        this.options = null
        this.game = null
        this.isSubmiting = false
        this.isAnswer = 2
      }
    },
    reloadCourseWare() {
      this.coursewareState = 'loading'
      this.coursewarePlayer && this.coursewarePlayer.reloadIts()
    },

    /**
     * 课件加载上报日志
     */
    sendLogger(msg = '', param, level = 'info') {
      try {
        if (typeof param === 'string') level = param
        const logMap = {
          error: coursewareLog?.error,
          info: coursewareLog?.info,
          action: coursewareLog?.action,
          success: coursewareLog?.success,
          warn: coursewareLog?.warning
        }
        const logFn = logMap[level] || coursewareLog?.info || (() => {})
        if (typeof logFn === 'function') {
          logFn(msg, param)
        }
      } catch (e) {
        console.error('sendLogger error', e)
      }
    },

    screenShareStateChange(status) {
      screenShareLog.info('[屏幕共享] 状态切换:', status)
      if (!status) {
        screenShareLog.info('[屏幕共享] 销毁远端流', this.teacherShareUid)
        const RtcService = getRtcService()

        RtcService.destroyRemoteVideo(this.teacherShareUid)
      }
      this.screenShareStatus = status
    },
    beginScreeShare() {
      setTimeout(() => {
        const screenShareElement = document.getElementById(this.screenDomID)
        screenShareLog.info('[屏幕共享] beginScreeShare', this.screenDomID, screenShareElement)
        if (screenShareElement && screenShareElement.innerHTML) return
        const RtcService = getRtcService()

        RtcService.createRemoteVideo(this.teacherShareUid, this.screenDomID)
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.canClick {
  pointer-events: initial;
}

.h5coursewareContainer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  bottom: 0;
  overflow: hidden;
  z-index: 7;

  &.topZindex {
    z-index: 1000;
  }

  .screen-share-class {
    background: #000;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 101;
    width: 100%;
    height: 100%;
  }

  .blackBoardImg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 101;
    width: 100%;
    height: 100%;
  }
}

.h5courseware {
  width: 100%;
  height: 100%;
  overflow: hidden;

  iframe {
    background: transparent;
    position: relative;
    z-index: 1;
  }

  :deep(.loading-contenter) {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 100%;
  }
}

.coursewareBg {
  background: url('../images/noCoursewareBg.png') no-repeat center center;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .tip {
    color: #fff;
    font-size: 20px;
    font-weight: 600;
  }
}

.h5courseware-loading {
  padding-top: 260px;
  z-index: 3;
}

:deep(.answerTip) {
  background: rgba(15, 25, 42, 0.7);
  border-radius: 8px;
  backdrop-filter: blur(4px);
  position: absolute;
  bottom: 16px;
  left: 16px;
  z-index: 100;
  padding: 12px 10px 12px 30px;
  color: #fff;
  line-height: 18px;
  font-size: 16px;
  font-weight: 600;
  display: none;
}

:deep(.answerTip::before) {
  background: url('./imgs/right.png') no-repeat center center;
  background-size: 16px 16px;
  width: 16px;
  height: 16px;
  position: absolute;
  left: 10px;
  top: 50%;
  content: '';
  transform: translate(0, -50%);
}

:deep(.gameDemoTip) {
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  bottom: 10px;
  left: 10px;
  z-index: 100;
  min-height: 30px;
  padding: 15px 15px 15px 30px;
  border-radius: 10px;
  color: #fff;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  font-weight: 500;
  display: none;

  &::before {
    position: absolute;
    left: 10px;
    top: 50%;
    width: 10px;
    height: 10px;
    background: #3370ff;
    content: '';
    transform: translate(0, -50%);
    border-radius: 10px;
  }
}

:deep(.gameDemoToast) {
  background: rgba(15, 25, 42, 0.95);
  position: absolute;
  max-width: 275px;
  top: 50%;
  left: 50%;
  z-index: 100;
  min-height: 30px;
  padding: 22px;
  border-radius: 10px;
  color: #fff;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 20px;
  font-size: 16px;
  transform: translate(-50%, -50%);
  font-weight: 500;
  display: none;
}

:deep(.gameEndTip),
:deep(.gameStartTip) {
  width: 100%;
  height: 100%;
  z-index: 100;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0);
  animation: bgFadeIn 200ms ease-out forwards;
  display: flex;
  justify-content: center;

  .banner {
    width: 100%;
    height: 17.6%;
    position: absolute;
    bottom: 35.1%;
    background: url('./gameStart/banner.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    opacity: 0;
    align-items: center;
    justify-content: center;
    animation-duration: 200ms;
    animation: bannerFadeIn 3s ease-in-out forwards;

    .text {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 44px;
      font-weight: 900;
      color: #ae4d00;
      animation-duration: 200ms;
      opacity: 0;
      animation: textAnimation 3s ease-in-out forwards;
    }

    .sunline1 {
      width: 20.6%;
      height: 100%;
      position: absolute;
      background: url('./gameStart/sunline1.png') no-repeat center center;
      background-size: 100% 100%;
      left: 0;
      opacity: 0;
      animation-duration: 200ms;
      animation: sunline1 3s linear infinite;
    }

    .sunline2 {
      background: url('./gameStart/sunline2.png') no-repeat center center;
      background-size: 100% 100%;
      width: 15%;
      height: 100%;
      position: absolute;
      right: 0;
      opacity: 0;
      animation-duration: 200ms;
      animation: sunline2 3s linear infinite;
    }
  }

  .streamer {
    width: 71%;
    height: 50%;
    position: absolute;
    bottom: 39%;
    opacity: 0;
    transform: scale(0.4);
    animation-duration: 200ms;
    background: url('./gameStart/streamer.png') no-repeat center center;
    background-size: 100% 100%;
    animation: streamerAnimation 3s ease-in-out forwards;
  }

  .up {
    background: url('./gameStart/up.png') no-repeat center center;
    background-size: 100% 100%;
    width: 60%;
    height: 40%;
    position: absolute;
    z-index: -1;
    bottom: 39%;
    text-align: center;
    opacity: 0;
    transform: scale(0.7);
    animation-delay: 200ms;
    animation: upAnimation 3s ease-in-out forwards;
  }

  .up.rate2 {
    height: 50%;
  }
}

:deep(.gameEndTip) {
  .up {
    background: url('./gameEnd/up.png') no-repeat center center;
    background-size: 100% 100%;
  }
}

@keyframes upAnimation {
  0% {
    transform: scale(0.7);
    opacity: 0;
  }

  20% {
    transform: scale(0.7);
    opacity: 0;
  }

  30% {
    transform: scale(1.06);
    opacity: 1;
  }

  35% {
    transform: scale(0.9);
    opacity: 1;
  }

  38% {
    transform: scale(1.05);
    opacity: 1;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }

  90% {
    transform: scale(1);
    opacity: 1;
  }

  100% {
    transform: scale(1);
    opacity: 0;
  }
}

@keyframes bgFadeIn {
  to {
    background: rgba(0, 0, 0, 0.4);
  }
}

@keyframes bannerFadeIn {
  0% {
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  /* 200ms / 3s = 6.67%，四舍五入到 10% */
  90% {
    opacity: 1;
  }

  /* 等待 2.6s */
  100% {
    opacity: 0;
  }

  /* 最后 200ms 将透明度从 1 变为 0 */
}

@keyframes textAnimation {
  0% {
    transform: scale(3);
    opacity: 0;
  }

  10% {
    transform: scale(3);
    opacity: 0;
  }

  20% {
    transform: scale(0.9);
    opacity: 1;
  }

  25% {
    transform: scale(1.05);
    opacity: 1;
  }

  30% {
    transform: scale(1);
    opacity: 1;
  }

  80% {
    transform: scale(1);
    opacity: 1;
  }

  90% {
    transform: scale(3);
    opacity: 0;
  }
}

@keyframes sunline1 {
  33% {
    left: 0;
    opacity: 0;
  }

  40% {
    opacity: 1;
  }

  60% {
    opacity: 1;
  }

  66% {
    left: 100%;
    opacity: 0;
  }

  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes sunline2 {
  40% {
    left: 0;
    opacity: 0;
  }

  57% {
    opacity: 1;
  }

  77% {
    opacity: 1;
  }

  83% {
    left: 100%;
    opacity: 0;
  }

  100% {
    left: 100%;
    opacity: 0;
  }
}

@keyframes streamerAnimation {
  0% {
    transform: scale(0.4);
    opacity: 0;
  }

  20% {
    transform: scale(0.4);
    opacity: 0;
  }

  30% {
    transform: scale(1);
    opacity: 1;
  }

  80% {
    transform: scale(1);
    opacity: 1;
  }

  90% {
    transform: scale(1);
    opacity: 0;
  }
}
</style>
