<template>
  <!-- 图片上墙 -->
  <div class="showPhotoWall" ref="showPhotoWall">
    <!-- 上墙图片loading -->
    <NeLoading :visible="photoLoading" :cover="false" />
    <img :src="photoWallImg" ref="photoImg" v-if="!photoLoading" />
  </div>
</template>

<script>
import like from '@neosjs/h5-like'
import NeLoading from './loading/index.vue'
import { useClassData } from '@/stores/classroom'

import star from './imgs/icon_star.png'
import donut from './imgs/icon_donut.png'
import heart from './imgs/icon_heart.png'
import icon_flower_r from './imgs/icon_flower_r.png'
import icon_flower_l from './imgs/icon_flower_l.png'
import flash from './imgs/icon_flash.png'
import btnHeart from './imgs/<EMAIL>'

export default {
  name: 'showPhotoWall',
  data() {
    return {
      photoLoading: true
    }
  },
  props: {
    photoWallImg: {
      type: String,
      default: ''
    },
    likeContainer: {
      type: String,
      default: ''
    }
  },
  components: {
    NeLoading
  },
  methods: {
    /**
     * 执行上墙操作
     */
    handlePhotoWall(imgUrl) {
      this.photoLoading = true
      const store = useClassData()
      const isAudition = store.baseData?.commonOption?.isAudition
      if (!isAudition) {
        this.createLikeContainer()
      }
      const photoImg = new Image()
      photoImg.src = imgUrl
      photoImg.onload = () => {
        // 图片加载成功，显示点赞按钮
        const likeContainer = document.getElementById('photoWallLikeContainer')
        likeContainer && (likeContainer.style.display = 'block')
        this.photoLoading = false
      }
    },

    /**
     * 由于点赞要在涂鸦层之上，所以点赞的区域要挂载到互动区
     */
    async createLikeContainer() {
      const likeContainer = document.getElementById('photoWallLikeContainer')
      // console.log(likeContainer, 'likeContainer')
      if (likeContainer) return
      const _likeDom = document.createElement('div')
      _likeDom.setAttribute('id', 'photoWallLikeContainer')
      _likeDom.setAttribute('class', 'photoWallLikeContainer')
      _likeDom.innerHTML = `<div class="likeBtn" id="likeBtn">
        <img src="${btnHeart}" id="heart" />
      </div>`
      document.getElementById(this.likeContainer).appendChild(_likeDom)
      like.init({
        icons: [icon_flower_l, star, donut, heart, flash, icon_flower_r],
        btnDom: 'likeBtn',
        flyDom: 'photoWallLikeContainer',
        className: 'fly',
        width: 200,
        height: 600
      })
    },
    // 销毁点赞
    async destroyLike() {
      const likeContainer = document.getElementById('photoWallLikeContainer')
      likeContainer && likeContainer.parentNode.removeChild(likeContainer)
      like.destroy()
    }
  },
  beforeUnmount() {
    this.destroyLike()
  },
  watch: {
    photoWallImg(val) {
      val ? this.handlePhotoWall(val) : this.destroyLike()
    }
  }
}
</script>

<style lang="scss" scoped>
.showPhotoWall {
  z-index: 101;
  position: relative;
  width: 100%;
  height: 100%;
  img {
    object-fit: contain;
    display: block;
    max-width: 100%;
    max-height: 100%;
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="scss">
.photoWallLikeContainer {
  position: absolute;
  right: 100px;
  bottom: 30px;
  display: none;
  z-index: 7;
  pointer-events: auto;
  .likeBtn {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 50px;
    height: 50px;
    border-radius: 100em;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2;
    &:hover {
      background: rgba(0, 0, 0, 0.7);
    }
    img {
      display: block;
      position: absolute;
      left: 50%;
      top: 50%;
      width: 36px;
      transition: width 0.3s;
      transform: translate(-50%, -50%);
      z-index: 2;
      &:active {
        width: 50px;
      }
    }
  }
  .fly {
    position: absolute;
    right: -10px;
    bottom: 65px;
    width: 100px;
    height: 300px;
    z-index: 1;
  }
}
</style>
