<template>
  <div v-if="show" class="loading-wrapper" :class="className" :style="loadingStyle">
    <div class="loading-contenter">
      <div class="loading-logo">
        <img src="@/assets/images/logo-loading.png" />
      </div>
      <div class="loading-animation" />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
defineOptions({
  name: 'ClassLoading'
})
const props = defineProps({
  // 显示状态
  show: {
    default: true,
    type: Boolean
  },
  // 尺寸
  size: {
    default: 'default',
    type: String,
    validator(value) {
      return ['small', 'default'].indexOf(value) !== -1
    }
  },
  // 上间距
  marginTop: {
    default: '0',
    type: String
  },
  // 下间距
  marginBottom: {
    default: '0',
    type: String
  }
})

const className = computed(() => {
  return `loading-${props.size}`
})

const loadingStyle = computed(() => {
  return {
    marginTop: props.marginTop,
    marginBottom: props.marginBottom
  }
})
</script>
<style lang="scss" scoped>
// loading样式
.loading-wrapper {
  overflow: hidden;
  .loading-contenter {
    position: relative;
    margin-left: auto;
    margin-right: auto;
  }
  .loading-logo {
    position: absolute;
    left: 15px;
    top: 15px;
  }
  .loading-animation {
    font-size: 10px;
    position: relative;
    text-indent: -9999em;
    border-top: 3px solid #fddf96;
    border-right: 3px solid #fddf96;
    border-bottom: 3px solid #fddf96;
    border-left: 3px solid #ffc330;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation: load 1.1s infinite linear;
    animation: load 1.1s infinite linear;
  }
  // 默认尺寸
  &.loading-default {
    .loading-contenter {
      width: 60px;
    }
    .loading-logo,
    .loading-logo img {
      width: 30px;
      height: 30px;
    }
    .loading-animation,
    .loading-animation:after {
      border-radius: 50%;
      width: 60px;
      height: 60px;
    }
  }
  // 小尺寸
  &.loading-small {
    .loading-contenter {
      width: 50px;
    }
    .loading-logo {
      position: absolute;
      left: 15px;
      top: 13px;
    }
    .loading-logo,
    .loading-logo img {
      width: 20px;
      height: 20px;
    }
    .loading-animation,
    .loading-animation:after {
      border-radius: 50%;
      width: 50px;
      height: 50px;
    }
  }
}
@-webkit-keyframes load {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes load {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
</style>
