<!-- 视图层 -->
<template>
  <div class="kickout">
    <Modal
      prefixCls="kickout"
      class="ignore-kickout-modal"
      :width="420"
      :closable="false"
      :maskClosable="false"
      :footer="null"
      :open="liveKickout"
    >
      <p>{{ $t('classroom.modules.liveKickout.notice') }}</p>
      <Button type="primary" shape="round" size="large" @click.stop="handleClose">
        {{ $t('common.confirm') }} <span class="time-out">({{ timeCount }}s)</span>
      </Button>
    </Modal>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref } from 'vue'
import { emitter } from '@/hooks/useEventBus'
import { Modal, Button } from 'ant-design-vue'
import { useRouter } from 'vue-router'
defineProps({
  options: {
    type: Object,
    default: null
  }
})
const router = useRouter()
const liveKickout = ref(false)
const timeCount = ref(10)
let timer = null
const liveKickoutFun = ircMessage => {
  console.log(ircMessage, 'ircMessage')
  if (ircMessage) {
    emitter.emit('liveQuit')
    liveKickout.value = true
    timer = setInterval(() => {
      if (timeCount.value > 0) {
        timeCount.value--
      } else {
        handleClose()
        clearInterval(timer)
        timer = null
      }
    }, 1000)
  }
}
const handleClose = () => {
  liveKickout.value = false
  const curBackUrl = window.location.href.split('backUrl=')[1]
      // || '/home'
      let queryObject = {};
      const path = curBackUrl.split('?')[0] || '/home'
      if (curBackUrl) {
        // 拿到当前 URL 的查询部分
        let rawQuery = curBackUrl.split('?')[1];

        // 再次解码整个查询字符串
        rawQuery = decodeURIComponent(rawQuery);

        // 手动将解码后的查询字符串拆解成对象
        rawQuery.split('&').forEach(param => {
          const [key, value] = param.split('=');
          queryObject[key] = value;
        });
      }
      console.log(queryObject);
      router.push({
        path: path,
        query: {
          ...queryObject
        }
      })
}
onMounted(() => {
  emitter.on('live-kickout', liveKickoutFun)
})
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
  emitter.off('live-kickout', liveKickoutFun)
})
</script>
<style lang="scss">
.kickout-root {
  .kickout-mask {
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 3000; // 高于全局悬浮层
  }
  p {
    font-size: 16px;
    font-weight: 500;
    color: #172b4d;
    line-height: 21px;
    text-align: center;
    margin-bottom: 20px;
  }
  .kickout-wrap {
    z-index: 3000; // 高于全局悬浮层
    .kickout {
      top: 50%;
      margin-top: -150px;
      .kickout-content {
        border-radius: 16px;
        padding: 40px 10px 20px;
        .kickout-body {
          height: 100px;
          display: flex;
          align-items: center;
          flex-direction: column;
          padding: 0;
          .ant-btn.ant-btn-primary {
            width: 220px;
            height: 40px;
          }

          .time-out {
            margin-left: 5px;
          }
        }
      }
    }
  }
}
</style>
