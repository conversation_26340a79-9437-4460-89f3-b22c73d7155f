<template>
  <div
    class="syllabus-container"
    :data-log="`课程卡片${this.syllabusData.classId}-${syllabusData.planId}`"
  >
    <div v-if="scheduleType === 'LESSON'">
      <div class="syllabus-card">
        <!-- todo 待确认 -->
        <!-- <div
          :class="['card-tag', syllabusData.isChange == 1 ? 'green-tag' : 'blue-tag']"
          v-if="lessonType"
        >
          <span class="tag-left"></span>
          <span class="tag-center">{{ lessonType }}</span>
          <span class="tag-right"></span>
        </div> -->

        <!-- <div v-if="syllabusData.isChange" class="lesson-status">
          {{ $t('courses.detail.transferred') }}
        </div> -->
        <div class="time-number">
          <span class="number">{{ padIndex }}</span>
          <span class="datetime">
            <span class="content">{{ syllabusData.classDateDesc }}</span>
            <span class="content"> {{ syllabusData.startTime }} - {{ syllabusData.endTime }} </span>
          </span>
        </div>
        <div class="syllabus-info">
          <div
            :class="[
              'header',
              isHasMoreContent ? '' : 'no-content',
              syllabusData.status === 2 ? 'header-active' : ''
            ]"
          >
            <div class="title">
              {{ syllabusData.lessonName }}
            </div>
            <div class="button-wrapper flex-layout">
              <template v-if="syllabusData.platformType !== 'OFFLINE'">
                <!-- 家长旁听按钮 -->
                <!-- 一期不展示家长旁听 -->
                <!-- <a-button
                  v-if="showParentAuditBtn"
                  type="link"
                  shape="round"
                  class="color-orange parent-btn"
                  :loading="parentAuditLoading"
                  v-mark-scroll="'courseDetail'"
                  @click.stop="toParentAudit"
                  v-log
                  :data-log="$t('courses.courseCard.parentAudit')"
                >
                  {{ $t('courses.courseCard.parentAudit') }}
                </a-button> -->
                <!-- 活动课不能看回放 -->
                <!-- v-mark-scroll="'courseDetail'" -->
                <a-button
                  v-if="status !== 7"
                  type="link"
                  shape="round"
                  :class="[classroomButton.buttonClassName]"
                  :loading="loading"
                  @click.stop="handleClassroomButton"
                  :data-log="classroomButton.buttonName"
                >
                  <div class="enter-title">
                    <div
                      v-if="classroomButton.buttonClassName.includes('enter-live')"
                      class="enter-icon"
                    >
                      <WaveAnimation color="#ff9f0a" />
                    </div>
                    <span>{{ classroomButton.buttonName }}</span>
                  </div>
                </a-button>
                <div v-if="status === 3 && showGenerateTips" class="tooltip">
                  <span>{{ $t('courses.detail.playBackTip') }}</span>
                  <label></label>
                </div>
              </template>
              <!-- v-mark-scroll="'courseDetail'" -->
              <a-button
                v-else
                type="link"
                shape="round"
                class="color-orange"
                :loading="loading"
                @click.stop="teachMethod"
                :data-log="$t('courses.faceToFace.attendClass')"
              >
                {{ $t('courses.faceToFace.attendClass') }}
              </a-button>
            </div>
          </div>
          <div
            :class="['footer', syllabusData.status === 2 && !isShowMore ? 'mt16' : '']"
            v-if="isHasMoreContent"
          >
            <div v-if="isShowMore" class="more" @click="isShowMore = false">
              {{ $t('courses.expand_more') }}
            </div>
            <LessonResource
              class="footer-content"
              v-else
              :classInfo="syllabusData"
              :lessonItem="syllabusData"
            />
            <div v-if="!isShowMore" @click="isShowMore = true" class="shouqi">
              {{  $t('courses.expand_fold') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <div class="exam-card">
        <div class="exam-tag">
          <div class="img"></div>
        </div>
        <div class="exam-info">
          <div class="title">
            {{ syllabusData.examName }}
          </div>
        </div>
        <div class="button-wrapper">
          <!-- v-mark-scroll="'courseDetail'" -->
          <a-button
            type="link"
            shape="round"
            :class="[examButton.buttonClassName]"
            :loading="examBtnLoading"
            @click.stop="handleExamButton"
            :data-log="examButton.buttonName"
          >
            {{ examButton.buttonName }}
          </a-button>
          <div v-if="showExamTips" class="tooltip">
            <span>{{ $t('courses.detail.examTip') }}</span>
            <label></label>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import LessonResource from './LessonResource.vue'
import WaveAnimation from '../Common/WaveAnimation.vue'
// import { nativeApi } from 'utils/electronIpc'
// import moment from 'moment'
import { getTeachMethod, getUserInClass } from '@/api/home/<USER>'
import { subPlatformTypeMap } from '@/utils/sensors.js'
// import { deleteFilesByPath } from 'utils/util'
// import { existsSync } from 'fs'
// import logger from '@/utils/logger'
// import "@lottiefiles/lottie-player";
// import { getAssetPath } from '@/utils/assetPath'
export default {
  name: 'SyllabusCard',
  props: {
    isOpenParentEntry: {
      type: Boolean,
      default: false
    },
    index: {
      default: 0,
      type: Number
    },
    teachers: {
      default: () => {
        return []
      },
      type: Array
    },
    courseDatas: {
      default: () => {
        return {}
      },
      type: Object
    },
    syllabusData: {
      default: () => {
        return {}
      },
      type: Object
    },
    scheduleType: {
      default: 'LESSON',
      type: String
    }
  },
  components: {
    LessonResource,
    WaveAnimation
  },
  // filters: {
  //   handleAssignmentEndTime(datetime, local) {
  //     const formatConfig = {
  //       us: 'MM/DD HH:mm',
  //       uk: 'DD/MM HH:mm',
  //       sg: 'DD/MM HH:mm',
  //       cn: 'MM/DD HH:mm'
  //     }
  //     const newDatetime = moment(datetime).format(formatConfig[local])
  //     return newDatetime
  //   }
  // },
  computed: {
    isHasMoreContent() {
      return (
        (this.syllabusData.previewQuestion && this.syllabusData.previewQuestion?.status != 1) ||
        this.syllabusData.inClassExam ||
        (this.syllabusData.assignment && this.syllabusData.assignment?.status !== 1) ||
        !!this.syllabusData.material?.count ||
        (this.syllabusData.performance && this.syllabusData.performance.available)
      )
    },
    // 是否显示家长旁听按钮
    showParentAuditBtn() {
      // 直播中且正式课且云控开启该功能且不是伪小班
      return (
        this.status == 2 &&
        this.syllabusData.lessonType == 'FORMAL' &&
        this.isOpenParentEntry &&
        this.syllabusData.subPlatformType != 1
      )
    },
    padIndex() {
      return this.index <= 9 ? '0' + this.index : this.index
    },
    lessonType() {
      const typeMap = {
        PLAYBACK: this.$t('courses.lessonType.playback'),
        AUDITION: this.$t('courses.lessonType.audition'),
        TEMPORARY: this.$t('courses.lessonType.temporary'),
        TRANSFERRED: this.$t('courses.detail.transferred')
      }
      return typeMap[this.syllabusData.lessonFlag]
    },
    // Classroom按钮样式信息
    classroomButton() {
      if (this.isAudition == 1 && this.status == 2) {
        return {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.playButton.startAuditing')
        }
      }
      const infoMap = {
        1: {
          buttonClassName: 'color-gray',
          buttonName: this.$t('courses.detail.playButton.startSoon')
        },
        2: {
          buttonClassName: 'color-orange enter-live',
          buttonName: this.$t('courses.detail.playButton.live')
        },
        3: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.playButton.playbackGenerating')
        },
        4: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.playButton.playback')
        },
        5: {
          buttonClassName: 'color-gray',
          buttonName: this.$t('courses.detail.playButton.playbackExpired')
        },
        6: {
          buttonClassName: 'color-text',
          buttonName: this.$t('courses.detail.playButton.unpaid')
        }
      }
      return infoMap[this.status]
    },
    // 阶段考试按钮样式信息
    examButton() {
      // examStatus: 1:未发布、2:已发布、3:已作答、4:已批改、5:已过期
      const examMap = {
        1: {
          buttonClassName: 'color-gray',
          buttonName: this.$t('courses.detail.exam.status[0]')
        },
        2: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[1]')
        },
        3: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[2]')
        },
        4: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[3]')
        },
        5: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[4]')
        }
      }
      return examMap[this.syllabusData.examStatus]
    },
    isAudition() {
      return this.syllabusData.lessonType === 'AUDITION' ? 1 : 0
    }
  },
  data() {
    const status = this.scheduleType === 'LESSON' ? this.syllabusData.status : 1
    return {
      isShowMore: true,
      demo: false,
      // local: this.$store.state.common.local,
      // 状态
      // 1: 待开始 2:直播中 3:回放待生成 4:回放已生成
      status,
      loading: false, // 进入教室按钮loading
      parentAuditLoading: false, // 家长旁听按钮loading
      showGenerateTips: false,
      showExamTips: false,
      timer: null,
      examTimer: null,
      showExpiredTip: false,
      previewBtnLoading: false, // 课前侧按钮loading
      assignmentBtnLoading: false, // 作业侧按钮loading
      examBtnLoading: false, // 阶段考试按钮loading
      classExamBtnLoading: false // 课中考试按钮loading
    }
  },
  methods: {
    showNextBtn(status) {
      if (status == 4 || status == 5 || status == 6) {
        return true
      } else {
        return false
      }
    },
    fullColumn(syllabusData) {
      if (syllabusData.previewQuestion && syllabusData.assignment && syllabusData.material) {
        if (
          syllabusData.assignment.status == 1 &&
          syllabusData.previewQuestion.status == 1 &&
          syllabusData.material.count <= 0
        ) {
          return true
        } else {
          return false
        }
      }
      return false
    },
    handleMouseEnter() {
      this.showExpiredTip = true
    },
    handleMouseout() {
      this.showExpiredTip = false
    },
    async handleExamButton(e) {
      e.stopPropagation()
      // 未发布
      if (this.syllabusData.examStatus == 1) {
        this.showExamTips = true
        clearTimeout(this.examTimer)
        this.examTimer = setTimeout(() => {
          this.showExamTips = false
        }, 3000)
        return
      }
      this.examBtnLoading = true
      this.$router.push({
        path: '/home/<USER>',
        query: {
          ...this.syllabusData.jumpParams,
          fromTo: this.$route.fullPath
        }
      })
    },
    // 家长旁听，进入教室需要传 isParentAudition：1
    toParentAudit() {
      this.parentAuditLoading = true
      // 埋点
      this.clickButtonEvent('hw_parent_class_enter_click')
      // playback：1回放 0直播,isParent: 是否家长，0否 1是
      const params = {
        isAudition: 1,
        playback: 0,
        isParent: 1
      }
      this.jumpToClassRoom(params)
    },
    async handleClassroomButton(e) {
      e.stopPropagation()
      // 待开始
      if (this.status == 1 || this.status == 5 || this.status == 6) {
        return
      }
      // 回放待生成
      if (this.status == 3) {
        this.showGenerateTips = true
        clearTimeout(this.timer)
        this.timer = setTimeout(() => {
          this.showGenerateTips = false
        }, 3000)
        return
      }
      // 回放已生成
      if (this.status == 4) {
        this.loading = true
        // this.deletePlayBackFiles()
        // 埋点
        this.clickButtonEvent('hw_stu_class_list_click', {
          previous_source: '学习中心二级页面',
          plan_mode: '回放'
        })
        // playback：1回放 0直播,isParent: 是否家长，0否 1是
        const params = {
          isAudition: this.isAudition,
          playback: 1,
          isParent: 0
        }
        this.jumpToClassRoom(params)
        return
      }
      // 直播中
      if (this.status == 2) {
        this.loading = true
        // 正式学生埋点
        if (this.isAudition == 0) {
          // 埋点
          this.clickButtonEvent('hw_stu_class_list_click', {
            previous_source: '学习中心二级页面',
            plan_mode: '直播'
          })
        }
        // 直接进入直播间
        // playback：1回放 0直播,isParent: 是否家长，0否 1是
        const params = {
          isAudition: this.isAudition,
          playback: 0,
          isParent: 0,
          planId: this.syllabusData.planId,
          classId: this.syllabusData.classId,
          backUrl: encodeURI(
            `/home/<USER>
          )
        }
        this.jumpToClassRoom(params, this.beforeJumpToClassRoom)
      }
    },
    // /**
    //  * 处理userData里playback文件夹
    //  */
    // async deletePlayBackFiles() {
    //   const coursewareDir = await nativeApi.getPathByName('userData')
    //   const dirPath = `${coursewareDir}/playback`
    //   console.log('删除playback文件夹内容', !existsSync(dirPath))
    //   if (!existsSync(dirPath)) {
    //     logger.send({
    //       tag: 'deletePlayback',
    //       content: { msg: 'playback文件夹不存在' }
    //     })
    //     return
    //   }
    //   deleteFilesByPath(dirPath, 7) // 保持7天
    // },
    // 如何上课
    teachMethod(e) {
      e.stopPropagation()
      // 待开始
      // if (this.status == 1 || this.status == 5 || this.status == 6) {
      //   return
      // }
      const params = {
        data: {
          classId: this.syllabusData.classId,
          orderNum: this.syllabusData.orderNum
        }
      }
      getTeachMethod(params)
        .then(res => {
          if (res.code === 0) {
            const attendData = {
              zoom: res.data.zoom,
              classIn: res.data.classIn,
              platformType: this.syllabusData.platformType,
              offline: res.data.offline
            }
            this.$emit('showAttendClass', attendData)
          }
        })
        .catch(err => {
          console.error('getTeachMethod', err)
        })
    },
    // 跳转直播页
    jumpToClassRoom(params, beforeJump) {
      const obj = {
        planId: this.syllabusData.planId,
        classId: this.syllabusData.classId,
        subPlatformType: this.syllabusData.subPlatformType,
        bizId: this.syllabusData.bizId,
        lessonType: this.syllabusData.lessonType,
        from: 'syllabus', // 埋点需要判断一级列表还是二级列表
        backUrl: encodeURI(
          `/home/<USER>
        ),
        ...params
      }
      const goClassRoom = query => {
        if (params.playback) {
          // todo 弹窗处理当前不支持回放，需要下载app 才行
          // this.$router.push({
          //   path: '/playback/ready-class',
          //   query: query
          // })
          this.$emit(
            'showDownLoadApp',
            this.$t(
              'today.sorry_course_playback_only_available_think_academy_app_future_web_support_15_erc'
            )
          )
          this.loading = false
        } else {
          // 如果是家长旁听，也需要进行拦截
          if (params.isAudition === 1 && params.isParent === 1 && obj.subPlatformType === 0) {
            this.$emit(
              'showDownLoadApp',
              this.$t('today.sorry_this_course_requires_think_academy_app_15_2dh')
            )
            this.parentAuditLoading = false
          } else {
            this.$router.push({
              path: '/classroom',
              query: {
                ...query,
                nps: 1
              }
            })
          }
        }
      }
      // 判断是否有跳转前回调，否则默认跳转
      if (typeof beforeJump === 'function') {
        // todo 需要确认是不是大班课若是大班课也提示同样的让去下载
        if (obj.subPlatformType === 0) {
          this.$emit(
            'showDownLoadApp',
            this.$t('today.sorry_this_course_requires_think_academy_app_15_2dh')
          )
          this.loading = false
        } else if (obj.subPlatformType === 2) {
          beforeJump(obj, goClassRoom)
        }
      } else {
        goClassRoom(obj)
      }
    },
    // 跳转前回调事件
    async beforeJumpToClassRoom(query, callback) {
      // 学生点击进入教室需要判断当前是否在教室中
      const needConfirm = await this.studentInClass()
      if (needConfirm) {
        this.loading = false

        this.$emit('openConfirm', query)
      } else {
        callback(query)
      }
    },
    async studentInClass() {
      // 校验是否同一设备，若是同一设备不限制进入操作
      let res = await getUserInClass({ planId: this.syllabusData.planId })
      if (res.code == 0) {
        // maybeInClass  是否在课堂中，1 - 在，0 - 不在
        // lastInClassSameDevice 用户上次进入课堂所使用的设备和当前设备是否相同，1 - 相同，2 - 不同
        return res.data.lastInClassSameDevice == 2 && res.data.maybeInClass == 1
      } else {
        return false
      }
    },
    // 文件数量
    showCount(syllabusData) {
      if (syllabusData && syllabusData.material) {
        if (syllabusData.material.count >= 2) {
          return `${syllabusData.materia.count}Files`
        } else {
          console.log(syllabusData.material.count)
          return `${syllabusData.material.count}File`
        }
      }
    },
    // 构建神策埋点数据
    buildSensorsData() {
      return {
        plan_id: this.syllabusData.planId,
        class_id: this.syllabusData.classId,
        package_id: this.syllabusData.packageId,
        lesson_type: subPlatformTypeMap[this.syllabusData.subPlatformType]
      }
    },
    // 按钮埋点事件
    clickButtonEvent(eventName, params = {}) {
      this.$sensors.track(eventName, {
        ...this.buildSensorsData(),
        ...params
      })
    }
  },
  mounted() {
    window.localStorage.removeItem('previewStatus')
    this.isShowMore = !(this.syllabusData.status === 2)
  },
  destoryed() {
    clearTimeout(this.timer)
    clearTimeout(this.examTimer)
  }
}
</script>

<style lang="scss" scoped>
.syllabus-container {
  display: flex;
  flex-direction: column;

  .exam-card {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    //padding: 0 20px 0 30px;
    // margin-bottom: 10px;
    border-radius: 8px;
    border: 1px solid #e6e6e6;
    padding-right: 20px;
    .exam-tag {
      position: absolute;
      top: -1px;
      left: -1px;
      width: 54px;
      height: 64px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #fff4dd;
      border-radius: 8px 0px 0px 8px;
      .img {
        width: 32px;
        height: 28px;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 100%;
        background-image: url('../../assets/images/courses/stage-exam.png');
      }
    }

    .exam-info {
      color: #172b4d;
      padding-left: 70px;
      //width: 100%;

      .title {
        font-size: 16px;
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
        color: #222222;
        line-height: 64px;
      }
    }
  }

  .syllabus-card {
    .time-number {
      margin-bottom: 8px;
      .number {
        height: 18px;
        font-size: 16px;
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
        color: #222222;
        line-height: 18px;
      }
      .datetime {
        height: 18px;
        .content {
          margin-left: 8px;
          font-size: 14px;
          font-family: Montserrat-Medium, Montserrat;
          font-weight: 500;
          color: #7a7a7a;
          line-height: 18px;
        }
      }
    }
    .card-tag {
      display: flex;
      position: absolute;
      right: 0;
      top: 0;

      .tag-left {
        width: 13px;
        height: 22px;
      }

      .tag-center {
        display: inline-block;
        height: 22px;
        font-size: 10px;
        font-weight: 600;
        color: #ffffff;
        line-height: 12px;
      }

      .tag-right {
        width: 10px;
        height: 22px;
      }
    }

    .blue-tag {
      .tag-left {
        background: url('../../assets/images/courses/blue-left.png') no-repeat;
        background-size: 100%;
      }

      .tag-center {
        background: url('../../assets/images/courses/blue-center.png') round;
        background-size: 100%;
      }

      .tag-right {
        background: url('../../assets/images/courses/blue-right.png') no-repeat;
        background-size: 100%;
      }
    }

    .green-tag {
      .tag-left {
        background: url('../../assets/images/courses/green-left.png') no-repeat;
        background-size: 100%;
      }

      .tag-center {
        background: url('../../assets/images/courses/green-center.png') round;
        background-size: 100%;
      }

      .tag-right {
        background: url('../../assets/images/courses/green-right.png') no-repeat;
        background-size: 100%;
      }
    }

    .lesson-status {
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px 10px 10px 18px;
      font-size: 10px;
      text-align: center;
      font-weight: 600;
      border-radius: 3px;
      background-image: url('../../assets/images/courses/course-transferred.png');
      background-size: cover;
      color: #fff;
      z-index: 88;
    }

    .syllabus-info {
      width: 100%;
      border-radius: 8px;
      //border: 1px solid #E6E6E6;
      .header {
        border: 1px solid #e6e6e6;
        border-bottom: none;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 20px 14px;
        border-radius: 8px 8px 0px 0px;
        // todo 文本宽度不能挤压隐藏怎么办，右侧按钮由于多语言影响可能会很长
        .title {
          height: 19px;
          font-size: 16px;
          font-family: Montserrat-SemiBold, Montserrat;
          font-weight: 600;
          color: #222222;
          line-height: 19px;
        }
      }
      .header-active {
        background: #ffaa0a;
        border-radius: 8px 8px 0px 0px;
        border: none;
        .title {
          color: #ffffff;
        }
      }
      .no-content {
        border-bottom: 1px solid #e6e6e6;
        border-radius: 8px;
      }
      .footer {
        border: 1px solid #e6e6e6;
        border-top: none;
        padding: 0 20px;
        border-radius: 0 0 8px 8px;
        .more {
          border-top: 1px solid #e6e6e6;
        }
        .more::after {
          background-image: url('../../assets/images/courses/icon-more.png');
        }
        .more:hover::after {
          background-image: url('../../assets/images/courses/icon-more-active.png');
        }
        .shouqi::after {
          background-image: url('../../assets/images/courses/icon-shouqi.png');
        }
        .shouqi:hover::after {
          background-image: url('../../assets/images/courses/icon-shouqi-active.png');
        }
        .more,
        .shouqi {
          cursor: pointer;
          padding: 16px 0px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 14px;
          font-family: Montserrat-Medium, Montserrat;
          font-weight: 500;
          color: #a6a6a6;
          line-height: 16px;
          &::after {
            margin-left: 4px;
            content: ''; /* 必须要有 content 属性 */
            display: inline-block;
            width: 12px; /* 设置图像宽度 */
            height: 12px; /* 设置图像高度 */
            background-size: contain; /* 或 cover，根据需求调整 */
            background-repeat: no-repeat;
          }
        }
        .more:hover,
        .shouqi:hover {
          color: #ff9f0a;
        }
        .footer-content {
          margin-top: 0px;
          .content-item {
            width: 377px !important;
          }
        }
      }
      .mt16 {
        padding-top: 16px;
      }
    }
  }

  .bottom-info {
    // height: 110px;
    width: 100%;
    border-radius: 10px;
    background: #fff;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;

    .wrapper {
      display: flex;
      flex-wrap: wrap;

      section {
        width: 50% !important;
        // flex: 1;
      }
    }

    .lesson-report {
      height: 54px;

      .report {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 54px;
        width: 180px;
        margin: 0 auto;

        span:first-child {
          display: inline-block;
          font-size: 14px;
          font-weight: 600;
          color: #ffaa0a;
          line-height: 16px;
          cursor: pointer;
        }

        span:last-child {
          cursor: pointer;
          display: inline-block;
          width: 24px;
          height: 24px;
          margin-left: 10px;
          background-repeat: no-repeat;
          background-position: 0 0;
          background-size: 100%;
          background-image: url('../../assets/images/courses/learn-report.png');
        }
      }
    }

    .classroom,
    .resources,
    .preview,
    .assignment {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px 12px 10px;

      .class-info {
        display: flex;
        align-items: center;
        .classroom-icon {
          background-image: url('../../assets/images/courses/classroom.png');
        }
        .examination-icon {
          background-image: url('../../assets/images/courses/examination.png');
        }

        .common-icon {
          width: 30px;
          height: 30px;
          margin-right: 8px;
          background-repeat: no-repeat;
          background-position: 0 0;
          background-size: 100%;
          flex-shrink: 0;
        }

        label {
          display: flex;
          flex-direction: column;

          .title {
            color: #172b4d;
            font-size: 14px;
            overflow: hidden;
            word-break: break-word;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }

          .content {
            font-size: 12px;
            color: #a2aab8;
          }
        }
      }

      &.full-column {
        width: 100% !important;
        // border-radius: 10px;
      }
    }
  }
}

:deep(.button-wrapper) {
  position: relative;
  margin-left: 10px;

  .expired-tip {
    width: 480px;
    height: 58px;
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    position: absolute;
    border-radius: 10px;
    right: -26px;
    top: -70px;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      width: 28px;
      height: 6px;
      position: absolute;
      bottom: -6px;
      right: 50px;
      background-image: url('../../assets/images/courses/tip-icon.png');
      background-size: cover;
    }

    .title {
      width: 320px;
      color: #fff;
    }

    .btn {
      width: 120px;
      height: 32px;
      line-height: 32px;
      //background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
      border-radius: 16px;
      display: inline-block;
      color: #fff;
      cursor: pointer;
      margin-left: 15px;
    }
  }

  > button {
    height: 32px;
    box-shadow: none;
    display: flex;
    align-items: center;
    position: relative;
    > span {
      font-size: 14px !important;
      font-family: Montserrat-SemiBold, Montserrat !important;
      font-weight: 600 !important;
    }
  }

  .color-gray {
    background: #f9f9f9;
    border-color: #f9f9f9;
    color: #a6a6a6;
  }

  .color-text {
    background: #f9f9f9;
    border-color: #f9f9f9;
    color: #a6a6a6;
    cursor: default;
  }

  .color-orange {
    background: #fff9ec;
    border-color: #fff9ec;
    color: #ff9f0a;
  }

  .ant-btn .anticon {
    margin-right: 10px;
  }

  .parent-btn {
    .anticon {
      margin-right: 4px;
    }
  }

  .enter-live {
    .enter-icon {
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }
  }
  .enter-title {
    display: flex;
    align-items: center;
    font-size: 14px;
    > span {
      font-family: Montserrat-SemiBold, Montserrat;
      font-weight: 600 !important;
      color: #ff9f0a;
    }
  }

  .color-green {
    background: #f2fcf8;
    border-color: #f2fcf8;
    color: #02ca8a;
  }

  .unpaid {
    font-size: 14px;
    color: #a2aab8;
    margin-right: 10px;
  }

  .finish-icon {
    width: 16px;
    height: 16px;
    // right: 30px;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100%;
    background-image: url('../../assets/images/courses/finish.png');
    margin-left: 5px;
  }

  .tooltip {
    position: absolute;
    width: 428px;
    height: 42px;
    right: 0;
    background: #333333;
    padding: 10px 15px;
    color: #ffffff;
    text-align: center;
    top: -53px;
    border-radius: 20px;

    label {
      position: absolute;
      bottom: -6px;
      width: 28px;
      height: 6px;
      right: 30px;
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100%;
      background-image: url('../../assets/images/courses/tooltip.png');
    }
  }
}
.header-active .button-wrapper .color-orange {
  background: #fff;
  border-color: #fff;
}
.flex-layout {
  display: flex;

  .parent-btn {
    padding: 0px 16px;
    font-size: 14px;
    margin-right: 14px;
    font-weight: 500;
  }
}

:deep(.footer-content) {
  .content-item {
    width: 377px !important;
  }
}
:deep(.ant-modal) {
  top: 240px;
}

:deep(.ant-modal-body) {
  padding: 15px;

  // width: 386px;
  .content {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .title-icon {
      width: 80px;
      height: 60px;
      position: absolute;
      top: -38px;
      display: inline-block;
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100%;
      background-image: url('../../assets/images/courses/exprid-title.png');
    }

    .title {
      font-size: 14px;
      font-weight: 600;
      color: rgba(23, 43, 77, 1);
      display: block;
      margin-top: 20px;
      text-align: center;
    }

    .sub-title {
      font-size: 14px;
      color: rgba(162, 170, 184, 1);
      margin-top: 4px;
    }

    .btn {
      display: flex;
      margin: 20px 0 5px 0;
      width: 100%;
      justify-content: space-between;
      padding: 0 18px;

      .no {
        width: 147px;
        height: 48px;
        background: rgba(255, 243, 220, 1);
        border-radius: 24px;
        color: #ffaa0a;
        font-size: 16px;
        line-height: 48px;
        text-align: center;
        cursor: pointer;
        transition:
          color 0.15s ease-in-out,
          background-color 0.15s ease-in-out,
          border-color 0.15s ease-in-out,
          box-shadow 0.15s ease-in-out;
      }

      .yes {
        width: 147px;
        height: 48px;
        background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
        border-radius: 24px;
        font-size: 16px;
        line-height: 48px;
        color: #fff;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}

:deep(.ant-modal-mask) {
  background-color: rgba(0, 0, 0, 0.5);
}
</style>
