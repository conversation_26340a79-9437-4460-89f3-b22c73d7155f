<template>
  <div class="today-lessons">
    <div v-if="props.nextLesson">
      <div class="title">{{ $t('today.next_lesson_15_xv3') }}</div>
      <LessonCard :lessonItem="props.nextLesson" />
    </div>
    <div v-if="props.lastLesson">
      <div class="title">{{ $t('today.last_class_15_i1j') }}</div>
      <LessonCard :lessonItem="props.lastLesson" @showDownLoadApp="showDownLoadApp"/>
    </div>
     <!-- 下载app 看回放或者上大班课 -->
     <GoDownloadModal :visible="showDownload" :title="downLoadTipText" @closeModal="showDownload = false"/>
  </div>
</template>
<script setup>
import LessonCard from './LessonCard.vue'
import GoDownloadModal from './GoDownLoadApp.vue'
import { ref } from 'vue'
const props = defineProps({
  nextLesson: {
    type: Object, // 类型
    default: null,  // 默认值
  },
  lastLesson: {
    type: Object, // 类型
    default: null,  // 默认值
  }
})
const showDownload = ref(false)
const downLoadTipText = ref('')
const showDownLoadApp = (title) => {
  showDownload.value = true
  downLoadTipText.value = title
}
</script>
<style lang="less" scoped>
.today-lessons {
  background: #FFFFFF;
  border-radius: 12px;
  padding: 0 20px 20px;
  width: 878px;
  .title {
    height: 22px;
    font-size: 20px;
    font-family: Montserrat-Bold, Montserrat;
    font-weight: bold;
    color: #222222;
    line-height: 22px;
    padding-top: 20px;
    box-sizing: content-box;
  }
}
</style>
