<template>
  <a-modal
    v-model:open="showModal"
    :width="450"
    :footer="null"
    :maskClosable="false"
    :closable="false"
    :centered="true"
    :keyboard="false"
    dialogClass="modal-simple close-color-white"
  >
    <div class="how-to-attend">
      <div class="header">{{ $t('courses.faceToFace.attendClass') }}</div>
      <template v-if="data.platformType === 'ZOOM'">
        <ul class="bg-list">
          <li class="list-item left-right">
            <div class="label">{{ $t('courses.faceToFace.location') }}</div>
            <div class="value">Zoom</div>
          </li>
          <li class="list-item can-copy">
            <div class="top">
              <div class="label">{{ $t('courses.faceToFace.zoomId') }}</div>
              <div class="copy-btn" @click="copyText('999-999-999')">
                <span>{{ $t('courses.faceToFace.copy') }}</span>
                <img src="@/assets/images/courses/copy.png" alt="" />
              </div>
            </div>
            <div class="value">{{ data.zoom && data.zoom.id }}</div>
          </li>
          <li class="list-item can-copy">
            <div class="top">
              <div class="label">{{ $t('courses.faceToFace.zoomPassword') }}</div>
              <div class="copy-btn">
                <span>{{ $t('courses.faceToFace.copy') }}</span>
                <img src="@/assets/images/courses/copy.png" alt="" />
              </div>
            </div>
            <div class="value">{{ data.zoom && data.zoom.password }}</div>
          </li>
        </ul>
        <div class="strong-words">
          <span class="strong">{{ $t('courses.faceToFace.zoomDownload') }}</span>
          {{ $t('courses.faceToFace.zoomDescribe') }}
        </div>
      </template>
      <template v-else-if="data.platformType === 'CLASS_IN'">
        <ul class="bg-list">
          <li class="list-item left-right">
            <div class="label">{{ $t('courses.faceToFace.location') }}</div>
            <div class="value">Classin</div>
          </li>
          <li class="list-item can-copy">
            <div class="top">
              <div class="label">{{ $t('courses.faceToFace.classinLink') }}</div>
              <div class="copy-btn">
                <span>{{ $t('courses.faceToFace.copy') }}</span>
                <img src="@/assets/images/courses/copy.png" alt="" />
              </div>
            </div>
            <div class="value">{{ data.classIn && data.classIn.href }}</div>
          </li>
        </ul>
        <div class="strong-words">
          <span class="strong">{{ $t('courses.faceToFace.classinDownload') }}</span>
          {{ $t('courses.faceToFace.classinDescribe') }}
        </div>
      </template>
      <template v-else-if="data.platformType === 'OFFLINE' || data.platformType === 'DUAL'">
        <div class="main-content">
          <div class="label">{{ $t('courses.faceToFace.location') }}</div>
          <div class="value">{{ data.offline.venueAddress + data.offline.classRoomName }}</div>
        </div>
        <div class="subtip">{{ $t('courses.faceToFace.classroomDescribe') }}</div>
      </template>
      <div v-else class="call-container">
        <p class="words">{{ $t('courses.faceToFace.ipadDescribe') }}</p>
        <p class="strong">844-844-6587</p>
      </div>
      <div class="button-wrapper">
        <a-button size="large" type="primary" shape="round" block @click="closeModal">
          {{ $t('common.gotIt') }}
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showModal: false
    }
  },
  watch: {
    visible(val) {
      this.showModal = val
    },
  },
  methods: {
    copyText(text) {
      const input = document.createElement('input')
      input.value = text
      document.body.appendChild(input)
      input.select()
      if (document.execCommand('copy')) {
        document.execCommand('copy')
        console.log('复制成功')
      } else {
        console.log('复制失败')
      }
      document.body.removeChild(input)
    },
    closeModal() {
      this.showModal = false
      this.$emit('closeModal')
    }
  }
}
</script>

<style lang="scss" scoped>
.how-to-attend {
  position: relative;
  width: 400px;
  padding: 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 16px;
  p {
    margin-bottom: 0;
  }
  ul,
  li {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .header {
    height: 55px;
    padding-top: 18px;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    color: #172b4d;
    line-height: 21px;
  }
  .bg-list {
    margin-top: 16px;
    padding: 18px 16px;
    background: rgba(222, 226, 231, 0.2);
    border-radius: 8px;
    .list-item {
      border-bottom: 1px solid rgba(222, 226, 231, 0.2);
      .label {
        height: 16px;
        margin-right: 16px;
        color: #a2aab8;
      }
      .value {
        width: 100%;
        height: 16px;
        color: #172b4d;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .copy-btn {
        width: 58px;
        height: 20px;
        background: rgba(222, 226, 231, 0.3);
        border-radius: 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        span {
          height: 14px;
          font-size: 12px;
          color: #a2aab8;
          line-height: 14px;
        }
        img {
          width: 20px;
        }
      }
      &.left-right {
        padding-bottom: 18px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .top {
        margin-top: 18px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      &:last-of-type {
        border-bottom: 0;
      }
    }
  }
  .strong-words {
    height: 56px;
    margin-top: 16px;
    margin-bottom: 34px;
    font-size: 12px;
    line-height: 14px;
    color: #a2aab8;
    text-align: center;
    .strong {
      color: #3370ff;
    }
  }
  .main-content {
    height: 84px;
    margin-top: 16px;
    padding: 18px 16px;
    background: rgba(222, 226, 231, 0.2);
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    .label {
      height: 16px;
      margin-right: 16px;
      color: #a2aab8;
      line-height: 16px;
    }
    .value {
      width: 100%;
      color: #172b4d;
    }
  }
  .subtip {
    height: 78px;
    padding-top: 16px;
    text-align: center;
    font-size: 12px;
    color: #a2aab8;
    line-height: 14px;
  }
  .call-container {
    height: 98px;
    padding-top: 16px;
    color: #a2aab8;
    line-height: 20px;
    text-align: center;
    .strong {
      color: #3370ff;
    }
  }
}
:deep(.button-wrapper) {
  > button {
    background: #FFAA0A !important;
  }
}
</style>
