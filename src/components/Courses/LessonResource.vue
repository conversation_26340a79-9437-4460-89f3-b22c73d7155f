<template>
  <div
  v-if="
    classInfo.previewQuestion ||
      classInfo.inClassExam ||
      classInfo.assignment ||
      !!classInfo.material?.count ||
      classInfo.performance
  "
  class="content"
>
  <!-- 课前测 -->
  <div
    v-if="classInfo.previewQuestion && classInfo.previewQuestion.status != 1"
    class="content-item"
    @click="handlePreviewButton(classInfo.previewQuestion)"
  >
    <div class="info">
      <img class="icon" src="@/assets/images/home/<USER>" />
      <div class="titleBox">
        <span class="text">
          {{
            classInfo.previewQuestion.title || $t('courses.previewQuestion.title')
          }}</span
        >
        <span class="subTitle">{{ classInfo.previewQuestion.endTime }}</span>
      </div>
    </div>
    <span class="files"> {{ previewQuestionButton.buttonName }}</span>
    <i v-if="showNextBtn(classInfo.previewQuestion.status)" class="iconfont icon-gengduo icon-view"></i>
  </div>
  <!-- 课中考试 -->
  <div v-if="classInfo.inClassExam" class="content-item" @click="handleClassExam(classInfo.inClassExam)">
    <div class="info">
      <img class="icon" src="@/assets/images/home/<USER>" />
      <div class="titleBox">
        <span class="text">
          {{ classInfo.inClassExam.title || $t('today.homework.exam') }}</span
        >
        <span
          class="subTitle"
          v-if="
            !classInfo.inClassExam.report.canViewReport &&
              [3, 4, 5].includes(classInfo.inClassExam.status)
          "
          >{{
            $t('courses.detail.reportTips', {
              canViewTime: classInfo.inClassExam.report.canViewTime
            })
          }}</span
        >
      </div>
    </div>
    <span class="files"> {{ examReportButton?.buttonName }}</span>
    <i v-if="
        !(
          !classInfo.inClassExam.report.canViewReport &&
          [3, 4, 5].includes(classInfo.inClassExam.status)
        )
      " class="iconfont icon-gengduo icon-view"></i>
  </div>
  <!-- 作业 -->
  <div
    v-if="classInfo.assignment && classInfo.assignment.status != 1"
    class="content-item"
    @click="handleAssignmentButton(classInfo.assignment)"
  >
    <div class="info">
      <img class="icon" src="@/assets/images/home/<USER>" />
      <div class="titleBox">
        <span class="text"> {{ $t('courses.assignment.title') }}</span>
      </div>
    </div>
    <span class="files"> {{ assignmentButton.buttonName }}</span>
    <i v-if="showNextBtn(classInfo.assignment.status)" class="iconfont icon-gengduo icon-view"></i>
  </div>
  <!-- 资料 -->
  <div
    v-if="classInfo.material && classInfo.material.count > 0"
    class="content-item"
    @click="handleToStudyResource(classInfo.material)"
  >
    <div class="info">
      <img class="icon" src="@/assets/images/home/<USER>" />
      <div class="titleBox">
        <span class="text"> {{ $t('courses.detail.resources') }}</span>
      </div>
    </div>
    <div>
      <span v-if="classInfo.material?.count == 1" class="files"
        >{{ classInfo.material?.count }} {{ $t('courses.detail.file') }}</span
      >
      <span v-else class="files"
        >{{ classInfo.material?.count }} {{ $t('courses.detail.files') }}</span
      >
      <i class="iconfont icon-gengduo icon-view"></i>
    </div>
  </div>
  <!-- 学情报告 -->
  <div
    v-if="classInfo.performance && classInfo.performance.available"
    class="content-item"
    @click="jumpToLessonReport(classInfo.performance)"
  >
    <div class="info">
      <img class="icon" src="@/assets/images/home/<USER>" />
      <div class="titleBox">
        <span class="text"> {{ $t('courses.detail.lessonReport') }}</span>
      </div>
    </div>
    <i class="iconfont icon-gengduo icon-view"></i>
  </div>
   <!-- 作业过期弹窗 -->
  <Modal
   :open="showExpiredTip && classInfo.assignment.status == 6"
   :closable="false"
   :footer="null"
   :centered="true"
   :width="386"
   class="examExpiredModal"
   @cancel="showExpiredTip = false"
 >
   <div class="expired-content" data-log="作业过期弹窗">
     <span class="expired-title-icon"></span>
     <span class="expired-title">{{ $t('courses.detail.expiredTips') }}</span>
     <div class="expired-btn">
       <div @click="handleMouseout" class="no">
         {{ $t('common.cancel') }}
       </div>
       <div
         @click="handleAssignmentJump(classInfo.assignment)"
         class="yes"
         :data-log="$t('courses.detail.homework.status[5]')"
       >
         {{ $t('courses.detail.homework.status[5]') }}
       </div>
     </div>
   </div>
 </Modal>
</div>
</template>
<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { queryHomeWorkUrl } from '@/api/home/<USER>'
import { useI18n } from 'vue-i18n'
import { Modal } from 'ant-design-vue'
import * as classBeforeSensor from '@/utils/sensorTrack/classBefore'
const { t } = useI18n()
const props = defineProps({
  classInfo: {
    default: () => ({}),
    type: Object
  },
  lessonItem: {
    default: () => ({}),
    type: Object
  },
})
const classInfo = computed(() => props.classInfo)
// 课前测按钮信息
const previewQuestionButton = computed(() => {
  // 按钮状态 1: Hidden 2: Unpublished 3: Submit 4: Reviewed 5: Submitted 6: Finished
  const infoMap = {
    1: {
      buttonClassName: '',
      buttonName: ''
    },
    2: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[0]')
    },
    3: {
      buttonClassName: 'color-green',
      buttonName: t('courses.detail.homework.status[6]')
    },
    4: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[2]')
    },
    5: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[3]')
    },
    6: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[4]')
    }
  }
  return props.classInfo.previewQuestion?.status ? infoMap[props.classInfo.previewQuestion.status] : {}
})

// 课中考试报告按钮信息
const examReportButton = computed(() => {
  // status:（1 未发布 2 已发布 3 已提交 4 已批改 5 已过期）
  const infoMap = {
    3: {
      buttonClassName: 'color-orange',
      buttonName: t('courses.detail.reportSubmitText')
    },
    4: {
      buttonClassName: 'color-orange',
      buttonName: t('courses.detail.reportBtnText')
    },
    5: {
      buttonClassName: 'color-orange',
      buttonName: t('courses.detail.reportExpiredText')
    }
  }

  const { report, status } = props.classInfo.inClassExam || {}

  if (status && [3, 4, 5].includes(status)) {
    if (report?.canViewReport) {
      return infoMap[status]
    } else {
      return {
        buttonName: ''
      }
    }
  } else {
    return {
      buttonName: ''
    }
  }
})

// 作业按钮样式信息
const assignmentButton = computed(() => {
  // 按钮状态 1: Hidden 2: Unpublished 3: Submit 4: Reviewed 5: Submitted 6: Finished
  const infoMap = {
    1: {
      buttonClassName: '',
      buttonName: ''
    },
    2: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[0]')
    },
    3: {
      buttonClassName: 'color-green',
      buttonName: t('courses.detail.homework.status[1]')
    },
    4: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[2]')
    },
    5: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[3]')
    },
    6: {
      buttonClassName: 'color-nobg',
      buttonName: t('courses.detail.homework.status[4]')
    }
  }
  return props.classInfo.assignment?.status ? infoMap[props.classInfo.assignment.status] : {}
})
const router = useRouter()
const route = useRoute()
const showExpiredTip = ref(false)
const showNextBtn = (status) => {
  if (status == 4 || status == 5 || status == 6) {
    return true
  } else {
    return false
  }
}
const handleMouseout = () =>  {
  showExpiredTip.value = false
}
const jumpToLessonReport = (performance) => {
  const lessonReportUrl = performance?.url
  classBeforeSensor.osta_class_report(props.lessonItem.planId)
  if (lessonReportUrl) {
    router.push({
      path: `/home/<USER>
      // ?lessonReportUrl=${encodeURIComponent(lessonReportUrl)}&backUrl=${
        // route.fullPath
      // }`,
      query: {
        planId: props.lessonItem.planId,
        lessonReportUrl: lessonReportUrl,
      }
    })
  }
}
// 跳转学习资料下载页面
const handleToStudyResource = (material) => {
  classBeforeSensor.osta_leraning_material(props.lessonItem.planI)
  router.push({
    path: '/home/<USER>',
    query: {
      classId: props.lessonItem.classId,
      lessonId: props.lessonItem.planId,
      filesNumber: material ? material.count : 0,
      // backUrl: this.backUrl
    }
  })
}
const handleAssignmentButton = (assignment) => {
  // 未发布情况不能跳转
  if (assignment.status == 2) {
    return
  }
  // 已过期，本期不会出现旧作业了
  // if (assignment.status == 6 && assignment.sourceType == 0) {
  //   handleOldAssignmentButton()
  //   return
  // }
  if (assignment.status == 6) {
    // 作业过期弹窗展示
    showExpiredTip.value = true
  } else {
    handleAssignmentJump(assignment)
  }
}
const handleAssignmentJump = (data) => {
  const { planId, classId, paperId, homeworkType } = data.jumpParams
  queryHomeWorkUrl(data.jumpParams).then(res => {
    if (res) {
      const assignmentUrl = res.data
      // console.log('看看执行了么',  res.data, `${paperJumpUrl}=${encodeURIComponent(assignmentUrl)}&backUrl=${
      //       this.$route.fullPath
      //     }`)
      if (assignmentUrl) {
        router.push({
          path: '/home/<USER>',
          // `${paperJumpUrl}=${encodeURIComponent(assignmentUrl)}&backUrl=${
          //   this.$route.fullPath
          // }`,
          query: {
            assignmentUrl: assignmentUrl, // 用 query 传
            planId,
            classId,
            homeworkStatus: homeworkType,
            paperId
          }
        })
      }
    }
  })
  classBeforeSensor.osta_do_homework(props.lessonItem.planId)
}
const handleClassExam = (inClassExam) => {
  const { status, report } = inClassExam
  if ([3, 4, 5].includes(status) && !report.canViewReport) {
    return
  }
  router.push({
    path: '/home/<USER>',
    query: {
      ...inClassExam.jumpParams,
      // fromTo: this.backUrl
    }
  })
}
const handlePreviewButton = (data) => {
  if (data.status == 2) return
  const paperJumpUrl = '/home/<USER>'
  const newParams = JSON.stringify(data.jumpParams)
  localStorage.setItem('paperJumpUrlParam', newParams)
  
  classBeforeSensor.osta_do_pre_test(props.lessonItem.planId)
  router.push({
    path: paperJumpUrl,
    query: {
      classId: data.classId,
      lessonId: data.jumpParams.planId,
      planId: data.jumpParams.planId,
      homeworkId: data.jumpParams.homeworkId || '',
      fromTo: route.fullPath,
      status: data ? data.status : 6
    }
  })
}
</script>
<style lang="scss" scoped>
.content {
  display: flex;
  flex-wrap: wrap;
  row-gap: 16px;
  column-gap: 16px;
  margin-top: 16px;
  .content-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 388px;
    cursor: pointer;
    padding: 8px 10px;
    height: 48px;
    background: #f8f8f8;
    border-radius: 4px;
    .info {
      display: flex;

      .icon {
        width: 32px;
        height: 32px;
        margin-right: 8px;
      }
      .titleBox {
        display: flex;
        flex-direction: column;
        width: 130px;
        justify-content: center;
        .text {
          height: 18px;
          font-size: 14px;
          font-family: Montserrat-SemiBold, Montserrat;
          font-weight: 600;
          color: #222222;
          line-height: 18px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .subTitle {
          font-size: 12px;
          color: rgba(23, 43, 77, 0.5);
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    .view {
      font-size: 12px;
    }
    .icon-view {
      width: 12px;
      color: #A6A6A6;
      margin-right: 10px;
    }
    .files {
      font-size: 12px;
      font-weight: 500;
      color: rgba(23, 43, 77, 0.5);
      line-height: 15px;
      margin-right: 4px;
      margin-left: auto;
    }
  }
}
</style>
