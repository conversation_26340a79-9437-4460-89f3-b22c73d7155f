<template>
  <!-- v-log
  v-mark-scroll="'courseList'" -->
  <div
    class="card-container"
    @click.stop="handleClick"
    :data-log="`点击课程卡片${courseData.classId}`"
  >
    <div class="course-card-content">
      <div class="course-title">
        <span class="label" v-if="courseData.tag">{{ courseData.tag }}</span>
        <span
          class="label office"
          v-else-if="courseData?.currentLesson?.lessonType === 'TEMPORARY'"
          >{{ $t('courses.lessonType.temporary') }}</span
        >
        {{ courseData.className }}
      </div>
      <div class="class-datetime">
        <div class="item-datetime">
          <i class="iconfont icon-riqi time-img"></i>
          <span class="content">
            {{ formatDate(courseData.startTimestamp) }} - {{ formatDate(courseData.endTimestamp) }}
          </span>
          <!-- courseData.timeDesc -->
        </div>
        <!-- todo 待确认是否需要修改  Daily 11:00 - 13:00(Asia/Shanghai) -->
        <!-- endTimestamp startTimestamp -->
        <div v-if="courseData.timeDesc2" class="item-datetime">
          <i class="iconfont icon-shijian time-zone-img"></i>
          <span class="zone-content">{{ courseData.timeDesc2 }}</span>
        </div>
      </div>
      <div class="teacthers" v-if="showeachers">
        <template v-for="(item, index) in showeachers">
          <div class="item" v-if="showeachers.length <= 2" :key="`if-${index}`">
            <span class="avator-wrap">
              <Avatar icon="user" :src="item.avatar" />
            </span>
            <div class="info">
              <div class="name">{{ item.name }}</div>
              <span class="title">
                <span>{{ item.identityName }}</span>
              </span>
            </div>
          </div>
          <div class="item-more" v-else :key="`else-${index}`">
            <div class="info">
              <span class="avator-wrap avator-wrap-more">
                <Avatar icon="user" :src="item.avatar" />
              </span>
            </div>
          </div>
        </template>
      </div>
      <!-- 本期不需要展示这个 -->
      <!-- <div v-if="classStatusConfig.className" class="class-status">
        <div :class="classStatusConfig.className"></div>
      </div> -->
    </div>
    <!-- bottom -->
    <div class="course-card-bottom">
      <!-- 卡片样式cardStyle:  1 直播中；2未结课； 3已结课；4已退费 5 回放; -->
      <!-- cardStyle 1 or 2 -->
      <section v-if="courseData.cardStyle === 1 || courseData.cardStyle === 2" class="hasClass">
        <div class="left">
          <!-- <span class="commonInClass"></span> -->
          <span>{{ lessonType }}</span>
        </div>
        <div class="right" v-if="courseData.platformType !== 'OFFLINE'">
          <div class="current-lesson-time" v-if="courseData.cardStyle === 1">
            <span class="started-time">
              {{
                $t('courses.courseCard.alreadyStarted', {
                  courseNum: courseData.currentLesson.orderNum,
                })
              }}
            </span>
            <img
              src="@/assets/images/courses/icon-currentLesson-arrow.png"
              class="icon-arrow-right"
            />
          </div>
          <div class="next-lesson-time" v-else>
            <span class="text-time">
              {{ $t('courses.courseCard.nextLesson') }}:{{ courseData.nextLessonTime }}
            </span>
            <img
              src="@/assets/images/courses/icon-nextLesson-arrow.png"
              class="icon-arrow-right"
            />
          </div>
        </div>
      </section>
      <!-- cardStyle 3、4、5 -->
      <section class="noneof-class" v-else>
        <span :class="classStatusConfig.className">{{ classStatusConfig.textName }}</span>
      </section>
    </div>
  </div>
</template>

<script>
import { Avatar } from 'ant-design-vue'
import { formatDate } from '@/utils/dateFormat.js';
export default {
  name: 'CourseCard',
  components: {
    Avatar,
  },
  data() {
    return {
      showeachers: [],
    }
  },
  props: {
    courseData: {
      default: () => {
        return {}
      },
      type: Object,
    },
  },
  computed: {
    isTemporary() {
      //直播中未开始，不是临时课时 cardStyle 1:直播中 2:未开始 3:已结束
      // 是临时课时 cardStyle：1 直播中 0:未开始 2:已结束
      return this.courseData.currentLesson.lessonType == 'TEMPORARY'
    },
    // 当前类型名称
    currentTypeName() {
      return this.$store.state.courses.currentTypeName
    },
    /**
     * 班级状态图标与文案配置
     */
    classStatusConfig() {
      const configMap = {
        // 直播中
        1: {
          className: 'timerClass',
          textName: this.$t('courses.courseCard.classInSession'),
        },
        // 未结课
        2: {
          className: 'sessionInClass',
          textName: this.$t('courses.courseCard.nextSession'),
        },
        // 已结课
        3: {
          className: 'completed',
          textName: this.$t('courses.courseCard.completed'),
        },
        // 已退费
        4: {
          className: 'refound',
          textName: this.$t('courses.courseCard.refunded'),
        },
        // 回放
        5: {
          className: 'playback',
          textName: this.$t('courses.courseCard.playback'),
        },
      }
      return configMap[this.courseData.cardStyle] || {}
    },
    lessonType() {
      const typeMap = {
        FORMAL: this.$t('courses.lessonType.formal'),
        PLAYBACK: this.$t('courses.lessonType.playback'),
        AUDITION: this.$t('courses.lessonType.audition'),
        TEMPORARY: this.$t('courses.lessonType.temporary'),
      }
      return typeMap[this.courseData.currentLesson.lessonType] || 'Other'
    },
  },
  methods: {
    formatDate,
    handleClick() {
      const courseData = this.courseData
      // 点击卡片埋点
      this.$sensors.track('hw_class_card_click', {
        class_card_state:
          courseData.cardStyle === 1 && courseData.platformType !== 'OFFLINE' ? '有' : '无', // 是否有直播中的课
      })
      // 因后端课次列表不容易返回cardStyle, 所以通过参数传入课次列表页
      // const path = `/courses/${courseData.classId}?cardstyle=${courseData.cardStyle}`
      this.$router.push({
        path: `/home/<USER>
        query: {
          classId: courseData.classId,
          // bizId: courseData.currentLesson.bizId,
          // cardstyle: courseData.cardStyle,
          lessonType: courseData?.currentLesson?.lessonType,
          currentTypeName: this.$t('courses.list.title'),
          fromCompletedCourses: this.$route.path.includes('/home/<USER>') ? 1 : 0
        },
      })
    },
    sliceTeacherList() {
      this.showeachers = this.courseData.teachers && this.courseData.teachers.slice(0, 5)
    },
  },
  async mounted() {
    this.sliceTeacherList()
  }
}
</script>

<style lang="scss" scoped>
.card-container {
  display: flex;
  flex-direction: column;
  //align-items: center;
  cursor: pointer;
  .course-card-content {
    position: relative;
    //width: 516px;
    //height: 186px;
    padding: 16px;
    background: #fff;
    border-radius: 12px;
    //margin-bottom: 1px;
    overflow: hidden;
    .class-status {
      position: absolute;
      right: 0px;
      top: 0;
      .completed,
      .refound {
        width: 60px;
        height: 52px;
        background-size: cover;
      }
      // 已完成
      .completed {
        background-image: url('../../assets/images/courses/completed.png');
      }
      // 已退费
      .refound {
        background-image: url('../../assets/images/courses/refound.png');
      }
      // 回放
      // .playback {
      //   // width: 97px;
      //   // height: 27px;
      //   // background: url('../../assets/images/courses/playback.png');
      //   // background-size: cover;
      // }
    }
    .course-title {
      //max-height: 44px;
      line-height: 24px;
      font-size: 20px;
      font-family: Montserrat-SemiBold, Montserrat;
      font-weight: 600;
      color: #222222;
      overflow: hidden;
      word-break: break-word;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      .label {
        position: relative;
        top: -2px;
        display: inline-block;
        height: 20px;
        line-height: 20px;
        //margin-right: 5px;
        padding: 0px 5px;
        background: #ff9f0a;
        border-radius: 4px;
        font-size: 12px;
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
        color: #fff;
      }
      .label.office {
        background: #64b9ff;
      }
    }
    .class-datetime {
      margin: 16px 0;
      //height: 40px;
      .item-datetime {
        display: flex;
        align-items: center;
        color: #a2aab8;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        .describe {
          margin-right: 8px;
        }
        .content {
          //margin-bottom: 10px;
        }
        .content,
        .zone-content {
          display: -webkit-box;
          width: 358px;
          height: 20px;
          font-size: 14px;
          font-family: Montserrat-Medium, Montserrat;
          font-weight: 500;
          color: #7a7a7a;
          line-height: 20px;
          overflow: hidden;
          word-break: break-all; //纯英文、数字、中文
          text-overflow: ellipsis; //省略号
          -webkit-box-orient: vertical; //垂直
          -webkit-line-clamp: 1; //显示一行
          white-space: pre-line;
        }
        .time-img,
        .time-zone-img {
          margin-right: 4px;
          color: #7a7a7a;
          font-size: 16px;
        }
      }
      .item-datetime:first-child {
        margin-bottom: 10px;
      }
    }
    .teacthers {
      bottom: 20px;
      display: flex;
      .item {
        display: flex;
        width: 173px;
      }
      .item-more {
        display: flex;
      }
      .avator-wrap {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 1px solid #e3e5e9;
        overflow: hidden;
      }
      .avator-wrap-more {
        margin-right: 10px;
      }
      .info {
        margin-left: 6px;
        line-height: 16px;
        font-weight: 500;
        display: flex;
        justify-content: center;
        flex-direction: column;
        .name,
        .title {
          width: 105px;
          overflow: hidden;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .name {
          font-size: 14px;
          font-family: Montserrat-SemiBold, Montserrat;
          font-weight: 600;
          color: #222222;
          //margin-bottom: 2px;
        }
        .title {
          font-size: 12px;
          font-family: Montserrat-Medium, Montserrat;
          font-weight: 500;
          color: #7a7a7a;
          span {
            padding: 2px 0px;
            border-radius: 4px;
            display: inline-block;
          }
        }
      }
    }
  }
  .course-card-bottom {
    display: none;
    position: relative;
    width: 516px;
    height: 38px;
    padding: 16px;
    background: #fff;
    border-radius: 10px;
    margin-bottom: 12px;
    .hasClass {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        line-height: 14px;
        span {
          color: #a2aab8;
          font-size: 12px;
          font-weight: 500;
        }
      }
      .right {
        .button-wrapper {
          width: 200px;
          text-align: right;
          > button {
            min-width: 70px;
            height: 32px;
          }
          .color-blue {
            background: #3370ff;
            border-color: #3370ff;
          }
          .color-gray {
            background: #d1d5db;
            border-color: #d1d5db;
          }
        }
      }
    }
    .next-lesson-time,
    .current-lesson-time {
      display: flex;
      align-items: center;
      .text-time {
        font-size: 12px;
        color: #a2aab8;
      }
      .started-time {
        font-size: 12px;
        color: #ff9f0a;
      }
      .icon-arrow-right {
        margin-left: 6px;
        width: 4px;
        height: 6px;
      }
    }
    .noneof-class {
      font-size: 14px;
      font-weight: 500;
      color: rgba(162, 170, 184, 1);
      line-height: 16px;
      text-align: center;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .playback {
        color: #3370ff;
      }
    }
  }
}
:deep(.avator-wrap) {
  .ant-avatar {
    border-radius: 0;
  }
}
</style>
