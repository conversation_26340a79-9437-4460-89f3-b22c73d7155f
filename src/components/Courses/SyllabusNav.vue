<template>
  <div v-if="courseDatas" class="nav-wrapper">
    <div class="content-wrapper">
      <section class="name-subject">
        <span class="label" v-if="courseDatas.tag">{{ courseDatas.tag }}</span>
        {{ courseDatas.className || '' }}
      </section>
      <section class="class-date">
        <div class="timeDesc1">
          <i class="iconfont icon-riqi icon-img"></i>
          <!-- <span>{{ `${courseDatas.timeDesc}` }}</span> -->
          <span>{{ `${formatDate(courseDatas.startTimestamp)} - ${formatDate(courseDatas.endTimestamp)}` }}</span>
        </div>
        <div v-if="courseDatas.timeDesc2" class="timeDesc2">
          <i class="iconfont icon-shijian icon-img"></i>
          <span>{{ `${courseDatas.timeDesc2}` }}</span>
        </div>
      </section>
      <section ref="teacher" class="teacher">
        <template v-if="showArrow">
          <span @click="clickLeftArrow" class="left-arrow"></span>
          <span class="left-shadow common-shadow"></span>
        </template>

        <div ref="teacherWrapper" class="teacthers" v-if="courseDatas.teachers">
          <div ref="scrollWrapper" class="teacher-wrapper">
            <div class="item" v-for="(item, index) in courseDatas.teachers" :key="index" ref="scrollList">
              <div
                v-if="item?.contactInfoListV2?.length"
                class="avator-wrap-contact"
                @click="openContact(item)"
              >
                <span class="avator-wrap pointer">
                  <a-avatar icon="user" :src="item.avatar" />
                  <span :class="getContactClass(item.contactInfoListV2)"></span>
                  <span class="contact-masker"></span>
                </span>
              </div>
              <span v-else class="avator-wrap">
                <a-avatar icon="user" :src="item.avatar" />
              </span>
              <div class="info">
                <div class="name">{{ item.name }}</div>
                <div class="title">
                  {{ item.identityName }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <template v-if="showArrow">
          <span class="right-shadow common-shadow"></span>
          <span @click="clickRightArrow" class="right-arrow"></span>
        </template>
      </section>
    </div>
    <TeacherContact v-if="isOpenContact" :data="curContactInfo" @closeModal="closeContact" />
  </div>
</template>

<script>
import TeacherContact from './TeacherContact.vue'
import { formatDate } from '@/utils/dateFormat.js';
export default {
  name: 'SyllabusNav',
  components: {
    TeacherContact
  },
  props: {
    courseDatas: {
      default: () => {
        return {}
      },
      type: Object
    }
  },
  data() {
    return {
      newTeachers: [],
      timer: null,
      animationId: null,
      showArrow: false,
      isOpenContact: false,
      curContactInfo: null
    }
  },
  methods: {
    formatDate,
    closeContact() {
      this.isOpenContact = false
      this.curContactInfo = null
    },
    openContact(item) {
      this.curContactInfo = item
      this.isOpenContact = true
      this.$sensors.track('hw_contact_teacher_icon_click', {
        teacher_category: item.identityType
      })
    },
    getContactClass(list) {
      if (list.length > 1) {
        return 'contact_normal'
      } else {
        const map = {
          weChat: 'contact_wechat',
          whatsApp: 'contact_whatsapp',
          line: 'contact_line'
        }
        return map[list[0]['type']]
      }
    },
    showPrevTeachers() {
      this.$refs.carousel.prev()
    },
    showNextTeachers() {
      this.$refs.carousel.next()
    },
    clickRightArrow() {
      const left = 1
      this.moveSlow(left)
    },
    clickLeftArrow() {
      const right = -1
      this.moveSlow(right)
    },
    moveSlow(direction) {
      let start = null
      let diff = null
      let scrollDistance = 240 // 滚动距离
      let step = timestamp => {
        if (!start) start = timestamp
        // 下一次绘制的实际发生时间与初始化的时间差
        diff = timestamp - start
        this.$refs.scrollWrapper.scrollLeft += direction * Math.min(diff / 15, 150)
        scrollDistance -= Math.min(diff / 15, 150)
        // requestAnimationFrame() 只会执行一次 所以递归调用
        if (diff < 1000 && scrollDistance > 0) {
          this.animationId = window.requestAnimationFrame(step)
        }
      }
      this.animationId = window.requestAnimationFrame(step)
    },
    calculateScrollWidth() {
      let scrollLists = document.querySelectorAll('.item')
      let scrollLength = 0
      Array.from(scrollLists).forEach(listItem => {
        // @log-ignore
        if (listItem && listItem.clientWidth) {
          scrollLength += listItem.clientWidth
        }
      })
      if (this.$refs.scrollWrapper && this.$refs.scrollWrapper.clientWidth <= scrollLength) {
        this.showArrow = true
        this.$refs.teacherWrapper.style.width = '94%' // show arrows
      }
    }
  },
  mounted() {
    // calculate scroll width
    this.$nextTick(() => this.calculateScrollWidth())
  },
  destoryed() {
    window.cancelAnimationFrame(this.animationId)
  }
}
</script>

<style lang="scss" scoped>
.nav-wrapper {
  //width: 878px;
  width: 907px;
  margin: 0px auto;
  display: flex;
  flex-direction: column;
  .content-wrapper {
    padding: 14px 20px 20px;
    //background: rgba(255, 255, 255, 1);
    border-radius: 12px;
    position: relative;
    background: #fff;
    background-image: url(/src/assets/images/courses/course-nav.png);
    background-repeat: no-repeat;
    background-position: right bottom; /* 背景图右下对齐 */
    background-size: 130px 116px; /* 设置背景图的宽高 */
  }
  .coupon {
    position: absolute;
    right: 0;
    top: 0;
    width: 41px;
    height: 22px;
    img {
      width: 100%;
    }
  }
  .name-subject {
    //max-height: 44px;
    line-height: 24px;
    font-size: 20px;
    font-family: Montserrat-SemiBold, Montserrat;
    font-weight: 600;
    color: #222;
    overflow: hidden;
    word-break: break-word;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 16px;

    .label {
      margin-right: 4px;
      position: relative;
      top: -2px;
      display: inline-block;
      height: 20px;
      line-height: 20px;
      padding: 0 5px;
      background: #ff9f0a;
      border-radius: 4px;
      font-size: 12px;
      font-family: Montserrat-SemiBold, Montserrat;
      font-weight: 600;
      color: #fff;
    }
  }
  .class-date {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;
    color: #7A7A7A;
    .timeDesc1 {
      .icon-img {
        //color: #7A7A7A;
      }
    }
    .timeDesc2 {
      margin-left: 50px;
    }
    .timeDesc1, .timeDesc2 {
      display: inline-flex;
      .icon-img {
        margin-right: 4px;
      }
    }
  }
  .teacher {
    position: relative;
    display: flex;
    justify-content: center;
    .teacthers {
      bottom: 20px;
      width: 100%;
      .teacher-wrapper {
        display: flex;
        flex-direction: row;
        cursor: pointer;
        -webkit-overflow-scrolling: touch;
        overflow-x: scroll;
        white-space: nowrap;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      .item {
        display: flex;
        margin-right: 15px;
        align-items: center;
      }
      .item-more {
        display: flex;
      }
      .avator-wrap-contact {
        position: relative;
        width: 32px;
        height: 32px;
      }
      .avator-wrap {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 1px solid #e3e5e9;
        overflow: hidden;
      }
      .avator-wrap-more {
        margin-right: 10px;
      }
      .pointer {
        cursor: pointer;
      }
      .contact_normal,
      .contact_wechat,
      .contact_whatsapp,
      .contact_line,
      .contact-masker {
        position: absolute;
        right: -2px;
        bottom: -2px;
        width: 20px;
        height: 20px;
        border-radius: 10px;
      }
      .contact-masker {
        background-color: rgba(0, 0, 0, 0.16);
        opacity: 0;
      }
      .contact-masker:hover {
        transition: all 0.1s;
        opacity: 1;
      }
      .contact_normal {
        background-image: url('../../assets/images/courses/contact/icon_contact.png');
        background-size: 100%;
      }
      .contact_wechat {
        background-image: url('../../assets/images/courses/contact/icon_wechat.png');
        background-size: 100%;
      }
      .contact_whatsapp {
        background-image: url('../../assets/images/courses/contact/icon_whatsapp.png');
        background-size: 100%;
      }
      .contact_line {
        background-image: url('../../assets/images/courses/contact/icon_line.png');
        background-size: 100%;
      }
      .info {
        margin-left: 8px;
        font-weight: 500;
        .name,
        .title {
          width: 105px;
          overflow: hidden;
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .name {
          margin-bottom: 2px;
          height: 18px;
          font-size: 14px;
          font-family: Montserrat-SemiBold, Montserrat;
          font-weight: 600;
          color: #222222;
          line-height: 18px;
        }
        .title {
          color: #a2aab8;
          font-size: 12px;
          height: 15px;
          font-size: 12px;
          font-family: Montserrat-Medium, Montserrat;
          font-weight: 500;
          color: #7A7A7A;
          line-height: 15px;
        }
      }
    }
    .left-arrow,
    .right-arrow {
      width: 18px;
      height: 38px;
      line-height: 18px;
      background: transparent;
      display: inline-block;
      cursor: pointer;
      position: absolute;
      margin-top: -19px;
      top: 50%;
      text-align: center;
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100%;
      img {
        width: 100%;
      }
    }
    .common-shadow {
      width: 40px;
      //height: 70px;
      //line-height: 70px;
      top: 50%;
      margin-top: -35px;
      text-align: center;
      position: absolute;
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100%;
      img {
        width: 100%;
      }
    }
    .right-shadow {
      right: 18px;
      background-image: url('../../assets/images/courses/right-shadow.png');
    }
    .left-shadow {
      left: 18px;
      background-image: url('../../assets/images/courses/left-shadow.png');
    }
    .left-arrow {
      left: 0px;
      background-image: url('../../assets/images/courses/arrow-left.png');
    }
    .right-arrow {
      right: 0px;
      background-image: url('../../assets/images/courses/arrow-right.png');
    }
    .teacthers::-webkit-scrollbar {
      display: none;
    }
  }

  // 班级状态
  .class-status {
    position: absolute;
    top: 0;
    right: 0;
    // 回放
    .playback {
      width: 97px;
      height: 27px;
      background: url('../../assets/images/courses/playback.png');
      background-size: cover;
    }
  }
}
:deep(.avator-wrap) {
  .ant-avatar {
    border-radius: 0;
  }
}
</style>
