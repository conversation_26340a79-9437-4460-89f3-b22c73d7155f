<template>
  <a-modal
    v-model:open="showModal"
    :width="500"
    :footer="null"
    :centered="true"
    :closable="false"
    :keyboard="false"
    :maskClosable="false"
    dialogClass="modal-simple no-apu-header"
    @ok="OK"
    @cancel="cancel"
  >
     <div class="center">
      <div class="confirm-title"></div>
     </div>
    <div class="confirm-con is-keep-all">
      <p>{{ title }}</p>
    </div>
    <div class="button-wrapper">
      <a-button size="large" class="defalut-btn" shape="round" block @click="cancel">
        {{ $t('common.cancel') }}
      </a-button>
      <a-button class="primary" size="large" type="primary" shape="round" block @click="OK">
        {{ $t('common.confirm') }}
      </a-button>
    </div>
  </a-modal>
</template>
<script>
export default {
  data() {
    return {
      showModal: false
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    }
  },
  watch: {
    visible(val) {
      this.showModal = val
    },
  },
  methods: {
    OK() {
      window.open(`${window.location.origin}/download`, '_blank');
      this.showModal = false
      this.$emit('closeModal')
    },
    cancel() {
      this.showModal = false
      this.$emit('closeModal')
    }
  }
}
</script>
<style lang="scss" scoped>
.center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.confirm-title {
  margin-bottom: 10px;
  width: 128px;
  height: 128px;
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100%;
  background-image: url('../../assets/images/courses/download-logo.png');
}
.confirm-con {
  text-align: center;
  font-size: 14px;
  color: #a2aab8;
  line-height: 16px;
}
.is-keep-all {
  word-break: keep-all;
  white-space: pre-line;
}
.button-wrapper {
  display: flex;
  margin-top: 20px;
  > button.primary {
    background: #FFAA0A !important;
    color: #FFFFFF !important;
  }
  .primary, .defalut-btn {
    font-size: 14px !important;
    font-family: Montserrat-SemiBold, Montserrat !important;
    font-weight: 600 !important;
  }
  .defalut-btn {
    margin-right: 20px;
    color: #7A7A7A !important;
    border: 1px solid #D3D3D3 !important;
    background: none;
  }
  .defalut-btn:hover {
    border: 1px solid #D3D3D3 !important;
  }
}
</style>
