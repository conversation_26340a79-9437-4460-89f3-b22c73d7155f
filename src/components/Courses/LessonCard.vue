<template>
  <div class="lesson-card">
    <div class="card-body">
      <div class="lesson-info">
        <div class="info-text">
          <div class="info-name">
            <span class="lesson-name" :class="{ 'is-temp': isTemporary }">{{
              isTemporary ? $t('today.lessonPlan.officeHour') : lessonItem.subjectTag
            }}</span>
            {{ lessonItem.lessonName }}
          </div>
          <div class="info-time">
            <div class="time">
              <i class="iconfont icon-shijian time-img"></i>
              <span class="content">{{
                formatDateRange(lessonItem.startTimestamp, lessonItem.endTimestamp)
              }}</span>
            </div>
            <div
              class="address"
              v-if="
                (lessonItem.platformType === 2 || lessonItem.platformType === 3) &&
                lessonItem.lessonResource?.classRoom
              "
            >
              <i class="iconfont icon-didian time-img"></i>
              <span class="content">{{
                `${lessonItem.lessonResource?.classRoom?.venueName}, ${lessonItem.lessonResource?.classRoom?.classroomName}`
              }}</span>
            </div>
          </div>
          <div class="teacher-info">
            <Avatar class="icon-user" icon="user" :size="32" :src="lessonItem.teacher.avatar" />
            <span class="name">{{ lessonItem.teacher.name }}</span>
          </div>
        </div>
      </div>
    </div>
    <LessonResource
      v-if="lessonItem.lessonResource"
      :classInfo="lessonItem.lessonResource"
      :lessonItem="lessonItem"
    />
    <div
      v-if="lessonItem.platformType === 4 && [1, 2, 3, 4].includes(lessonItem.status)"
      class="footer"
      :class="{
        'footer-live': lessonItem.platformType === 4 && lessonItem.status === 2,
        'footer-not-start': lessonItem.platformType === 4 && lessonItem.status === 1
      }"
    >
      <!-- 未开课 -->
      <template v-if="lessonItem.platformType === 4 && lessonItem.status === 1">
        <span class="call-word">{{ $t('today.lessonPlan.lessonModal.notStart') }}</span>
      </template>
      <!-- 直播中 -->
      <template v-if="lessonItem.platformType === 4 && lessonItem.status === 2">
        <div class="enter-info">
          <span class="footer-text">{{ $t('today.lessonPlan.lessonModal.courseBegin') }}</span>
        </div>
        <Button type="link" shape="round" class="enter-btn" @click="jumpToClassRoom(false)">
          <div class="enter-title">
            <div class="enter-icon">
              <WaveAnimation color="#ff9f0a" />
            </div>
            <span>{{ $t('today.lessonPlan.lessonModal.enter') }}</span>
          </div>
        </Button>
      </template>
      <!-- 回放生成中 -->
      <template v-if="lessonItem.platformType === 4 && lessonItem.status === 3">
        <span class="call-word">{{ $t('today.lessonPlan.lessonModal.generating') }}</span>
      </template>
      <!-- 看回放 -->
      <template v-if="lessonItem.platformType === 4 && lessonItem.status === 4">
        <span class="call-word">{{ $t('today.lessonPlan.lessonModal.over') }}</span>
        <Button type="link" shape="round" class="palyback-btn" @click="jumpToClassRoom(true)">
          <span>{{ $t('today.lessonPlan.lessonModal.playBack') }}</span>
        </Button>
      </template>
    </div>
  </div>
</template>
<script setup>
import { computed } from 'vue'
import { Avatar, Button } from 'ant-design-vue'
import LessonResource from './LessonResource.vue'
import WaveAnimation from '../Common/WaveAnimation.vue'
import { formatDateRange } from '@/utils/dateFormat.js'
// import { getLessonResourceApi } from 'api/today/index'
// import '@lottiefiles/lottie-player'
// import { todaySensorEvent } from 'utils/sensorEvent'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
// import { getAssetPath } from '@/utils/assetPath'

// 获取国际化实例
const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const emit = defineEmits(['showDownLoadApp'])

const props = defineProps({
  lessonItem: {
    default: () => ({}),
    type: Object
  }
})

// const modalVisible = ref(false)
// const classInfo = ref({})
// const lessonModal = ref(null)

const isTemporary = computed(() => {
  // @log-ignore
  return props.lessonItem.lessonType === 'TEMPORARY'
})

// const curTitleImg = computed(() => {
//   // @log-ignore
//   return isTemporary.value
//     ? require('@/assets/images/today/icon-lessons-office.png')
//     : require('@/assets/images/today/icon-lessons-courses.png')
// })

//  跳转直播页
const jumpToClassRoom = playback => {
  const obj = {
    planId: props.lessonItem.planId,
    classId: props.lessonItem.classId,
    subPlatformType: props.lessonItem.subPlatformType,
    lessonType: props.lessonItem.lessonType,
    nps: 1,
    backUrl: encodeURI(
      `${route.path}?planId=${props.lessonItem.planId}&classId=${props.lessonItem.classId}`
    )
  }
  const goClassRoom = query => {
    if (playback) {
      // this.$router.push({
      //   path: '/playback/ready-class',
      //   query: query
      // })
      emit(
        'showDownLoadApp',
        t('today.sorry_course_playback_only_available_think_academy_app_future_web_support_15_erc')
      )
    } else {
      if (obj.subPlatformType === 0) {
        emit('showDownLoadApp', t('today.sorry_this_course_requires_think_academy_app_15_2dh'))
      } else if (obj.subPlatformType === 2) {
        router.push({
          path: '/classroom',
          query: query
        })
        // todaySensorEvent('osta_today_rencent_schedule_enter_classroom', {
        //   plan_id: this.lessonItem.planId
        // })
      }
    }
  }
  goClassRoom(obj)
}

// todo 分情况看是否展示弹窗逻辑
// const jumpToClassRoom = () => {
//   lessonModal.value.jumpToClassRoom(false)
//   // todaySensorEvent('osta_today_rencent_schedule_enter_classroom', {
//   //   plan_id: props.lessonItem.planId
//   // })
// }

/**
 * 获取课次资源信息
 */
// const getLessonInfo = async () => {
//   const data = {
//     lessonType: props.lessonItem.lessonType,
//     classId: props.lessonItem.classId,
//     planId: props.lessonItem.planId
//   }
//   getLessonResourceApi(data)
//     .then(async res => {
//       console.log('获取课次信息', res)
//       if (res && res.code == 0) {
//         classInfo.value = res.data || {}
//       } else {
//         console.error('获取课次信息', res.code)
//       }
//     })
//     .catch(() => {
//       console.error('获取课次信息失败catch')
//     })
// }

// const setSensorsData = () => {
//   const status = props.lessonItem.status === 1 ? 0 : props.lessonItem.status === 2 ? 1 : 2
//   todaySensorEvent('osta_today_rencent_schedule_lesson_item_cilck', {
//     plan_id: props.lessonItem.planId,
//     lesson_state: status
//   })
// }
</script>
<style lang="scss" scoped>
.lesson-card {
  cursor: pointer;
  width: 100%;
  padding: 20px;
  margin: 10px 0 0px 0;

  background: #ffffff;
  border-radius: 12px;
  border: 2px solid #f1f2f4;
  &:hover {
    //border: 2px solid #ffeecd;
  }
  .card-body {
    display: flex;
    justify-content: space-between;
    .lesson-info {
      display: flex;
      position: relative;
      flex-wrap: nowrap;
      flex: 1;
      .lesson-name {
        position: relative;
        top: -2px;
        display: inline-block;
        height: 20px;
        line-height: 20px;
        padding: 0 5px;
        background: #ff9f0a;
        border-radius: 4px;
        font-size: 12px;
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
        color: #fff;
        &.is-temp {
          background: #64b9ff;
        }
      }
      .info-text {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        flex: 1;
        width: 0;
        .info-name {
          //max-height: 44px;
          line-height: 24px;
          font-size: 20px;
          font-family: Montserrat-SemiBold, Montserrat;
          font-weight: 600;
          color: #222;
          overflow: hidden;
          word-break: break-word;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .info-time {
          margin: 12px 0;
          display: flex;
          align-items: center;
          gap: 60px;
          .time,
          .address {
            display: flex;
            gap: 4px;
            align-items: center;
            .time-img {
              line-height: 16px;
              color: #7a7a7a;
            }
            .content {
              height: 18px;
              font-size: 14px;
              font-family: Montserrat-Medium, Montserrat;
              font-weight: 500;
              color: #7a7a7a;
              line-height: 18px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
        .teacher-info {
          display: flex;
          align-items: center;
          .icon-user {
            background-color: #f1f2f4;
          }
          .name {
            margin-left: 8px;
            height: 18px;
            font-size: 14px;
            font-family: Montserrat-SemiBold, Montserrat;
            font-weight: 600;
            color: #222222;
            line-height: 18px;
          }
        }
      }
    }
  }
}
.footer {
  display: flex;
  height: 60px;
  align-items: center;
  justify-content: space-between;
  margin-top: 16px;
  background: #fff5e6;
  border-radius: 8px;
  padding: 16px;
  color: #ff9f0a;
  .call-word {
    font-size: 20px;
    font-weight: 600;
    // color: #172b4d;
    //line-height: 18px;
  }
  .palyback-btn {
    display: inline-block;
    min-width: 100px;
    height: 32px;
    background: #ffffff;
    border-radius: 16px;
    color: #ff9f0a;
    font-size: 14px;
    > span {
      font-family: Montserrat-SemiBold, Montserrat;
      font-weight: 600 !important;
      color: #ff9f0a;
    }
  }
}
.footer-live {
  background: #ffaa0a;
  color: #ffffff;
  .enter-info {
    display: flex;
    align-items: center;
    .footer-text {
      font-size: 20px;
      font-weight: 600;
      color: #ffffff;
      //line-height: 20px;
    }
    .icon-class {
      margin-right: 8px;
      width: 18px;
      height: 18px;
    }
  }
  .enter-btn {
    background: #ffffff;
    border-radius: 16px;
    color: #ff9f0a;
    min-width: 100px;
    .enter-title {
      display: flex;
      align-items: center;
      font-size: 14px;
      > span {
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600 !important;
        color: #ff9f0a;
      }
    }
    .enter-icon {
      width: 12px;
      height: 12px;
      margin-right: 4px;
      display: inline-flex;
    }
  }
}
.footer-not-start {
  background: #f9f9f9;
  color: #a6a6a6;
}
</style>
