<template>
  <div
    v-if="downloadItem"
    class="download-wrapper"
    :class="downloadItem.fileExt ? `${downloadItem.fileExt}_wrapper` : 'pdf_wrapper'"
  >
    <div class="inner">
      <span class="common_icon file_icon"></span>
      <div class="classinfo">
        <span class="title">{{
          `${isNote ? `[${$t('courses.detail.classNotes')}]` : ''}${downloadItem.name}`
        }}</span>
        <label>
          <span>{{ downloadItem.uploadTime }}</span>
          <span>{{ downloadItem.size }}</span>
        </label>
      </div>
      <div @click="open(downloadItem.fileCate)" class="download_btn">
        {{ isNote ? $t('today.view_15_7ot') : (downloadItem.fileCate === 1
          ? $t('courses.studyResources.download') : $t('today.view_15_7ot'))
        }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue'

// 接收 props
const props = defineProps({
  downloadItem: {
    type: Object,
    default: () => ({})
  },
  isNote: {
    type: Boolean,
    default: false
  }
})
const isNote = ref(props.isNote)
const downloadItem = ref(props.downloadItem)
// 定义 emits
const emit = defineEmits(['openVideo', 'openPdf'])

// 功能方法
const open = fileCate => {
  // 视频文件播放
  if (fileCate === 2) {
    emit('openVideo', downloadItem.value.fileUrl)
  } else {
    if (isNote.value) {
      emit('openPdf', downloadItem.value.fileUrl)
    } else {
      downloadFile()
    }
  }
}
const downloadFile = async() => {
  // 视频文件播放视频
  // if (fileCate == 2) {
  //   this.$emit('openVideo', this.downloadItem.fileUrl)
  //   return
  // }
  // console.log('123', this.downloadItem)
  const link = document.createElement('a')
  const body = document.querySelector('body')
  link.href = downloadItem.value.fileUrl
  link.download = checkFileName(downloadItem.value.name)
  link.style.display = 'none'
  body.appendChild(link)
  link.click()
  body.removeChild(link)
  window.URL.revokeObjectURL(link.href)
}
const checkFileName = (fileName) => {
  let name = fileName
  if (fileName.length && fileName[0] === '.') {
    name = fileName.slice(1)
  }
  return name.replace(/[<>:"\\/\\|?*]+/g, '')
}
</script>

<style scoped lang="scss">
.download-wrapper {
  padding: 16px;
  border-radius: 6px;
  .inner {
    display: flex;
    align-items: center;
    .common_icon {
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100%;
      width: 34px;
      height: 40px;
      display: inline-block;
      margin-right: 8px;
    }
    .classinfo {
      display: flex;
      flex-direction: column;
      .title {
        font-weight: 600;
        font-size: 18px;
        line-height: 22px;
        margin-bottom: 4px;
        color: #172b4d;
      }
      label {
        color: #a2aab8;
        font-weight: 500;
        font-size: 14px;
        line-height: 18px;
        span:nth-child(1) {
          margin-right: 10px;
        }
      }
    }
    .download_btn {
      padding: 0 20px;
      margin-left: auto;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 32px;
      background: #ff9f0a;
      border-radius: 16px;
      color: #fff;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      min-width: 100px;
    }
  }
}
.mp4_wrapper,
.m3u8_wrapper {
  border-radius: 12px;
  border: 2px solid #f1f2f4;
  margin-bottom: 10px;
  .file_icon {
    background-image: url('../../assets/images/home/<USER>');
  }
  .downloadLoading {
    font-size: 14px;
    color: #3370ff;
    margin-right: 5px;
  }
}
.pdf_wrapper {
  border-radius: 12px;
  border: 2px solid #f1f2f4;
  margin-bottom: 10px;
  //background: #fff5f4;
  .file_icon {
    background-image: url('../../assets/images/home/<USER>');
  }
  .downloadLoading {
    font-size: 14px;
    color: #ff503f;
    margin-right: 5px;
  }
}
</style>
