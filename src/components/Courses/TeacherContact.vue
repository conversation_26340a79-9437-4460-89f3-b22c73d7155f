<template>
  <a-modal
    v-model="visible"
    :width="375"
    :footer="null"
    centered
    :maskClosable="false"
    :afterClose="closeModal"
    dialogClass="modal-simple"
    :body-style="bodystyle"
  >
    <template v-slot:title>
      <div class="person-info">
        <div class="avator-wrap">
          <a-avatar icon="user" :size="50" :src="data.avatar" />
        </div>
        <div class="info">
          <div class="name word-ellipsis">{{ data.name }}</div>
          <div class="title">
            {{ data.identityName }}
          </div>
        </div>
      </div>
    </template>
    <div class="tea-contact">
      <div class="tip">
        {{ $t('courses.teacherContact.tip') }}
      </div>
      <div class="contact-list">
        <ul class="bg-list">
          <li
            v-for="(item, index) in data.contactInfoListV2"
            :key="index"
            class="list-item can-copy"
          >
            <div class="top">
              <div class="label">
                <span :class="typeMap[item.type]['class']"></span>
                <span v-if="item.value.account" class="title word-ellipsis">{{
                  item.value.account
                }}</span>
              </div>
              <div
                v-if="item.value.account"
                class="copy-btn "
                @click="copyText(item.value.account, item.type)"
              >
                <a :href="typeMap[item.type]['schemes']">
                  <span class="copy-title">{{ $t('courses.teacherContact.copy') }}</span>
                  <span class="copy-icon"></span>
                </a>
              </div>
            </div>
            <div
              v-if="item.value.qrCode"
              :class="['qrCode', !item.value.account ? 'qrCodeOnly' : '']"
            >
              <img :src="item.value.qrCode" />
              <div class="qrCode-tip">
                <span class="scan-icon"></span>
                <span class="title">{{ $t('courses.teacherContact.addContact') }}</span>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      visible: true,
      bodystyle: {
        minHeight: '197px',
        maxHeight: '454px'
      },
      typeMap: {
        weChat: {
          class: 'contact_wechat',
          schemes: 'weixin://'
        },
        whatsApp: {
          class: 'contact_whatsapp',
          schemes: 'whatsapp://'
        },
        line: {
          class: 'contact_line',
          schemes: 'line://'
        }
      }
    }
  },
  mounted() {
    this.$sensors.track('hw_contact_teacher_pv', {
      teacher_category: this.data.identityType
    })
  },
  methods: {
    copyText(text) {
      const input = document.createElement('input')
      input.value = text
      document.body.appendChild(input)
      input.select()
      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$Message.success(this.$t('account.accountVerification.copiedSuccessfully'))
      } else {
        this.$Message.error(this.$t('account.accountVerification.copiedFailed'))
      }
      document.body.removeChild(input)
    },
    closeModal() {
      this.visible = false
      this.$emit('closeModal')
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.ant-modal-header) {
  margin: 0px 24px;
  padding: 24px 0px 10px;
  border-bottom: 1px solid rgba(32, 0, 0, 0.1);
}
:deep(.ant-modal-close-x) {
  margin-top: 4px;
}
.person-info {
  display: flex;
  margin-right: 15px;
  align-items: center;
}
.avator-wrap {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #e3e5e9;
  overflow: hidden;
}
.contact_wechat,
.contact_whatsapp,
.contact_line,
.contact-masker {
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-repeat: no-repeat;
}
.contact_wechat {
  background-image: url('@/assets/images/courses/contact/icon_wechat_max.png');
  background-size: 100%;
}
.contact_whatsapp {
  background-image: url('@/assets/images/courses/contact/icon_whatsapp_max.png');
  background-size: 100%;
}
.contact_line {
  background-image: url('@/assets/images/courses/contact/icon_line_max.png');
  background-size: 100%;
}
.word-ellipsis {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.info {
  margin-left: 6px;
  line-height: 17px;
  padding: 7px 0 6px 16px;
  .name,
  .title {
    width: 225px;
    text-align: left;
  }
  .name {
    color: #172b4d;
    font-size: 16px;
    margin-bottom: 4px;
    font-weight: 600;
  }
  .title {
    color: #a2aab8;
    font-size: 14px;
    font-weight: 500;
    span {
      border-radius: 4px;
      display: inline-block;
    }
  }
}
.tea-contact {
  position: relative;
  padding: 10px 24px 24px 24px;
  font-size: 14px;
  font-weight: 500;
  line-height: 16px;
  .tip {
    height: 14px;
    font-size: 12px;
    font-weight: 500;
    color: #172b4d;
    line-height: 14px;
    text-align: center;
  }
  .contact-list {
    .item {
      color: red;
    }
  }
  ul,
  li {
    list-style: none;
    margin: 0;
    padding: 0;
  }
  .bg-list {
    .top {
      margin-top: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .list-item {
      margin-top: 20px;
      .label {
        display: flex;
        align-items: center;
        margin-right: 16px;
        color: #172b4d;
        .title {
          padding-left: 10px;
          flex: 1;
        }
      }
      .value {
        width: 100%;
        height: 16px;
        color: #172b4d;
        text-align: right;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .copy-btn {
        width: 58px;
        height: 20px;
        border-radius: 10px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        a {
          display: flex;
          align-items: center;
        }
        .copy-title {
          height: 14px;
          font-size: 12px;
          color: #a2aab8;
          line-height: 14px;
          font-weight: 600;
          margin-right: 4px;
        }
        .copy-icon {
          width: 20px;
          height: 20px;
          background-image: url('@/assets/images/courses/copy2.png');
          background-size: 100%;
        }
      }
      .copy-btn:hover {
        .copy-title {
          color: #ffaa0a;
        }
        .copy-icon {
          background-image: url('@/assets/images/courses/copy-hover.png');
        }
      }
      .qrCode {
        margin-left: 40px;
        margin-top: 10px;
        img {
          width: 190px;
          height: 190px;
          border-radius: 13px;
          border: 1px solid rgba(34, 34, 34, 0.1);
          padding: 9px;
          object-fit: contain;
        }
        .qrCode-tip {
          margin-top: 10px;
          display: flex;
          .scan-icon {
            display: inline-block;
            width: 14px;
            height: 14px;
            background-image: url('@/assets/images/courses/contact/icon_scan.png');
            background-size: 100%;
          }
          .title {
            margin-left: 5px;
            width: 171px;
            font-size: 12px;
            font-weight: 500;
            color: #a2aab8;
            line-height: 14px;
          }
        }
      }
      .qrCodeOnly {
        margin-top: -30px;
      }
    }
  }
}
</style>
