<template>
  <div class="wrapper">
    <div v-for="(item, index) in dataList" :key="index" class="container">
      <div
        :class="[
          'timeline',
          item.scheduleType === 'LESSON' ? '' : 'timeline-exam',
          `status-${timelineStatusMap[item.scheduleType === 'LESSON' ? item?.lessonDetails?.status : dataList[index - 1]?.lessonDetails?.status]}`
        ]"
      >
        <div class="round" />
        <div class="line" />
      </div>
      <div class="content">
        <!-- index <= 9 ? '0' + this.index : this.index -->
        <!-- <span class="time-display">{{ item.lessonDetails.timeDisplay }}</span>
        <span v-for="(tag, tagIndex) in item.lessonDetails.timeTags" :key="tagIndex">
          <span
            v-if="tag != 'COMPLETE'"
            class="time-tag"
            :class="`status-${timeTagStatusMap[tag]}`"
            >{{ $t('today.lessonPlan.timeTextsMap')[tag] }}</span
          >
        </span> -->
        <SyllabusCard
          class="item-syllabus"
          :index="item.scheduleType === 'LESSON' ? item.lessonDetails.orderNum : 0"
          :teachers="teachers"
          :isOpenParentEntry="isOpenParentEntry"
          :course-datas="courseDatas"
          :scheduleType="item.scheduleType"
          :syllabus-data="item.scheduleType === 'LESSON' ? item.lessonDetails : item.examDetails"
          @openConfirm="openConfirm"
          @showAttendClass="showAttendClass"
          @showDownLoadApp="showDownLoadApp"
        />
        <!-- <LessonCard :lesson-item="item" /> -->
      </div>
    </div>
    <GoClassModal
      :visible="showConfirmModal"
      :params="needParams"
      @closeModal="closeClassModal"
    ></GoClassModal>
    <!-- 怎么上课方式 -->
    <HowToAttendClass :data="attendClassData" :visible="showAttendModal" @closeModal="closeModal" />
    <!-- 下载app 看回放或者上大班课 -->
    <GoDownloadModal
      :visible="showDownload"
      :title="downLoadTipText"
      @closeModal="showDownload = false"
    ></GoDownloadModal>
  </div>
</template>

<script>
import SyllabusCard from './SyllabusCard.vue'
import GoClassModal from './ConfirmGoClassRoom.vue'
import GoDownloadModal from './GoDownLoadApp.vue'
import HowToAttendClass from './HowToAttendClass.vue'
import { getLocalStorageConfig } from '@/utils/initConfig'
import { useRequestInfo } from '@/hooks/useRequestInfo'
const { getSchoolCode } = useRequestInfo()
export default {
  name: 'SyllabusList',
  data() {
    return {
      showDownload: false,
      downLoadTipText: '',
      timelineStatusMap: {
        1: 'not-start',
        2: 'live',
        3: 'completed',
        4: 'completed',
        5: 'completed',
        6: 'completed'
      },
      showConfirmModal: false, // 进入教室确认弹框
      needParams: {}, //加入教室所需参数
      isOpenParentEntry: false, // 该分校是否打开家长旁听入口
      // 如何上课数据
      attendClassData: {},
      showAttendModal: false
    }
  },
  methods: {
    showDownLoadApp(title) {
      this.showDownload = true
      this.downLoadTipText = title
    },
    closeClassModal() {
      this.showConfirmModal = false
      this.needParams = {}
    },
    // 确认是否进入教室弹框
    openConfirm(params) {
      this.showConfirmModal = true
      this.needParams = params
    },
    // todo 这个是获取旁听云控处理
    // // 获取云控配置信息判断是否打开家长旁听入口
    async initParentEntry() {
      const configs = getLocalStorageConfig()
      // 获取当前schoolcode 判断是否包含
      const str = (configs || []).filter(item => item.configKey === 'openParentEntry')[0]?.configValue || ''
      const curSchoolCode = getSchoolCode()
      this.isOpenParentEntry = str.includes(curSchoolCode)
      console.log(configs, 'configInfo')
      // 初始化配置缓存本地
      // setCloudConfig(configInfo)
    },
    // 展开如何上课方式
    showAttendClass(data) {
      console.log('看看啥情况，为啥不展示呢', data)
      this.attendClassData = data
      this.showAttendModal = true
    },
    closeModal() {
      this.showAttendModal = false
    }
  },
  created() {
    this.initParentEntry()
  },
  components: {
    SyllabusCard,
    GoClassModal,
    HowToAttendClass,
    GoDownloadModal
  },
  props: {
    teachers: {
      default: () => {
        return []
      },
      type: Array
    },
    courseDatas: {
      default: () => {
        return {}
      },
      type: Object
    },
    dataList: {
      default: () => {
        return []
      },
      type: Array
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 907px;
  margin: 12px auto 0 auto;
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
}
.container {
  display: flex;
  padding: 0 16px 0 0px;
  margin-bottom: 16px;
  .timeline {
    display: flex;
    align-items: center;
    flex-direction: column;
    margin-right: 7px;
    padding-top: 5px;
    .round {
      flex-shrink: 0;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #ffffff;
      border: 3px solid #d0d0d0;
    }
    .line {
      margin-top: 12px;
      height: 100%;
      border-left: 2px dashed #d0d0d0;
    }
  }
  .timeline-exam {
    padding-top: 0px;
  }
  .status-not-start {
    .round {
      border-color: #d0d0d0;
    }
    .line {
      border-left: 2px dashed #d0d0d0;
    }
  }
  .status-live {
    .round {
      border-color: #ffaa0a;
    }
    .line {
      border-left: 2px dashed #ffaa0a;
    }
  }
  .status-completed {
    .round {
      border-color: #92cf5a;
    }
    .line {
      border-left: 2px dashed #92cf5a;
    }
  }
  .content {
    width: 100%;
    margin-left: 16px;
    .time-display {
      display: inline-block;
      height: 16px;
      font-size: 12px;
      font-weight: 500;
      color: #172b4d;
      line-height: 16px;
    }
    .time-tag {
      display: inline-block;
      background: rgba(23, 43, 77, 0.08);
      border-radius: 2px;
      color: rgba(23, 43, 77, 0.4);
      height: 18px;
      text-align: center;
      font-size: 12px;
      line-height: 16px;
      padding: 1px 4px;
      margin-left: 10px;
    }
    .status-gray {
      background: rgba(23, 43, 77, 0.08);
      color: rgba(23, 43, 77, 0.4);
    }
    .status-orange {
      background: rgba(255, 145, 31, 0.1);
      color: rgba(255, 145, 31, 1);
    }
    .status-blue {
      background: rgba(41, 163, 234, 0.1);
      color: rgba(41, 163, 234, 1);
    }
  }
}
</style>
