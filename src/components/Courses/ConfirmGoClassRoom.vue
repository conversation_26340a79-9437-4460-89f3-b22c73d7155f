<template>
  <a-modal
    v-model:open="showModal"
    :width="380"
    :centered="true"
    :closable="false"
    :keyboard="false"
    :okText="$t('courses.confirmModal.backBtn')"
    :cancelText="$t('courses.confirmModal.enterBtn')"
    dialogClass="modal-simple no-apu-header"
    @ok="OK"
    @cancel="joinClassRoom"
  >
    <!-- todo ant-design-vue 组件主题色 定制 -->
    <div class="confirm-title">
      {{ $t('courses.confirmModal.title') }}
    </div>
    <div class="confirm-con is-keep-all">
      <p>{{ $t('courses.confirmModal.content') }}</p>
      <p class="redText">{{ $t('courses.confirmModal.warning') }}</p>
    </div>
  </a-modal>
</template>
<script>
export default {
  data() {
    return {
      showModal: false
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    params: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  watch: {
    visible(val) {
      this.showModal = val
    }
  },
  methods: {
    OK() {
      this.showModal = false
      this.$emit('closeModal')
    },
    joinClassRoom() {
      this.$emit('closeModal')
      this.$router.push({
        path: '/classroom',
        query: {
          ...this.params,
          nps: 1
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.confirm-title {
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #172b4d;
  line-height: 25px;
  margin-bottom: 10px;
}
.confirm-con {
  text-align: center;
  font-size: 14px;
  color: #a2aab8;
  line-height: 16px;
}
.is-keep-all {
  word-break: keep-all;
  white-space: pre-line;
}
.redText {
  color: red;
  margin-top: 4px;
}
</style>
