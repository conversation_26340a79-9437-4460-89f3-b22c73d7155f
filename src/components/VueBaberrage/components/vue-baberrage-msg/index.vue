<template>
  <div class="baberrage-item" :class="item.barrageClass" :style="item.style">
    <!-- <template v-if="isCustom">
      <slot></slot>
    </template> -->
    <div class="normal">
      <div class="baberrage-avatar"><img :src="item.icon_pc" /></div>
      <div class="baberrage-msg" v-html="item.message"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, useSlots } from 'vue'

// 定义组件名称
defineOptions({
  name: 'vue-baberrage-message'
})

// 定义 props
const props = defineProps({
  item: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

// 获取插槽
const slots = useSlots()

// 弹幕格式是否是自定义
const isCustom = ref(false)

// 组件挂载时检查是否有默认插槽
onMounted(() => {
  console.log('弹幕vue-baberrage-message', props.item)

  // Vue 3中使用slots.default检测默认插槽是否存在且有内容
  // isCustom.value = !!slots.default && slots.default().length > 0
})
</script>

<style lang="less">
.baberrage-item {
  position: absolute;
  width: auto;
  display: block;
  color: #000;
  transform: translateX(500%);
  padding: 5px 0 5px 0;
  box-sizing: border-box;
  text-align: left;
  white-space: nowrap;

  .normal {
    display: flex;
    box-sizing: border-box;
    padding: 5px;

    .baberrage-avatar {
      width: 30px;
      height: 30px;
      border-radius: 50px;
      overflow: hidden;

      img {
        width: 30px;
      }
    }
  }

  .baberrage-msg {
    line-height: 30px;
    padding-left: 8px;
    white-space: nowrap;
  }
}

.baberrage-item .normal {
  background: rgba(0, 0, 0, 0.7);
  border-radius: 100px;
  color: #fff;
}
</style>
