<template>
  <div
    class="baberrage-stage"
    :style="{ width: boxWidthVal + 'px' }"
    v-show="isShow"
    ref="stageRef"
  >
    <div class="baberrage-top">
      <VueBaberrageMsg
        v-for="item in topQueue"
        :key="item.id"
        class="baberrage-item"
        :item="item"
      />
    </div>
    <!-- Normal -->
    <div class="baberrage-lane" v-for="lane in lanes" :key="lane.id">
      <VueBaberrageMsg
        v-for="item in lane.laneQueue"
        :key="item.runtimeId"
        class="baberrage-item"
        :item="item"
      >
        <slot :item="item"></slot>
      </VueBaberrageMsg>
    </div>
    <div class="baberrage-bottom">
      <VueBaberrageMsg
        v-for="item in bottomQueue"
        :key="item.id"
        class="baberrage-item"
        :item="item"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import hyphenateStyleName from 'hyphenate-style-name'
import VueBaberrageMsg from './components/vue-baberrage-msg/index.vue'
import { MESSAGE_TYPE } from './constants/index.js'
import WidthCalcultor from './utils/widthCalcultor'
import toPX from 'to-px'

// 定义组件名称
defineOptions({
  name: 'vue-baberrage'
})

// Props 定义
const props = defineProps({
  isShow: {
    type: Boolean,
    default: true
  },
  lanesCount: {
    type: Number,
    default: 0
  },
  barrageList: {
    type: Array,
    default: () => []
  },
  boxWidth: {
    type: Number,
    default: 0
  },
  boxHeight: {
    type: Number,
    default: 0
  },
  // 每条弹幕的基本高度
  messageHeight: {
    type: Number,
    default: 40
  },
  messageGap: {
    type: Number,
    default: 5
  },
  loop: {
    type: Boolean,
    default: false
  },
  maxWordCount: {
    type: Number,
    default: 20
  },
  throttleGap: {
    type: Number,
    default: 2000
  },
  // 放置函数
  posRender: {
    type: Function
  },
  // 弹幕样式
  barrageStyle: {
    type: Object,
    default: () => ({
      fontSize: '16px'
    })
  },
  // 弹幕队列的最大数量，一旦超过，则将最先进入的弹幕剔除掉
  maxLength: {
    type: Number,
    default: 0 // 0表示没有限制
  }
})

// 事件定义
const emit = defineEmits(['barrage-list-empty'])

// 响应式状态
const stageRef = ref(null)
const boxWidthVal = ref(props.boxWidth)
const boxHeightVal = ref(props.boxHeight)
const loopVal = ref(props.loop)
const laneNum = ref(0) // 泳道总数量，将舞台分为固定泳道，防止弹幕重叠
const lanes = ref([]) // 泳道数据的数组，每一个元素为一个泳道的数据
const startTime = ref(null)
const frameId = ref(null)
const readyId = ref(0)
const topQueue = ref([]) // 顶部队列
const bottomQueue = ref([]) // 底部队列
const normalQueue = ref([]) // 正常队列，新弹幕先进入队列，一定时限内再显示在ShowList
const showInd = ref(0) // 用指针来代替频繁环操作
const indexShowQueue = ref([]) // 随机展示位置环
const taskQueue = ref([])
const taskIsRunning = ref(false)
const taskLastTime = ref(null)
const isFixLanes = ref(false) // 如果有传lanes的话，lanes默认会规定

// 初始化动画相关函数
window.requestAnimationFrame =
  window.requestAnimationFrame ||
  window.mozRequestAnimationFrame ||
  window.webkitRequestAnimationFrame ||
  window.msRequestAnimationFrame
window.cancelAnimationFrame =
  window.cancelAnimationFrame ||
  window.mozCancelAnimationFrame ||
  function (requestID) {
    clearTimeout(requestID)
  }

// 布置泳道
// 如果laneseMode为true的话，第一个参数为泳道数量，反之为舞台高度
function setUpLane(newHeightOrNewLanes, lanesMode = false) {
  // 舞台高度变化重新计算泳道
  const oldLaneNum = laneNum.value >>> 0
  if (!lanesMode) {
    if (newHeightOrNewLanes === 0) {
      newHeightOrNewLanes = stageRef.value.parentNode.offsetHeight
      newHeightOrNewLanes = newHeightOrNewLanes === 0 ? window.innerHeight : newHeightOrNewLanes
    }

    boxHeightVal.value = newHeightOrNewLanes
    laneNum.value = Math.floor(newHeightOrNewLanes / (props.messageHeight + props.messageGap * 2))
  } else {
    laneNum.value = props.lanesCount
    boxHeightVal.value = props.lanesCount * (props.messageHeight + props.messageGap)
  }
  // 计算泳道数量
  if (oldLaneNum < laneNum.value) {
    for (let i = oldLaneNum; i < laneNum.value; i++) {
      lanes.value.push({
        id: i,
        laneQueue: []
      })
    }
  } else {
    lanes.value.splice(laneNum.value)
  }
}

// init indexShowQueue
function shuffle() {
  let len = laneNum.value
  // 按顺序展示
  indexShowQueue.value = Array.from({ length: len }, (e, i) => i)
}

// 节流函数
function insertToReadyShowQueue() {
  clearTimeout(readyId.value)
  readyId.value = setTimeout(() => {
    // 优先队列比较特殊，需要提取出要优先展示的数据，优先插入队列
    let priorities = []
    for (let i = props.barrageList.length; i--; ) {
      if (props.barrageList[i].priority) {
        // 从barrageList删除并插入priorities
        priorities.unshift(props.barrageList.splice(i, 1)[0])
      }
    }
    if (priorities.length > 0) {
      addTask(() => {
        // requestAnimationFrame触发该任务
        normalQueue.value = [...normalQueue.value, ...priorities]
      }, true)
    }

    // 整个流程是：
    // 将barrageList切分成多个任务块，每块数据最多laneNum条，
    // 按顺序每隔throttleGap间隔塞入一个任务块到normalQueue中
    while (props.barrageList.length > 0) {
      // 每次任务最多插入和泳道数量相同的数据
      let current = props.barrageList.splice(0, laneNum.value)
      addTask(() => {
        // requestAnimationFrame触发该任务
        normalQueue.value = [...normalQueue.value, ...current]
      })
    }
    updateBarrageDate()
  }, 300)
}

// 更新弹幕数据
function updateBarrageDate(timestamp) {
  if (startTime.value == null) startTime.value = timestamp
  if (typeof timestamp !== 'undefined') {
    move(timestamp)
  }
  if (normalQueue.value.length > 0 || topQueue.value.length > 0 || bottomQueue.value.length > 0) {
    play()
  } else {
    // 如果弹幕序列为空发出事件 barrageListEmpty
    emit('barrage-list-empty')
    frameId.value = null
  }
}

// 开始弹幕
function play() {
  frameId.value = requestAnimationFrame(updateBarrageDate)
}

// 暂停弹幕
function pause() {
  normalQueue.value = []
  lanes.value = []
  clearTimeout(readyId.value)
  cancelAnimationFrame(frameId.value)
}

// 重设弹幕
function replay() {
  normalQueue.value.forEach(item => {
    item.startTime = null
  })
  play()
}

// 弹幕移动
function move(timestamp) {
  // 使用索引遍历而不是forEach，以便于处理数组修改
  for (let i = 0; i < normalQueue.value.length; i++) {
    const item = normalQueue.value[i]

    if (item.startTime) {
      // 有startTime表示已经添加到了浏览器的弹幕中
      // 进行正常的弹幕移动即可
      if (item.type === MESSAGE_TYPE.NORMAL) {
        // 正常弹幕
        normalMove(item, timestamp)
        // 退出条件
        if (item.left + item.width < 0) {
          // 清理弹幕 防止内存泄漏
          if (!lanes.value[item.laneId]) continue

          let leng = normalQueue.value.length
          let diff = leng - props.maxLength
          if (loopVal.value) {
            if (props.maxLength && diff && i < diff) {
              // 需要处理当队列中的条数大于允许的最大条数
              // 根据顺序要将队列前面的数据清理掉
              // 也就是i < diff的弹幕要清理
              normalQueue.value.splice(i, 1)
              i-- // 调整索引
            } else {
              // 如果循环则重新加入数据
              itemReset(item, timestamp)
            }
          } else {
            if (props.maxLength && diff && i < diff) {
              normalQueue.value.splice(i, 1)
              i-- // 调整索引
            }
          }
        }
      }
    } else {
      // 否则表示还没有添加到浏览器的弹幕中，
      // 添加之
      if (item.type === MESSAGE_TYPE.FROM_TOP) {
        if (item.position !== 'top' && item.position !== 'bottom') {
          throw new Error('Position only between top and bottom when the type equal 1')
        }
        // 固定弹幕
        fixMove(item, timestamp)
        normalQueue.value.splice(i, 1)
        i-- // 调整索引
      }
      // 弹幕初始化
      itemReset(item, timestamp)
    }
  }
  // 更新队列
  queueRefresh(timestamp)
}

// 正常移动
function normalMove(item, timestamp) {
  // 时间差
  let progress = timestamp - item.currentTime
  item.currentTime = timestamp
  // 移动距离
  let moveVal = item.speed * progress

  // 如果移动距离为0或者NaN 跳过，保持动画连续和减少重绘
  if (moveVal <= 0 || isNaN(moveVal)) return
  item.left -= moveVal
  // 设置移动
  moveTo(item, { x: item.left, y: item.top < 0 ? 0 : item.top })
}

// 固定弹幕
function fixMove(item, timestamp) {
  // 判断是否在队列中
  const queue = item.position === 'top' ? topQueue.value : bottomQueue.value
  if (!queue.includes(item)) {
    queue.push(item)
  }
}

// 队列数据刷新
function queueRefresh(currentTime) {
  // 使用 filter 而不是 shift，避免索引问题
  topQueue.value = topQueue.value.filter(item => {
    return item.startTime + item.time * 1000 > currentTime
  })

  bottomQueue.value = bottomQueue.value.filter(item => {
    return item.startTime + item.time * 1000 > currentTime
  })
}

// 选择空闲可以插入的泳道
function selectPos() {
  // 如果有用户设置的函数则使用用户的
  if (props.posRender) {
    // 传入参数为当前所有泳道
    return props.posRender(lanes.value)
  } else {
    // 根据模式选择
    // 按次序轮询插入对应的泳道
    if (showInd.value + 1 > laneNum.value || normalQueue.value.length == 1) {
      showInd.value = 0
    }
    return showInd.value++
  }
}

function isWaiting(msg) {
  // 如果弹幕left大于舞台宽度 则判断为正在等待状态
  return msg.left > boxWidthVal.value
}

// 初始化弹幕数据并插入泳道
function itemReset(item, timestamp) {
  item.runtimeId = uuidv4()
  item.message = (item.data && item.data.message) || item.message
  item.type = item.type || MESSAGE_TYPE.NORMAL
  item.position = item.position || 'top'
  item.barrageClass = item.barrageClass || 'normal'
  item.startTime = timestamp
  item.currentTime = timestamp
  item.speed = boxWidthVal.value / (item.time * 1000)
  item.cssStyle = {}
  let style = {
    ...props.barrageStyle,
    ...(item.style || {})
  }
  // style转为css style
  Object.keys(style).forEach(key => {
    item.cssStyle[hyphenateStyleName(key)] = style[key]
  })
  // 正常一个英文字符占半个字宽，中文字符占一个字宽
  item.width =
    strlen(item.message) * toPxiel(item.cssStyle['font-size'] || '9px') * 0.6 +
    (item.extraWidth || 0) +
    WidthCalcultor(item.cssStyle)

  if (item.type === MESSAGE_TYPE.NORMAL) {
    let laneInd = selectPos()
    item.laneId = laneInd
    let lastLeft = boxWidthVal.value
    // 当前泳道的数据队列
    let queue = lanes.value[laneInd].laneQueue
    if (queue.length > 0) {
      const last = queue[queue.length - 1]
      if (last.left + last.width > boxWidthVal.value) {
        // 最后一条数据的超出了弹幕展示框的右侧
        // 如果是标记为优先展示的弹幕，需要插入到最近的点进行插入
        if (item.priority) {
          let index = queue.length - 1
          let indexItem = null
          // 所有后面的弹幕都需要后移一个item.width + props.messageGap
          while (index >= 0) {
            indexItem = queue[index]
            if (indexItem.left >= boxWidthVal.value) {
              indexItem.left += item.width + props.messageGap
              index--
            } else {
              break
            }
          }
          // lastLeft取弹幕容器宽度和上一个弹幕的右侧位置的较大值
          lastLeft = Math.max(lastLeft, indexItem.left + indexItem.width + props.messageGap)
          // 在index位置插入这个优先展示弹幕
          // 并将弹幕的priority属性去掉,避免二次读取
          delete item.priority
          queue.splice(index + 1, 0, item)
        } else {
          // 添加一个间隙messageGap
          lastLeft = last.left + last.width + props.messageGap
          queue.push(item)
        }
      } else {
        queue.push(item)
      }
    } else {
      queue.push(item)
    }
    // 计算位置
    item.top = indexShowQueue.value[laneInd] * (props.messageHeight + props.messageGap)
    item.left = lastLeft
  } else {
    item.left = (boxWidthVal.value - item.width) / 2
    if (item.position === 'top') {
      item.top = (topQueue.value.length - 1) * props.messageHeight + props.messageGap * 2
    } else {
      item.top = boxHeightVal.value - (bottomQueue.value.length * props.messageHeight + 100)
    }
  }
  moveTo(item, { x: item.left, y: item.top < 0 ? 0 : item.top })
}

function toPxiel(len) {
  if (isNumber(len)) {
    return toPX(len + 'px')
  } else {
    return toPX(len)
  }
}

// Vue 3 不再需要 $set，直接赋值即可
function moveTo(item, { x, y }) {
  item.style = {
    ...item.cssStyle,
    transform: 'translate3d(' + item.left + 'px,' + item.top + 'px,0)'
  }
}

// 往任务队列中塞入任务
function addTask(fun, priority = false) {
  if (priority) {
    // 优先展示要插入到任务队列前面
    taskQueue.value.unshift(fun)
  } else {
    taskQueue.value.push(fun)
  }
  // 如果任务队列有数据且当前没有进行任务则执行之
  if (taskQueue.value.length > 0 && !taskIsRunning.value) {
    taskIsRunning.value = true
    window.requestAnimationFrame(runTask)
  }
}

// 执行弹幕任务
function runTask(time) {
  // 当runTask被触发的时间点距离上次触发的时间点要大于等于throttleGap才执行任务
  if (!taskLastTime.value || time - taskLastTime.value >= props.throttleGap) {
    let func = taskQueue.value.shift()
    taskLastTime.value = time
    func()
  }

  if (taskQueue.value.length > 0) {
    // 如果任务队列中有数据，则下一帧触发runTask
    window.requestAnimationFrame(runTask)
  } else {
    taskIsRunning.value = false
  }
}

// 计算中英文的长度
function strlen(str) {
  let len = 0
  for (let i in str) {
    if (str.charCodeAt(i) > 127 || str.charCodeAt(i) === 94) {
      len += 2
    } else {
      len++
    }
  }
  return len
}

function isNumber(val) {
  return !isNaN(val)
}

// 监听 barrageList 变化
watch(
  () => props.barrageList,
  () => {
    insertToReadyShowQueue()
  },
  { deep: true }
)

// 监听 boxHeight 变化
watch(
  () => props.boxHeight,
  newHeight => {
    if (!isFixLanes.value) {
      setUpLane(newHeight)
    }
  }
)

// 监听 lanesCount 变化
watch(
  () => props.lanesCount,
  newLanes => {
    if (newLanes > 0) {
      setUpLane(newLanes, true)
      isFixLanes.value = true
    }
  }
)

// 监听 loop 变化
watch(
  () => props.loop,
  newLoop => {
    loopVal.value = newLoop
  }
)

// 生命周期钩子
onMounted(() => {
  // Calculate the size of Stage
  if (boxWidthVal.value === 0) {
    boxWidthVal.value = stageRef.value.parentNode.offsetWidth
  }

  // 判断是否有设置lanes
  if (props.lanesCount === 0) {
    setUpLane(boxHeightVal.value)
  } else {
    setUpLane(props.lanesCount, true)
    isFixLanes.value = true
  }

  shuffle()
  play()
})

onBeforeUnmount(() => {
  pause()
})

// 暴露公共方法
defineExpose({
  play,
  pause,
  replay,
  boxWidthVal,
  boxHeightVal
})
</script>

<style>
.baberrage-stage {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
