<template>
  <div class="camera-allow-confirm">
    <AllowAccessDialog v-model="visible" theme="orange" @ok="handleOk" @cancel="handleCancel">
      <div class="content">
        <div class="icon-camera"></div>
        <div class="notice">
          {{ t('classroom.smallClass.cameraAllowConfirm.notice') }}
        </div>
      </div>
    </AllowAccessDialog>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import AllowAccessDialog from '@/components/AllowAccessDialog/index.vue'
import { emitter } from '@/hooks/useEventBus'
import { useClassData } from '@/stores/classroom'

defineOptions({
  name: 'CameraAllowConfirm'
})
// 获取国际化实例
const { t } = useI18n()

// 获取 store
const store = useClassData()

// 响应式状态
const visible = ref(false)
const timer = ref(null)
const duration = 10 * 10000
const cameraStatus = computed(() => !store.selfInfo.audioMute)
// 监听摄像头状态
watch(
  () => cameraStatus.value,
  val => {
    if (val) {
      closeDialog()
    }
  }
)

// 生命周期钩子
onMounted(() => {
  emitter.on('showCameraAllowConfirm', autoClose)
})

onBeforeUnmount(() => {
  emitter.off('showCameraAllowConfirm', autoClose)
})

// 方法
const handleOk = () => {
  emitter.emit('openCamera', true)
  closeDialog()
  sendLogger('学生同意打开摄像头')
}

const handleCancel = () => {
  closeDialog()
  sendLogger('学生拒绝打开摄像头')
}

const closeDialog = () => {
  visible.value = false
  timer.value && clearTimeout(timer.value)
}

const autoClose = () => {
  console.log(111111982)
  visible.value = true
  // 自动关闭
  timer.value && clearTimeout(timer.value)
  timer.value = setTimeout(() => {
    closeDialog()
    sendLogger('授权打开摄像头提示自动关闭')
  }, duration)
}

const sendLogger = msg => {
  msg
}
</script>

<style lang="scss" scoped>
.camera-allow-confirm {
  position: absolute;
  right: 10px;
  bottom: 10px;

  .content {
    display: flex;
    align-items: center;
  }

  .icon-camera {
    width: 34px;
    height: 34px;
    background: url('./assets/icon-camera.png') no-repeat;
    background-size: cover;
  }

  .notice {
    flex: 1;
    margin-left: 10px;
    line-height: 17px;
    color: #dee2e7;
  }
}
</style>
