<template>
  <div class="microphone-disabled-notice">
    <AllowAccessDialog
      v-model="visible"
      :showCancelButton="false"
      :okValue="t('common.gotIt')"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <div class="content">
        <div :class="[showOnOrOff == 'on' ? 'icon-microphone' : 'icon-off-microphone']"></div>
        <div class="notice">
          {{
            showOnOrOff == 'on'
              ? t('classroom.smallClass.teacherDisabledOnMicrophone.notice')
              : t('classroom.smallClass.teacherDisabledOffMicrophone.notice')
          }}
        </div>
      </div>
    </AllowAccessDialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import AllowAccessDialog from '../../AllowAccessDialog'
import { emitter } from '@/hooks/useEventBus'

defineOptions({
  name: 'TeacherDisabledMicrophoneNotice',
})
// 获取国际化实例
const { t } = useI18n()

// 响应式状态
const visible = ref(false)
const showOnOrOff = ref('on')
let timer = null
const duration = 5 * 1000

// 方法
const handleOk = () => {
  closeDialog()
}

const handleCancel = () => {
  closeDialog()
}

const closeDialog = () => {
  visible.value = false
  timer && clearTimeout(timer)
}

const autoClose = (type) => {
  visible.value = true
  showOnOrOff.value = type
  // 自动关闭
  timer && clearTimeout(timer)
  timer = setTimeout(() => {
    closeDialog()
  }, duration)
}

// 生命周期钩子
onMounted(() => {
  emitter.on('showTeacherDisabledMicrophoneNotice', autoClose)
})

onBeforeUnmount(() => {
  emitter.off('showTeacherDisabledMicrophoneNotice', autoClose)
})
</script>

<style scoped lang="scss">
.microphone-disabled-notice {
  position: absolute;
  right: 10px;
  bottom: 10px;

  .content {
    display: flex;
    align-items: center;
  }

  .icon-microphone {
    width: 34px;
    height: 34px;
    background: url('./assets/icon-microphone.png') no-repeat;
    background-size: cover;
  }

  .icon-off-microphone {
    width: 34px;
    height: 34px;
    background: url('./assets/icon-off-microphone.png') no-repeat;
    background-size: cover;
  }

  .notice {
    flex: 1;
    margin-left: 10px;
    line-height: 17px;
    color: #dee2e7;
  }
}
</style>
