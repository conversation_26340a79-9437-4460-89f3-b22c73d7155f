<template>
  <div v-if="isShowPlay" class="atmosephereBox">
    <canvas class="canvas" id="atmosepherePAG"></canvas>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, nextTick } from 'vue'
// import logger from 'utils/logger'
import { emitter } from '@/hooks/useEventBus'
import { getAssetPath } from '@/utils/assetPath'

// 响应式状态
const isShowPlay = ref(false)
let pagView = null
let audioEl = null

// 监听氛围工具事件
emitter.on('Ambiance_Tools', id => {
  if (pagView && pagView.isPlaying) {
    if (audioEl) {
      audioEl.pause()
      audioEl = null
    }
    pagView.stop()
    sendLogger('未播放完成点击播放其他效果', { id }, 'warn')
  }
  isShowPlay.value = true
  nextTick(() => {
    playAnimation(id)
  })
})

// 播放动画
const playAnimation = async fileName => {
  if (!window.PAG) {
    window.PAG = await window.libpag.PAGInit()
  }
  sendLogger('开始播放氛围效果', { fileName })
  const url = getAssetPath(`/pagfiles/${fileName}.pag`)
  const buffer = await fetch(url).then(response => response.arrayBuffer())
  const pagFile = await window.PAG.PAGFile.load(buffer)

  // Set the width from the PAGFile.
  const canvas = document.getElementById('atmosepherePAG')
  const styleDeclaration = window.getComputedStyle(canvas, null)
  canvas.width = Number(styleDeclaration.width.replace('px', ''))
  canvas.height = Number(styleDeclaration.height.replace('px', ''))

  // canvas.width = pagFile.width()
  // canvas.height = pagFile.height()
  pagView = await window.PAG.PAGView.init(pagFile, canvas, { useScale: false })

  // Get audio data.
  const audioBytes = pagFile.audioBytes()
  if (audioBytes.byteLength > 0) {
    audioEl = document.createElement('audio')
    audioEl.preload = 'auto'
    const blob = new Blob([audioBytes], { type: 'audio/mp3' })
    audioEl.src = URL.createObjectURL(blob)
    pagView.addListener('onAnimationStart', () => {
      audioEl.play()
    })
    pagView.addListener('onAnimationEnd', () => {
      isShowPlay.value = false
      sendLogger('氛围效果播放完毕')
    })
    pagView.addListener('onAnimationCancel', () => {
      if (audioEl) {
        audioEl.pause()
        audioEl = null
      }
      sendLogger('取消播放氛围效果', { fileName })
    })
  }

  pagView.setRepeatCount(1)

  await pagView.play()
}

// 销毁 pagView
const destroyPagView = () => {
  if (audioEl) {
    audioEl.pause()
    audioEl = null
  }
}

// 发送日志
const sendLogger = (msg, params = {}, level = 'info') => {
  // logger.send({
  //   tag: 'PAG',
  //   level,
  //   content: {
  //     msg,
  //     params,
  //   },
  // })
}

// 组件卸载前清理资源
onBeforeUnmount(() => {
  destroyPagView()
  emitter.off('Ambiance_Tools')
})
</script>

<style lang="scss" scoped>
.atmosephereBox {
  width: 100%;
  pointer-events: auto;
  top: -1px;
  height: calc(100% + 2px);
  bottom: -1px;

  .canvas {
    width: 100%;
    height: 100%;
  }
}
</style>
