<template>
  <div>
    <div
      class="mic"
      v-if="!isShowClick"
      :class="{ mute: showMuteIcon, active: microphoneStatus }"
      @mousedown="mouseDownEvent"
    ></div>
    <div
      v-else
      class="btn-mic"
      :class="{ active: microphoneStatus }"
      @click="handleClickMicrophone"
    >
      <div v-if="microphoneStatus" class="mic-wave" :style="micVolumeStyle"></div>
    </div>
  </div>
</template>

<script setup>
import { watch } from 'vue'
// import logger from 'utils/logger'
import { useMicroPhoneStatus } from '@/hooks/useMicroPhoneStatus.js'
defineOptions({
  name: 'MicrophoneBtn'
})
// 麦克风
const {
  handleClickMicrophone,
  microphoneStatus,
  currentVolume,
  updateMicBoxStyle,
  micVolumeStyle,
  showMuteIcon,
  mouseDownEvent,
  isShowClick
} = useMicroPhoneStatus()

watch(
  () => currentVolume.value,
  newValue => {
    console.log(111111)
    updateMicBoxStyle(newValue)
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped>
.btn-mic {
  width: 48px; // 0.48rem * 100px
  height: 48px; // 0.48rem * 100px
  border-radius: 24px; // 0.24rem * 100px
  background-size: cover;
  background-image: url('../../imgs/mic-close.png');
  position: relative;
  cursor: pointer;
  margin-bottom: 16px; // 0.16rem * 100px

  &.active {
    background-image: url('../../imgs/mic-open.png');
  }

  .mic-wave {
    width: 24px; // 0.24rem * 100px
    height: 24px; // 0.24rem * 100px
    position: absolute;
    left: 50%;
    bottom: 50%;
    transform: translate(-50%, 12px); // 0.12rem * 100px
    overflow: hidden;
    background-image: url('../../imgs/mic-green.png');
    background-position: bottom;
    background-size: 100%;
    transition: all 0.6s;
  }
}

.mic {
  width: 48px; // 0.48rem * 100px
  height: 48px; // 0.48rem * 100px
  border-radius: 24px; // 0.24rem * 100px
  background-size: cover;
  background-image: url('../../imgs/speak.png');
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-bottom: 16px; // 0.16rem * 100px

  &.mute {
    cursor: not-allowed;
    background-image: url('../../imgs/mute-speak.png');
  }

  &.active {
    background-image: url('../../imgs/speaking.png');
  }
}

.animate-wave {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 76px;
  height: 76px;
  border-radius: 50%;
  z-index: -1;
}

@keyframes opac {
  0% {
    width: 48px;
    height: 48px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  100% {
    width: 100%;
    height: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
}

.animate-wave * {
  position: absolute;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  animation: opac 2s infinite;
}

.animate-wave .w2 {
  animation-delay: 0.5s;
}

.animate-wave .w3 {
  animation-delay: 1s;
}

.animate-wave .w4 {
  animation-delay: 1.5s;
}

@keyframes move {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(90deg);
  }

  100% {
    transform: rotate(180deg);
  }
}
</style>
