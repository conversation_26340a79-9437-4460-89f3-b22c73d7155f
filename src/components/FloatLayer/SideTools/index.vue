<template>
  <div id="side-tools" class="side-tools" v-show="silderIsVisible">
    <Microphone></Microphone>
    <div
      class="handsup-button"
      :class="{ active: isHandsUpActive, close: isHandsUpClose }"
      @click="debouncedHandsUpSwitch"
    >
      <div class="left">
        <span></span>
      </div>
      <div class="right">
        <span></span>
      </div>
    </div>
    <div
      class="emoji-button"
      :class="{ active: isEmojiActived, cooling: isEmojiCooling, close: isEmojiClose }"
      @click="handleEmojiSwitch"
    >
      <div class="emoji-tip emoji-tip-close" v-if="isEmojiClose && isShowEmojiCloseTip">
        {{ emojiCloseTipText }}
      </div>
      <div class="emoji-tip emoji-tip-cooling" v-if="isEmojiCooling && isShowEmojiCoolingTip">
        {{ emojiCoolingTipText }}
      </div>
    </div>
    <div
      class="chatbox-button"
      :class="{ active: isChatBoxOpen }"
      id="tools-chatbox"
      @click="handleChatBoxSwitchOpen"
    >
      <div v-if="isChatBoxHasNewMsg && !isChatBoxOpen" class="chat-new-msg"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watchEffect, onMounted, onBeforeUnmount } from 'vue'

import { useEmojiStatus } from '@/hooks/useEmojiStatus.js'
import _debounce from 'lodash/debounce'
import { useRequestInfo } from '@/hooks/useRequestInfo.js'
import { useVolume } from '@/hooks/useVolume'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import Microphone from './components/Microphone/index.vue'
import { useClassData } from '@/stores/classroom'
import { emitter } from '@/hooks/useEventBus'
import { handsUpLog } from '@/utils/web-log/HWLogDefine.js'

defineOptions({
  name: 'SideTools'
})
const chatMsgPriority = {
  topic: 0,
  notice: 1,
  privMsg: 99
}

// 获取 store
const store = useClassData()
const { getSchoolCode } = useRequestInfo()

const { reduceOtherVolume } = useVolume()
const multVideoLinkStatus = ref(false) // 多人上台互动状态

// 监听多人上台举手状态
emitter.on('raiseHandForMultVideoLink', status => {
  multVideoLinkStatus.value = status
})

// 举手
const isHandsUpActive = ref(false)
const handsUpTimer = ref(null)
const isHandsUpClose = computed(() => {
  return store.selfVideoMicLink
})
const baseData = computed(() => store.baseData)
const cameraStatus = computed(() => store.selfInfo.videoMute)
const microphoneStatus = computed(() => !store.selfInfo.audioMute)
const silderIsVisible = computed(() => store.sliderVisible)

watchEffect(() => {
  if (isHandsUpClose.value) {
    isHandsUpActive.value = false
    clearTimeout(handsUpTimer.value)
  }
})

function handleHandsUpSwitch() {
  if (isHandsUpActive.value) return
  if (isHandsUpClose.value) return
  isHandsUpActive.value = true
  // 举手后发群聊消息通知其他学生
  sendPeerMessage()
  handsUpLog.action('学员举手')
  // 通知local显示举手
  emitter.emit('sendRaiseHand', true)
  classLiveSensor.osta_raise_hand()
  handsUpTimer.value = setTimeout(() => {
    isHandsUpActive.value = false
  }, 10000)
}

onBeforeUnmount(() => {
  clearTimeout(handsUpTimer.value)
})

// 表情
const {
  handleEmojiSwitch,
  isEmojiActived,
  isEmojiCooling,
  isShowEmojiCoolingTip,
  emojiCoolingTipText,
  isEmojiClose,
  isShowEmojiCloseTip,
  emojiCloseTipText
} = useEmojiStatus()

// chatbox
onMounted(() => {
  console.log('store', store)
  reduceOtherVolume()
})

const sendPeerMessage = () => {
  sendPeerMessageToTour()
  if (multVideoLinkStatus.value) {
    sendPeerMessageToTeacher({ type: 125 })
  }
}

/**
 * 给主讲老师发送举手消息
 */
const sendPeerMessageToTeacher = ({ type }) => {
  // 向主讲老师发送消息
  const { stuInfo, configs } = baseData.value

  const content = {
    type: type || 125,
    status: 6,
    stuId: stuInfo.id,
    cameraIsOpen: cameraStatus.value ? 1 : 2, // 摄像头开启状态
    mikeAvailable: microphoneStatus.value ? 1 : 2 // 麦克风开启状态
  }
  window.ChatClient.PeerChatManager.sendPeerMessage(
    [{ nickname: configs.teacherIrcId }],
    JSON.stringify(content),
    chatMsgPriority.notice
  )
}

const sendPeerMessageToTour = async () => {
  const schoolCode = getSchoolCode()
  // 向辅导老师发送消息
  const { planId, classId, stuIRCId, stuInfo, teacherInfo, stime, configs } = baseData.value
  const content = {
    type: 160,
    msg: 'raiseHand',
    parameter: {
      schoolCode: schoolCode,
      planId,
      roomId: classId,
      studentId: stuIRCId,
      uid: stuInfo.id,
      teacherId: teacherInfo.id,
      teacherName: teacherInfo.name,
      startTime: stime,
      currenTime: new Date().getTime(),
      device: 'web',
      deviceVersion: 1,
      AppVersion: 1
    }
  }
  window.ChatClient.PeerChatManager.sendPeerMessage(
    [{ nickname: configs.tutorIrcId }],
    JSON.stringify(content),
    chatMsgPriority.notice
  )
}

const isChatBoxOpen = computed(() => store.chatBox.isShowBox)
const isChatBoxHasNewMsg = computed(() => store.chatBox.hasNewMsg)

function handleChatBoxSwitchOpen() {
  store.updateChatBoxInfo({
    isShowBox: !isChatBoxOpen.value
  })
}

// 使用 debounce 包装函数
const debouncedHandsUpSwitch = _debounce(handleHandsUpSwitch, 300)
</script>

<style lang="scss" scoped>
@keyframes move {
  0% {
    transform: rotate(0deg);
  }

  50% {
    transform: rotate(90deg);
  }

  100% {
    transform: rotate(180deg);
  }
}

.side-tools {
  display: flex;
  flex-direction: column;
  pointer-events: auto;
}

.handsup-button {
  width: 48px; // 0.48rem * 100px
  height: 48px; // 0.48rem * 100px
  border-radius: 24px; // 0.24rem * 100px
  background-size: cover;
  background-image: url('./imgs/handsUp.png');
  cursor: pointer;
  margin-bottom: 16px; // 0.16rem * 100px
  position: relative;

  &:hover {
    background-image: url('./imgs/handsUp-hover.png');
  }

  &.active {
    background-image: url('./imgs/handsUp-hover.png');

    .left {
      width: 50%;
      height: 100%;
      position: absolute;
      left: 0px;
      top: 0;
      overflow: hidden;
    }

    .left span {
      width: 100%;
      height: 100%;
      position: absolute;
      box-sizing: border-box;
      border: 2px solid #ffb013; // 0.02rem * 100px
      border-width: 2px 0 2px 2px; // 0.02rem * 100px
      border-radius: 50px 0 0 50px; // 0.5rem * 100px
      left: 0;
      top: 0;
      transform-origin: 100% 50%;
      transform: rotate(0deg);
      animation: move 5s 5s linear;
    }
    .right {
      width: 50%;
      height: 100%;
      position: absolute;
      right: 0px;
      top: 0px;
      overflow: hidden;
    }

    .right span {
      width: 100%;
      height: 100%;
      position: absolute;
      box-sizing: border-box;
      border: 2px solid #ffb013; // 0.02rem * 100px
      border-width: 2px 2px 2px 0; // 0.02rem * 100px
      border-radius: 0 50px 50px 0; // 0.5rem * 100px
      right: 0;
      top: 0px;
      transform-origin: 0 50%;
      transform: rotate(180deg);
      animation: move 5s linear;
    }
  }

  &.close {
    background-image: url('./imgs/handsUp-close.png');
  }
}

.emoji-button {
  width: 48px; // 0.48rem * 100px
  height: 48px; // 0.48rem * 100px
  border-radius: 24px; // 0.24rem * 100px
  background-size: cover;
  background-image: url('./imgs/emoji.png');
  cursor: pointer;
  margin-bottom: 16px; // 0.16rem * 100px

  &:hover {
    background-image: url('./imgs/emoji-hover.png');
  }

  &.active {
    background-image: url('./imgs/emoji-hover.png');
  }

  &.close {
    background-image: url('./imgs/emoji-close.png');
  }

  &.cooling {
    background-image: url('./imgs/emoji-cooling.png');
  }

  .emoji-tip {
    padding: 10px 16px; // 0.1rem * 100px, 0.16rem * 100px
    background: rgba(0, 0, 0, 0.7);
    border-radius: 4px; // 0.04rem * 100px
    position: relative;
    left: -14px; // -0.14rem * 100px
    top: 50%;
    transform: translateY(-50%) translateX(-100%);
    font-size: 14px; // 0.14rem * 100px
    font-family: Montserrat-Regular, Montserrat;
    font-weight: 400;
    color: #ffffff;
    text-align: center;

    &::after {
      content: '';
      width: 0;
      height: 0;
      border-top: 6px solid transparent; // 0.06rem * 100px
      border-bottom: 6px solid transparent; // 0.06rem * 100px
      border-left: 6px solid rgba(0, 0, 0, 0.7); // 0.06rem * 100px
      position: absolute;
      right: -3px; // -0.03rem * 100px
      top: 50%;
      transform: translateY(-50%);
    }
  }

  .emoji-tip-close {
    width: 150px; // 1.5rem * 100px
  }

  .emoji-tip-cooling {
    width: 250px; // 2.5rem * 100px
  }
}

.chatbox-button {
  width: 48px; // 0.48rem * 100px
  height: 48px; // 0.48rem * 100px
  border-radius: 24px; // 0.24rem * 100px
  background-size: cover;
  background-image: url('./imgs/chatbox.png');
  cursor: pointer;
  position: relative;

  &:hover {
    background-image: url('./imgs/chatbox-hover.png');
  }

  &.active {
    background-image: url('./imgs/chatbox-hover.png');
  }

  .chat-new-msg {
    position: absolute;
    width: 8px; // 0.08rem * 100px
    height: 8px; // 0.08rem * 100px
    background: #ff4b4b;
    top: 2px; // 0.02rem * 100px
    right: 2px; // 0.02rem * 100px
    border-radius: 50%;
  }
}
</style>
