<template>
  <div id="floatLayerController">
    <SideTools class="tools-container" style="z-index: 96"></SideTools>
    <CameraAllowConfirm class="camera-confirm" style="z-index: 98"></CameraAllowConfirm>
    <MediaAccessModel class="media-access" style="z-index: 99"></MediaAccessModel>
    <EmojiPane class="emoji-pane" style="z-index: 95; position: absolute"> </EmojiPane>
    <Atmosephere style="z-index: 100; position: absolute"></Atmosephere>
    <CountDownLayer style="z-index: 93" ref="CountDown" :options="baseData.commonOption" />
    <SpeakGuide
      v-if="isShowSpeakGuide"
      class="speak-guide"
      @close="closeSpeakGuideHandle"
    ></SpeakGuide>
    <SpeakIng v-if="currentTipType === tipTypeMap.SPEAKING" class="speak-ing"></SpeakIng>
    <SpeakTimeShort
      v-if="currentTipType === tipTypeMap.SPEAKING_SHORT"
      class="speak-time-short"
    ></SpeakTimeShort>
    <SpeakMuteToast
      v-if="currentTipType === tipTypeMap.SPEAK_TYPE_CHANGE"
      :msg="showAudioMuteText"
      class="speak-mute-toast"
    ></SpeakMuteToast>
    <midTips v-if="showTip" style="z-index: 93" ref="midtipsRef"></midTips>
  </div>
</template>

<script setup>
import { computed, onMounted, onBeforeUnmount, ref, nextTick } from 'vue'
import CameraAllowConfirm from './CameraAllowConfirm/index.vue'
import MediaAccessModel from './MediaAccessModel/index.vue'
import SideTools from './SideTools/index.vue'
import EmojiPane from './EmojiPane/index.vue'
import Atmosephere from './Atmosephere/index.vue'
import CountDownLayer from './countDownLayer/index.vue'
import SpeakGuide from './SpeakGuide/index.vue'
import SpeakIng from './SpeakIng/index.vue'
import SpeakTimeShort from './SpeakTimeShort/index.vue'
import SpeakMuteToast from './SpeakMuteToast/index.vue'
import midTips from './midTips/index.vue'
import { useMicroPhoneTip } from '@/hooks/useMicroPhoneTip'
import { useClassData } from '@/stores/classroom'
import { emitter } from '@/hooks/useEventBus'
import { classRestLog } from '@/utils/web-log/HWLogDefine'
defineOptions({
  name: 'FloatLayer'
})
// 获取 store
const store = useClassData()
const baseData = computed(() => store.baseData)
const { isShowSpeakGuide, closeSpeakGuideHandle, showAudioMuteText, tipTypeMap, currentTipType } =
  useMicroPhoneTip()

const midtipsRef = ref(null)
const showTip = ref(false)

onMounted(() => {
  emitter.on('activeTip', handleActiveTip)
})

onBeforeUnmount(() => {
  emitter.off('activeTip', handleActiveTip)
})

function handleActiveTip({ pub, type }) {
  classRestLog.info('课间休息-展示横幅', pub, type)
  showTip.value = true
  setTimeout(() => {
    showTip.value = false
  }, 4200)
  nextTick(() => {
    if (pub) {
      midtipsRef.value.createActiveStartTip(type)
    } else {
      midtipsRef.value.createActiveEndTip(type)
    }
  })
}
</script>

<style lang="scss" scoped>
#floatLayerController {
  .tools-container {
    position: absolute;
    right: 16px; /* 0.16rem * 100px */
    bottom: 16px; /* 0.16rem * 100px */
  }

  .camera-confirm {
    pointer-events: auto;
    position: absolute;
    right: 100px; /* 1rem * 100px */
    bottom: 10px; /* 0.1rem * 100px */
  }

  .media-access {
    pointer-events: auto;
  }

  .emoji-pane {
    pointer-events: auto;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
  }

  .speak-guide {
    z-index: 93;
    position: absolute;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
  }

  .speak-ing {
    z-index: 93;
    position: absolute;
    bottom: 16px;
    left: 50%;
    transform: translateX(-50%);
  }

  .speak-time-short {
    z-index: 93;
    position: absolute;
    bottom: 50%;
    left: 50%;
    transform: translateX(-50%);
  }

  .speak-mute-toast {
    z-index: 93;
    position: absolute;
    bottom: 50%;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
