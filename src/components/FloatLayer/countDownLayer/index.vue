<!-- 视图层 -->
<template>
  <CountDown id="count-down" v-if="show" :time="time"></CountDown>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick } from 'vue'
// import logger from 'utils/logger'
import CountDown from '@/components/countDown/CountDown.vue'
import { emitter } from '@/hooks/useEventBus'
defineOptions({
  name: 'CountDownLayer'
})
// 定义 props
const props = defineProps({
  options: {
    type: Object,
    default: null
  }
})

// 响应式状态
const show = ref(false)
const ircMsg = ref({})
// 课件休息剩余时间
const leftTime = ref(0)

// 计算属性
const time = computed(() => {
  console.log('leftTime', leftTime.value)
  return leftTime.value * 1000
})

// 方法
/**
 * 初始化倒计时
 */
const init = () => {
  if (isNaN(ircMsg.value.type)) {
    let currentTime = new Date().getTime()
    leftTime.value =
      ircMsg.value.duration -
      (props.options.nowTime - ircMsg.value.beginTime) -
      parseInt((currentTime - window._requestBasicTime) / 1000)
    console.info(`倒计时init, 剩余时间: ${leftTime.value},duration:${ircMsg.value.duration}`)
  } else {
    leftTime.value = ircMsg.value.duration
    console.info(`倒计时init, 剩余时间: ${leftTime.value},type:${ircMsg.value.type}`)
  }
}

// 生命周期钩子
onMounted(() => {
  emitter.on('room.countDown', ircMessage => {
    console.info('收到倒计时消息:', ircMessage)
    ircMsg.value = ircMessage
    const pubStatus = !!ircMessage.pub
    if (pubStatus) {
      show.value = false
      nextTick(() => {
        init()
        show.value = true
      })
    }
    if (show.value && !pubStatus) {
      leftTime.value = 0
      show.value = false
    }
  })
})

onBeforeUnmount(() => {
  emitter.off('room.countDown')
})
</script>

<style lang="scss" scoped>
#count-down {
  pointer-events: auto;
  position: absolute;
}
</style>
