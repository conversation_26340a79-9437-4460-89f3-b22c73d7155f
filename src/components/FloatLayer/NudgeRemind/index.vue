<template>
  <transition name="move">
    <div class="nudge-wrapper" v-if="isVisible">
      <div class="background">
        <span class="tip">{{
          showTextRef ? showTextRef : $t('classroom.smallClass.请注意专心听讲哦')
        }}</span>
        <div class="nudge-button" @click="gotIt">
          {{ $t('classroom.smallClass.我知道了') }}
        </div>
      </div>
      <img class="icon" :src="imageRef ? imageRef : nudgeRemindIcon" alt="" />
    </div>
  </transition>
</template>

<script>
import { defineComponent, onBeforeUnmount, onMounted, watch, ref } from 'vue'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { emitter } from '@/hooks/useEventBus'
import { remindLog } from '@/utils/web-log/HWLogDefine'
import nudgeRemindTips from './audio/nudge_remind_tips.mp3'
import nudgeRemindIcon from './imgs/nudge_remind_icon.png'

export default defineComponent({
  name: 'NudgeRemind',

  setup() {
    const isVisible = ref(false) //是否展示组件
    const imageRef = ref('') //图片地址
    const showTextRef = ref('') //提示文案
    const audioLoaded = ref(null) //音频实例
    const timeoutId = ref(0) //5s延时的Id，用于取消延时操作
    /**
     * 关闭拍一拍提醒
     */
    function gotIt() {
      remindLog.action('拍一拍提醒', '用户点击关闭按钮')
      isVisible.value = false
    }
    /**
     * 展示拍一拍互动
     */
    const showNudge = async data => {
      const { imageUrl, tipText, audioUrl } = data
      remindLog.info('拍一拍提醒', '开启', JSON.stringify(data))
      // 拍一拍提醒展示中，立即结束
      if (isVisible.value) {
        remindLog.info('拍一拍提醒', '连续开启')
        destroyData()
      }
      // 埋点数据上报
      classLiveSensor.osta_nudge_received()
      // 1.播放启动提示音
      const tipsAudio = new Audio(nudgeRemindTips)
      tipsAudio.play()
      // 2.展示文本
      showTextRef.value = tipText
      // 3.图片渲染
      loadImg(imageUrl)
      // 4.展示拍一拍提醒
      isVisible.value = true
      // 5.延时500ms，加载音频，无音频时 5s关闭互动
      if (audioUrl) {
        await sleep(500)
        audioLoaded.value = new Audio(audioUrl)
      } else {
        remindLog.info('拍一拍提醒', '无音频')
        delayClose()
      }
    }
    /**
     * 加载在线图片
     */
    function loadImg(url) {
      let img = new Image()
      img.src = url
      img.onload = () => {
        remindLog.info('拍一拍提醒', '图片加载完成')
        imageRef.value = url
      }
      img.onerror = error => {
        remindLog.error('拍一拍提醒', '图片加载失败', error)
      }
    }
    /**
     * 音频播放完成
     */
    function onAudioEnded() {
      remindLog.info('拍一拍提醒', '音频播放结束')
      audioLoaded.value = null
      // 在这里添加播放完成，关闭拍一拍提醒
      delayClose()
    }
    /**
     * 音频播放异常
     */
    function onAudioError(e) {
      remindLog.error('拍一拍提醒', '音频播放失败', e)
      audioLoaded.value = null
      //音频加载失败，5s关闭拍一拍提醒
      delayClose()
    }
    /**
     * 线程阻塞
     */
    const sleep = time => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve()
        }, time)
      })
    }
    /**
     * 销毁数据
     */
    const destroyData = () => {
      remindLog.info('拍一拍提醒', '销毁数据')
      audioLoaded.value = null
      showTextRef.value = ''
      imageRef.value = ''
      //取消5s延时
      timeoutId.value = 0
    }
    /**
     * 延时关闭
     */
    function delayClose() {
      timeoutId.value = setTimeout(() => {
        timeoutId.value = 0
        remindLog.info('拍一拍提醒', '5s计时结束，关闭拍一拍提醒')
        isVisible.value = false
      }, 5000)
    }
    /**
     * 监听延时Id，用于自动取消监听
     */
    watch(timeoutId, (newValue, oldValue) => {
      if (oldValue !== 0) {
        clearTimeout(oldValue)
      }
    })
    /**
     * 监听音频实例变化，处理音频播放和销毁
     */
    watch(audioLoaded, (newValue, oldValue) => {
      if (newValue) {
        remindLog.info('拍一拍提醒', '添加音频监听，并播放音频')
        newValue.addEventListener('ended', onAudioEnded)
        newValue.addEventListener('error', onAudioError)
        newValue.play()
      }
      if (oldValue) {
        remindLog.info('拍一拍提醒', '移除音频监听，并暂停播放')
        oldValue.pause()
        oldValue.removeEventListener('ended', onAudioEnded)
        oldValue.removeEventListener('error', onAudioError)
      }
    })
    /**
     * 监听拍一拍提醒关闭时，销毁数据
     */
    watch(isVisible, newValue => {
      if (newValue === false) {
        destroyData()
      }
    })
    onMounted(() => {
      remindLog.info('拍一拍提醒', '组件挂载')
      emitter.on('userNudgeRemind', showNudge)
    })
    onBeforeUnmount(() => {
      remindLog.info('拍一拍提醒', '组件卸载')
      destroyData()
      emitter.off('userNudgeRemind', showNudge)
    })
    return {
      isVisible,
      gotIt,
      imageRef,
      showTextRef,
      nudgeRemindIcon
    }
  }
})
</script>

<style lang="scss" scoped>
.nudge-wrapper {
  display: flex;
  position: fixed;
  left: calc(50% - 240px);
  bottom: 0;
  width: 480px;
  height: 120px;
  pointer-events: auto;
  .background {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 50%;
    align-self: end;
    align-items: center;
    background-image: url('./imgs/nudge_remind_bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .tip {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-overflow: ellipsis;
      overflow: hidden;
      word-wrap: break-word;
      white-space: normal;
      margin-left: 108px;
      line-height: 1;
      flex: 1;
      width: 250px;
      font-weight: bold;
      color: #ffffff;
      font-size: 18px;
    }
    .nudge-button {
      width: 90px;
      height: 32px;
      background: #ffffff;
      border-radius: 16px;
      margin: 0 16px 0 8px;
      color: #ff9f28;
      font-size: 14px;
      font-weight: bold;
      text-align: center;
      line-height: 32px;
      cursor: pointer;
      &:hover {
        background: #ffeecd;
      }
    }
  }
  .icon {
    position: absolute;
    width: 90px;
    height: 120px;
    left: 10px;
  }
}

.move-leave-active,
.move-enter-active {
  transition: transform 1s ease;
  transform: translateY(100%); //必须添加，否则不会触发入场动画
}

.move-enter-from,
.move-leave-to {
  transform: translateY(100%);
}

.move-enter-to,
.move-leave-from {
  transform: translateY(0%);
}
</style>
