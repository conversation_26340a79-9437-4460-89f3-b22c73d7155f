<template>
  <CommonModel
    dialogClass="ignore-mediaSecurityAccess"
    :isShowModal="showModal"
    :type="type"
    :rightBtnText="t('classroom.modules.mediaSecurityAccess.confirm')"
    :title="titleTip"
    :subTitle="subTitleTip"
    :zIndex="9999"
    :showLeftBtn="false"
    @rightBtnOperation="confirmAccess"
  />
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import CommonModel from '@/components/Common/CommonModel.vue' // import logger from 'utils/logger'
import { emitter } from '@/hooks/useEventBus'
defineOptions({
  name: 'MediaAccessModel'
})
// 获取国际化实例
const { t } = useI18n()

// 响应式状态
const showModal = ref(false)
const type = ref('default')
const titleMsg = ref('')
const subTitleMsg = ref('')

// 计算属性
const titleTip = computed(() => {
  return titleMsg.value
})

const subTitleTip = computed(() => {
  return subTitleMsg.value
})

// 生命周期钩子
onMounted(() => {
  emitter.on('showMediaSecurityAccess', mediaType => {
    type.value = mediaType
    checkAccess()
  })
})

onBeforeUnmount(() => {
  emitter.off('showMediaSecurityAccess')
})

// 方法
/**
 * 检查权限
 */
const checkAccess = async () => {
  subTitleMsg.value = t('classroom.modules.mediaSecurityAccess.tip')

  const handler = {
    camera: () => {
      titleMsg.value = t('classroom.modules.mediaSecurityAccess.camera')
      showModal.value = true
      // logger.send({
      //   tag: 'access',
      //   content: {
      //     msg: '无摄像头权限弹窗',
      //     cameraAccess: cameraAccess.value,
      //     type: type.value
      //   }
      // })
    },
    microphone: () => {
      titleMsg.value = t('classroom.modules.mediaSecurityAccess.microphone')
      showModal.value = true
      // logger.send({
      //   tag: 'access',
      //   content: {
      //     msg: '无麦克风权限弹窗',
      //     microphoneAccess: microphoneAccess.value,
      //     type: type.value
      //   }
      // })
    },
    default: () => {
      // 优先判断 摄像头和麦克风 同时无权限。 因为 文案显示不一样
      titleMsg.value = t('classroom.modules.mediaSecurityAccess.deniedAccess')
      showModal.value = true
      // logger.send({
      //   tag: 'access',
      //   content: {
      //     msg: '无麦克风和摄像头权限弹窗',
      //     cameraAccess: cameraAccess.value,
      //     microphoneAccess: microphoneAccess.value,
      //     type: type.value
      //   }
      // })
    }
  }
  return handler[type.value]()
}

/**
 * 允许授权
 */
const confirmAccess = async () => {
  showModal.value = false
}
</script>

<style lang="scss" scoped>
:deep(.ignore-mediaSecurityAccess) {
  .bottom-wrapper {
    .btn {
      width: 45%;
    }
  }
}
</style>
