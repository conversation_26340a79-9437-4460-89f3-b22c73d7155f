<!-- 一个引导模块 -->
<template>
  <div class="speak-ing">
    <div class="speak-ing__pag">
      <canvas class="canvas" id="speakIngPag"></canvas>
    </div>
    <div class="speak-ing__text" @click="close">
      {{ t('liveroomMicSpeakingHUDTitle') }}
    </div>
  </div>
</template>

<script setup>
import { onBeforeUnmount, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
// import logger from 'utils/logger'
import { getAssetPath } from '@/utils/assetPath'

defineOptions({
  name: 'SpeakIng'
})
// 获取国际化实例
const { t } = useI18n()

// 定义 emits
const emit = defineEmits(['close'])

// 方法
const close = () => {
  emit('close')
}

const sendLogger = (msg, params = {}, level = 'info') => {
  // logger.send({
  //   tag: 'PAG',
  //   level,
  //   content: {
  //     msg,
  //     params
  //   }
  // })
}

let pagView = null

const playAnimation = async () => {
  if (!window.PAG) {
    window.PAG = await window.libpag.PAGInit()
  }
  sendLogger('开始播放speaking氛围效果')
  const url = getAssetPath('/pagfiles/Speak/speakIng.pag')
  const buffer = await fetch(url).then(response => response.arrayBuffer())
  const pagFile = await window.PAG.PAGFile.load(buffer)

  // Set the width from the PAGFile.
  const canvas = document.getElementById('speakIngPag')
  if (!canvas) return
  canvas.width = canvas.offsetWidth
  canvas.height = canvas.offsetHeight
  pagView = await window.PAG.PAGView.init(pagFile, canvas, {
    useCanvas2D: true,
    useScale: false
  })
  pagView.addListener('onAnimationEnd', () => {
    sendLogger('speak播放完毕')
  })
  pagView.addListener('onAnimationCancel', () => {
    sendLogger('speak取消播放')
  })

  pagView.setRepeatCount(0)

  await pagView.play()
}

// 生命周期钩子
onMounted(() => {
  playAnimation()
})

onBeforeUnmount(() => {
  if (pagView) {
    pagView.destroy()
    pagView = null
  }
})
</script>

<style lang="scss" scoped>
.speak-ing {
  width: 480px;
  height: 147px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;

  &__pag {
    width: 208px;
    height: 80px;

    .canvas {
      width: 100%;
      height: 100%;
    }
  }

  &__text {
    font-size: 16px;
    font-family:
      PingFangSC-Semibold,
      PingFang SC;
    font-weight: 600;
    color: #ffffff;
    line-height: 22px;
  }
}
</style>
