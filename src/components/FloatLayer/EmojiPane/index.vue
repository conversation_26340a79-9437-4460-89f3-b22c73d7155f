<template>
  <div ref="emojiPaneRef" v-if="showEmojiPane" class="emojis-pane-container">
    <section class="emojis-nav">
      <ul>
        <li
          class="emoji-nav-content"
          :class="emojiNav.emojiPackageId == currentOrderId ? 'active' : ''"
          v-for="(emojiNav, key) in getDynamicEmolist"
          :key="emojiNav.emojiPackageId"
          @click="selectedEmoji(emojiNav.emojiPackageId, key)"
        >
          <img v-if="emojiNav.isLocal" src="@/assets/images/live/icon_emoji_native.png" alt="" />
          <img v-else :src="emojiNav.picture" alt="" />
        </li>
      </ul>
    </section>
    <!-- 表情列表 -->
    <section class="emojis-list">
      <div class="emoji-scroll-wrapper" ref="scrollWrapper">
        <template v-for="emojiGroup in getDynamicEmolist">
          <expiredEmojiChunk
            :imgSrc="emojiGroup.picture"
            :key="emojiGroup.emojiPackageId"
            v-if="emojiGroup.isOver == true && emojiGroup.overShow == true"
          ></expiredEmojiChunk>
          <emojiChunk
            class="emoji-show-list"
            v-else
            :list="emojiGroup.content"
            :key="emojiGroup.emojiPackageId + 1"
            :lineNum="emojiGroup.isLocal == true ? 3 : 2"
            :isLocal="emojiGroup.isLocal"
            @handleClick="(params, index) => sendEmoji([params, index], emojiGroup)"
          >
          </emojiChunk>
        </template>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import emojiChunk from './emojiChunk.vue'
import expiredEmojiChunk from './expiredEmojiChunk.vue'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { getLottieEmojiLists, sendEmojiBurryPoint } from '@/utils/emojiUtils.js'
import { emitter } from '@/hooks/useEventBus'
import { useClassData } from '@/stores/classroom'
import { useBaseData } from '@/stores/baseData'
import { useIrcService } from '@/hooks/useIrcService'

import { onClickOutside } from '@vueuse/core'

defineOptions({
  name: 'EmojiPane'
})
// 获取 store
const store = useClassData()
const baseStore = useBaseData()

const { getIrcService } = useIrcService()

// 定义 emits
const emit = defineEmits(['handleSendEmoji'])

// 响应式状态
const showEmojiPane = ref(false)
const nickName = ref('')
const avatar = ref('')
const currentOrderId = ref(0) // 表情包emojiPackageId
const scrollWrapper = ref(null)

// 计算属性
const getDynamicEmolist = computed(() => {
  console.log('getDynamicEmolist', store.dynamicEmojiLists)
  return store.dynamicEmojiLists
})

const options = computed(() => {
  return store.baseData.commonOption
})

const packageId = computed(() => {
  return store.baseData.planInfo.packageId
})

// 方法
// 获取用户信息
const getUserInfoData = async () => {
  const { nickName: name, avatar: userAvatar } = baseStore.userInfo
  nickName.value = name
  avatar.value = userAvatar
}

const sendEmoji = ([params, key], emojiGroup) => {
  console.log('params', params, key)
  // 发群聊消息，其他学员展示用
  let opts
  if (params.type == 1) {
    // 本地表情
    opts = emojiIrcConfig('send_emoji', params)
  } else {
    // type 2 动态表情-静态图 type 3 动态表情lottie图
    // 同一个ircType 根据不同类型处理不同逻辑 区分lottie的json和图片
    const type = params.lottieUrl.endsWith('.json') ? 2 : 3
    params = Object.assign(params, {
      type,
      name: params.emojiName
    })
    // 动态表情
    opts = emojiIrcConfig('animation_emoji', params)
    opts.content.data.resource = {
      emojiName: params.emojiName,
      emojiId: params.emojiId,
      emojiPicture: params.emojiPicture,
      lottieUrl: params.lottieUrl
    }
  }
  const SignalService = getIrcService()
  SignalService.sendRoomMessage(opts)
  // 记录表情点击次数
  emit('handleSendEmoji')
  // 广播表情发送消息, 本地表情回显使用
  emitter.emit('sendEmoji', params)
  // 关闭表情面板
  closeEmojiPane()
  // 表情发送埋点
  sendEmojiBurryPoint(options.value, params, emojiGroup, key, packageId.value)
  classLiveSensor.osta_cb_send_msg({
    type: 4,
    contentType: 'emoji',
    msg: `${params.name}`
  })
}

// 表情irc消息配置
const emojiIrcConfig = (emojitype, params) => {
  return {
    roomList: options.value.roomlist,
    content: {
      ircType: emojitype,
      data: {
        name: params.name,
        type: params.type
      },
      from: {
        username: nickName.value,
        path: avatar.value
      }
    },
    chatMsgPriority: 99
  }
}
const emojiPaneRef = ref(null)
onClickOutside(emojiPaneRef, event => {
  closeEmojiPane()
})

const closeEmojiPane = () => {
  showEmojiPane.value = false
  currentOrderId.value = 0
}

const openEmojiPane = () => {
  showEmojiPane.value = true
}

const selectedEmoji = (emojiPackageId, key) => {
  currentOrderId.value = emojiPackageId
  const scrollDom = document.getElementsByClassName('emoji-show-list')[key]
  if (scrollDom) {
    scrollDom.scrollIntoView({
      inline: 'center'
    })
  }
}

// 定义事件处理函数
const handleOpenEmojiPane = state => {
  if (state) {
    openEmojiPane()
  } else {
    closeEmojiPane()
  }
}

// 生命周期钩子
onMounted(() => {
  // 使用命名函数绑定事件
  emitter.on('openEmojiPane', handleOpenEmojiPane)

  getUserInfoData()

  // 动态表情包初始化
  getLottieEmojiLists().then(allEmojiList => {
    store.updateDynamicEmojiLists(allEmojiList)
  })
})

onBeforeUnmount(() => {
  // 移除特定的事件处理函数
  emitter.off('openEmojiPane', handleOpenEmojiPane)
})
</script>

<style lang="scss" scoped>
.emojis-pane-container {
  width: 763px;
  height: 256px;
  background: #0056a4;
  border-radius: 20px 20px 0 0;
  position: relative;
  overflow: hidden;

  .emojis-nav {
    width: 100%;
    height: 56px;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('./imgs/emoji-nav.png');

    ul {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      padding: 4px 8px 0 8px;
    }

    .emoji-nav-content {
      width: 44px;
      height: 44px;
      display: flex;
      background-repeat: no-repeat;
      background-size: cover;
      background-image: url('./imgs/emoji-nav-normal.png');
      margin-right: 16px;
      justify-content: center;
      padding-top: 5px;

      img {
        display: block;
        width: 30px;
        height: 30px;
      }

      &.active {
        background-image: url('./imgs/emoji-nav-selected.png');
      }
    }
  }

  .emojis-list {
    padding: 6px;
    display: flex;

    .emoji-scroll-wrapper {
      display: flex;
      // display: -webkit-box;
      align-items: center;
      flex-direction: row;
      -webkit-overflow-scrolling: touch;
      overflow: hidden;
      white-space: nowrap;
      scroll-behavior: smooth;

      &::-webkit-scrollbar {
        display: none;
      }
    }
  }
}
</style>
