/**
 * 表情符号映射关系配置
 * localImageName: 本地图片表情名称
 */
const emoticonConfig = {
  '[smile]': {
    localImageName: 'e_smile'
  },
  '[pleading]': {
    localImageName: 'e_pleading'
  },
  '[XD]': {
    localImageName: 'e_xd'
  },
  '[LOL]': {
    localImageName: 'e_lol'
  },
  '[thinking]': {
    localImageName: 'e_thinking'
  },
  '[the_rock]': {
    localImageName: 'e_the_rock'
  },
  '[party]': {
    localImageName: 'e_party'
  },
  '[quiet]': {
    localImageName: 'e_quiet'
  },
  '[sad]': {
    localImageName: 'e_sad'
  },
  '[shame]': {
    localImageName: 'e_shame'
  },
  '[scream]': {
    localImageName: 'e_scream'
  },
  '[cry]': {
    localImageName: 'e_cry'
  },
  '[A]': {
    localImageName: 'e_a'
  },
  '[B]': {
    localImageName: 'e_b'
  },
  '[true]': {
    localImageName: 'e_true'
  },
  '[false]': {
    localImageName: 'e_false'
  },
  '[surprise]': {
    localImageName: 'e_surprise'
  },
  '[heart]': {
    localImageName: 'e_heart'
  },
  '[unicorn]': {
    localImageName: 'e_unicorn'
  },
  '[ghost]': {
    localImageName: 'e_ghost'
  },
  '[yeah]': {
    localImageName: 'e_yeah'
  },
  '[like]': {
    localImageName: 'e_like'
  },
  '[hands_up]': {
    localImageName: 'e_hands_up'
  },
  '[clap]': {
    localImageName: 'e_clap'
  }
}

export { emoticonConfig }
