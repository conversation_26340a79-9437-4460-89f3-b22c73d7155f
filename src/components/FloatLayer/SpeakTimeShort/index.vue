<!-- 一个引导模块 -->
<template>
  <div class="speak-ing">
    {{ t('liveroomHoldDownMicBtnWhenSpeakingToastForPC') }}
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
defineOptions({
  name: 'SpeakGuide',
})
// 获取国际化实例
const { t } = useI18n()
</script>

<style lang="scss" scoped>
.speak-ing {
  width: 480px;
  min-height: 68px;
  padding: 24px 40px;
  font-size: 16px;
  font-family:
    PingFangSC-Semibold,
    PingFang SC;
  font-weight: 600;
  color: #ffffff;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
</style>
