<template>
  <Modal
    v-model:open="visible"
    class="set-nps-modal"
    :width="368"
    :maskClosable="false"
    :centered="true"
    :keyboard="false"
    :okText="$t('courses.nps.submitBtn')"
    wrapClassName="modal-simple"
    :afterClose="afterClose"
    :footer="null"
  >
    <template v-slot:title>
      <div>{{ $t('courses.nps.feedback') }}</div>
    </template>
    <template v-slot:closeIcon>
      <div class="close">
        <img src="./imgs/icon_close.png" class="close-icon" />
      </div>
    </template>
    <div class="content">
      <div class="degree">
        <div :class="['tip', curEmoji]">{{ $t('courses.nps.enjoyTip') }}</div>
        <Rate v-model:value="score" :allow-clear="false">
          <template v-slot:character>
            <Icon>
              <template #component>
                <svg
                  width="1em"
                  height="1em"
                  t="1683267543764"
                  class="icon"
                  fill="currentColor"
                  viewBox="0 0 1068 1024"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  p-id="1530"
                >
                  <path
                    d="M344.867479 994.529018a133.554301 133.554301 0 0 1-193.78729-140.810751l34.234419-199.44109L40.363674 512.932209a133.554301 133.554301 0 0 1 74.0336-227.799119l200.286933-29.114838L404.254625 74.562476a133.554301 133.554301 0 0 1 239.50738 0l89.570417 181.500295 200.331451 29.114837a133.554301 133.554301 0 0 1 73.989083 227.799119l-144.950934 141.255932 34.234419 199.485608a133.554301 133.554301 0 0 1-193.787291 140.810751l-179.140835-94.200301-179.140836 94.200301z"
                    p-id="1531"
                  ></path>
                </svg>
              </template>
            </Icon>
          </template>
        </Rate>
      </div>
      <div v-if="curTagList.length" class="reason">
        <div class="tip">
          {{ $t('courses.nps.contactTip') }}
        </div>
        <a-checkbox-group class="tags" v-model="selectTagIds">
          <a-checkbox v-for="item in curTagList" :key="item.id" :value="item.id">
            <span class="word-ellipsis">{{ item.name }}</span>
          </a-checkbox>
        </a-checkbox-group>
        <a-textarea
          v-if="isShowDescription"
          class="text-area"
          v-model="remark"
          :maxLength="1000"
          :auto-size="{ minRows: 4, maxRows: 6 }"
          :placeholder="$t('courses.nps.reason')"
        />
      </div>
    </div>
    <div class="footer">
      <a-button
        :class="['ant-btn-primary', score ? 'footer-btn' : 'ant-btn-disabled']"
        :disabled="!score"
        @click="handleOk"
      >
        <span>{{ $t('courses.nps.submitBtn') }}</span>
      </a-button>
    </div>
  </Modal>
</template>

<script>
import { Modal, Rate } from 'ant-design-vue'
// import * as Icon from '@ant-design/icons-vue';
import { submitNpsFeedback } from '@/api/home/<USER>'
import _debounce from 'lodash/debounce'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import Icon from '@ant-design/icons-vue'
export default {
  components: {
    Icon,
    Rate,
    Modal
  },
  data() {
    return {
      visible: true,
      language: '',
      selectTagIds: [],
      remark: '',
      tagMapList: {},
      score: 0,
      scoreMap: {
        0: {
          tag: 'zeroTagList',
          emoji: ''
        },
        1: {
          tag: 'oneTagList',
          emoji: 'sad'
        },
        2: {
          tag: 'twoTagList',
          emoji: 'sad'
        },
        3: {
          tag: 'threeTagList',
          emoji: 'normal'
        },
        4: {
          tag: 'fourTagList',
          emoji: 'happy'
        },
        5: {
          tag: 'fiveTagList',
          emoji: 'happy'
        }
      },
      curTagList: [],
      curEmoji: ''
    }
  },
  props: {
    planId: {
      type: [String, Number]
    },
    classId: {
      type: [String, Number]
    },
    tagList: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    isShowDescription() {
      return this.selectTagItems.some(item => item.detailFlag === 1)
    },
    selectTagItems() {
      return this.curTagList.filter(item => this.selectTagIds.includes(item.id))
    }
  },
  watch: {
    isShowDescription(val) {
      if (!val) this.remark = ''
    },
    score: {
      handler() {
        this.selectTagIds = []
        this.selectTagItems = []
        this.curTagList = this.tagMapList[this.scoreMap[this.score]['tag']] || []
        this.curEmoji = this.scoreMap[this.score]['emoji'] || ''
      },
      deep: true,
      immediate: true
    }
  },
  async created() {
    this.tagMapList = this.tagList || {}
    classLiveSensor.osta_nps_exposed({ plan_id: this.planId, class_id: this.classId })
  },
  methods: {
    // 关闭后初始化数据
    afterClose() {},
    handleOk: _debounce(function () {
      submitNpsFeedback({
        planId: +this.planId,
        classId: +this.classId,
        tagList: this.selectTagIds,
        score: this.score,
        remark: this.remark
      })
        .then(res => {
          if (res.code === 0) {
            this.$Message.success(this.$t('courses.nps.submitSuccess'))
            classLiveSensor.osta_nps_submit({
              plan_id: String(this.planId),
              class_id: String(this.classId),
              nps_score: String(this.score),
              nps_tag_id_list: this.selectTagIds.join(','),
              nps_other_reason_content: this.remark
            })
            this.visible = false
          }
        })
        .catch(() => {
          this.visible = false
        })
    }, 300)
  }
}
</script>
<style lang="scss">
.set-nps-modal {
  .ant-modal-header {
    padding: 12px 0px 2px;
  }
  .ant-modal-close:hover {
    background: none;
  }
  .ant-modal-body {
    font-size: 0px;
  }
  .modal-simple .ant-modal-title {
    font-weight: 600;
    line-height: 25px;
    height: 21px;
  }
  .ant-checkbox {
    display: none;
  }
  .ant-checkbox + span {
    padding: 0px;
  }
  .ant-checkbox-wrapper .word-ellipsis {
    background: #f5f5f5;
    border-radius: 18px;
    padding: 0px 16px;
    height: 32px;
    line-height: 32px;
    font-size: 12px;
    color: #172b4d;
    margin: 4px 0px;
  }
  .ant-checkbox-wrapper-checked .word-ellipsis {
    background: #fff6e6;
    font-weight: 600;
    color: #ffab0a;
  }
  .ant-rate {
    color: #ffab0a;
    margin: 17px 0px;
  }
  .ant-rate-star:not(:last-child) {
    margin-right: 22px;
  }
  .ant-btn-disabled,
  .ant-btn-disabled:hover {
    background: #fff9ec;
    color: #ffaa0a;
    border: none;
  }
  .ant-btn-primary {
    margin: 0 auto;
    width: 130px;
    height: 34px;
    //line-height: 34px;
    border-radius: 17px;
    font-size: 14px;
    font-weight: 500;
  }
}
</style>
<style lang="scss" scoped>
.close {
  position: relative;
  .close-icon {
    position: absolute;
    right: -4px;
    top: -16px;
    width: 32px;
    height: 20px;
  }
}
.word-ellipsis {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.content {
  text-align: center;
  .degree {
    .tip {
      height: 30px;
      line-height: 30px;
      font-size: 14px;
      font-weight: 500;
      color: #a2aab8;
    }
    .happy::after {
      background: url('./imgs/icon_happy.png') no-repeat;
    }
    .normal::after {
      background: url('./imgs/icon_normal.png') no-repeat;
    }
    .sad::after {
      background: url('./imgs/icon_sad.png') no-repeat;
    }
    .happy::after,
    .normal::after,
    .sad::after {
      margin-left: 4px;
      content: ' ';
      display: inline-block;
      width: 18px;
      height: 18px;
      vertical-align: text-top;
      background-size: cover;
    }
  }
  .reason {
    .tip {
      line-height: 17px;
      font-size: 12px;
      color: #172b4d;
      margin-bottom: 6px;
      padding: 0 10px;
    }
  }
}
.text-area {
  width: 336px;
  margin: 8px 0px;
  border: 1px solid #e3e3e3;
  caret-color: #ffab0a;
  background: #ffffff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #a2aab8;
  line-height: 17px;
  resize: none !important;
  &::-webkit-scrollbar {
    display: none;
  }
  &::placeholder {
    font-size: 12px;
    color: #a2aab8;
    line-height: 17px;
  }
}

.reason {
  margin: 8px 0px;
  .tags {
    margin-left: 16px;
    margin-right: 8px;
    padding-right: 8px;
    font-size: 0px;
    max-height: 112px;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 2px;
      height: 2px;
    }
  }
}
.footer {
  text-align: center;
  padding-bottom: 30px;
}
.footer-btn {
  background: linear-gradient(45deg, #ffd518 0%, #ffaa0a 100%);
  color: #ffffff;
  &:hover {
    color: #ffffff;
    border: none;
  }
}
</style>
