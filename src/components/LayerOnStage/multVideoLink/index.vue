<template>
  <div>
    <div
      class="student-item"
      v-for="item in data"
      :key="item.userId"
      :style="{
        left: pptratio.width * item.originXRatio + 'px',
        top: pptratio.height * item.originYRatio + 'px',
        width: pptratio.width * item.WRatio + 'px',
        height: pptratio.height * item.HRatio + 'px',
        paddingTop: ((pptratio.width * item.WRatio) / 308) * 30 + 'px',
        paddingRight: ((pptratio.width * item.WRatio) / 308) * 10 + 'px',
        paddingBottom: ((pptratio.width * item.WRatio) / 308) * 10 + 'px',
        paddingLeft: ((pptratio.width * item.WRatio) / 308) * 10 + 'px'
      }"
    >
      <div
        v-if="item.videoFrame"
        class="decorate-frame"
        :style="{
          zIndex: 1000,
          backgroundImage: 'url(' + item.videoFrame + ')',
          backgroundSize: `${pptratio.width * item.WRatio}px ${pptratio.height * item.HRatio}px`
        }"
      ></div>
      <div class="item-block" :class="{ 'default-item': !item.videoFrame }">
        <div
          v-show="item.cameraIsOpen == 1"
          class="stu-video"
          :id="'mult-video-' + item.userId"
        ></div>
        <div v-if="item.cameraIsOpen == 2" class="stu-img">
          <div
            v-if="item?.prop?.avatarFrame?.resourceUrl"
            class="decorate-frame"
            :style="
              'background: url(' + item?.prop?.avatarFrame?.resourceUrl + ') no-repeat center'
            "
          ></div>
          <img :src="item.avatar" alt="" />
        </div>
        <lottie-player
          v-if="msgType === 4 && urlMap[item.addCoin] && isShowCoins"
          autoplay
          mode="normal"
          :src="urlMap[item.addCoin]"
          class="lottie-player"
        />
        <!-- 授权涂鸦标识 -->
        <LevelIcon
          class="level-icon"
          v-if="item.levelId > 0"
          :levelId="item.levelId"
          :subLevel="item.subLevel"
        />

        <div class="bottom-bar">
          <p class="stu-name word-ellipsis">
            {{ item.nickName }}
          </p>
          <div class="authgraffiti" v-if="item.isAuthorize"></div>
        </div>
        <div class="medal-wrapper" v-show="item.correctCount">
          <PairIcon :correctCount="item.correctCount" />
        </div>
        <div class="emoji-wrapper" v-if="item.emoticonName">
          <EmoticonMessage
            :willAutoClear="true"
            :name="item.emoticonName"
            :type="item.emoticonType"
            :userId="item.userId"
            :width="70"
            :height="70"
            :emojiId="item.emojiId"
            :lottieUrl="item.lottieUrl"
            @clearEmoticon="handleClearEmoticon"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue'
import { useClassData } from '@/stores/classroom'
import PairIcon from '@/components/VideoGroup/StudentTool/PairIcon/index.vue'
import LevelIcon from '@/components/VideoGroup/StudentTool/LevelIcon/index.vue'
import EmoticonMessage from '@/components/EmoticonMessage/index.vue'
import _debounce from 'lodash/debounce'
import { studentOnStageLog } from '@/utils/web-log/HWLogDefine.js'
import '@lottiefiles/lottie-player'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { useRtcService } from '@/hooks/useRtcService'
import { useIrcService } from '@/hooks/useIrcService'
import { emitter } from '@/hooks/useEventBus'
import { getAssetPath } from '@/utils/assetPath'

// 定义组件属性
const props = defineProps({
  options: {
    type: Object,
    default: () => {
      return {}
    }
  }
})

// 获取全局状态和服务
const store = useClassData()

// 响应式状态
const stuId = ref(props.options.stuInfo.id)
const urlMap = reactive({
  5: getAssetPath('/lottiefiles/coins/coins-add-5/data.json'),
  10: getAssetPath('/lottiefiles/coins/coins-add-10/data.json')
})
const ircMsg = ref({})
const stuList = ref([])
const pptratio = reactive({
  width: '',
  height: ''
})
const data = ref([])
const isShowCoins = ref(false)
const lastInteraction = ref('') // 避免监听和消除互动打两次埋点
const highEncoderConfig = reactive({
  // 高分辨率
  width: 320,
  height: 240,
  bitrate: 120,
  frameRate: 10
})
const lowEncoderConfig = reactive({
  // 低分辨率
  bitrate: 80,
  frameRate: 10,
  width: 160,
  height: 120
})
// 1、开始互动，学生举手。
// 2、学生上台
// 3、学生下台
// 4、主讲给上麦学生添加金币
// 5、主讲滑动回显窗口。
const msgType = ref(null)

const { getRtcService } = useRtcService()
const { getIrcService } = useIrcService()

// 计算属性
const microphoneStatus = computed(() => !store.selfInfo.audioMute)

const cameraStatus = computed(() => !store.selfInfo.videoMute)

const setVideoEncoderConfiguration = isOnStage => {
  let config
  if (isOnStage) {
    config = highEncoderConfig
  } else {
    config = lowEncoderConfig
  }
  const RtcService = getRtcService()
  RtcService?.setVideoEncoderConfiguration(config)
}

/**
 * 获取 上台 / 下台 / 全部 学生list
 */
const getUpDownStuObj = (newVal, oldVal) => {
  // 2、学生上台3、学生下台
  let allList = new Map()
  const arr = [...oldVal, ...newVal]
  arr.forEach(element => {
    allList.set(element.userId, element)
  })
  const oldUserList = oldVal.map(item => item.userId) || []
  const newUserList = newVal.map(item => item.userId) || []
  const newListMap = new Set(newUserList)
  const oldListMap = new Set(oldUserList)
  // 需下台学生
  const downstu = oldUserList.filter(item => !newListMap.has(item))
  // 需要上台的学生
  const upstu = newUserList.filter(item => !oldListMap.has(item))
  return {
    downstu,
    upstu,
    allList
  }
}

/**
 * 操作 上台 下台
 */
const upDownClass = (newVal, oldVal, isDestroy = false) => {
  studentOnStageLog.info('upDownClass', newVal)
  const upDownStuObj = getUpDownStuObj(newVal, oldVal)
  // 下台
  upDownStuObj.downstu.forEach(item => upDownStuObj.allList.delete(item))
  // const allStuList = Object.values(strMapToObj(upDownStuObj.allList) || []) || []
  upDownStuObj.downstu.forEach(i => {
    let delIndex = null
    data.value.forEach((item, index) => {
      if (i == item.userId) {
        delIndex = index
      }
    })
    delIndex != null && data.value.splice(delIndex, 1)
  })
  upDownStuObj.upstu.forEach(i => {
    data.value.length <= 4 && data.value.push(upDownStuObj.allList.get(i))
  })
  data.value.forEach(item => {
    if (stuId.value == item.userId) {
      if (cameraStatus.value) {
        item.cameraIsOpen = 1
      } else {
        item.cameraIsOpen = 2
      }
    }
    store.studentList.forEach(stu => {
      if (stu.userId == item.userId) {
        item.micIsOpen = !stu.audioMute ? 1 : 2
        item.levelId = stu.prop.levelId
        item.subLevel = stu.prop.subLevel
        item.prop = stu.prop || {}
        item.videoFrame = stu.prop?.videoFrame?.resourceUrl
        item.correctCount = stu?.correctCount || 0
        if (stuId.value != item.userId) {
          item.cameraIsOpen = !stu.videoMute ? 1 : 2
        }
      }
    })
  })
  // 授权涂鸦时不会走上台逻辑，但需要变更isAuthorize字段状态
  newVal.forEach(i => {
    data.value.map(item => {
      if (i.userId === item.userId) {
        item.isAuthorize = i.isAuthorize
        if (i.isAuthorize) {
          studentOnStageLog.info('multVideoLink-授权涂鸦开启', i.userId)
        }
      }
      return item
    })
  })
  nextTick(() => {
    studentOnStageLog.info('当前台上的学生', upDownStuObj.upstu)
    studentOnStageLog.info('当前下台的学生', upDownStuObj.downstu)
    // 上台视频显示逻辑
    if (!isDestroy) {
      upDownStuObj.upstu.forEach(item => {
        // 更新学员上台状态
        updateStudentList(item, {
          multVideoLinkStatus: true
        })
        const videoId = `mult-video-${item}`
        if (item == stuId.value) {
          classLiveSensor.osta_ia_on_stage(ircMsg.value?.interactId, true)
          setVideoEncoderConfiguration(true)
          // emitter.emit('raiseHandDisabled', true)
          // 自己视频显示
          setupLocalVideo(videoId)
          // 本地未开启麦克风，则提示打开麦克风
          studentOnStageLog.info('多人上台触发上台')
          store.updateSelfVideoMicLink(true)
        } else {
          // 视频拉流
          setTimeout(() => {
            setupRemoteVideo(item, videoId)
            // 音频加大
            emitter.emit('onlink_adjust_volume', {
              v: 'up',
              uid: item
            })
          }, 100)
        }
      })
    }
    // 下台视频回显区逻辑恢复
    upDownStuObj.downstu.forEach(item => {
      if (item == stuId.value) {
        setVideoEncoderConfiguration(false)
        // emitter.emit('raiseHandDisabled', false)
        store.updateSelfVideoMicLink(false)
        if (lastInteraction.value != ircMsg.value?.interactId) {
          classLiveSensor.osta_ia_on_stage(ircMsg.value?.interactId, false)
          lastInteraction.value = ircMsg.value?.interactId
        }
      } else {
        // 降低
        emitter.emit('onlink_adjust_volume', {
          v: 'down',
          uid: item
        })
      }
      // 销毁远端视图
      destroyRemoteVideo(item)
      updateStudentList(item, {
        multVideoLinkStatus: false
      })
    })
  })
}

/**
 * 接收消息
 */
const receiveMessage = noticeContent => {
  studentOnStageLog.info(
    `收到:【${messageDescription(noticeContent)}】消息，消息详情：`,
    noticeContent
  )

  // 1、开始互动，学生举手。
  // 2、学生上台
  // 3、学生下台
  // 4、主讲给上麦学生添加金币
  // 5、主讲滑动回显窗口。

  msgType.value = noticeContent.status
  stuList.value = noticeContent.data || []
  emitter.emit('setIsAuthorizedStatus', noticeContent)
}

const messageDescription = noticeContent => {
  ///添加noticeContent的安全保护，类型校验
  if (!noticeContent || typeof noticeContent.status !== 'number') {
    return '未知消息'
  }
  ///判断消息类型
  switch (noticeContent.status) {
    case 1:
      return '开始互动，学生举手'
    case 2:
      return '学生上台'
    case 3:
      return '学生下台'
    case 4:
      return '主讲给上麦学生添加金币'
    case 5:
      return '主讲滑动回显窗口'
    default:
      return '未知消息'
  }
}

// 暴露外部组件调用receiveMessage
defineExpose({
  receiveMessage
})

// 重置学生窗口位置
const dragVideoView = newVal => {
  newVal.forEach(newValItem => {
    studentOnStageLog.info('multVideoLink-dragVideoView', data.value)
    data.value.forEach((dataItem, dataIndex) => {
      if (dataItem.userId == newValItem.userId) {
        data.value[dataIndex].originYRatio = newValItem.originYRatio
        data.value[dataIndex].originXRatio = newValItem.originXRatio
      }
    })
  })
  return newVal
}

/**
 * 更新学生字段值
 */
const updateStudentVal = (uid, key, val) => {
  // @log-ignore
  data.value.forEach(item => {
    if (item.userId == uid) {
      item[key] = val
    }
  })
}

/**
 * 更新学生列表
 */
const updateStudentList = (stuId, params) => {
  store.changeStudentInfo(stuId, params)
}

/**
 * 获取容器宽度
 */
const getSize = () => {
  const boundingClientRectController = document.getElementById('stageLayer').getBoundingClientRect()
  pptratio.width = boundingClientRectController.width
  pptratio.height = boundingClientRectController.height
}

/**
 * 设置本地视频视图
 * @param {String} id
 */
const setupLocalVideo = id => {
  const RtcService = getRtcService()
  RtcService.createLocalVideo(id)
}

/**
 * 销毁远端视频视图
 */
const destroyRemoteVideo = uid => {
  studentOnStageLog.info('销毁远端视频视图')
  const RtcService = getRtcService()
  RtcService.destroyRemoteVideo(uid)
}

/**
 * 设置远端视频视图
 * @param {*} uid uid
 * @param {*} id id
 */
const setupRemoteVideo = (uid, id) => {
  const RtcService = getRtcService()
  RtcService.createRemoteVideo(uid, id)
}

/**
 * 处理表情消息
 */
const handleEmoticon = (uid, params) => {
  data.value.forEach(item => {
    if (item.userId == uid) {
      item.emoticonName = params.name
      item.emoticonType = params.type
      item.emojiId = params.emojiId
      item.lottieUrl = params.lottieUrl
    }
  })
}

/**
 * 清除表情
 */
const handleClearEmoticon = userId => {
  data.value.forEach(item => {
    if (item.userId == userId) {
      item.emoticonName = ''
      item.emoticonType = ''
      item.emojiId = ''
      item.lottieUrl = ''
    }
  })
}

/**
 * 处理摄像头状态切换
 */
const handleSwitchCameraStatus = status => {
  updateStudentVal(stuId.value, 'cameraIsOpen', status ? 1 : 2)
}

// 事件监听函数
const listenRemoteVideoStateChanged = ({ uid, mute }) => {
  studentOnStageLog.info(
    `上台用户视频状态变化，uid：${uid},视频状态：${mute === true ? '关闭' : '开启'}`
  )
  data.value.forEach(item => {
    if (uid == item.userId) {
      if (mute) {
        ///关闭摄像头
        item.cameraIsOpen = 2
      } else {
        /// 值为1  表示当前用户打开了摄像头
        ///正在推流
        if (
          document.getElementById(`mult-video-${uid}`) &&
          !document.getElementById(`mult-video-${uid}`).innerHTML
        ) {
          setTimeout(() => {
            setupRemoteVideo(uid, `mult-video-${uid}`)
          }, 150)
        }
        /// 值为1  表示当前用户打开了摄像头
        item.cameraIsOpen = 1
      }
    }
  })
}

const listenRemoteAudioStateChanged = ({ uid, mute }) => {
  // 远端禁用音频
  if (mute) {
    updateStudentVal(uid, 'micIsOpen', 2)
  } else {
    updateStudentVal(uid, 'micIsOpen', 1)
  }
}

const listenGroupAudioVolumeIndication = speakers => {
  // @log-ignore
  speakers.forEach(item => {
    updateStudentVal(item.uid == 0 ? stuId.value : item.uid, 'volume', item.volume)
  })
}

const listenUpdateMicrophoneStatus = status => {
  emitter.emit('raiseHandSendMessageToTeacher', {
    type: 127
  })
  updateStudentVal(stuId.value, 'micIsOpen', status ? 1 : 2)
}

const listenLocalEmoticonMessage = params => {
  handleEmoticon(stuId.value, params)
}

const listenOnRecvRoomMessage = res => {
  const { content, fromUserInfo } = res
  console.log(content)
  const { ircType, data: msgData } = content
  const commonMsg = {
    name: msgData?.name,
    type: msgData?.type
  }
  // 处理表情消息
  if (ircType == 'send_emoji') {
    handleEmoticon(fromUserInfo.userId, {
      ...commonMsg,
      emojiId: msgData?.emojiId,
      lottieUrl: msgData?.lottieUrl
    })
  }
}

// 绑定事件
const bindEvent = () => {
  const RtcService = getRtcService()
  const SignalService = getIrcService()
  RtcService.on('studentVideoMute', listenRemoteVideoStateChanged)
  RtcService.on('studentAudioMute', listenRemoteAudioStateChanged)
  RtcService.on('groupAudioVolumeIndication', listenGroupAudioVolumeIndication)
  emitter.on('sendEmoji', listenLocalEmoticonMessage)
  SignalService.on('onRecvRoomMessage', listenOnRecvRoomMessage)
}

// 解绑事件
const unbindEvent = () => {
  const RtcService = getRtcService()
  const SignalService = getIrcService()
  studentOnStageLog.info('multVideoLink-onBeforeUnmount', '解绑事件')

  RtcService.off('studentVideoMute', listenRemoteVideoStateChanged)
  RtcService.off('studentAudioMute', listenRemoteAudioStateChanged)
  RtcService.off('groupAudioVolumeIndication', listenGroupAudioVolumeIndication)
  emitter.off('sendEmoji', listenLocalEmoticonMessage)
  SignalService.off('onRecvRoomMessage', listenOnRecvRoomMessage)
}

// 监听数据变化
watch(
  stuList,
  (newVal, oldVal) => {
    studentOnStageLog.info('监听上下台消息,stuList发生改变', newVal)
    // 如果是历史消息，拉去别人的
    if (props.options.isHistory) {
      upDownClass(newVal, oldVal)
    }
    switch (msgType.value) {
      case 2:
      case 3:
      case 6: {
        // 6表示授权/取消授权涂鸦
        if (props.options.isHistory) {
          return
        }
        studentOnStageLog.info(
          'multVideoLink-mounted',
          'wathc',
          ircMsg.value,
          props.options.isHistory
        )
        upDownClass(newVal, oldVal)
        studentOnStageLog.info('datas', data.value)
        break
      }
      case 4: {
        // 主讲给上麦学生添加金币
        // 得到奖励的同学
        const list = newVal.filter(item => item.addCoin !== 0)
        // 清除之前的所得记录
        data.value.forEach(item => (item.addCoin = 0))
        list.forEach(element => {
          data.value.forEach(item => {
            if (element.userId === item.userId) {
              item.addCoin = element.addCoin
            }
          })
          if (element.userId == stuId.value) {
            // 广播更新金币
            emitter.emit('updateAchievement', 'add', element.addCoin)
          }
        })
        // 展示动效
        isShowCoins.value = true
        let timer = null
        if (timer) {
          return
        }
        timer = setTimeout(() => {
          // 清除动效
          isShowCoins.value = false
          clearTimeout(timer)
          timer = null
        }, 4000)
        break
      }
      case 5:
        // 主讲滑动回显窗口
        dragVideoView(newVal, oldVal)
        break
      default:
        break
    }
  },
  { deep: true }
)

// 监听台上学员变化
watch(data, newVal => {
  studentOnStageLog.info('multVideoLink-data', newVal)
  const userList = []
  newVal.forEach(item => {
    userList.push(item.userId)
  })
  store.updateVideoMicLinkUsers(userList)
})

// 监听摄像头状态
watch(
  () => cameraStatus.value,
  newVal => {
    handleSwitchCameraStatus(newVal)
  },
  { immediate: true }
)

// 监听麦克风状态
watch(
  () => microphoneStatus.value,
  newVal => {
    listenUpdateMicrophoneStatus(newVal)
  },
  { immediate: true }
)

// 生命周期钩子
onMounted(() => {
  bindEvent()
  nextTick(() => {
    getSize()
    window.addEventListener('resize', _debounce(getSize, 500))
  })
})

onBeforeUnmount(() => {
  setVideoEncoderConfiguration(false)

  upDownClass([], data.value)
  unbindEvent()

  // 移除事件监听
  window.removeEventListener('resize', _debounce(getSize, 500))
})
</script>

<style lang="scss" scoped>
.student-item {
  background-size: cover;
  position: absolute;
  z-index: 101;
  overflow: hidden;
  .item-block {
    position: relative;
    border-radius: 10px;
    height: 100%;
    background: url('@/components/VideoGroup/img/black-lines-bg.png');
    background-size: contain;
    &.default-item {
      border-radius: 10px;
      border: 0.4px solid rgb(216, 216, 216, 0.9);
    }
  }
}
.stu-name {
  color: #fff;
  height: 26px;
  line-height: 26px;
  padding-left: 8px;
  margin-right: 8px;
  border-radius: 6px;
}
.word-ellipsis {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.stu-video {
  position: absolute;
  width: 100%;
  height: 100%;
  color: #fff;
  overflow: hidden;
  border-radius: 10px;
}
.stu-img {
  width: 96px;
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
  }
  .decorate-frame {
    background-size: 95px 95px !important;
  }
}
.authgraffiti {
  width: 16px;
  height: 16px;
  margin-right: 11px;
  background: url('@/assets/images/smallClass/icon-auth-pen-new.png') center center no-repeat;
  background-size: 100% auto;
  margin-left: auto;
}
.bottom-bar {
  position: absolute;
  width: 100%;
  height: 32px;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  border-radius: 0 0 10px 10px;
}
.microphone-wrapper {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  z-index: 102;
  width: 16px;
  height: 16px;
  margin-right: 11px;
}
.medal-wrapper {
  position: absolute;
  left: 5px;
  top: 5px;
}
.level-icon {
  right: 6px;
  top: 6px;
}
.raise-hand-wrapper {
  position: absolute;
  right: 4px;
  top: 4px;
  z-index: 10;
}
.emoji-wrapper {
  // 表情区域
  position: absolute;
  background: rgba(0, 0, 0, 0.4);
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1;
  border-radius: 10px;
}
.decorate-frame {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
</style>
