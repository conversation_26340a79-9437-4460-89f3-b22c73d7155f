<template>
  <div class="teacher-on-stage" :class="classTypeName">
    <div class="teacher-on-stage-module" :class="[windowStatusClassName]" :style="[styleAttribute]">
      <div class="teacher-on-stage-video" :class="{ 'teacher-off': !teacherVideoStatus }">
        <div
          id="teacherOnStageVideo"
          :class="{ teacherOnStageVideoPostion: teacherVideoStatus }"
        ></div>
      </div>
      <div class="private-chat-title" v-if="isPrivateChat">
        {{ $t('classroom.modules.teacherOnStage.privateChat') }}
      </div>
      <div class="teacher-info">
        <div class="name">{{ teacherName }}</div>
        <div class="microphone-wrapper">
          <img src="../imgs/icon-micro-open.png" v-if="audioStatus" />
          <img src="../imgs/icon-micro-close.png" v-else />
        </div>
      </div>
      <div v-if="teacherOffline" class="video-error">
        <div class="wrapper">
          <div class="notice">
            <p>{{ $t('classroom.modules.teacherOnStage.notice[0]') }}</p>
            <p>{{ $t('classroom.modules.teacherOnStage.notice[1]') }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useClassData } from '@/stores/classroom'
import _debounce from 'lodash/debounce'
import { useRtcService } from '@/hooks/useRtcService'

const { getRtcService } = useRtcService()
// Props 定义
const props = defineProps({
  options: {
    type: Object,
    default: null
  }
})

// Emits 定义
const emit = defineEmits(['close'])

// Store
const store = useClassData()

// 响应式状态
const windowStatus = ref('normal') // normal: 正常 maximize: 最大化
const originXRatio = ref(0) // 上麦视图左上角X坐标相对于课件X坐标的比例
const originYRatio = ref(0) // 上麦视图左上角Y坐标相对于课件Y坐标的比例
const WRatio = ref(0) // 上麦视图宽度相对于课件宽度比例
const HRatio = ref(0) // 上麦视图高度相对于课件高度比例
const teacherName = ref('')
const isPrivateChat = ref(false) // 是否在私聊
const styleAttribute = ref({
  left: 0,
  top: 0,
  width: 0,
  height: 0
})
const classType = props.options.commonOption.classType

// 计算属性
const teacherVideoStatus = computed(() => !store.teacherInfo.videoMute)

const onStageStatus = computed(() => {
  return store.teacherInfo.onStageStatus
})

const teacherOffline = computed(() => {
  return !store.teacherInfo.inClass
})

const audioStatus = computed(() => {
  return !store.teacherInfo.audioMute
})

const canCreateVideo = computed(() => {
  // 已有视频流且开启老师上台
  return teacherVideoStatus.value && onStageStatus.value
})

const classTypeName = computed(() => {
  return classType == 2 ? 'small-class' : 'large-class'
})

const windowStatusClassName = computed(() => {
  return windowStatus.value
})

// 方法
const updateTeacherMsg = msg => {
  store.changeTeacherInfo(msg)
}

const listenerEvent = () => {
  window.addEventListener('resize', _debounce(updateWindowPosition, 500))
}

const handlerMessage = message => {
  const {
    pub,
    originXRatio: xRatio,
    originYRatio: yRatio,
    WRatio: wR,
    HRatio: hR,
    teacherName: name
  } = message
  console.log(message, 'messagemessage')
  if (onStageStatus.value != pub) {
    // 如果更改当前开启关闭状态就通知teacherVideo
    updateTeacherMsg({
      onStageStatus: pub
    })
  }
  if (pub) {
    originXRatio.value = xRatio
    originYRatio.value = yRatio
    WRatio.value = wR
    HRatio.value = hR
    teacherName.value = name
    // thinkClass.RtcService.resizeRender(teacherUid)
    nextTick(() => {
      updateWindowStatus(message.isMaximized ? 'maximize' : 'normal')
      updateWindowPosition()
    })

    // logger.send({
    //   tag: 'userTrack',
    //   content: {
    //     msg: '老师上台'
    //   }
    // })
  }
}

const updateWindowStatus = status => {
  windowStatus.value = status
}

const updateWindowPosition = () => {
  const boundingClientRectController = document.getElementById('stageLayer').getBoundingClientRect()
  let coursewareWidth = boundingClientRectController.width
  let coursewareHeight = boundingClientRectController.height
  styleAttribute.value.left = coursewareWidth * originXRatio.value + 'px'
  styleAttribute.value.top = coursewareHeight * originYRatio.value + 'px'
  styleAttribute.value.width = coursewareWidth * WRatio.value + 'px'
  styleAttribute.value.height = coursewareHeight * HRatio.value + 'px'
}

const createTeacherVideo = () => {
  nextTick(() => {
    const RtcService = getRtcService()
    RtcService.createTeacherVideo('teacherOnStageVideo')
  })
}

const removeTeacherVideo = () => {
  nextTick(() => {
    if (document.getElementById('teacherOnStageVideo')) {
      document.getElementById('teacherOnStageVideo').innerHTML = ''
    }
  })
}

// 开始函数
const receiveMessage = message => {
  isPrivateChat.value = message?.privateChat || false
  handlerMessage(message)
}

// 结束函数
const destroyInteraction = message => {
  receiveMessage(message)
  window.removeEventListener('resize', _debounce(updateWindowPosition, 500))
  removeTeacherVideo()
  emit('close', 'teacher_video_mic')
}

// 生命周期钩子
onMounted(() => {
  listenerEvent()
})

// 监听器
watch(
  canCreateVideo,
  val => {
    if (val) {
      createTeacherVideo()
    } else {
      removeTeacherVideo()
    }
  },
  { immediate: true }
)

// 暴露方法给父组件
defineExpose({
  receiveMessage,
  destroyInteraction
})
</script>

<style lang="scss" scoped>
.teacher-on-stage {
  &.large-class {
    position: absolute;
    top: 44px;
  }
}
.teacher-on-stage-module {
  position: absolute;
  z-index: 999;
  overflow: hidden;
  &.maximize .teacher-on-stage-video.teacher-off {
    background: url('./imgs/img-bg.png') no-repeat center;
    background-size: cover;
  }
  &.maximize .teacher-info {
    border-radius: 0px;
  }
  .teacher-on-stage-video {
    width: 100%;
    height: 100%;
    background: #000;
    &.teacher-off {
      background: url('./imgs/on-stage.png') no-repeat center;
      background-size: cover;
    }
  }
  .private-chat-title {
    position: absolute;
    left: 5px;
    top: 5px;
    border-radius: 4px;
    color: #fff;
    padding: 3px 5px 3px 18px;
    background: #ff5353 url('./imgs/chat.png') no-repeat 5px center;
    background-size: 10px 10px;
  }
  .teacher-info {
    position: absolute;
    left: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    right: 0;
    height: 26px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
    border-radius: 0px 0px 4px 4px;
    .name {
      margin-left: 9px;
      line-height: 26px;
      font-size: 12px;
      color: #f4f6fa;
      overflow: hidden;
    }
    .microphone-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 16px;
      height: 16px;
      margin: 0 9px 0 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .video-error {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #3367ff;
    .wrapper {
      position: relative;
      width: 100%;
      height: 160px;
      background: url('./imgs/bg-error.png') no-repeat center;
      background-size: 178px 160px;
      display: flex;
      justify-content: center;
      align-items: center;
      .notice {
        position: absolute;
        bottom: 0;
      }
      p {
        text-align: center;
        color: #fff;
        line-height: 14px;
        font-size: 12px;
      }
    }
  }
}
.teacherOnStageVideoPostion {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}
</style>
