<template>
  <div class="stage-layer" id="stageLayer">
    <template v-for="interaction in interactions" :key="interaction.name">
      <component
        :ref="
          el => {
            if (el) interactionRefs[interaction.name] = el
          }
        "
        :is="componentsMap[interaction.componentName]"
        v-if="interaction.isShow"
        :options="interaction.options"
        @close="interactionClose"
      ></component>
    </template>
  </div>
</template>

<script setup>
import { reactive, onMounted, onBeforeUnmount, nextTick, ref } from 'vue'
import { useClassData } from '@/stores/classroom'
import TeacherOnStage from './teacherOnStage/index.vue'
import StudentOnStage from './multVideoLink/index.vue'
import { emitter } from '@/hooks/useEventBus'

// 定义组件名称
defineOptions({
  name: 'OnStageLayer'
})

// 创建组件映射
const componentsMap = {
  TeacherOnStage,
  StudentOnStage
}

// 获取 store
const store = useClassData()

// 创建引用对象存储互动组件实例
const interactionRefs = ref({})

// 互动组件列表
const interactions = reactive([
  {
    name: 'TeacherOnStage',
    componentName: 'TeacherOnStage', // 这里的名称需要与componentsMap中的键匹配
    layout: 'full',
    isShow: false,
    keys: ['teacher_video_mic', 'graffiti_board_video'],
    options: {
      // 使用 store 的值而不是 store.state
      ...store.baseData,
      ircMsg: '',
      isHistory: false
    }
  },
  {
    name: 'StudentOnStage',
    componentName: 'StudentOnStage', // 这里的名称需要与componentsMap中的键匹配
    layout: 'full',
    isShow: false,
    keys: ['mult_video_mic'],
    options: {
      // 使用 store 的值而不是 store.state
      ...store.baseData,
      ircMsg: '',
      isHistory: false
    }
  }
])

/**
 * 根据互动类型查找互动组件
 * @param {string} key 互动类型
 * @returns {object} 互动组件对象
 */
function findInteraction(key) {
  const interaction = interactions.find(item => item.keys.includes(key))
  return interaction
}

/**
 * 处理互动组件关闭事件
 * @param {string} key 互动类型
 */
function interactionClose(key) {
  const interaction = findInteraction(key)
  if (interaction) {
    interaction.isShow = false
  }
}

/**
 * 处理打开互动组件
 * @param {object} interaction 互动组件对象
 * @param {object} options 互动选项
 */
const handleOpenInteraction = (interaction, options) => {
  if (!interaction) return

  interaction.isShow = true
  interaction.options.ircMsg = options.noticeContent
  interaction.options.isHistory = options.isHistory

  nextTick(() => {
    const interactionRef = interactionRefs.value[interaction.name]
    if (interactionRef && interactionRef.receiveMessage) {
      interactionRef.receiveMessage(options.noticeContent)
    }
  })
}

// 生命周期钩子
onMounted(() => {
  // 监听互动事件
  emitter.on('onStage', options => {
    try {
      const { key, noticeContent, isHistory, sendTime } = options
      const { pub, open, publishTopic } = noticeContent || {}

      const interaction = findInteraction(key)
      if (!interaction) return

      if (pub || open || publishTopic) {
        store.updateInteractionStatus({
          interactionName: key,
          interactionStatus: true
        })

        interaction.isShow = true
        interaction.options.ircMsg = noticeContent
        interaction.options.isHistory = isHistory
        console.log(111111, isHistory)
        interaction.options['sendTime'] = sendTime

        // 如果当前同一类型互动是打开状态，则先销毁上一个再打开新的互动
        if (interaction.isShow && interaction.id !== noticeContent.interactId) {
          interaction.isShow = false
          interaction.id = noticeContent.interactId
          nextTick(() => {
            handleOpenInteraction(interaction, options)
          })
        } else {
          handleOpenInteraction(interaction, options)
        }
      } else if (
        pub === false ||
        open === false ||
        pub === 0 ||
        open === 0 ||
        publishTopic === false // publishTopic 是答题板私有控制，答题板的关闭，是由端上的倒计时决定的
      ) {
        store.updateInteractionStatus({
          interactionName: key,
          interactionStatus: false
        })

        // 有些互动在收到停止作答和结束互动信令时还需要例如提交答案等操作，因此需要内部处理
        const interactionRef = interactionRefs.value[interaction.name]
        if (interactionRef && interactionRef.destroyInteraction) {
          interactionRef.destroyInteraction(noticeContent)
          return
        }
        interaction.isShow = false
      }
    } catch (error) {
      console.error('Error in onStage event handler:', error)
    }
  })
})

onBeforeUnmount(() => {
  // 移除事件监听
  emitter.off('onStage')
})
</script>

<style lang="scss" scoped>
.stage-layer {
  width: 100%;
  height: 100%;
}
</style>
