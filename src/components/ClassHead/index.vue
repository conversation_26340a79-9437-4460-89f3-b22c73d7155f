<template>
  <div class="live-header">
    <div class="windows-exit">
      <div class="button button-exit" @click="handleBack"></div>
    </div>
    <div class="title">
      <span class="plan-name" v-if="planName">
        {{ planName }}
      </span>
    </div>
    <div class="right">
      <div class="item">
        <!-- <NetworkStatus @update-network-quality="updateNetworkQuality" /> -->
      </div>
      <!-- <PrintScreen @click="printScrenClick" /> -->
      <div class="item">
        <div class="button button-refresh" @click="handleRefresh"></div>
      </div>
    </div>
    <Header></Header>
  </div>
</template>
<script setup>
import { Modal } from 'ant-design-vue';
import { useRouter } from 'vue-router'
import Header from './Header/index.vue'
const router = useRouter()
defineOptions({
  name: 'ClassHead',
})
defineProps({
  planName: {
    type: String,
    default: 'xx',
  },
})
const handleBack = () => {
  console.log('handleBack')
  Modal.confirm({
    title: '确定退出课堂吗？',
    okText: '确定',
    cancelText: '取消',
    onOK() {
      router.push({
        path: '/',
      })
      console.log('确定')
    },
    onCancel() {
      console.log('取消')
    }
  })
}
const handleRefresh = () => {
  console.log('handleRefresh')
  window.location.reload()
}
</script>

<style lang="scss" scoped>
.live-header {
  background: #1a1a1a;
  position: relative;
  height: 44px;
  display: flex;
  align-items: center;
  z-index: 999;

  .title {
    width: 100%;
    height: 44px;
    line-height: 44px;
    font-size: 16px;
    color: #dee2e7;
    font-weight: 500;
    text-align: center;
    .plan-name {
      width: 40%;
      overflow: hidden;
      display: inline-block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .windows-exit {
    position: absolute;
    left: 0;
    left: 12px;
    .button-exit {
      transform: rotate(180deg);
      background-image: url('@/assets/images/live/icon-exit.png');
      &:hover {
        background-image: url('@/assets/images/live/icon-exit-hover.png');
      }
    }
    .button {
      width: 30px;
      height: 30px;
      background-size: cover;
      cursor: pointer;
    }
  }

  .right {
    position: absolute;
    right: 12px;
    z-index: 1;
    display: flex;
    align-items: center;

    .item {
      margin-left: 20px;
      cursor: pointer;
    }

    .button {
      width: 30px;
      height: 30px;
      background-size: cover;
      cursor: pointer;
    }

    .button-exam {
      background-image: url('@/assets/images/live/icon-exam2.png');
      &:hover {
        background-image: url('@/assets/images/live/icon-exam2-hover.png');
      }
    }

    .button-refresh {
      background-image: url('@/assets/images/live/icon-refresh.png');

      &:hover {
        background-image: url('@/assets/images/live/icon-refresh-hover.png');
      }
    }

    .button-more {
      background-image: url('@/assets/images/live/icon-more.png');

      &:hover {
        background-image: url('@/assets/images/live/icon-more-hover.png');
      }
    }

    .button-feedback {
      cursor: pointer;
      background-image: url('@/assets/images/live/icon-feedback.png');

      &:hover {
        background-image: url('@/assets/images/live/icon-feedback-hover.png');
      }
    }

    .button-homework {
      background-image: url('@/assets/images/live/icon-homework2.png');
      position: relative;
      &:hover {
        background-image: url('@/assets/images/live/icon-homework2-hover.png');
      }
      span {
        width: 6px;
        height: 6px;
        background: linear-gradient(180deg, #ff66b6 0%, #ff3636 100%);
        display: inline-block;
        position: absolute;
        right: -5px;
        border-radius: 50%;
      }
    }
  }
}
</style>
