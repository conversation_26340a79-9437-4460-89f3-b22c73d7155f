<template>
  <div class="photoWallContainer correctBox" id="correctPhotoWallContainer">
    <div class="toolbar correctBar" v-if="showCamera || showGraffiti">
      <i class="back" @click="goClassRoom"></i>
    </div>
    <!-- 相机拍照功能-->
    <camera
      v-if="showCamera && !showGraffiti"
      :options="correctOptions"
      :delay="cameraDelayTime"
      @changeTakePhotoWay="changeTakePhotoWay"
      @submit="submitPhoto"
      @photoData="getPhotoData"
    />
    <!-- 涂鸦功能 -->
    <graffiti
      v-if="showGraffiti && !showCamera"
      @submit="submitPhoto"
      @changeTakePhotoWay="changeTakePhotoWay"
      @photoData="getPhotoData"
      :options="correctOptions"
      :cameraAccess="cameraAccess"
      :closeInteractive="closeInteractive"
    />
    <!--上传进度条：拍照、涂鸦 共用-->
    <uploadProgress :percent="percent" :visible="showUploadProgress" />
  </div>
</template>

<script>
import camera from '@/interactions/TakePicture/components/camera.vue'
import graffiti from '@/interactions/TakePicture/components/graffiti.vue'
import uploadProgress from '@/interactions/TakePicture/components/progress.vue'
import PhotoWall from '@/interactions/TakePicture/photoWall.js'
import { useMediaAccess } from '@/hooks/useMediaAccess.js'
import Upload from '@/utils/upload'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { emitter } from '@/hooks/useEventBus'
import { message } from 'ant-design-vue'
export default {
  name: 'correctAnswerPhotoWall',
  data() {
    return {
      showCamera: true, // 是否显示相机
      showGraffiti: false, // 是否显示涂鸦
      cameraDelayTime: 2000, // 相机延迟调用2秒
      percent: 0, // 上传进度
      photoData: false, // 图片数据
      cameraAccess: true, // 摄像头权限
      showUploadProgress: false,
      closeInteractive: false, // 互动是否结束，主要是给涂鸦用
      correctOptions: null, // 订正基础数据
      photoWallOptions: null,
      chatMsgPriority: {
        topic: 0,
        notice: 1,
        privMsg: 99
      }
    }
  },
  props: {
    isPhotoWallShow: {
      type: Boolean,
      default: false
    },
    currentInteractionId: {
      type: Number,
      default: 0
    }
  },
  components: {
    camera,
    graffiti,
    uploadProgress
  },
  watch: {
    showCamera: {
      handler(val) {
        this.photoWallOptions = this.getOptions()
        const isBusy = val ? 1 : 2
        this.sendPeerMessageToTutor(isBusy) //开启拍照上墙，告诉辅导摄像头被占用
      },
      deep: true,
      immediate: true
    }
  },
  async mounted() {
    if (!this.photoWallOptions) return // 没有互动数据，啥事也干不了，这里直接 return
    // 这里检查一下摄像头权限，如果没有摄像头权限，则直接进入涂鸦订正

    const { cameraAccess, getCameraAccess } = useMediaAccess()
    await getCameraAccess()
    if (cameraAccess.value !== true) {
      this.sendLogger(`作业盒子批改拍照上墙初始化摄像头无权限，code:${cameraAccess}`)
      this.showCamera = this.cameraAccess = false
      this.showGraffiti = true
    }
    this.correctOptions = this.photoWallOptions
    console.log('看下相关的photoWallOptions:::', this.photoWallOptions)
    this.photoWall = new PhotoWall({
      ...this.correctOptions
    })
    this.sendLogger('进入订正拍照环节')
  },
  beforeUnmount() {
    this.sendPeerMessageToTutor(2)
  },
  methods: {
    sendPeerMessageToTutor(isBusy) {
      const content = {
        type: 170,
        isFunction: 1,
        msg: '拍照上墙',
        parameter: {
          cameraBusy: isBusy
        }
      }
      window.ChatClient.PeerChatManager.sendPeerMessage(
        [{ nickname: this.photoWallOptions.roomMessage.roomInfo.configs.tutorIrcId }],
        JSON.stringify(content),
        this.chatMsgPriority.privMsg
      )
    },
    getOptions() {
      const options = localStorage.getItem('photoWallOptions')
      let photoWallOptions = ''
      try {
        photoWallOptions = JSON.parse(options)
      } catch (error) {
        console.error('重新拍照获取option格式化错误', options)
      }
      return photoWallOptions
    },
    changeTakePhotoWay(way) {
      if (way === 'graffiti') {
        this.showCamera = false
        this.showGraffiti = true
      } else {
        this.showCamera = true
        this.showGraffiti = false
      }
    },
    /**
     * 提交数据
     */
    async submitPhoto() {
      this.sendLogger(`点击提交订正，correctOptions：${this.correctOptions}`)
      if (!this.correctOptions) return // 没有互动数据，是不能提交的
      this.showUploadProgress = true
      const upload = new Upload({
        scene: 'pictureWall'
      })
      const { key: uploadKey } = await upload.putFile({
        filePath: 'pictureWall.jpg',
        file: this.photoData.blob,
        progress: percent => {
          this.percent = percent
        },
        success: res => {
          this.sendLogger(`上传照片成功, url: ${res.url}`)
          classLiveSensor.osta_ia_photowall_submit(this.currentInteractionId, res)
        },
        fail: err => {
          this.percent = 0 // 上传进度清0
          this.photoWall.showSubmitTip({
            text: this.$t('common.uploadFailed'),
            icon: 'error',
            dom: 'correctPhotoWallContainer'
          })
          console.error('上传照片失败', err)
          this.sendLogger('上传照片失败')
        }
      })
      // 提交数据到后台
      if (uploadKey) {
        this.showUploadProgress = false
        const submitRes = await this.photoWall.submitPhoto(uploadKey)
        this.sendLogger(`提交照片地址，增加金币，submitRes：${submitRes}`)
        if (submitRes.code === 0 && submitRes.data) {
          this.showGraffiti = this.showCamera = false
          if (!this.isPhotoWallShow) {
            emitter.emit('uploadSuccess', {
              tips: this.$t('common.sumbittedSuccessfully'),
              coins: 0
            })
            this.photoWall.showCoinsTip({
              callback: () => {
                this.sendPeerMessageToTutor(2)
                this.$emit('destoryCorrectDom')
              }
            })
          } else {
            this.sendPeerMessageToTutor(2)
            this.$emit('destoryCorrectDom')
          }
        } else {
          this.percent = 0 // 上传进度清0
          message.error(this.$t('common.submittedError'))
          this.sendPeerMessageToTutor(2)
          this.$emit('destoryCorrectDom')
        }
      }
    },
    /**
     * 获取各组件发来的图片数据
     */
    getPhotoData(res) {
      // @log-ignore
      this.photoData = res
    },
    /**
     * 订正返回教室
     */
    goClassRoom() {
      this.showCamera = this.showGraffiti = false
      this.sendPeerMessageToTutor(2)
      this.$emit('destoryCorrectDom')
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '') {
      console.log('msg')
    }
  }
}
</script>

<style lang="scss" scoped>
@use '@/interactions/TakePicture/style/common.scss' as *;
@use '@/interactions/TakePicture/style/app.scss' as *;
.correctBox {
  position: fixed;
  z-index: 1003;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  .correctBar {
    margin: 20px 0 0 20px;
  }
}
</style>
