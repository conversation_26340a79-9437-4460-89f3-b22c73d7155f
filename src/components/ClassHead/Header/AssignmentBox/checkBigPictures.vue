<template>
  <Modal
    id="big-image-tag"
    wrapClassName="small-class"
    :closable="false"
    :maskClosable="false"
    :footer="null"
    :getContainer="getContainer"
    v-model:open="visibleBigImage"
    :title="t('classroom.modules.assignmentBox.modalTitle')"
  >
    <div :class="[`content-${boxinfoMap[boxMsg.newCorrectStatus]}`, 'content-header']">
      <span
        class="img-title-top"
        :class="[
          `content-${boxinfoMap[boxMsg.newCorrectStatus]}`,
          `img-title-${boxinfoMap[boxMsg.newCorrectStatus]}`
        ]"
      ></span>
      <span @click="closeBigPicture" class="bigpicture-close"></span>
    </div>
    <div class="picture-content">
      <div class="show-img">
        <img
          v-if="boxMsg.photoUrl || boxMsg.correctUrl"
          :src="
            boxMsg.newCorrectStatus == 0 || boxMsg.newCorrectStatus == 3
              ? boxMsg.photoUrl
              : boxMsg.correctUrl
          "
        />
        <img v-else src="@/assets/images/live/assignmnetBigPicture.png" alt="" />
      </div>
    </div>
    <div
      v-if="[2, 5].includes(boxMsg.newCorrectStatus) && boxMsg.isShowIncorrectModify"
      class="reupload picture-reupload"
      @click="openPhotoWall"
    >
      <img src="@/assets/images/live/icon_correction.png" />
      {{ t('classroom.modules.assignmentBox.checkBigPictures.buttonName') }}
    </div>
  </Modal>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { emitter } from '@/hooks/useEventBus'
import { Modal } from 'ant-design-vue'
// 使用国际化
const { t } = useI18n()

// Props 定义
defineProps({
  boxMsg: {
    type: Object,
    default: () => ({})
  }
})

// Emits 定义
const emit = defineEmits(['closeBigPicture', 'openPhotoWallFormBigPicture'])

// 响应式状态
const visibleBigImage = ref(true)
const boxinfoMap = ref({
  0: 'grading',
  1: 'correct',
  2: 'incorrect',
  3: 'grading',
  5: 'half-correct'
})
// 获取容器元素
const getContainer = () => {
  return document.getElementById('assignment-container')
}

// 方法
const closeBigPicture = () => {
  // logger.send({
  //   tag: 'student.Interact',
  //   content: {
  //     msg: '点击关闭大图',
  //     interactType: 'Wall',
  //   },
  // })
  emit('closeBigPicture')
}

const openPhotoWall = () => {
  console.log('[测试bug]点击触发')
  // logger.send({
  //   tag: 'student.Interact',
  //   content: {
  //     msg: '点击查看练习按钮',
  //     interactType: 'Wall',
  //   },
  // })
  emit('openPhotoWallFormBigPicture')
  closeBigPicture()
}

// 生命周期钩子
onMounted(() => {
  nextTick(() => {
    const bigImgDom = document.querySelector('#big-image-tag')
    emitter.emit('emitBigImgDom', bigImgDom)
  })
})
</script>

<style lang="scss">
.small-class {
  // left: 50% !important;
  // transform: translateX(-50%) !important;
  // bottom: 16px !important;
  // overflow: hidden !important;
  // right: auto !important;
  // top: auto !important;
  .ant-modal {
    // top: 0px;
    // padding-bottom: 0px;
    width: 660px !important;
    // z-index: 101;
  }
  .ant-modal-content {
    background: transparent;
    padding: 0;
  }
  .ant-modal-header {
    display: none;
  }
  .ant-modal-body {
    padding: 0;
  }
  .picture-content {
    border-radius: 0px 0px 10px 10px;
    width: 100%;
    padding: relative;
    padding: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    .show-img {
      position: relative;
      overflow: hidden;
      border-radius: 8px;
      img {
        max-height: calc(100vh - 160px);
        width: 100%;
        -o-object-fit: contain;
        object-fit: contain;
      }
    }
  }
  .content-header {
    height: 50px;
    border-radius: 10px 10px 0px 0px;
    position: relative;
  }
  .content-correct {
    background: #65d555;
  }
  .content-half-correct {
    background: #ffb234;
  }
  .content-incorrect {
    background: #ff7434;
  }
  .content-grading {
    background: #39a1ff;
  }
  .img-title-top {
    width: 100px;
    height: 22px;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: contain;
    position: absolute;
    z-index: 888888;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }
  .img-title-correct {
    background-image: url('@/assets/images/live/img-title-correct.png');
  }
  .img-title-half-correct {
    width: 195px;
    background-image: url('@/assets/images/live/img-title-half-correct.png');
  }
  .img-title-incorrect {
    width: 124px;
    background-image: url('@/assets/images/live/img-title-incorrect.png');
  }
  .img-title-grading {
    background-image: url('@/assets/images/live/img-title-grading.png');
  }
  .bigpicture-close {
    width: 22px;
    height: 22px;
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100%;
    position: absolute;
    top: 12px;
    right: 12px;
    background-image: url('@/assets/images/live/icon_bigpoctrue_close.png');
    cursor: pointer;
  }
  .reupload {
    background: rgba(0, 0, 0, 0.6);
    border-radius: 100px 0px 0px 100px;
    position: absolute;
    top: 66px;
    right: 0px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .picture-reupload {
    height: 30px;
    color: #fff;
    font-size: 14px;
    font-weight: 600;
    line-height: 30px;
    padding: 0 16px;
    img {
      width: 18px;
      height: 18px;
      margin-right: 6px;
    }
  }
  .picture-content {
    min-height: 496px;
  }
  .show-img {
    width: 644px;
  }
}
.lottie-player {
  position: absolute;
}
</style>
