<template>
  <div v-if="networkStatus" class="network-status" :class="networkStatusClass">
    <div class="status-button" @mouseenter="handleMouseenter" @mouseleave="handleMouseleave"></div>
    <div v-if="showStatusPanel" class="status-panel">
      <div class="panel-wrapper">
        <div class="title-wrapper">
          <div class="icon" />
          <div class="title">
            {{ statusConfig.title }}
          </div>
        </div>
        <div v-if="statusConfig.description" class="description">
          {{ statusConfig.description }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useClassData } from '@/stores/classroom'
// 使用国际化
const { t } = useI18n()

// 响应式状态
const showStatusPanel = ref(false)
const store = useClassData()

// 下行网络质量
const downlinkNetworkQuality = computed(() => store.networkQuality)

// 网络状态
const networkStatus = computed(() => {
  const statusMap = {
    // '0': 'weak',
    1: 'good',
    2: 'good',
    3: 'normal',
    4: 'weak',
    5: 'weak',
    6: 'weak'
  }
  return statusMap[downlinkNetworkQuality.value] || ''
})

// 状态文案配置
const statusConfig = computed(() => {
  const statusMap = {
    good: {
      title: t('classroom.modules.networkStatus.statusMap.good.title')
    },
    normal: {
      title: t('classroom.modules.networkStatus.statusMap.normal.title')
    },
    weak: {
      title: t('classroom.modules.networkStatus.statusMap.weak.title'),
      description: t('classroom.modules.networkStatus.statusMap.weak.description')
    }
  }
  return statusMap[networkStatus.value] || {}
})

// 网络状态样式
const networkStatusClass = computed(() => {
  return networkStatus.value ? `status-${networkStatus.value}` : ''
})

// 方法
const handleMouseenter = () => {
  showStatusPanel.value = true
}

const handleMouseleave = () => {
  showStatusPanel.value = false
}
</script>

<style lang="scss" scoped>
.network-status {
  position: relative;
  z-index: 1;

  &.status-good {
    .status-button {
      background-image: url('./images/icon-wifi-good.png');
    }
    .panel-wrapper {
      min-width: 215px;
    }
    .status-panel {
      .icon {
        background-image: url('./images/icon-wifi-good-tone.png');
      }
      .title {
        color: #02ca8a;
      }
    }
  }

  &.status-normal {
    .status-button {
      background-image: url('./images/icon-wifi-normal.png');
    }
    .panel-wrapper {
      min-width: 225px;
    }
    .status-panel {
      .icon {
        background-image: url('./images/icon-wifi-normal-tone.png');
      }
      .title {
        color: #ffaa0a;
      }
    }
  }

  &.status-weak {
    .status-button {
      background-image: url('./images/icon-wifi-weak.png');
    }
    .panel-wrapper {
      min-width: 225px;
    }
    .status-panel {
      .icon {
        background-image: url('./images/icon-wifi-weak-tone.png');
      }
      .title {
        color: #ff503f;
      }
    }
  }

  .status-button {
    width: 30px;
    height: 30px;
    background-size: cover;
    cursor: pointer;
    margin-left: 20px;
  }

  .status-panel {
    position: absolute;
    top: 38px;
    right: 0;
    white-space: nowrap;

    .panel-wrapper {
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0px 1px 6px 0px rgba(188, 188, 188, 0.4);
    }

    .title-wrapper {
      display: flex;
      align-items: center;
      height: 36px;
      padding: 0 8px;
      background: #fff;

      .icon {
        width: 22px;
        height: 22px;
        margin-right: 8px;
        background-size: cover;
      }

      .title {
        font-size: 16px;
      }
    }

    .description {
      padding: 8px;
      background: #f4f6fa;
      color: #a2aab8;
      font-size: 16px;
    }
  }
}
</style>
