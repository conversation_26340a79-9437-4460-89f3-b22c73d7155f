<template>
  <div class="chartNum">
    <audio :src="getCoinsAudio" class="hide" ref="getCoinsSound"></audio>
    <div class="box-item">
      <li class="number-item" v-for="(item, index) in orderNum" :key="index">
        <span>
          <i ref="numberItem" :class="{ addAnimate: !isShowNum }"></i>
        </span>
      </li>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { emitter } from '@/hooks/useEventBus'
import getCoinsAudio from '../audio/get-coins.mp3'

// 定义 props
const props = defineProps({
  startNum: {
    type: Number,
    default: 0
  },
  endNum: {
    type: Number,
    default: 0
  },
  // 延迟滚动时间，如果不播放飞金币，只翻转情况，应设置为0
  delayTime: {
    type: Number, // 单位秒
    default: 1
  }
})

// 定义 emits
const emit = defineEmits(['end'])

// 响应式状态
const orderNum = ref(props.startNum.toString().split(''))
const timerClose = ref(null)
const timer = ref(null)
const animationTime = ref(1)
const isShowNum = ref(true)
const numberItem = ref([])
const getCoinsSound = ref(null)

// 设置文字滚动
const setNumberTransform = (isSkipCompare = false) => {
  const numberItems = numberItem.value // 拿到数字的ref，计算元素数量
  const numberArr = orderNum.value.filter(item => !isNaN(item))
  const startNumberArr = props.startNum
    .toString()
    .split('')
    .filter(item => !isNaN(item))

  // 结合CSS 对数字字符进行滚动,显示订单数量
  for (let index = 0; index < numberItems.length; index++) {
    const elem = numberItems[index]
    let isEqualBefore = 0
    if (!isSkipCompare && startNumberArr[index] && numberArr[index] === startNumberArr[index]) {
      isEqualBefore = 1
    }
    elem.style['background-position-y'] = `-${numberArr[index] * 18 + 180 * isEqualBefore}px`
  }
}

// 定时增长数字
const increaseNumber = delayTime => {
  setNumberTransform(true)

  // 若需要飞金币特效，则需要金币结束飞之后再播放翻转数字
  if (delayTime > 0) {
    const handleEndCoinFly = () => {
      orderNum.value = props.endNum.toString().split('')
      isShowNum.value = false
      nextTick(() => {
        setNumberTransform(false)
        getCoinsSound.value?.play()
      })
      timerClose.value = setTimeout(() => {
        emit('end')
      }, animationTime.value * 1000)

      // 移除事件监听，避免内存泄漏
      emitter.off('endCoinFly', handleEndCoinFly)
    }

    emitter.on('endCoinFly', handleEndCoinFly)
  } else {
    // 只需要播放翻转数字效果，定时器即可
    timer.value = setTimeout(() => {
      orderNum.value = props.endNum.toString().split('')
      isShowNum.value = false
      nextTick(() => {
        setNumberTransform(false)
        getCoinsSound.value?.play()
      })
    }, delayTime * 1000)

    timerClose.value = setTimeout(
      () => {
        emit('end')
      },
      (delayTime + animationTime.value) * 1000
    )
  }
}

// 生命周期钩子
onMounted(() => {
  increaseNumber(props.delayTime)
})

onBeforeUnmount(() => {
  clearTimeout(timer.value)
  clearTimeout(timerClose.value)
})

// 定义组件名称
defineOptions({
  name: 'CountToComponent'
})
</script>

<style scoped lang="scss">
/*具体值value总量滚动数字设置*/
.box-item {
  position: relative;
  font-size: 100px;
  line-height: 40px;
  text-align: center;
  list-style: none;
  writing-mode: vertical-lr;
  text-orientation: upright;
  /*文字禁止编辑*/
  -moz-user-select: none; /*火狐*/
  -webkit-user-select: none; /*webkit浏览器*/
  -ms-user-select: none; /*IE10*/
  -khtml-user-select: none; /*早期浏览器*/
  user-select: none;
  /* overflow: hidden; */
}

/*滚动数字设置*/
.number-item {
  & > span {
    width: 10px;
    height: 18px;
    display: block;
    overflow: hidden;

    & > i {
      font-style: normal;
      width: 10px;
      height: 180px;
      display: block;
      background: url(../imgs/num-bg.png) repeat-y;
      background-position: 0px 0px;
      background-size: 100% 100%;
    }

    .addAnimate {
      transition: background-position 1s cubic-bezier(0, 0.51, 0, 0.98);
    }
  }
}
</style>
