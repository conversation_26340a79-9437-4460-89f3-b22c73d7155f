<template>
  <canvas v-if="isShow" id="flyCoins" :style="{ left: left, top: top }"></canvas>
</template>
<script>
import { emitter } from '@/hooks/useEventBus'
import { getAssetPath } from '@/utils/assetPath'

export default {
  data() {
    return {
      left: '0px',
      top: '0px',
      isShow: false,
      PAG: null,
      pagFile: null
    }
  },
  mounted() {
    this.$nextTick(() => {
      window.addEventListener('resize', this.resizeHandler)
      emitter.on('coinFly', obj => {
        console.log('看下执行定向金币 coinFly 来了', obj)
        this.setPosition(obj)
      })
    })
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.resizeHandler)
  },
  methods: {
    setPosition(obj = {}) {
      this.isShow = true
      this.$nextTick(() => {
        console.log('coinFly1', obj?.id)
        console.log('看下执行定向金币 coinFly in  obj?.id', obj?.id)

        const ele = obj.id
          ? document.getElementById(obj.id)?.getBoundingClientRect()
          : document.getElementById('coin-position')?.getBoundingClientRect()
        const flyEle = document.getElementById('flyCoins')?.getBoundingClientRect()
        this.left = ele.right - ele.width / 2 - flyEle.width * 0.7 + 'px'
        this.top = obj.top ? obj.top : '0px'
        this.initCanvas()
      })
    },
    resizeHandler() {
      if (this.isShow) this.setPosition()
    },
    async initCanvas() {
      if (!window.PAG) {
        window.PAG = await window.libpag.PAGInit()
      }
      const url = getAssetPath('/pagfiles/flyCoinsPag.pag')
      fetch(url)
        .then(response => response.blob())
        .then(async blob => {
          const file = new window.File([blob], url.replace(/(.*\/)*([^.]+)/i, '$2'))
          const pagFile = await window.PAG.PAGFile.load(file)
          const pagView = await window.PAG.PAGView.init(pagFile, '#flyCoins')
          pagView.setRepeatCount(1)
          pagView.addListener('onAnimationEnd', () => {
            console.log('看下执行定向金币 coinFly in 飞完了')

            this.isShow = false
            emitter.emit('endCoinFly')
          })
          await pagView.play()
        })
    }
  }
}
</script>
<style scoped lang="scss">
#flyCoins {
  position: fixed;
  top: 0px;
  height: calc(80.444vh);
  width: calc(80.444vh / 0.724);
}
</style>
