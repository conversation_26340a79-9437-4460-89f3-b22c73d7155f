<template>
  <div class="coins-container">
    <div class="coins coin-opration" id="coin-position" @click.prevent.stop="openBadgePane($event)">
      <span :class="['icon-coins', { 'add-coin': addCoin }, 'coin-opration']"></span>
      <div v-if="!addCoin" class="coin-num">
        <div class="img-num" v-for="(i, index) in String(planIdCoin).split('')" :key="index">
          <img :src="numImgArr[i]" class="coin-opration" />
        </div>
      </div>
      <CountTo
        v-else
        :startNum="startCoin"
        :endNum="endCoin"
        @end="countEnd"
        :delayTime="delayTime"
      />
      <div class="coin-detail" v-if="isShowCoinDetail" data-name="coin-detail">
        <div class="coins-blank">
          <div class="coins-curlesson-coins">
            <div class="tip">
              {{ $t('classroom.smallClass.coins.currentLessonCoinsTips') }}
            </div>
            <div class="content">
              <span class="icon-coins"></span>
              <span v-if="coinDatas" class="number">{{ coinDatas.planIdCoin }}</span>
            </div>
          </div>
          <div class="divider"></div>
          <div class="coins-total-coins">
            <div class="tip">
              {{ $t('classroom.smallClass.coins.totalCoinsTips') }}
            </div>
            <div class="content">
              <span class="icon-coins"></span>
              <span v-if="coinDatas" class="number">{{ coinDatas.totalCoin }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import _debounce from 'lodash/debounce'
import { studentCoinAndMedal } from '@/api/classroom/index'
import CountTo from './CountTo/index.vue'
import { useRoute } from 'vue-router'
import { emitter } from '@/hooks/useEventBus'
import { useClassData } from '@/stores/classroom'

// 导入图片资源
import zero from './imgs/0.png'
import one from './imgs/1.png'
import two from './imgs/2.png'
import three from './imgs/3.png'
import four from './imgs/4.png'
import five from './imgs/5.png'
import six from './imgs/6.png'
import seven from './imgs/7.png'
import eight from './imgs/8.png'
import nine from './imgs/9.png'

// 响应式状态
const numImgArr = [zero, one, two, three, four, five, six, seven, eight, nine]
const coinDatas = ref(null)
const planIdCoin = ref(0)
const level = ref(0)
const addCoin = ref(false)
const startCoin = ref(0)
const endCoin = ref(0)
const delayTime = ref(1)
const currentEvent = ref(null)
const isShowCoinDetail = ref(false)

// 获取 store
const store = useClassData()
const route = useRoute()
const { planId } = route.query

// 计算属性
const goldCoins = computed(() => store.goldCoins)
const options = computed(() => store.baseData.commonOption)
const ircStatus = computed(() => store.ircStatus)

// 方法
const countEnd = async () => {
  await initMedalCoins()
  addCoin.value = false
  checkRun(currentEvent.value)
  currentEvent.value = null
}

const checkRun = async event => {
  if (!event) return
  const res = await studentCoinAndMedal({
    planId: planId
  })

  if (res && res.code === 0 && !addCoin.value) {
    coinDatas.value = res.data
    if (planIdCoin.value < res.data.planIdCoin) {
      planIdCoin.value = coinDatas.value.planIdCoin || ''
    }
    level.value = coinDatas.value.medalNum
  }
}

const dealAddCoin = (coin, delay= 1) => {
  if (!addCoin.value) {
    addCoin.value = true
    startCoin.value = +planIdCoin.value
    endCoin.value = startCoin.value + coin
    delayTime.value = delay
    delay && emitter.emit('coinFly')
    console.log('看下执行定向金币 coinFly in', delay, startCoin.value, endCoin.value)
  } else {
    currentEvent.value = {
      coin,
      delay
    }
  }
  planIdCoin.value = +planIdCoin.value + coin
  console.log('看下执行定向金币 1', addCoin.value, planIdCoin.value)
}

const openBadgePane = _debounce(async event => {
  if (addCoin.value) return
  console.log(ircStatus.value, 'this.ircStatus')
  if (!ircStatus.value) {
    return
  }
  if (event.target.className.includes('coin-opration')) {
    isShowCoinDetail.value = !isShowCoinDetail.value
    if (isShowCoinDetail.value) {
      await initMedalCoins()
    }
  }
}, 300)

const initMedalCoins = async () => {
  try {
    const res = await studentCoinAndMedal({
      planId: planId
    })
    if (res && res.code == 0) {
      coinDatas.value = res.data
      planIdCoin.value = coinDatas.value.planIdCoin || ''
      level.value = coinDatas.value.medalNum
    }
  } catch (error) {
    console.error(error)
  }
  console.log('看下执行定向金币 2', coinDatas.value, planIdCoin.value)
}

// 处理事件监听
const handleUpdateAchievement = (type, num) => {
  console.log(num)
  initMedalCoins()
}

const handleAddCoin = (isPlayAnimation, coin, isTurnoverNum = true) => {
  console.log('看下执行定向金币 0', isPlayAnimation, coin, isTurnoverNum)
  if (isPlayAnimation) {
    dealAddCoin(coin, 1)
  } else if (isTurnoverNum) {
    dealAddCoin(coin, 0)
  } else {
    initMedalCoins()
  }
}

const handleDocumentClick = () => {
  isShowCoinDetail.value = false
}

// 生命周期钩子
onMounted(() => {
  // 初始化勋章金币数量
  initMedalCoins()

  // 注册事件监听
  emitter.on('updateAchievement', handleUpdateAchievement)
  emitter.on('addCoin', handleAddCoin)

  // 点击其他区域关闭更多弹窗
  document.addEventListener('click', handleDocumentClick)
})

onBeforeUnmount(() => {
  // 移除事件监听
  emitter.off('updateAchievement', handleUpdateAchievement)
  emitter.off('addCoin', handleAddCoin)
  document.removeEventListener('click', handleDocumentClick)
})
</script>

<style scoped lang="scss">
.coins-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  padding: 16px 20px;
  background: #1a1a1a;
  border-radius: 8px;

  .coin-opration {
    cursor: pointer;
  }

  .coins {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
    position: relative;
    color: #fff;
  }

  .coin-num {
    display: flex;
    justify-content: center;
    align-items: center;

    .img-num {
      display: flex;
    }

    img {
      height: 18px;
      width: 10px;
      border: 0px;
    }
  }

  .icon-coins {
    width: 18px;
    height: 18px;
    margin-right: 6px;
    display: inline-block;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url('./imgs/icon-coins.png');
  }

  .coin-detail {
    position: absolute;
    right: -10px;
    top: 50px;
    background: #1a1a1a;
    border-radius: 8px;
    padding: 16px 20px;
    z-index: 999;
    cursor: default;

    &:before {
      position: absolute;
      right: 20px;
      top: -16px;
      content: '';
      width: 0;
      height: 0;
      border-top: 8px solid transparent;
      border-right: 8px solid transparent;
      border-left: 8px solid transparent;
      border-bottom: 8px solid black;
    }

    .tip {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      margin: 0 0 6px 0;
      text-align: center;
      white-space: nowrap;
    }

    .number {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
    }

    .divider {
      width: 1px;
      height: 44px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      margin: 0 20px;
    }

    .coins-blank {
      display: flex;
      box-flex: 1;
    }

    .coins-curlesson-coins {
      min-width: 60px;
    }

    .content {
      align-items: center;
      justify-content: center;
      display: flex;
    }

    .coins-total-coins {
      min-width: 60px;
    }
  }
}
</style>
