<template>
  <Modal
    v-model:open="visible"
    width="400px"
    :maskClosable="false"
    :closable="false"
    :centered="true"
    :title="t('classroom.modules.deviceTest.dialogTitle')"
    dialogClass="modal-simple"
    :okText="t('common.confirm')"
    :cancelText="t('common.cancel')"
    @ok="handleOk"
  >
    <div class="modal-device-test">
      <div class="device-item">
        <div class="device-head">
          <div class="title">
            <span class="color-gray">
              {{ t('classroom.modules.deviceTest.cameraTitle') }}
            </span>
            {{ cameraStatusInfo.text }}
          </div>
          <div class="icon" :class="cameraStatusInfo.className"></div>
        </div>
        <Select
          :value="defaultVideoDeviceId"
          :disabled="cameraAccess === false"
          :default-value="defaultVideoDeviceId"
          :style="{ width: selectWidth }"
          @change="handleChangeVideoDevice"
        >
          <Option v-for="(item, index) in videoDevices" :value="item.deviceId" :key="index">
            {{ item.label }}
          </Option>
          <template #suffixIcon>
            <ArrowBottomIcon class="suffix-icon" />
          </template>
        </Select>
      </div>
      <div class="device-item">
        <div class="device-head">
          <div class="title">
            <span class="color-gray">
              {{ t('classroom.modules.deviceTest.microphoneTitle') }}
            </span>
            {{ microphoneStatusInfo.text }}
          </div>
          <div class="icon" :class="microphoneStatusInfo.className"></div>
        </div>
        <Select
          :disabled="microphoneAccess === false"
          :value="defaultAudioRecordingDeviceId"
          :default-value="defaultAudioRecordingDeviceId"
          :style="{ width: selectWidth }"
          @change="handleChangeAudioRecordingDevice"
        >
          <Option
            v-for="(item, index) in audioRecordingDevices"
            :value="item.deviceId"
            :key="index"
          >
            {{ item.label }}
          </Option>
          <template #suffixIcon>
            <ArrowBottomIcon class="suffix-icon" />
          </template>
        </Select>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { useI18n } from 'vue-i18n'
import { Modal, Select } from 'ant-design-vue'
import ArrowBottomIcon from './assets/icon-arrow-bottom.svg?component'
import { emitter } from '@/hooks/useEventBus.js'
import { useRtcService } from '@/hooks/useRtcService'
import { useMediaAccess } from '@/hooks/useMediaAccess.js'
import { deviceTestLog } from '@/utils/web-log/HWLogDefine.js'

const { Option } = Select
const { getRtcService } = useRtcService()
// 使用国际化
const { t } = useI18n()

// 响应式状态
const visible = ref(false)
const videoDevices = ref([]) // 摄像头设备列表
const audioRecordingDevices = ref([]) // 麦克风设备列表
const defaultVideoDeviceId = ref('') // 默认摄像头设备ID
const defaultAudioRecordingDeviceId = ref('') // 默认麦克风设备ID
// 🔧 新增：记录原始设备ID，用于判断是否有变化
const originalVideoDeviceId = ref('') // 原始摄像头设备ID
const originalAudioRecordingDeviceId = ref('') // 原始麦克风设备ID
const { cameraAccess, getCameraAccess, microphoneAccess, getMicrophoneAccess } = useMediaAccess()
// 计算属性
const selectWidth = ref('340px')

/**
 * 摄像头状态信息
 */
const cameraStatusInfo = computed(() => {
  if (!videoDevices.value.length) {
    return {
      className: 'icon-error',
      text: t('classroom.modules.deviceTest.statusNames.disabled')
    }
  } else if (cameraAccess.value === true) {
    return {
      className: 'icon-success',
      text: t('classroom.modules.deviceTest.statusNames.usable')
    }
  } else if (cameraAccess.value === false) {
    return {
      className: 'icon-warn',
      text: t('classroom.modules.deviceTest.statusNames.unauthorized')
    }
  } else {
    return {}
  }
})

/**
 * 麦克风状态信息
 */
const microphoneStatusInfo = computed(() => {
  if (!audioRecordingDevices.value.length) {
    return {
      className: 'icon-error',
      text: t('classroom.modules.deviceTest.statusNames.disabled')
    }
  } else if (microphoneAccess.value === true) {
    return {
      className: 'icon-success',
      text: t('classroom.modules.deviceTest.statusNames.usable')
    }
  } else if (microphoneAccess.value === false) {
    return {
      className: 'icon-warn',
      text: t('classroom.modules.deviceTest.statusNames.unauthorized')
    }
  } else {
    return {}
  }
})

/**
 * 显示弹窗
 */
const showModal = () => {
  visible.value = true
  getDevices()
  getMediaAccess()
}

/**
 * 隐藏弹窗
 */
const hideModal = () => {
  visible.value = false
}

/**
 * 确认按钮点击事件
 */
const handleOk = async () => {
  deviceTestLog.action('点击设备测试确认按钮，开始切换设备')

  const RtcService = getRtcService()

  // 🔧 优化：只有当设备ID发生变化时才执行切换
  if (defaultVideoDeviceId.value !== originalVideoDeviceId.value) {
    deviceTestLog.info('摄像头设备已变更，执行切换:', {
      原始: originalVideoDeviceId.value,
      新设备: defaultVideoDeviceId.value
    })
    RtcService.switchVideoDevice(defaultVideoDeviceId.value)
  } else {
    deviceTestLog.info('摄像头设备未变更，跳过切换')
  }

  if (defaultAudioRecordingDeviceId.value !== originalAudioRecordingDeviceId.value) {
    deviceTestLog.info('麦克风设备已变更，执行切换:', {
      原始: originalAudioRecordingDeviceId.value,
      新设备: defaultAudioRecordingDeviceId.value
    })
    RtcService.switchAudioDevice(defaultAudioRecordingDeviceId.value)
  } else {
    deviceTestLog.info('麦克风设备未变更，跳过切换')
  }

  hideModal()
}

/**
 * 摄像头设备变更事件
 */
const handleChangeVideoDevice = val => {
  defaultVideoDeviceId.value = val
}

/**
 * 麦克风设备变更事件
 */
const handleChangeAudioRecordingDevice = val => {
  defaultAudioRecordingDeviceId.value = val
}

/**
 * 获取设备列表
 */
const getDevices = async () => {
  const RtcService = getRtcService()
  const videoDevicesList = await RtcService.getVideoDevices()
  const audioRecordingDevicesList = await RtcService.getAudioRecordingDevices()
  videoDevices.value = videoDevicesList
  audioRecordingDevices.value = audioRecordingDevicesList

  const defaultVideoLabel = RtcService.getCurrentVideoDevice()
  const defaultAudioRecordingLabel = RtcService.getCurrentAudioDevice()
  defaultVideoDeviceId.value = videoDevicesList.find(
    item => item.label === defaultVideoLabel
  )?.deviceId
  defaultAudioRecordingDeviceId.value = audioRecordingDevicesList.find(
    item => item.label === defaultAudioRecordingLabel
  )?.deviceId

  // 🔧 新增：记录原始设备ID
  originalVideoDeviceId.value = defaultVideoDeviceId.value
  originalAudioRecordingDeviceId.value = defaultAudioRecordingDeviceId.value

  deviceTestLog.info(
    `查询当前默认使用设备, 摄像头: ${defaultVideoLabel}, 麦克风: ${defaultAudioRecordingLabel}, 扬声器: `
  )
  deviceTestLog.info(
    `查询设备列表, 摄像头: ${JSON.stringify(videoDevicesList)}, 麦克风: ${JSON.stringify(
      audioRecordingDevicesList
    )}`
  )
}

/**
 * 获取媒体权限
 */
const getMediaAccess = async () => {
  await getCameraAccess()
  await getMicrophoneAccess()
}

// 生命周期钩子
onMounted(() => {
  deviceTestLog.info('设备测试组件已挂载')

  emitter.on('deviceTestShow', showModal)
})
onBeforeUnmount(() => {
  emitter.off('deviceTestShow', showModal)
})
</script>

<style lang="scss" scoped>
.modal-device-test {
  position: relative;
  width: 340px;
  margin: 30px auto 20px auto;
  .device-item {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .device-head {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .icon {
      width: 14px;
      height: 14px;
      margin-left: 8px;
      background-repeat: no-repeat;
      background-size: cover;
    }
    .icon-success {
      background-image: url('./assets/icon-success.png');
    }
    .icon-error {
      background-image: url('./assets/icon-error.png');
    }
    .icon-warn {
      background-image: url('./assets/icon-warn.png');
    }
  }
  .authorize-guide {
    margin: 5px 0 0 10px;
    color: #3370ff;
    span {
      text-decoration: underline;
      cursor: pointer;
    }
  }
  .color-gray {
    color: #a2aab8;
  }
}

// 🎯 自定义 SVG 图标样式
.suffix-icon {
  width: 12px;
  height: 12px;
  display: inline-block;
  color: #bfbfbf; // 设置图标颜色（因为 SVG 使用了 currentColor）
  transition: color 0.3s ease; // 添加颜色过渡效果

  // 悬停效果
  &:hover {
    color: #1890ff;
  }
}
</style>
