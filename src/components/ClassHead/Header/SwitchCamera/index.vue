<template>
  <div class="switch-camera-container" data-name="switch-camera">
    <span class="text" data-name="switch-camera-text">
      {{ t('classroom.smallClass.camera.buttonName') }}
    </span>
    <Switch
      :checked="cameraStatus"
      size="small"
      @change="handleCameraSwitch"
      data-name="switch-camera-btn"
    />
  </div>
</template>

<script setup>
import { useCameraStatus } from '@/hooks/useCameraStatus'
import { Switch } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'

// 定义组件名称
defineOptions({
  name: 'SwitchVideo'
})

// 使用国际化
const { t } = useI18n()

// 使用摄像头状态钩子
const { handleCameraSwitch, cameraStatus } = useCameraStatus()
</script>

<style scoped lang="scss">
.switch-camera-container {
  display: flex;
  justify-content: space-between;
}

.text {
  color: #fff;
  font-size: 16px;
  white-space: nowrap;
}

:deep(.ant-switch:not(.ant-switch-checked)) {
  background-color: #202020;
}
</style>
