<template>
  <a-modal
    :open="isShowModal"
    @update:visible="$emit('update:isShowModal', $event)"
    :width="width"
    :maskClosable="false"
    :centered="true"
    :keyboard="false"
    :closable="false"
    :footer="null"
    :dialogClass="classes"
    :destroyOnClose="destroyOnClose"
    :zIndex="zIndex"
  >
    <div class="common-model-wrapper">
      <div class="top-wrapper">
        <div class="title">{{ title }}</div>
        <div class="sub-title">{{ subTitle }}</div>
      </div>
      <div class="content-wrapper" v-if="$slots.default"><slot></slot></div>
      <div class="bottom-wrapper" v-if="showFooter">
        <a-button
          v-if="showLeftBtn"
          type="primary"
          shape="round"
          size="large"
          :loading="leftLoading"
          class="btn left-btn"
          @click="leftOperation()"
        >
          {{ leftBtnText }}
        </a-button>
        <a-button
          v-if="showRightBtn"
          type="primary"
          shape="round"
          size="large"
          :disabled="disabled"
          :loading="rightLoading"
          class="btn right-btn"
          @click="rightOperation()"
        >
          {{ rightBtnText }}
        </a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
export default {
  props: {
    width: {
      type: Number,
      default: 343,
    },
    title: {
      type: String,
      default: '',
    },
    isShowModal: {
      type: Boolean,
      default: false,
    },
    subTitle: {
      type: String,
      default: '',
    },
    leftBtnText: {
      type: String,
      default: '',
    },
    rightBtnText: {
      type: String,
      default: '',
    },
    showLeftBtn: {
      type: Boolean,
      default: true,
    },
    showRightBtn: {
      type: Boolean,
      default: true,
    },
    dialogClass: {
      type: [String, Array], // 修正类型定义
      default: '',
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    destroyOnClose: {
      type: Boolean,
      default: false,
    },
    zIndex: {
      type: Number,
      default: 1000,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    leftLoading: {
      type: Boolean,
      default: false,
    },
    rightLoading: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    classes() {
      let dialogClass = this.dialogClass
      if (Array.isArray(this.dialogClass)) {
        dialogClass = this.dialogClass.join(' ')
      }
      return `common-model-cls ${dialogClass}`
    },
  },
  methods: {
    leftOperation() {
      this.$emit('leftBtnOperation')
    },
    rightOperation() {
      this.$emit('rightBtnOperation')
    },
  },
}
</script>
