<template>
  <div class="switch-camera-container" data-name="switch-camera">
    <span class="text" data-name="switch-camera-text">
      {{ t('liveroomDisableOthersVideoMenu') }}
    </span>
    <Switch
      :checked="cameraOthersStatus"
      size="small"
      @change="handleOthersCameraSwitch"
      data-name="switch-camera-btn"
    />
  </div>
</template>

<script setup>
import { useCameraStatus } from '@/hooks/useCameraStatus'
import { Switch } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'

// 定义组件名称
defineOptions({
  name: 'SwitchOtherVideo'
})

// 使用国际化
const { t } = useI18n()

// 使用摄像头状态钩子
const { handleOthersCameraSwitch, cameraOthersStatus } = useCameraStatus()
</script>

<style scoped lang="scss">
.switch-camera-container {
  display: flex;
  justify-content: space-between;
  gap: 8px;
  align-items: center;
}

.text {
  color: #fff;
  font-size: 14px;
  white-space: nowrap;
}

:deep(.ant-switch:not(.ant-switch-checked)) {
  background-color: #202020;
}
</style>
