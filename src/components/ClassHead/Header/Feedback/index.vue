<template>
  <div v-if="showStatus" class="container-feedback ignore-conf">
    <div class="feedback-popup">
      <div class="popup-wrapper">
        <div class="popup-header">
          <label></label>
          <span>
            {{ $t('classroom.modules.feedback.headerName') }}
          </span>
        </div>
        <div class="popup-contenter">
          <FeedbackOptions @update-checked-info="updateCheckedInfo" />
          <div class="feedback-textarea">
            <textarea
              v-model="content"
              maxlength="500"
              :placeholder="$t('classroom.modules.feedback.placeholder')"
            >
            </textarea>
          </div>
          <div class="agree-wrapper" @click="handleClickAgree">
            <div class="agree-checkbox">
              <div v-if="agreeChecked" class="icon-checked"></div>
            </div>
            {{ $t('classroom.modules.feedback.screenshotTips') }}
          </div>
          <div class="button-wrapper">
            <div @click="handleCancel">
              {{ $t('common.cancel') }}
            </div>
            <div @click="handleSend">
              {{ $t('common.send') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import FeedbackOptions from './feedbackOptions.vue'
import { useClassData } from '@/stores/classroom'
import { message } from 'ant-design-vue'
import { useRequestInfo } from '@/hooks/useRequestInfo'
import { useIrcService } from '@/hooks/useIrcService'
// 引用pakage.json文件
import { version } from '../../../../../package.json'
// import { version } from 'pakage.json'
// import { dataURLtoBlob } from '@/utils/util'
// import { nativeApi } from 'utils/electronIpc'
// import { getSchoolCode } from 'utils/local'
// import { chatMsgPriority } from '@/LargeClass/base/interaction-handler/interaction-conf'
// import Upload from '@/utils/upload'
// import screenshot from 'utils/screenshot'
// import logger from 'utils/logger'
import { emitter } from '@/hooks/useEventBus'

const { getIrcService } = useIrcService()
export default {
  props: {
    rtcConfig: {
      type: Object,
      default: null
    },
    downlinkNetworkQuality: {
      type: Number,
      default: null
    }
  },
  components: {
    FeedbackOptions
  },
  data() {
    return {
      showStatus: false,
      agreeChecked: true,
      content: '',
      checkedCode: '',
      checkedName: '',
      options: useClassData().baseData.commonOption
    }
  },
  mounted() {
    emitter.on('handleOpenFeedback', flag => {
      flag && (this.showStatus = true)
    })
  },
  methods: {
    handleShow() {
      this.showStatus = true
    },
    handleHide() {
      this.showStatus = false
    },
    handleClickAgree() {
      console.log('@@@handleClickAgree', this.agreeChecked)
      this.agreeChecked = !this.agreeChecked
    },
    handleSend() {
      message.destroy()

      this.handleHide()
      const params = JSON.stringify({
        checkedName: this.checkedName,
        content: this.content.replace(/[\r\n]/g, ' '),
        agreeChecked: this.agreeChecked
      })
      window.setTimeout(() => {
        message.info(this.$t('classroom.modules.feedback.sendSuccessNotice'))
      }, 100)
      if (this.agreeChecked) {
        // this.uploadScreenshot(screenshotUrl => {
        //   this.sendMessage(screenshotUrl)
        // })
        this.sendMessage()
      } else {
        this.sendMessage()
      }
      this.sendLogger(`点击Send按钮: ${params}`)
    },
    handleCancel() {
      this.handleHide()
    },

    // async sendMessage(screenshotUrl) {
    async sendMessage() {
      /**
    @疑问 这里因为用到了nativeApi，先暂时注释掉
    @答 意见反馈不需要截图，反馈功能需要，
    @解答人 未知
    @问题创建人 白海禄
    @创建时间 2025-05-19 16:05:25
    @是否解决 未解决
    */

      const { getSchoolCode } = useRequestInfo()
      // const token = getToken()
      const schoolCode = getSchoolCode()

      // const schoolCode = await getSchoolCode()
      // const deviceInfo = await nativeApi.getDeviceInfo()
      const questionMsg = this.content.replace(/[\r\n]/g, ' ')
      const content = {
        type: 150,
        from: 'flv',
        name: this.options.nickName,
        msg: this.$t('classroom.modules.feedback.defaultMsg'),
        parameter: {
          schoolCode: schoolCode,
          planId: this.options.planId,
          roomId: this.options.classId,
          studentId: this.options.stuIRCId,
          uid: this.options.stuId,
          teacherId: this.options.teacherInfo.id,
          teacherName: this.options.teacherInfo.name,
          teacherRoomId: this.options.configs.rtcConfig.teacherRoomId,
          startTime: this.options.stime,
          currenTime: new Date().getTime(),
          // device: deviceInfo.platform,
          // deviceVersion: deviceInfo.osVersion,
          AppVersion: version,
          question: this.checkedName,
          question_msg: questionMsg
          // question_url: screenshotUrl || ''
        }
      }

      // const res = window.ChatClient.PeerChatManager.sendPeerMessage(
      //   [{ nickname: this.options.configs.tutorIrcId }],
      //   content,
      //   chatMsgPriority.privMsg
      // )
      const store = useClassData()

      const opts = {
        nickname: store.baseData.configs.teacherIrcId,
        content: content,
        chatMsgPriority: 99 // 私聊类型
      }
      const IrcService = getIrcService()

      IrcService.sendPeerMessage(opts)

      this.resetData()
      // if (res != 0) {
      //   this.sendLogger(`发送反馈消息失败: ${content} irc消息发送返回状态: ${res}`)
      //   return
      // }
      // this.sendLogger(`发送反馈消息成功: ${content}`)
    },

    // async uploadScreenshot(callback) {
    // try {
    //   // const { thumbnail } = await screenshot({
    //   //   thumbnailWidth: 1024,
    //   //   thumbnailQuality: 1,
    //   // })
    //   /**
    //   @疑问 上面是获取问题反馈的截图，下面是获取截屏的代码
    //   @答 未确认
    //   @解答人 未知
    //   @问题创建人 白海禄
    //   @创建时间 2025-05-19 16:25:13
    //   */
    //   let thumbnail = null
    //   const thumbnailBlob = dataURLtoBlob(thumbnail)
    //   const upload = new Upload({
    //     scene: 'classFeedback',
    //   })
    //   upload.putFile({
    //     filePath: 'classFeedback.jpg',
    //     file: thumbnailBlob,
    //     progress: (percent) => {
    //       console.info('uploadScreenshot-progress', percent)
    //     },
    //     success: (res) => {
    //       // console.log('uploadScreenshot-success', res)
    //       callback && callback(res.url)
    //       this.sendLogger(`截屏上传成功:${res.url}`)
    //     },
    //     fail: () => {
    //       // console.log('uploadScreenshot-fail')
    //       callback && callback()
    //       this.sendLogger(`截屏上传失败`, 'error')
    //     },
    //   })
    // } catch (error) {
    //   console.error('上传截屏失败', error)
    //   callback && callback()
    // }
    // },
    updateCheckedInfo(code, name) {
      this.checkedCode = code
      this.checkedName = name
      // console.log('updateCheckedInfo', code, name)
    },
    resetData() {
      this.content = ''
      this.checkedCode = ''
      this.checkedName = ''
    },
    /**
     * 日志上报
     */
    sendLogger(msg, level = 'info') {
      // logger.send({
      //   tag: 'liveFeedback',
      //   content: {
      //     msg: msg,
      //   },
      //   level,
      // })
    }
  },
  beforeUnmount() {
    emitter.off('handleOpenFeedback')
  }
}
</script>

<style scoped lang="scss">
.container-feedback {
  position: fixed;

  bottom: 0;
  right: 0;
  width: 300px;

  z-index: 1000;
  background: rgba(0, 0, 0, 0.9);
  .feedback-popup {
    width: 300px;
    height: 100%;
    top: 75px;
    right: 10px;
    z-index: 999;
    border-radius: 15px;
    // background: rgba(0, 0, 0, 0.8);
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      display: block;
      filter: blur(20px);
      background: rgba(0, 0, 0, 0.6);
      z-index: -1;
    }
    .popup-wrapper {
      position: relative;
      padding: 40px 10px 10px 10px;
    }
    .popup-header {
      height: 30px;
      line-height: 30px;
      margin-bottom: 10px;
      font-size: 16px;
      color: #fff;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      label {
        width: 15px;
        height: 15px;
        display: block;
        background-image: url('../images/feedback.png');
        background-size: cover;
        margin-right: 10px;
      }
    }
  }
  .agree-wrapper {
    position: relative;
    margin: 10px 0 15px 22px;
    color: #6f727b;
    cursor: pointer;
    .agree-checkbox {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 4px;
      left: -22px;
      width: 14px;
      height: 14px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 4px;
      .icon-checked {
        width: 10px;
        height: 10px;
        background-image: url('../images/icon-checked.png');
        background-size: cover;
      }
    }
  }
  .button-wrapper {
    display: flex;
    justify-content: center;
    padding-left: 10px;
    margin-top: 50px;
    div {
      width: 134px;
      height: 40px;
      background: rgba(162, 170, 184, 0.24);
      border-radius: 22px;
      font-size: 14px;
      font-weight: 600;
      color: #a2aab8;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      margin-right: 10px;
      &:hover {
        color: #fff;
      }
    }
  }
  .feedback-textarea {
    textarea {
      width: 280px;
      height: 50vh;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 8px;
      border: 2px solid rgba(255, 255, 255, 0.1);
      padding: 10px;
      outline: none;
      color: #fff !important;
      resize: none;
    }
  }
}
.ignore-conf {
  top: 44px;
  height: calc(100vh - 44px);
}
</style>
