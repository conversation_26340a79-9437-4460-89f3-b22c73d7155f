<template>
  <div class="header-container">
    <p class="plan-name">{{ baseData.planInfo.name }}</p>
    <div class="right-tools">
      <Coins />
      <div class="divider"></div>
      <NetworkStatus />

      <Tooltip placement="bottom">
        <template #title>
          <span>{{ t('classroom.smallClass.homework.buttonName') }}</span>
        </template>
        <div
          class="button button-assignmentBox"
          @click="handleAssignmentBox"
          data-log="作业盒子"
          data-name="button-assignmentBox"
          v-if="locale != 'uk'"
          :class="{ 'message-tip': showNewMessageTip }"
        ></div>
      </Tooltip>
      <Tooltip placement="bottom">
        <template #title>
          <span>{{ t('classroom.smallClass.examReport.buttonName') }}</span>
        </template>
        <div
          v-if="reportBtnVisible"
          class="button button-examReport"
          @click="handleExamReport"
          data-log="课中考试报告"
        ></div>
      </Tooltip>
      <div class="relative">
        <div
          class="button button-more"
          ref="moreButtonRef"
          :class="{ 'button-more-hover': isShowMoreTools }"
          @click="handleMore"
          data-log="更多菜单"
          data-name="button-more"
        ></div>
        <div class="more-menu" ref="moreMenuRef" v-show="isShowMoreTools">
          <div id="switch-carema"><SwitchCamera /></div>
          <div id="switch-other-video"><SwitchOtherVideo /></div>

          <div @click="handleDeviceTest" data-log="硬件测试">
            {{ t('classroom.modules.deviceTest.dialogTitle') }}
          </div>
          <div @click="handleFeedback" data-log="问题反馈">
            {{ t('classroom.smallClass.feedback.buttonName') }}
          </div>
        </div>
      </div>
      <div class="button button-exit" @click="handleExit(settlementInfo)"></div>
    </div>
    <!-- 设备检测 -->
    <DeviceTest />
    <!-- 问题反馈 -->
    <Feedback />
    <!-- 考试报告 -->
    <ExamReport />
    <!-- <RetryDialog /> -->
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useClassData } from '@/stores/classroom'
import { Tooltip } from 'ant-design-vue'
import { useExit } from '@/hooks/useExit'
import Coins from './Coins/index.vue'
import NetworkStatus from './NetworkStatus.vue'
import SwitchCamera from './SwitchCamera/index.vue'
import SwitchOtherVideo from './SwitchCamera/others.vue'
import DeviceTest from './DeviceTest/index.vue'
import Feedback from './Feedback/index.vue'
import ExamReport from './ExamReport/index.vue'
import { lookExamReportApi } from '@/api/classroom'
import { emitter } from '@/hooks/useEventBus'
import { onClickOutside } from '@vueuse/core'

// 使用国际化
const { t } = useI18n()
// 获取 store
const store = useClassData()
const baseData = store.baseData
// 响应式状态
const isShowMoreTools = ref(false)
const settlementInfo = ref({})
const showNewMessageTip = ref(false) // 作业盒子是否有新批改消息提示
const reportBtnVisible = ref(false)
const examReportInfo = ref({})

// 更多菜单引用
const moreMenuRef = ref(null)
const moreButtonRef = ref(null)

// 计算属性
const locale = computed(() => store.locale)

// 使用 exit hook
const { handleExit } = useExit(baseData.planInfo)

/**
 * 显示作业批改消息提示
 */
const changeShowMessageTip = () => {
  showNewMessageTip.value = true
}

/**
 * 处理设备检测事件
 */
const handleDeviceTest = () => {
  isShowMoreTools.value = false
  emitter.emit('deviceTestShow')
}

/**
 * 处理更多菜单
 */
const handleMore = () => {
  isShowMoreTools.value = !isShowMoreTools.value
}

/**
 * 处理作业盒子
 */
const handleAssignmentBox = () => {
  // 打开作业盒子-clickoutside排除的dom
  const wrapperDom = document.querySelector('.button-assignmentBox')
  emitter.emit('handleOpenBox', wrapperDom)
  showNewMessageTip.value = false
}

/**
 * 处理考试报告
 */
const handleExamReport = async () => {
  await checkIsShowReportBtn()
  emitter.emit('handleOpenExamReport', examReportInfo.value)
}

/**
 * 处理问题反馈
 */
const handleFeedback = () => {
  emitter.emit('handleOpenFeedback', true)
}

/**
 * 判断是否展示考试报告入口
 */
const checkIsShowReportBtn = async () => {
  const res = await lookExamReportApi({
    planId: baseData.planInfo.id, // 课次id
    platform: '3' // pc端
  })
  if (!res || res.code != 0) return
  examReportInfo.value = res.data
  reportBtnVisible.value = res.data?.showReportEnter === '1' // '1': 展示 '0':不展示
}

// 使用 onClickOutside 监听点击外部事件
onClickOutside(moreMenuRef, event => {
  // 如果点击的是触发按钮，不做任何操作
  if (moreButtonRef.value && moreButtonRef.value.contains(event.target)) {
    return
  }
  isShowMoreTools.value = false
})

// 生命周期钩子
onMounted(() => {
  // 不再需要 document.addEventListener('click', clickOutsideHandler)
  emitter.on('smallClassShowMessageTip', changeShowMessageTip)
  checkIsShowReportBtn() // 判断是否展示考试报告按钮
})

onBeforeUnmount(() => {
  // 不再需要 document.removeEventListener('click', clickOutsideHandler)
  emitter.off('smallClassShowMessageTip', changeShowMessageTip)
})
</script>

<style lang="scss" scoped>
.header-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-content: space-between;
  justify-content: space-between;

  .plan-name {
    height: 100%;
    color: #fff;
    line-height: 44px;
    margin: 0 20px 0 12px;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: normal;
    flex: 1;
  }

  .right-tools {
    display: flex;
    margin-right: 12px;
    align-items: center;

    .divider {
      width: 1px;
      height: 20px;
      border: 1px solid #525252;
      margin-left: 20px;
    }

    .button {
      width: 30px;
      height: 30px;
      background-size: cover;
      margin-left: 20px;
      cursor: pointer;
    }

    .button-screenShot {
      background-image: url('./images/icon-screenshot.png');

      &:hover {
        background-image: url('./images/icon-screenshot-hover.png');
      }
    }

    .button-assignmentBox {
      background-image: url('./images/icon-assignmentBox.png');

      &:hover {
        background-image: url('./images/icon-assignmentBox-hover.png');
      }
    }

    .message-tip {
      position: relative;

      &:after {
        position: absolute;
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #ff4b4b;
        right: 0;
        top: 0;
      }
    }

    .button-examReport {
      background-image: url('./images/icon-examReport.png');

      &:hover {
        background-image: url('./images/icon-examReport-hover.png');
      }
    }

    .button-exit {
      background-image: url('./images/icon-exit.png');

      &:hover {
        background-image: url('./images/icon-exit-hover.png');
      }
    }

    .button-more {
      background-image: url('./images/icon-more.png');
      position: relative;

      &:hover {
        background-image: url('./images/icon-more-hover.png');
      }
    }

    .button-more-hover {
      background-image: url('./images/icon-more-hover.png');
    }

    .more-menu,
    .open-sticker-tip {
      position: absolute;
      right: -10px;
      top: 50px;
      background: #1a1a1a;
      border-radius: 8px;
      z-index: 999;
      color: #fff;

      & > div {
        padding: 12px 0;
        border-radius: 4px;
        color: #fff;
        font-size: 16px;
        cursor: pointer;
      }

      & > div:not(:last-child) {
        border-bottom: solid 1px rgba(255, 255, 255, 0.1);
      }

      &::before {
        position: absolute;
        top: -6px;
        right: 20px;
        content: '';
        width: 8px;
        height: 6px;
        background-image: url('./images/arrow-up.png');
        background-size: cover;
      }
    }

    .more-menu {
      min-width: 150px;
      padding: 4px 12px;
    }

    .open-sticker-tip {
      width: 260px;
      padding: 16px;

      .tip {
        position: relative;
        padding: 0 0 0 32px;

        &::before {
          position: absolute;
          top: 3px;
          left: 0px;
          content: '';
          width: 24px;
          height: 24px;
          background-image: url('./images/icon-info.png');
          background-size: cover;
        }
      }
    }
  }
}

.relative {
  position: relative;
}

.sticker-forbid {
  :deep(.ant-btn[disabled]),
  :deep(.ant-btn-primary[disabled]) {
    color: #fff;

    &::before {
      background: rgba(255, 255, 255, 0.7);
      display: block;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
    }
  }
}

:deep(.got-info) {
  .bottom-wrapper {
    justify-content: center;
  }

  .left-btn {
    display: none;
  }
}

@keyframes disappear {
  0% {
    opacity: 1;
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.disappear-in-3 {
  animation-name: disappear;
  animation-duration: 3s;
}
</style>
