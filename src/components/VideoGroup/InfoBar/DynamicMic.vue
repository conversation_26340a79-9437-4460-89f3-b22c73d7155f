<template>
  <div class="mic-box" ref="micBox">
    <template v-if="microphoneStatus">
      <div class="mic-bg-green"></div>
      <div class="mic-bg-green transition" :style="micBoxStyle"></div>
    </template>
    <template v-else>
      <div class="mic-bg-off"></div>
    </template>
  </div>
</template>

<script setup>
import { ref, watch, nextTick } from 'vue'

const props = defineProps({
  volume: {
    type: Number,
    default: 225,
  },
  microphoneStatus: {
    type: Boolean,
    default: false,
  },
})

const micBox = ref(null)

const micBoxStyle = ref({
  height: '0px',
  top: '0px',
})

const updateMicBoxStyle = (volume) => {
  nextTick(() => {
    const boxElement = micBox.value
    if (!boxElement) {
      console.warn("Could not find 'mic-box' element.")
      return
    }
    const boxHeight = boxElement.clientHeight
    const height = parseInt(boxHeight * (volume / 225))
    micBoxStyle.value = {
      height: `${height}px`,
      top: `${boxHeight - height}px`,
    }
  })
}

watch(
  () => props.volume,
  (newValue) => {
    updateMicBoxStyle(newValue)
  },
  {
    immediate: true,
  },
)
</script>

<style scoped>
.mic-box {
  position: relative;
  height: 100%;
}

.mic-bg-green {
  position: absolute;
  height: 100%;
  inset: 0;
  z-index: 0;
  background: url('../img/green_mic.png'); /* 替换为实际的背景颜色或图片 */
  background-size: cover;
  background-position: top;
}

.mic-bg-off {
  position: absolute;
  height: 100%;
  inset: 0;
  background: url('../img/off_mic.png'); /* 替换为实际的背景颜色或图片 */
  background-size: cover;
  background-position: top;
  z-index: 0;
}

.transition {
  transition: all 0.8s ease-in-out;
}
</style>
