<template>
  <!-- 下方信息栏 -->
  <div class="info-bar">
    <!-- 私聊显示 -->
    <template v-if="showType === showBarType.privateChat">
      <div class="help-text"><i class="help-icon"></i>{{ $t('common.helpOthers') }}</div>
    </template>
    <!-- 老师上台信息 -->
    <template v-else-if="showType === showBarType.onStage">
      <div class="stage-text">
        {{ $t('common.onStage') }}
      </div>
    </template>
    <!-- 名字显示 -->
    <template v-else-if="showType === showBarType.nameInfo">
      <div class="name-text">{{ name }}</div>
      <div class="mic-container" v-if="showMicrophone">
        <DynamicMic :microphoneStatus="microphoneStatus" />
      </div>
    </template>
  </div>
</template>

<script setup>
import DynamicMic from './DynamicMic.vue'
import { showBarType } from '../constant.js'
defineOptions({
  name: 'InfoBar'
})
// 定义 props
defineProps({
  showType: {
    type: String,
    default: showBarType.nameInfo
  },
  name: {
    type: String,
    default: ''
  },
  volume: {
    type: Number,
    default: 0
  },
  microphoneStatus: {
    type: Boolean,
    default: false
  },
  showMicrophone: {
    type: Boolean,
    default: false
  }
})
</script>

<style lang="scss" scoped>
.info-bar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0 4px; /* 0.04rem * 100px */
  padding-top: 8px; /* 0.08rem * 100px */
  padding-bottom: 4px; /* 0.04rem * 100px */
  background: linear-gradient(to bottom, transparent, black);
  display: flex;
  justify-content: space-between;
  z-index: 20;
  overflow: hidden;
}

.help-text {
  line-height: 26px; /* 0.26rem * 100px */
  font-size: 14px; /* 假设 text-middle 对应的字体大小 */
  color: #fff; /* 假设 text-name 对应的颜色 */
  white-space: nowrap;
  @extend .helpName;
}

.stage-text {
  font-size: 12px; /* 假设 text-small 对应的字体大小 */
  color: #fff; /* 假设 text-name 对应的颜色 */
  white-space: nowrap;
  @extend .helpName;
}

.name-text {
  font-size: 12px; /* 假设 text-small 对应的字体大小 */
  color: #fff; /* 假设 text-name 对应的颜色 */
  flex-grow: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mic-container {
  width: 18px; /* 0.18rem * 100px */
  height: 18px; /* 0.18rem * 100px */
  flex-shrink: 0;
}

.help-icon {
  width: 12px; /* 0.12rem * 100px */
  height: 12px; /* 0.12rem * 100px */
  display: inline-block;
  margin-left: 2px; /* 0.02rem * 100px */
  background-repeat: no-repeat;
}

@keyframes scroll_show {
  0% {
    transform-origin: left;
    transform: scale3d(1, 1, 1) translateX(100%);
  }
  100% {
    transform-origin: left;
    transform: scale3d(1, 1, 1) translateX(-100%);
  }
}

.helpName {
  white-space: nowrap;
  animation: scroll_show 10s infinite linear;
  display: flex;
  align-items: center;

  i {
    width: 12px; /* 0.12rem * 100px */
    height: 12px; /* 0.12rem * 100px */
    display: inline-block;
    margin: 0 2px 0 0; /* 0.02rem * 100px */
    background: url('../img/chat-01.png') no-repeat left center;
  }
}
</style>
