<template>
  <div v-if="status" class="raise-hand-container">
    <img class="hand-image" :src="resource?.resourceUrl || defaultAvatar" />
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount } from 'vue'
import defaultAvatarImg from './img/icon-raise-hand.png'
defineOptions({
  name: 'RaiseHandMessage'
})
// 定义 props
const props = defineProps({
  status: {
    type: Boolean,
    default: false
  },
  userId: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 10
  },
  resource: {
    type: Object,
    default: () => {
      return {
        resourceId: '',
        resourceUrl: ''
      }
    }
  }
})

// 定义 emit
const emit = defineEmits(['updateStatus'])

// 数据
const timer = ref(null)
const defaultAvatar = defaultAvatarImg

// 清除定时器
const clearTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
}

// 监听状态变化
watch(
  () => props.status,
  newVal => {
    if (newVal) {
      clearTimer()
      timer.value = setTimeout(() => {
        emit('updateStatus', false, props.userId)
      }, props.duration * 1000)
    }
  },
  { immediate: true }
)

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  clearTimer()
})
</script>

<style lang="scss" scoped>
@keyframes axisY {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.raise-hand-container {
  height: 14vh; /* 1.17rem * 100px */
  background-repeat: no-repeat;
  background-size: cover;
  position: absolute;
  bottom: 0;
  animation: axisY 0.3s cubic-bezier(0, 0, 0.5, 1.5) forwards;
}

.hand-image {
  height: 14vh; /* 1.17rem * 100px */
}
</style>
