<template>
  <div class="pair-icon">
    <span class="right-icon"></span>
    <span v-if="parsedCorrectCount != 'm'" class="plus-icon"></span>
    <div class="contain-animation" :class="{ 'count-animation': animationFlag }">
      <span
        v-for="(item, index) in parsedCorrectCount.toString().split('')"
        class="count-number"
        :class="[correctCountClass(item)]"
        :key="index"
      ></span>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount } from 'vue'
import { cloudConfigValueBySchoolCode } from '@/utils/initConfig'

defineOptions({
  name: 'PairIcon'
})
// 数据
const animationFlag = ref(false)
const timer = ref(null)
let correctCountMax = ''

// 获取配置
cloudConfigValueBySchoolCode('correct_count_max').then(res => {
  correctCountMax = res
})

// 定义 props
const props = defineProps({
  type: {
    type: String,
    default: 'small'
  },
  correctCount: {
    type: Number,
    default: 0
  }
})

// 计算属性
const parsedCorrectCount = computed(() => {
  if (
    correctCountMax &&
    !isNaN(Number(correctCountMax)) &&
    Number(correctCountMax) > 0 &&
    props.correctCount >= Number(correctCountMax)
  ) {
    return 'm' //-1显示max
  } else {
    return props.correctCount
  }
})

// 方法
const clearTimer = () => {
  // @log-ignore
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
}

const correctCountClass = index => {
  // @log-ignore
  return `bg-count-img-${index}`
}

// 监听
watch(
  () => parsedCorrectCount.value,
  newVal => {
    if (newVal) {
      animationFlag.value = true
      clearTimer()
      timer.value = setTimeout(() => {
        animationFlag.value = false
      }, 3300)
    }
  }
)

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  // @log-ignore
  clearTimer()
})
</script>

<style lang="scss" scoped>
@keyframes zoom {
  0% {
    transform: scale(1);
  }
  60% {
    transform: scale(2);
  }
  100% {
    transform: scale(1);
  }
}

.pair-icon {
  display: flex;
  position: absolute;
  left: 5px; /* 0.05rem * 100px */
  top: 5px; /* 0.05rem * 100px */
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 9px;
  opacity: 0.7;
  border-image: linear-gradient(136deg, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)) 1 1;
  padding: 2px 5px 2px;

  .contain-animation {
    display: flex;
  }

  .count-number {
    display: inline-block;
    background-size: contain;
    z-index: 10;
  }

  .count-animation {
    animation: zoom 0.3s ease-in-out 3s 1;
  }

  .right-icon {
    background: url('./imgs/right.png') no-repeat;
    background-size: 14px 14px;
    width: 14px;
    height: 14px;
  }

  .plus-icon {
    background: url('./imgs/plus.png') no-repeat;
    background-size: 9px 10px;
    width: 9px;
    height: 10px;
    margin: 0 1px;
  }
}

@for $i from 0 through 9 {
  .bg-count-img-#{$i} {
    background-image: var(--count-#{$i}-img);
    background-repeat: no-repeat;
    height: 12px;
    width: 11px;
    background-position: center;
  }
}

.bg-count-img-m {
  background-image: var(--count-m-img);
  background-repeat: no-repeat;
  height: 12px;
  width: 34px;
  background-position: center;
  margin-left: 1px;
}

.class-onstage-layer {
  z-index: 100;

  .pair-icon {
    border-radius: 15px;
    padding: 4px 7px 4px;

    .right-icon {
      background-size: 22px 22px;
      width: 22px;
      height: 22px;
    }

    .plus-icon {
      background-size: 15px 16px;
      width: 15px;
      height: 16px;
      margin: 0 2px;
    }
  }

  @for $i from 0 through 9 {
    .bg-count-img-#{$i} {
      height: 20px;
      width: 18px;
    }
  }

  .bg-count-img-m {
    height: 18px;
    width: 56px;
    margin-left: 3px;
  }
}
</style>
