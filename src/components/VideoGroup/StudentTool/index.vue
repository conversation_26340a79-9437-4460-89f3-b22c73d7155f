<template>
  <div v-if="stuInfo" class="student-tool">
    <div v-show="stuInfo.correctCount && !stuInfo.multVideoLinkStatus">
      <PairIcon :correctCount="stuInfo.correctCount" />
    </div>
    <div class="hand-container" v-if="stuInfo.raiseHandStatus">
      <RaiseHandMessage
        :status="stuInfo.raiseHandStatus"
        @updateStatus="hideHand"
        :resource="stuInfo?.prop?.hand"
      />
    </div>
    <div
      :class="['emoticon-container', stuInfo.multVideoLinkStatus ? '' : 'emoticon-bg']"
      v-if="stuInfo.emoticonName"
    >
      <EmoticonMessage
        v-show="!stuInfo.multVideoLinkStatus"
        :willAutoClear="true"
        :name="stuInfo.emoticonName"
        :type="stuInfo.emoticonType"
        :emojiId="stuInfo.emojiId"
        :width="emoticonSize"
        :height="emoticonSize"
        :lottieUrl="stuInfo.lottieUrl"
        @clearEmoticon="handleClearEmoticon"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import PairIcon from './PairIcon/index.vue'
import RaiseHandMessage from './RaiseHandMessage/index.vue'
import EmoticonMessage from '@/components/EmoticonMessage/index.vue'
import { useClassData } from '@/stores/classroom'

defineOptions({
  name: 'StudentTool'
})
// 定义 props
const props = defineProps({
  id: {
    type: String,
    default: ''
  }
})

// 获取 store
const store = useClassData()

// 计算属性
const stuInfo = computed(() => store.studentList.find(item => item.userId == props.id))

// 更新学生列表
const updateStudentList = (data = {}) => {
  store.changeStudentInfo(props.id, data)
}

// 隐藏举手
const hideHand = () => {
  updateStudentList({ raiseHandStatus: false })
}

/**
 * 清空表情名称
 */
const handleClearEmoticon = () => {
  let emojiObj = {
    emoticonName: '',
    emoticonType: 1,
    lottieUrl: '',
    emojiId: ''
  }
  updateStudentList(emojiObj)
}
// 动态计算表情尺寸
const emoticonSize = computed(() => {
  const windowHeight = window.innerHeight
  if (windowHeight <= 480)
    return 40 // 超小屏幕
  else if (windowHeight <= 550)
    return 50 // 小屏幕
  else if (windowHeight <= 700)
    return 60 // 中等屏幕
  else return 80 // 大屏幕
})
</script>

<style lang="scss" scoped>
.student-tool {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
}

.hand-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
}

.emoticon-container {
  position: absolute;
  width: 100%;
  height: 100%;
  inset: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;

  &.emoticon-bg {
    background-color: rgba(0, 0, 0, 0.4);
  }
}
</style>
