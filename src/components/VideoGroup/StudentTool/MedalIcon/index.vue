<template>
  <div class="medal-container">
    <div class="medal-icon" :class="[levelClass]"></div>
    <span class="level-text">Lv{{ level }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'
defineOptions({
  name: 'MedalIcon',
})
// 定义 props
const props = defineProps({
  type: {
    type: String,
    default: 'small',
  },
  level: {
    type: Number,
    default: 0,
  },
})

const levelClass = computed(() => {
  return `bg-level-img-${props.level}`
})
</script>

<style lang="scss" scoped>
.medal-container {
  display: flex;
  align-items: center;
}

.medal-icon {
  width: 24px; /* 0.24rem * 100px */
  height: 24px; /* 0.24rem * 100px */
  background-size: contain;
  z-index: 10;
}

.level-text {
  position: relative;
  width: 35px; /* 0.35rem * 100px */
  height: 17px; /* 0.17rem * 100px */
  top: 4px; /* 0.04rem * 100px */
  padding-left: 10px; /* 0.1rem * 100px */
  left: -8px; /* -0.08rem * 100px */
  border-top-right-radius: 3px; /* 0.03rem * 100px */
  border-bottom-right-radius: 3px; /* 0.03rem * 100px */
  color: white;
  line-height: 17px; /* 0.17rem * 100px */
  background-color: rgb(0, 0, 0, 0.5); /* bg-level-color */
  font-size: 12px; /* 0.12rem * 100px */
}

@for $i from 1 through 7 {
  .bg-level-img-#{$i} {
    background-image: var(--level-#{$i}-img);
  }
}

.bg-level-color {
  background-color: rgb(0, 0, 0, 0.5);
}
</style>
