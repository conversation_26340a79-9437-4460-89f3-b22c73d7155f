<template>
  <div class="level-icon-container">
    <span class="level-text" :class="classNames">
      <span class="level-text-content"
        >{{ levelText }} <span v-if="levelId != 4">{{ subLevelText }}</span></span
      >
    </span>
    <img :src="levelImg" class="level-image" />
  </div>
</template>

<script setup>
import { computed, reactive } from 'vue'
import { useI18n } from 'vue-i18n'
defineOptions({
  name: 'LevelIcon',
})
// 获取国际化实例
const { t } = useI18n()

// 定义 props
const props = defineProps({
  levelId: {
    type: Number,
    default: 0,
  },
  subLevel: {
    type: Number,
    default: 0,
  },
})

// 数据
const levelTextMap = reactive({
  1: t('classroom.chats.chats.level.wood'),
  2: t('classroom.chats.chats.level.silver'),
  3: t('classroom.chats.chats.level.gold'),
  4: t('classroom.chats.chats.level.diamond'),
})

const subLevelTextMap = reactive({
  1: 'Ⅰ',
  2: 'Ⅱ',
  3: 'Ⅲ',
  4: 'Ⅳ',
})

const classMap = reactive({
  1: 'level-wood',
  2: 'level-silver',
  3: 'level-gold',
  4: 'level-diamond',
})

// 计算属性
const levelText = computed(() => {
  return levelTextMap[props.levelId]
})

const subLevelText = computed(() => {
  return subLevelTextMap[props.subLevel]
})

const classNames = computed(() => {
  return classMap[props.levelId]
})

const levelImg = computed(() => {
  if (props.levelId == 1) {
    return new URL(`./imgs/level/wood-${props.subLevel}.png`, import.meta.url).href
  } else if (props.levelId == 2) {
    return new URL(`./imgs/level/silver-${props.subLevel}.png`, import.meta.url).href
  } else if (props.levelId == 3) {
    return new URL(`./imgs/level/gold-${props.subLevel}.png`, import.meta.url).href
  } else if (props.levelId == 4) {
    return new URL(`./imgs/level/diamond.png`, import.meta.url).href
  } else {
    return new URL(`./imgs/level/wood-1.png`, import.meta.url).href
  }
})
</script>

<style lang="scss" scoped>
.level-icon-container {
  display: flex;
  position: absolute;
  right: 5px; /* 0.05rem * 100px */
  top: 5px; /* 0.05rem * 100px */
  transition: all 0.5s linear;
  cursor: pointer;

  &:hover {
    right: 76px; /* 0.76rem * 100px */
  }
}

.level-text {
  position: absolute;
  font-size: 14px; /* 0.14rem * 100px */
  display: flex;
  align-items: center;
  font-weight: bold;
  overflow: hidden;
  left: 20px; /* 0.2rem * 100px */
  width: 0;
  height: 28px; /* 0.28rem * 100px */
  background-color: black;
  transition: width linear;

  .level-icon-container:hover & {
    width: 80px; /* 0.8rem * 100px */
  }
}

.level-text-content {
  position: relative;
  left: 21px; /* 0.21rem * 100px */
  white-space: nowrap;
}

.level-image {
  width: 28px; /* 0.28rem * 100px */
  height: 28px; /* 0.28rem * 100px */
}

.level-wood {
  color: #956335;
  background: url('./imgs/level/woodText.png') no-repeat;
  background-size: 100% 100%;
}

.level-silver {
  color: #5296dc;
  background: url('./imgs/level/silverText.png') no-repeat;
  background-size: 100% 100%;
}

.level-gold {
  color: #f89701;
  background: url('./imgs/level/goldText.png') no-repeat;
  background-size: 100% 100%;
}

.level-diamond {
  color: #fff;
  background: url('./imgs/level/diamondText.png') no-repeat;
  background-size: 100% 100%;
}
</style>
