<template>
  <!-- 学生视频窗口 -->
  <div class="video-box">
    <!-- 学生视频 -->
    <div v-show="renderVideo" id="video-group-local"></div>
    <!-- 头像 -->
    <div class="avatar-box" v-if="!renderVideo">
      <div
        v-if="self?.prop?.avatarFrame?.resourceUrl && !self.multVideoLinkStatus"
        class="avatar-frame"
        :style="'background: url(' + self?.prop?.avatarFrame?.resourceUrl + ') no-repeat center'"
      ></div>
      <img class="avatarImg" :src="self.avatar" v-if="!self.multVideoLinkStatus" />
      <img v-else class="onStageStyle" src="../img/bg-mult-video-link.png" />
    </div>
    <!-- 下方信息栏 -->
    <InfoBar
      :showType="infoType"
      :name="self.nickName"
      :microphoneStatus="microphoneStatus"
      :showMicrophone="false"
    ></InfoBar>
    <StudentTool :id="self.userId"></StudentTool>
  </div>
</template>

<script setup>
import { computed, watch, nextTick, onBeforeUnmount } from 'vue'
import InfoBar from '../InfoBar/index.vue'
import { stuSeatingLog } from '@/utils/web-log/HWLogDefine'
import StudentTool from '../StudentTool/index.vue'
import { emitter } from '@/hooks/useEventBus'
import { useClassData } from '@/stores/classroom'
import { showBarType } from '../constant'
import { useRtcService } from '@/hooks/useRtcService'

const { getRtcService } = useRtcService()
const store = useClassData()

const self = computed(() => store.selfInfo)
const microphoneStatus = computed(() => !self.value.audioMute)
const cameraStatus = computed(() => !self.value.videoMute)
const wallRandomCallStatus = computed(() => store.wallRandomCallStatus)
const renderVideo = computed(() => {
  return (
    self.value.inClass &&
    !wallRandomCallStatus.value &&
    cameraStatus.value &&
    !self.value.multVideoLinkStatus
  )
})

const reportRtcStatus = (options = {}) => {
  emitter.emit('rtcStatusChange', options)
}

const infoType = computed(() => {
  return showBarType.nameInfo
})

// 监听进入离开教室和相机状态
const isOpenVideo = computed(() => {
  return self.value.inClass && cameraStatus.value
})

watch(
  () => isOpenVideo.value,
  status => {
    stuSeatingLog.info('displayVideo', status)
    reportRtcStatus({
      displayVideo: status,
      micIsOpen: microphoneStatus.value
    })
  },
  { immediate: true }
)
// 监听是否可以创建视频
watch(
  () => renderVideo.value,
  value => {
    if (value) {
      nextTick(() => {
        const localVideoElement = document.getElementById('video-group-local')
        if (localVideoElement) {
          const RtcService = getRtcService()
          stuSeatingLog.info('学生坐席', localVideoElement)
          RtcService.createLocalVideo('video-group-local')
        } else {
          stuSeatingLog.error('本地视频dom不存在')
        }
      })
    } else {
      const ele = document.getElementById('video-group-local')
      if (ele) {
        ele.innerHTML = ''
      }
    }
  },
  {
    immediate: true
  }
)
watch(
  () => microphoneStatus.value,
  status => {
    reportRtcStatus({
      displayVideo: cameraStatus.value,
      micIsOpen: status
    })
  }
)

// 处理金币激励勋章变化
const handleCorrectSelfMedalData = data => {
  store.changeStudentInfo(0, {
    level: data.level,
    correctCount: data.correctCount
  })
}

// 处理表情发送通知
const handleSendEmoji = params => {
  let emojiStatus = {
    emoticonName: params.name, // 表情名称
    emoticonType: params.type // 表情类型 1 本地表情 2 lottie表情
  }
  if (params.type == 2 || params.type == 3) {
    emojiStatus = Object.assign(emojiStatus, {
      lottieUrl: params.lottieUrl, // lottie表情url,
      emojiId: params.emojiId // lottie 表情id
    })
  }
  store.changeStudentInfo(0, emojiStatus)
}

// 处理本地举手状态发送通知
const handleSendRaiseHand = status => {
  console.log('handleSendRaiseHand', status)
  store.changeStudentInfo(0, {
    raiseHandStatus: status
  })
}

/**
 * 监听Vue事件
 */
const listenerEvent = () => {
  // 监听金币激励勋章变化
  emitter.on('chats.correctSelfMedalData', handleCorrectSelfMedalData)
  // 监听表情发送通知
  emitter.on('sendEmoji', handleSendEmoji)
  // 监听本地举手状态发送通知
  emitter.on('sendRaiseHand', handleSendRaiseHand)
}

/**
 * 移除事件监听
 */
const removeEventListeners = () => {
  emitter.off('chats.correctSelfMedalData', handleCorrectSelfMedalData)
  emitter.off('sendEmoji', handleSendEmoji)
  emitter.off('sendRaiseHand', handleSendRaiseHand)
}

// 组件销毁前移除事件监听
onBeforeUnmount(() => {
  removeEventListeners()
})

const init = () => {
  listenerEvent()
}
init()
</script>
<style lang="scss" scoped>
.video-box {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 100%;
  background: url('../img/black-lines-bg.png') center center;
  background-size: contain;
  #video-group-local {
    width: 100%;
    height: 100%;
  }
  .avatar-box {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    .avatar-frame {
      width: calc(50% + 24px);
      height: calc(50% + 24px);
      position: absolute;
      background-size: cover !important;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 10;
    }
    .avatarImg {
      position: relative;
      border-radius: 50%;
      width: 50%;
      height: 50%;
    }
    .onStageStyle {
      width: 60%;
      height: 60%;
    }
  }
}
</style>
