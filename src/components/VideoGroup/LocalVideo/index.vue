<template>
  <div class="flex-shrink-0">
    <!-- 学生本人 -->
    <StudentVideo ref="LocalVideo" />
  </div>
</template>
<script setup>
import { computed } from 'vue'
import StudentVideo from './StudentVideo.vue'
import { useClassData } from '@/stores/classroom'

import { emitter } from '@/hooks/useEventBus'
defineOptions({
  name: 'LocalVideo'
})

const store = useClassData()

const baseData = computed(() => store.baseData)
const selfId = computed(() => baseData.value.commonOption.stuId)

const chatAudioChange = noticeContent => {
  // 被私聊学员收到老师私聊信令后学员端默认仅拉取老师音频，在被私聊期间不响应互听开关信令;其它非被私聊学员在收到老师私聊其它学员信令后停止拉取老师音频和被私聊学员音频，在老师私聊其它学员期间响应互听开关信令（但特别注意的是，即使互听打开也不能拉取正在私聊学员的音频）;在私聊结束后老师端需要补充下发一次当前互听开关状态，保证被私聊的学员互听状态及时同步到老师设置的状态（这里也实现了在互听开启时其它非被私聊学员端会重新拉取被私聊学员的音频）；学员端收到私聊结束信令后恢复拉取老师音频。
  if (noticeContent.pub) {
    store.updatePrivateChatInfo(noticeContent)
    if (selfId.value == noticeContent.userId) {
      store.updateSelfVideoMicLink(true)
    }
  } else {
    store.updatePrivateChatInfo({})
    if (selfId.value == noticeContent.userId) {
      store.updateSelfVideoMicLink(false)
    }
  }
}
// todo 抽到哪里?
emitter.on('changeAudioStatus', chatAudioChange)
</script>
<style lang="scss" scoped>
.flex-shrink-0 {
  flex-shrink: 0;
  height: 100%;
  display: flex;
  aspect-ratio: 1;
}
</style>
