<template>
  <!-- 老师视频窗口 -->
  <div class="videoBox">
    <!-- 老师视频 -->
    <div v-show="renderTeacherVideo" id="teacherVideoContainer"></div>
    <!-- 老师上台 -->
    <div v-if="teacherOnStageStatus" class="onStageShow"></div>
    <!-- 下方信息栏 -->
    <InfoBar
      :showType="infoType"
      :name="name"
      :microphoneStatus="teacherMicrophoneStatus"
      :showMicrophone="teacherInclass"
    ></InfoBar>
  </div>
</template>

<script setup>
import InfoBar from './InfoBar/index.vue'
import { computed, watch, nextTick } from 'vue'
import { useClassData } from '@/stores/classroom'
import { useRtcService } from '@/hooks/useRtcService'
import { showBarType } from './constant'

const { getRtcService } = useRtcService()
const store = useClassData()
const teacherInclass = computed(() => store.teacherInfo.inClass)
const teacherOnStageStatus = computed(() => store.teacherInfo.onStageStatus)
const teacherOnPrivateChatStatus = computed(() => store.teacherInfo.privateChatStatus)
const renderTeacherVideo = computed(() => {
  return store.teacherInfo.inClass && !store.teacherInfo.videoMute && !teacherOnStageStatus.value
})
const name = computed(() => store.teacherInfo.nickName)
const teacherMicrophoneStatus = computed(() => !store.teacherInfo.audioMute)
const infoType = computed(() => {
  if (teacherOnPrivateChatStatus.value) {
    return showBarType.privateChat
  } else if (teacherOnStageStatus.value) {
    return showBarType.onStage
  } else {
    return showBarType.nameInfo
  }
})
watch(teacherMicrophoneStatus, newValue => {
  console.log(`audio:老师麦克风状态变化, newValue: ${newValue}`)
  const RtcService = getRtcService()
  if (newValue) {
    RtcService.muteRemoteAudio('teacher', false)
  } else {
    RtcService.muteRemoteAudio('teacher', true)
  }
})
watch(
  () => renderTeacherVideo.value,
  newValue => {
    if (newValue) {
      nextTick(() => {
        const container = document.getElementById('teacherVideoContainer')
        if (container) {
          container.innerHTML = ''
          const RtcService = getRtcService()

          RtcService.createTeacherVideo('teacherVideoContainer')
        }
      })
    } else {
      const ele = document.getElementById('teacherVideoContainer')
      if (ele) {
        ele.innerHTML = ''
      }
    }
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.videoBox {
  display: flex;
  flex-direction: column;
  position: relative;
  aspect-ratio: 4/3;
  background: url('./img/teacher-no-camera.png') center center no-repeat;
  background-size: cover;
  #teacherVideoContainer {
    width: 100%;
    height: 100%;
  }
  .onStageShow {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    height: 100%;
    width: 100%;
    background: url('./img/onStage.png') center no-repeat;
    background-size: cover;
  }
}
</style>
