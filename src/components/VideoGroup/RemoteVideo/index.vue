<template>
  <div
    class="video-container"
    ref="showBox"
    @mouseenter="enterVideoGroup"
    @mouseleave="leaveVideoGroup"
  >
    <!-- 背景框 -->
    <div class="background-box" v-show="!canScroll">
      <FillRemoteVIdeoItem class="video-item" v-for="item in 8" :key="item" />
    </div>
    <!-- 左箭头 -->
    <div v-show="arrowShow.pre" class="arrow-left" @click="pageChange('pre')">
      <img class="arrow-icon" src="../img/left-arrow.png" />
    </div>

    <div class="video-wrapper" ref="scroll">
      <div class="video-list" ref="allRemoteBox">
        <RemoteVideoItem
          class="video-item"
          v-for="item in inClassStudentList"
          :key="item.userId"
          :remoteStuInfo="item"
          :remoteAudioStatus="remoteAudioStatus"
          :hideRemoteVideo="hideRemoteVideo"
        />
      </div>
    </div>
    <!-- 右箭头 -->
    <div v-show="arrowShow.next" class="arrow-right" @click="pageChange('next')">
      <img class="arrow-icon" src="../img/right-arrow.png" />
    </div>
  </div>
</template>

<script setup>
defineOptions({
  name: 'RemoteVideoGroup'
})
import { computed, ref, reactive, nextTick, onMounted, onBeforeUnmount } from 'vue'
import { useClassData } from '@/stores/classroom'
import RemoteVideoItem from './RemoteVideoItem.vue'
import FillRemoteVIdeoItem from './FillRemoteVIdeoItem.vue'
import BScroll from '@better-scroll/core'
import MouseWheel from '@better-scroll/mouse-wheel'
import { emitter } from '@/hooks/useEventBus'

// 获取 store 和 refs
const store = useClassData()
const showBox = ref(null)
const scroll = ref(null)
const allRemoteBox = ref(null)

// 计算属性
const wallRandomCallStatus = computed(() => store.wallRandomCallStatus)
const studentList = computed(() => store.studentlistNoSelf)
const inClassStudentList = computed(() => studentList.value.filter(item => item.inClass))
const canScroll = computed(() => {
  return inClassStudentList.value.length > 7
})
const isExaminationStatus = ref(false)
const isFocusMode = ref(false)

// 计算是否隐藏远程视频
const hideRemoteVideo = computed(
  () => wallRandomCallStatus.value || isExaminationStatus.value || isFocusMode.value
)
const remoteAudioStatus = computed(() => store.remoteAudioStatus)

// 监听课中考试状态
emitter.on('setExaminationStatus', data => {
  isExaminationStatus.value = data
})

// 监听焦点模式
emitter.on('focus_mode', status => {
  isFocusMode.value = status
})

// Intersection Observer 相关
let intersectionObserver = null

const initObserver = () => {
  intersectionObserver = new IntersectionObserver(
    entries => {
      for (let v of entries) {
        const uid = v.target.dataset.id
        // 更新学员可见状态
        store.changeStudentInfo(uid, {
          isIntersecting: v.isIntersecting
        })
      }
    },
    {
      root: document.querySelectorAll('.video-wrapper')[0],
      threshold: 0
    }
  )
}

const toggleObserver = (uid, isObserve) => {
  const ele = document.querySelector(`#remote-observe-${uid}`)
  if (isObserve) {
    ele && intersectionObserver.observe(ele)
  } else {
    ele && intersectionObserver.unobserve(ele)
  }
  nextTick(() => {
    bs.refresh()
    if (isInVideoGroup) {
      showArrowHandle()
    }
  })
}

emitter.on('toggleObserver', toggleObserver)

// BetterScroll 相关
let bs = null
BScroll.use(MouseWheel)
initObserver()

const getPageSize = () => {
  const remoteVideos = allRemoteBox.value.children
  if (remoteVideos.length > 0) {
    const remoteVideoRect = remoteVideos[0].getBoundingClientRect()
    const boxtRect = showBox.value.getBoundingClientRect()
    const pageSize = Math.floor(boxtRect.width / remoteVideoRect.width)
    return pageSize
  } else {
    return false
  }
}

const firstIntersectingEleIndex = () => {
  let index = 0
  for (let i = 0; i < inClassStudentList.value.length; i++) {
    if (inClassStudentList.value[i].isIntersecting) {
      index = i
      break
    }
  }
  return index
}

const pageChange = dir => {
  const pageSize = getPageSize()
  const currentIndex = firstIntersectingEleIndex()
  const direction = dir == 'pre' ? -1 : 1
  let scrollToEleIndex = currentIndex + direction * pageSize

  if (dir == 'pre' && scrollToEleIndex < 0) {
    scrollToEleIndex = 0
  }
  if (dir == 'next' && scrollToEleIndex > inClassStudentList.value.length - 1) {
    scrollToEleIndex = inClassStudentList.value.length - 1
  }
  const userId = inClassStudentList.value[scrollToEleIndex].userId
  const pageToEle = document.getElementById(`remote-observe-${userId}`)
  bs.scrollToElement(pageToEle)
  showArrowHandle()
}

const arrowShow = reactive({
  pre: false,
  next: false
})

let isInVideoGroup = false

const enterVideoGroup = () => {
  isInVideoGroup = true
  showArrowHandle()
}

const leaveVideoGroup = () => {
  isInVideoGroup = false
  hideArrowHandle()
}

const showArrowHandle = () => {
  const rect = allRemoteBox.value.getBoundingClientRect()
  const boxRect = showBox.value.getBoundingClientRect()
  arrowShow.pre = rect.left < boxRect.left
  arrowShow.next = rect.right > boxRect.right + 1
}

const hideArrowHandle = () => {
  arrowShow.pre = false
  arrowShow.next = false
}

onMounted(() => {
  bs = new BScroll(scroll.value, {
    scrollX: true,
    scrollY: false,
    mouseWheel: true,
    disableMouse: false,
    disableTouch: false,
    bounce: false,
    throttleTime: 300
  })
  bs.on('mousewheelMove', () => {
    showArrowHandle()
  })
})

onBeforeUnmount(() => {
  intersectionObserver && intersectionObserver.disconnect()
  bs && bs.destroy()

  // 清理事件监听
  emitter.off('setExaminationStatus')
  emitter.off('focus_mode')
  emitter.off('toggleObserver')
})
</script>

<style lang="scss" scoped>
.video-container {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  flex-grow: 1;

  .background-box {
    position: absolute;
    display: flex;
    overflow: hidden;
    width: 100%;
    height: 100%;
  }

  .video-wrapper {
    position: relative;
    display: flex;
    overflow: hidden;
    width: 100%;
    height: 100%;

    .video-list {
      display: flex;
      height: 100%;
    }
  }

  .video-item {
    margin-right: 2px;
    aspect-ratio: 1;
    height: 100%;

    &:last-child {
      margin-right: 0;
    }
  }

  .arrow-left {
    width: 20px;
    height: 36px;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.4);
    left: 4px;
    z-index: 10;
    border-radius: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .arrow-right {
    width: 20px;
    height: 36px;
    position: absolute;
    background-color: rgba(0, 0, 0, 0.4);
    right: 4px;
    z-index: 10;
    border-radius: 1px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
  }

  .arrow-icon {
    width: 10px;
    height: 20px;
  }
}

/* 锁定横向滑动 */
:global(html) {
  scrollbar-width: none; /* Firefox */
  overscroll-behavior-x: none;
  overflow-x: clip;
}

:global(::-webkit-scrollbar) {
  display: none; /* Chrome/Safari */
}

/* 触控板滚动特征检测 */
@media (pointer: fine) and (hover: hover) {
  :global(body) {
    touch-action: pan-y;
  }
}
</style>
