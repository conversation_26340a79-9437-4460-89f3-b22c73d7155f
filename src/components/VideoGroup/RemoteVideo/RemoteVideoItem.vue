<template>
  <div class="remote-video-wrap">
    <!-- 学生视频窗口 -->
    <div :id="`remote-observe-${stuInfo.userId}`" :data-id="stuInfo.userId" class="video-window">
      <!-- 学生视频 -->
      <div v-show="renderVideo" :id="'remote-' + stuInfo.userId" class="video-content"></div>
      <div v-if="isPlayAudio" class="speaking-indicator">
        <img src="../img/speaking.png" alt="" />
      </div>
      <!-- 头像 -->
      <div v-if="!renderVideo" class="avatar-container">
        <div
          v-if="stuInfo?.prop?.avatarFrame?.resourceUrl && !stuInfo.multVideoLinkStatus"
          class="avatar-frame"
          :style="
            'background: url(' + stuInfo?.prop?.avatarFrame?.resourceUrl + ') no-repeat center'
          "
        ></div>
        <img v-if="!stuInfo.multVideoLinkStatus" class="avatar-image" :src="stuInfo.avatar" />
        <img v-else class="mult-video-link" src="../img/bg-mult-video-link.png" />
      </div>
      <!-- 下方信息栏 -->
      <InfoBar
        :showType="infoType"
        :name="stuInfo.nickName"
        :microphoneStatus="!stuInfo.audioMute"
      ></InfoBar>
      <StudentTool :id="stuInfo.userId"></StudentTool>
    </div>
  </div>
</template>

<script setup>
import { computed, watch, nextTick, onBeforeUnmount, onMounted } from 'vue'

import InfoBar from '../InfoBar/index.vue'
import StudentTool from '../StudentTool/index.vue'
import { showBarType } from '../constant'
import { emitter } from '@/hooks/useEventBus'
import { useRtcService } from '@/hooks/useRtcService'
import { useClassData } from '@/stores/classroom'

const { getRtcService } = useRtcService()
const store = useClassData()
// 定义props
const props = defineProps({
  remoteStuInfo: {
    type: Object,
    default: null
  },
  hideRemoteVideo: {
    type: Boolean,
    default: null
  }
})

const stuInfo = computed(() => props.remoteStuInfo)
const isIntersecting = computed(() => stuInfo.value?.isIntersecting)
const multVideoLinkStatus = computed(() => stuInfo.value?.multVideoLinkStatus)
const videoMute = computed(() => stuInfo.value?.videoMute)
const userId = computed(() => stuInfo.value?.userId)
const privateChatInfo = computed(() => store.privateChatInfo)
const selfId = computed(() => store.selfInfo.userId)
// 计算是否渲染视频
const renderVideo = computed(() => {
  console.log(
    'renderVideo',
    isIntersecting.value,
    multVideoLinkStatus.value,
    videoMute.value,
    props.hideRemoteVideo
  )
  return (
    isIntersecting.value && !multVideoLinkStatus.value && !videoMute.value && !props.hideRemoteVideo
  )
})
const audioMute = computed(() => stuInfo.value.audioMute)
const isPlayAudio = computed(() => {
  if (audioMute.value) {
    // 学生未推流 不播放音频
    console.log(`audio:学生未推流 不播放音频, userId: ${userId.value}`)
    return false
  }
  // 学生推流
  if (privateChatInfo.value.pub) {
    console.log(
      `audio:私聊开启中，userId: ${userId.value},私聊学生id${privateChatInfo.value.userId}`
    )
    // 私聊
    if (privateChatInfo.value.userId == userId.value) {
      // 私聊对象是这个学生 不播放音频
      console.log(`audio:私聊开启中，对象是这个学生 不播放音频, userId: ${userId.value}`)
      return false
    }
    if (privateChatInfo.value.userId == selfId.value) {
      // 私聊对象是我本人，也不播放其他学生音频
      console.log(`audio:私聊开启中，对象是我本人，静音其他人`)
      return false
    }
  }
  if (multVideoLinkStatus.value) {
    // 多人上台，播放音频
    console.log(`audio:多人上台中，播放该学生音频, userId: ${userId.value}`)
    return true
  }
  // 未多人上台，按全员互听状态播放音频
  console.log(
    `audio:没有任何互动，按全员互听状态播放音频, userId: ${userId.value}, remoteAudioStatus: ${store.remoteAudioStatus}`
  )
  return store.remoteAudioStatus
})

// 信息栏类型
const infoType = computed(() => {
  return showBarType.nameInfo
})

// 监听视频渲染状态变化
watch(
  () => renderVideo.value,
  value => {
    if (value) {
      console.log('创建学生视频', value, userId.value)
      nextTick(() => {
        const remoteElement = document.getElementById(`remote-${userId.value}`)
        if (remoteElement && remoteElement.innerHTML) {
          console.warn(`当前已创建学生视频，禁止重复触发,uid：${userId.value}`)
          return
        }
        const RtcService = getRtcService()
        RtcService.createRemoteVideo(userId.value, `remote-${userId.value}`)
      })
    } else {
      nextTick(() => {
        const remoteElement = document.getElementById(`remote-${userId.value}`)
        if (remoteElement) {
          remoteElement.innerHTML = ''
        }
      })
    }
  },
  { immediate: true }
)
watch(
  () => isPlayAudio.value,
  value => {
    const RtcService = getRtcService()
    console.log(`audio:isPlayAudio: ${value}, userId: ${userId.value}`)
    if (value) {
      RtcService.muteRemoteAudio(userId.value, false)
    } else {
      RtcService.muteRemoteAudio(userId.value, true)
    }
  }
)

// 触发观察者切换
const triggerToggleObserver = (uid, status) => {
  emitter.emit('toggleObserver', uid, status)
}

// 生命周期钩子
onMounted(() => {
  triggerToggleObserver(userId.value, true)
})

onBeforeUnmount(() => {
  triggerToggleObserver(userId.value, false)
})
</script>

<style lang="scss" scoped>
.remote-video-wrap {
  position: relative;

  // 视频开关区域
  .video-switch-wrapper {
    display: none;
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 20;

    .video-switch-container {
      font-size: 12px;
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .hide-video-button,
      .show-video-button {
        width: 82px;
        height: 30px;
        line-height: 26px;
        text-align: center;
        border-radius: 16px;
        color: #fff;
        border: 2px solid rgba(255, 255, 255, 0.4);
        cursor: pointer;
      }

      .hide-video-button {
        background: #ffaa0a;
      }

      .show-video-button {
        background: #02ca8a;
      }
    }
  }

  &:hover {
    .video-switch-wrapper {
      display: block;
    }
  }
}

.video-window {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-size: contain;
  background-image: url('../img/black-lines-bg.png');
}

.video-content {
  height: 100%;
  width: 100%;
}

.speaking-indicator {
  position: absolute;
  top: 0;
  right: 0;
  margin-top: 2px;
  margin-right: 2px;
  width: 24%;
  height: 24%;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.6);
  img {
    width: 100%;
    height: 100%;
  }
}

.avatar-container {
  display: flex;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-repeat: no-repeat;
  align-items: center;
}

.avatar-image {
  position: relative;
  border-radius: 50%;
  width: 50%;
  height: 50%;
}

.mult-video-link {
  width: 54px;
  height: 62px;
}
.avatar-frame {
  width: calc(50% + 24px);
  height: calc(50% + 24px);
  position: absolute;
  background-size: cover !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}
</style>
