<template>
  <div class="video-list">
    <TeacherVideo />
    <LocalVideo />
    <RemoteVideo />
  </div>
</template>
<script setup>
import TeacherVideo from './TeacherVideo.vue'
import LocalVideo from './LocalVideo/index.vue'
import RemoteVideo from './RemoteVideo/index.vue'
import { setRtcStatus } from '@/api/classroom'
import { onMounted, computed, onBeforeUnmount } from 'vue'
import { emitter } from '@/hooks/useEventBus'
import { useClassData } from '@/stores/classroom'
import { useRtcService } from '@/hooks/useRtcService'
defineOptions({
  name: 'VideoGroup'
})
const store = useClassData()
const { getRtcService } = useRtcService()
const self = computed(() => store.selfInfo)

const commonOption = computed(() => store.baseData.commonOption)

const sendRtcStatus = async (options = {}) => {
  // 后端status接收参数状态
  // 1:推视频、2:推音频、3:推音视频、0:断流
  let status = -1
  const cameraStatus = !self.value.audioMute
  const microphoneStatus = !self.value.videoMute

  if (cameraStatus) {
    status = 1
  }
  if (microphoneStatus) {
    status = 2
  }
  if (cameraStatus && microphoneStatus) {
    status = 3
  }
  if (status == -1) {
    return
  }
  let params = {
    planId: commonOption.value.planId,
    classId: commonOption.value.classId,
    status: status,
    micPermission: microphoneStatus ? 1 : 2,
    cameraPermission: cameraStatus ? 1 : 2,
    micIsOpen: microphoneStatus ? 1 : 2,
    cameraIsOpen: cameraStatus ? 1 : 2
  }

  if (typeof options.micIsOpen === 'boolean') {
    params['micIsOpen'] = options.micIsOpen ? 1 : 2
  }
  if (typeof options.displayVideo === 'boolean') {
    params['cameraIsOpen'] = options.displayVideo ? 1 : 2
  }

  await setRtcStatus(params)
}

const liveQuitHandle = () => {
  const RtcService = getRtcService()
  RtcService.unpublish()
  RtcService.leaveChannel()
  RtcService.isKickout = true
}
emitter.on('liveQuit', liveQuitHandle)
emitter.on('rtcStatusChange', sendRtcStatus)
onMounted(() => {
  sendRtcStatus()
})
onBeforeUnmount(() => {
  emitter.off('liveQuit', liveQuitHandle)
  emitter.off('rtcStatusChange', sendRtcStatus)
})
</script>
<style lang="scss" scoped>
.video-list {
  background-color: #000000;
  width: 100%;
  position: relative;
  display: flex;
  gap: 2px;
  height: 15%;
  /* 防止内容溢出撑开父容器 */
  max-width: 100%;
  overflow: hidden;
  /* 确保宽度不超过 mainContainer */
  box-sizing: border-box;
}
</style>
