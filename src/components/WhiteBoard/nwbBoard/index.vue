<template>
  <canvas id="nwb-canvas"></canvas>
</template>

<script setup>
import { onMounted, onBeforeUnmount, nextTick, computed, ref } from 'vue'
import { whiteBoardHooks } from './whiteBoardHooks'
import { DrawingMode } from '@thinkacademy/nwb'

import { useIrcService } from '@/hooks/useIrcService'
import { useClassData } from '@/stores/classroom'
import { fetchIsAuthorizedUserList } from '@/api/classroom'
import { emitter } from '@/hooks/useEventBus'
import { getHistoryMessage } from '@/core/ThinkService/getHistoryMsg'
import { whiteBoardLog } from '@/utils/web-log/HWLogDefine'

const store = useClassData()
const baseData = computed(() => store.baseData)
const props = defineProps({
  parentDomClass: {
    type: String,
    default: '.nwbBoard'
  },
  ratioWidth: {
    type: Number,
    default: 4
  },
  ratioHeight: {
    type: Number,
    default: 3
  }
})

const { getIrcService } = useIrcService()
const pageId = ref(0)

const sendLogger = (customerMsg, loggerParams = {}) => {
  // Logan.log('loggerParams', customerMsg, loggerParams)
}

const handleOnRecvRoomBinMessageNotice = data => {
  // @log-ignore
  sendLogger('实时接收涂鸦数据', {
    dbKey: data.dbkey,
    keyMsgId: data.keyMsgId
  })
  boardInstance.receiveMsgHandle(data)
}

// 涂鸦实例
let boardInstance = null

// 监听窗口Resize 设置涂鸦区域大小
const onWindowResize = (screenWidth, screenHeight) => {
  if (boardInstance) {
    boardInstance.resizeCanvas({
      width: screenWidth,
      height: screenHeight
    })
    sendLogger('触发涂鸦resize方法，重新渲染', {
      screenWidth,
      screenHeight
    })
  } else {
    sendLogger('涂鸦实例不存在，无法触发resize方法', { screenWidth, screenHeight })
  }
}
// 监听whiteboard-content-show区域resize设置涂鸦区域大小
let resizeContentOb = new ResizeObserver(entries => {
  for (let entry of entries) {
    const { width, height } = entry.contentRect
    if (width !== 0 && height !== 0) {
      onWindowResize(width, height)
    }
  }
})

const fetchIsAuthorizedUserInfo = async params => {
  try {
    const res = await fetchIsAuthorizedUserList(params)
    return res.code === 0 ? res.data.pageKeyList : []
  } catch (e) {
    console.error('获取授权学员列表报错', e)
    return []
  }
}

// 涂鸦历史恢复
const handleHistoryMessage = async dbKey => {
  const teachdbkey = dbKey.split('_').splice(1).join('_')
  let userPromise = []
  const params = {
    appId: baseData.value.configs.ircAk,
    sk: baseData.value.configs.ircSk,
    businessId: 3,
    liveId: baseData.value.planInfo.id,
    ircApiHost: baseData.value.configs.ircApiHost
  }
  userPromise.push(
    getHistoryMessage({
      dbkey: teachdbkey,
      ...params
    })
  )
  const isAuthorizedUserList = await fetchIsAuthorizedUserInfo({
    planId: Number(baseData.value.planInfo.id)
  })

  const curPageKeyUserList = isAuthorizedUserList.find(e => e.pageKey === teachdbkey)

  if (curPageKeyUserList && curPageKeyUserList.userIdList) {
    curPageKeyUserList.userIdList.forEach(stu => {
      const studbkey = `${stu}_${teachdbkey}`
      userPromise.push(
        getHistoryMessage({
          dbkey: studbkey,
          ...params
        })
      )
    })
  }
  Promise.all(userPromise)
    .then(resAll => {
      let msgs = resAll.flat()
      try {
        boardInstance.receiveBatchMsgHandle(msgs)
      } catch (e) {
        console.error('handleRecoverHistoryMessage', e)
      }
    })
    .catch(err => {
      console.error('获取历史涂鸦失败', err)
    })
}

const setModeHandle = mode => {
  console.log('setMode', mode)

  boardInstance.setDrawingMode(mode)
  if (mode === 'none') {
    document.querySelector('.nwbBoard').style.pointerEvents = 'none'
  } else {
    document.querySelector('.nwbBoard').style.pointerEvents = 'auto'
  }
  sendLogger('设置绘制模式', {
    mode
  })
}
const setStrokeHandle = config => {
  boardInstance.setStroke(config)
  sendLogger('设置画笔宽度')
}
const setEraserWidthHandle = width => {
  boardInstance.setEraserWidth(width)
  sendLogger('设置橡皮宽度')
}

// const revokeHandle = () => {
//   boardInstance.undo()
//   sendLogger('撤销涂鸦')
// }
const listenerSignalService = () => {
  const SignalService = getIrcService()
  SignalService.on('onRecvRoomBinMessageNotice', handleOnRecvRoomBinMessageNotice)

  emitter.on('changePageId', ({ id, currentCourseWareData }) => {
    changePageId(id, currentCourseWareData)
  })

  emitter.on('setMode', setModeHandle)
  emitter.on('setStroke', setStrokeHandle)
  emitter.on('setEraserWidth', setEraserWidthHandle)
}

const removeListenerSignalService = () => {
  const ircServer = getIrcService()
  ircServer.off('onRecvRoomBinMessageNotice', handleOnRecvRoomBinMessageNotice)

  emitter.off('changePageId')
  emitter.off('setMode', setModeHandle)
  emitter.off('setStroke', setStrokeHandle)
  emitter.off('setEraserWidth', setEraserWidthHandle)
}

const changePageId = (id, currentCourseWareData) => {
  if (pageId.value === id) return
  pageId.value = id

  whiteBoardLog.info(
    '检测到当前涂鸦的pageId和最新的pageid不一致，涂鸦翻页changePageId',
    pageId.value,
    currentCourseWareData
  )

  const { specificLiveKey, courseWareId, pageId: pageIds } = currentCourseWareData
  if (!specificLiveKey || !courseWareId || !pageIds) {
    console.error('课件信息错误', currentCourseWareData)
    return
  }
  const dbKey = `${specificLiveKey}_${courseWareId}_${pageIds}`
  const stuDbKey = `${baseData.value.stuInfo.id}_${dbKey}`
  boardInstance.pageChange(stuDbKey)
  // 翻页后上报一下该页被授权的学生列表，再翻回来的时候拉取历史消息时使用
  console.log('xxr:翻页获取历史消息', stuDbKey)
  sendLogger('涂鸦翻页', {
    stuDbKey
  })
}

onMounted(() => {
  boardInstance = whiteBoardHooks(
    {
      id: 'nwb-canvas',
      getHistoryMessage: handleHistoryMessage,
      maxChars: 1000
    },
    {
      defaultMode: DrawingMode.none
    }
  )
  nextTick(() => {
    resizeContentOb.observe(document.querySelector(props.parentDomClass))
  })
  listenerSignalService()
})

onBeforeUnmount(() => {
  removeListenerSignalService()
  resizeContentOb.disconnect()

  boardInstance.destroy()
  boardInstance = null
})
</script>

<style lang="scss" scoped></style>
