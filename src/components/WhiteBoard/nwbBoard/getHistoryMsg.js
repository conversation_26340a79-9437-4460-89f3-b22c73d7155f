import axios from 'axios'
import Md5 from 'crypto-js/md5'

const sendLogger = (customerMsg, loggerParams = {}, level = 'info') => {
  console.log('sendLogger', customerMsg, loggerParams, level)
}
export async function getHistoryMessage({ dbkey, appId, sk, businessId, liveId, ircApiHost }) {
  console.log('翻页', dbkey, appId, sk, businessId, liveId, ircApiHost)
  const fetchUrl = `${ircApiHost}/chat/v4/getHistoryBinaryMessages2`
  const GET_SIZE = 1500
  const timestamp = new Date().getTime()
  const nonce = Math.random().toString()
  const signature = Md5(appId + '\n' + timestamp + '\n' + nonce + '\n' + sk + '\n')
  const url = `${fetchUrl}?appId=${appId}&businessId=${businessId}&liveId=${liveId}&timestamp=${timestamp}&signature=${signature}&nonce=${nonce}`
  sendLogger('涂鸦获取历史数据getHistoryMessage', {
    fetchUrl,
    size: GET_SIZE,
    timestamp,
    nonce,
    signature,
    url
  })
  return new Promise(resolve => {
    let params = {
      key: dbkey,
      startMsgId: '0',
      endMsgId: '+inf',
      order: 0,
      page: 1,
      pageSize: GET_SIZE
    }
    sendLogger('涂鸦获取历史数据', { ...params })
    axios
      .post(url, params, { withCredentials: false, timeout: 15000 })
      .then(res => {
        console.log('获取翻页数据:', res.data.code)
        if (res.data.code === 0) {
          let promiseAll = []
          if (res.data.content.pages > 1) {
            for (let i = 2; i <= res.data.content.pages; i++) {
              sendLogger('涂鸦获取历史数据', { ...params, page: i })
              promiseAll.push(
                axios.post(url, { ...params, page: i }, { withCredentials: false, timeout: 15000 })
              )
            }
          }
          Promise.all(promiseAll)
            .then(resAll => {
              resAll.unshift(res)
              console.log('resAll', resAll)
              let msgsAll = []
              resAll.map(e => {
                if (e.data.code === 0) {
                  console.log('获取数据:', e.data.content.msgs)
                  msgsAll.push(...e.data.content.msgs)
                }
              })
              console.log('msgsAll', msgsAll)
              sendLogger('涂鸦获取历史数据成功')
              resolve(msgsAll)
            })
            .catch(err => {
              sendLogger('涂鸦获取历史数据报错', { err }, 'error')
              resolve([])
            })
        }
      })
      .catch(err => {
        sendLogger('涂鸦获取历史数据报错', { err }, 'error')
        resolve([])
      })
  })
}
