import { nwb } from '@thinkacademy/nwb'
import { useIrcService } from '@/hooks/useIrcService'
import { useClassData } from '@/stores/classroom'

/**
 *
 * @param {Object} options
 * @param {string} options.id
 * @param {function} options.getHistoryMessage
 * @param {function} getDbKey // 翻页传入dbkey
 * @returns
 */

export const whiteBoardHooks = (options, config) => {
  const store = useClassData()

  const { getIrcService } = useIrcService()
  // 发涂鸦二进制消息
  const sendMsgHandle = msg => {
    // @log-ignore
    let { dbKey, keyMsgId, content } = msg
    if (config.dbKeyPrefix) {
      dbKey = `${config.dbKeyPrefix}${dbKey}`
    }
    const SignalService = getIrcService()
    SignalService.sendRoomBinMessage(
      [store.baseData.commonOption.roomlist[0]],
      dbKey,
      keyMsgId,
      content
    )
  }
  const parmas = {
    ...options,
    role: 'student',
    userId: store.baseData.stuInfo.id + '',
    userName: store.baseData.stuInfo.nickName, // 用于展示的用户昵称
    serverTimestamp: +new Date() + store.baseData.commonOption.timeOffset,
    messageSender: sendMsgHandle
  }
  const canvasInstall = new nwb(parmas)
  canvasInstall.setDrawingMode(config.defaultMode)

  return canvasInstall
}
