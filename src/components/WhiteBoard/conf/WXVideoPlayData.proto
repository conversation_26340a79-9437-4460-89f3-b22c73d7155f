syntax = "proto3";


enum WXVideoActionType{
    WXVideoActionPlay     = 0;                // 播放
    WXVideoActionPause    = 1;                // 暂停
    WXVideoActionStop     = 2;                // 停止
}


message WXVideoData {
    WXVideoActionType type = 1;		//指令类型
    string url = 2;			//视频地址
    int32 time = 3;			//指令对应视频的时间
    float x = 4;                        //坐标点
    float y = 5;
    float width = 6;                    //宽高
    float height = 7;
}
