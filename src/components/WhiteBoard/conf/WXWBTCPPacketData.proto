syntax = "proto3";
import "./any.proto";
import "./WXVideoPlayData.proto";
import "./WXChatUpData.proto";


enum WXWBMessageType{
    WXWBMessageTypeRecover = 0;                 //恢复
    WXWBMessageTypeCancel = 1;                  //撤销
    WXWBMessageTypeUpdateShape = 2;             //更新形状
    WXWBMessageTypeDraw = 3;                    //画
    WXWBMessageTypeDeleteShape = 4;             //删除图形
    WXWBMessageTypeClearAll = 5;                //清屏
    WXWBMessageTypeScroll = 6;                  //画布滚动
    WXWBMessageTypeGeometry = 7;                //几何图形绘制
    WXWBMessageTypeChoose = 9;                  //形状选择
    WXWBMessageTypePointer = 10;                //指针
    WXWBMessageTypeRuler = 11;                  //教学工具-直尺
    WXWBMessageTypeCompass = 12;                //教学工具-圆规
    WXWBMessageTypeTimer = 13;                  //教学工具-计时器
    WXWBMessageTypeUpdateMiniBoard = 14;        //更新小黑板
    WXWBMessageTypeDeleteMiniBoard = 16;        //删除小黑板
    WXWBMessageTypeUpdateCustomView = 17;       //更新自定义View
    WXWBMessageTypeBatchAction = 18;            //批量操作
    WXWBMessageTypeTextBox = 19;                //文本框
    WXWBMessageTypeSignalCourseware = 20;	    //信令课件
    WXWBMessageTypeCustomBusiness = 1000;       //业务自定义
}
enum WXTPointType{
    WXWBPointTypePen = 0;                       //笔
    WXWBPointTypeEraser = 1;                    //橡皮
    WXWBPointTypeLine = 2;                      //线
    WXWBPointArrowLine = 3;                     //单箭头
    WXWBPointArrow2Line = 4;                    //双箭头
    WXWBPointTriangle = 5;                      //三角形
    WXWBPointRect = 6;                          //正方形
    WXWBPointEllipses = 7;                      //椭圆
    WXWBPointPrarllelogram = 8;                 //平行四边形
    WXWBPointPolygons = 9;                      //多边形
    WXWBPointTypeSelect = 10;                   //选择模式
    WXWBPointCircle = 11;                       // 11 圆形
    WXWBPointRightTriangle = 12;                // 12 直角三角形
    WXWBPointAxis = 13;                         // 13 轴线
    WXWBPointCube = 14;                         // 14 立方体
    WXWBPointCylinder = 15;                     // 15 圆柱
    WXWBPointCone = 16;                         // 16 圆锥
    WXWBPointDashed = 17;                       // 17 虚线
    WXWBPointCoordinate = 18;                   // 18 坐标图形
    WXWBPointAutoShape = 19;                    // 19 自动判断图形
    WXWBPointDiamond = 20;                      // 20 菱形
    WXWBPointPointer = 21;                      // 21 激光笔
    WXWBPointIsoscelesTriangle = 22;            // 22 等腰三角形
    WXWBPointLine2 = 23;                        // 23 线(支持旋转)
    WXWBPointEllipses2 = 24;                    // 24 椭圆(没有中心点)

    //数学学科工具开始
    WXWBPointTwoCoordinateSystem = 30;          // 30 二维直角坐标系
    WXWBPointThreeCoordinateSystem = 31;        // 31 三维直角坐标系
    WXWBPointCurve1 = 32;                       // 32 曲线1
    WXWBPointCurve2 = 33;                       // 33 曲线2
    WXWBPointCurve3 = 34;                       // 34 曲线3
    WXWBPointCurve4 = 35;                       // 35 曲线4
    WXWBPointCurve5 = 36;                       // 36 曲线5
    WXWBPointCurve6 = 37;                       // 37 曲线6
    WXWBPointCurve7 = 38;                       // 38 曲线7
    WXWBPointCurve8 = 39;                       // 39 曲线8
    WXWBPointCurve9 = 40;                       // 40 曲线9
    WXWBPointCurve10 = 41;                      // 41 曲线10
    WXWBPointCurve11 = 42;                      // 42 曲线11
    WXWBPointCurve12 = 43;                      // 43 曲线12
    WXWBPointCurve13 = 44;                      // 44 曲线13
    WXWBPointIsoscelesTrapezoid = 45;           // 45 等腰梯形
    WXWBPointRightTrapezoid = 46;               // 46 直角梯形
    WXWBPointPentagon = 47;                     // 47 五边形
    WXWBPointHexagon = 48;                      // 48 六边形
    WXWBPointFrontCube = 49;                    // 49 立方体1
    WXWBPointSideCube = 50;                     // 50 立方体2
    WXWBPointRectangularPyramid1 = 51;          // 51 四棱锥1
    WXWBPointRriangularPyramid = 52;            // 52 三棱锥
    WXWBPointTriangularPrism = 53;              // 53 三棱柱
    WXWBPointCylinderPlatform = 54;             // 54 柱形台体
    WXWBPointSphere = 55;                       // 55 球体
    WXWBPointCubePlatform = 56;                 // 56 方形台体
    WXWBPointCorpusTrapezoideum = 57;           // 57 斜方体
    WXWBPointTruncatedTriangularPrism = 58;     // 58 斜三棱柱
    WXWBPointRectangularPyramid2 = 59;          // 59 四棱锥2
    //物理学科工具开始
    WXWBPointPhysicsCoordinate = 60;            // 60 坐标系
    WXWBPointPhysicsVector = 61;                // 61 矢量
    WXWBPointPhysicsSwitch = 62;                // 62 开关
    WXWBPointPhysicsBattery = 63;               // 63 电池
    WXWBPointPhysicsLight = 64;                 // 64 灯泡
    WXWBPointPhysicsAmmeter = 65;               // 65 电流表
    WXWBPointPhysicsVoltmeter = 66;             // 66 电压表
    WXWBPointPhysicsResistance = 67;            // 67 电阻
    WXWBPointPhysicsRheostat = 68;              // 68 变阻器
    WXWBPointPhysicsElectromotor = 69;          // 69 电动机
    WXWBPointPhysicsBell = 70;                  // 70 电铃
    //化学学科工具开始
    WXWBPointChemistryElectronic1 = 71;         // 71 一层电子结构
    WXWBPointChemistryElectronic2 = 72;         // 72 二层电子结构
    WXWBPointChemistryElectronic3 = 73;         // 73 三层电子结构
    WXWBPointChemistryCoordinate = 74;          // 74 坐标系
    WXWBPointChemistryTestTube = 75;            // 75 试管
    WXWBPointChemistryBeaker = 76;              // 76 烧杯
    WXWBPointChemistryWildMouthBottle = 77;     // 77 广口瓶
    WXWBPointChemistryConicalFlask = 78;        // 78 锥形瓶
    WXWBPointChemistryFunnel = 79;              // 79 漏斗
    WXWBPointChemistryUShapePipe = 80;          // 80 U型管
    WXWBPointChemistryLongNeckFunnel = 81;      // 81 长颈漏斗
    WXWBPointChemistrySeparatingFunnel = 82;    // 82 分液漏斗
    WXWBPointChemistryBentPipe = 83;            // 83 弯型导管
    WXWBPointChemistryElectrolyticTank1 = 84;   // 84 电解池1
    WXWBPointChemistryElectrolyticTank2 = 85;   // 85 电解池2
    WXWBPointChemistryElectrolyticTank3 = 86;   // 86 电解池3
    WXWBPointChemistryBenzeneRing = 87;         // 87 苯环
    WXWBPointChemistryAlcoholLamp = 88;         // 88 酒精灯
    WXWBPointChemistryGasBottle1 = 89;          // 89 集气瓶1
    WXWBPointChemistryGasBottle2 = 90;          // 90 集气瓶2
    WXWBPointCustomCircular = 91;		// 91 圆弧

    //自定义形状
    WXWBPointCustomShapeImage = 100;            //100 插入图片
    WXWBPointFluorescentPen = 1000; 		    // 1000 荧光笔
    WXWBMessageTypeBatchMove = 1001;        //批量移动
    WXWBMessageTypeBatchDelete = 1002;      //批量删除
    WXWBMessageTypeBatchCopy = 1003;      //批量复制
}
message WXWBPageConfig {
  string currentPage = 1;                       //涂鸦当前页
  repeated string pageList = 2;                 //涂鸦整体页信息，包括所有页的 ID list
}
message WXWBRulerConfig {
  bool enable = 1;                              // enable: true 设置直尺，false 关闭直尺
  float left = 2;                               // 设置直尺左上角点位置
  float top = 3;
  float length = 4;                             // 设置直尺长度
  float rotate = 5;                             // 设置直尺旋转角度
}
message WXWBCompassesConfig {
  bool enable = 1;                              // enable: true 显示圆规并设置，false 关闭圆规显示
  float left = 2;                               // 设置圆规中心点的位置
  float top = 3;
  float radius = 4;                             // 设置圆规半径
  float rotate = 5;                             // 设置圆规旋转角度
}

message WXWBChooseConfig {
  repeated int32 selectShape = 1;               // 被选择的线的形状 id 的集合
  uint32 selectLineWidth = 2;
  string selectLineColor = 3;
  uint32 selectLineType = 4;                    // 0 直线 1 虚线
  float selectStartX = 5;                       // 选择范围起点横坐标
  float selectStartY = 6;                       // 选择范围起点纵坐标
  float selectEndX = 7;                         // 选择范围终点横坐标
  float selectEndY = 8;                         // 选择范围终点纵坐标
}

message WXWBRevokeAndRecoverConfig {
  repeated string actionList = 1;               // 撤销和恢复后，剩余 message ID list
  uint32 history_index = 2;                       //历史数据index
  uint32 is_reverse = 3;                          //恢复撤销是否翻转
}
enum WXWBPointAction{
    WXWBPointActionStart    = 0;                // 开始点
    WXWBPointActionMove     = 1;                // 移动中的点
    WXWBPointActionEnd      = 2;                // 结束点
}
enum WXWBLineType{
    WXWBLineTypeSolid  = 0;                     //直线
    WXWBLineTypeDashed = 1;                     //点划线
    WXWBLineTypeDotted = 2;                     //虚线
    WXWBLineTypeDashed2 = 3;                    //点划线2
}
message WXWBPoint{
    WXWBPointAction action = 1;                 //点类型
    float x = 2;                                //坐标点
    float y = 3;
    float lineWidth = 4;			            //线宽 用于压感笔迹
    //培优字段开始
    string erase = 10;                          //当前点被擦除的橡皮 id
}
message WXWBOffset{
    float offsetX = 1;                          //水平移动
    float offsetY = 2;                          //垂直移动
}

message WXWBChooseShapeOffset {
  float startOffsetX = 1;                       // 开始点x的偏移量
  float startOffsetY = 2;                       // 开始点y的偏移量
  float endOffsetX = 3;                         // 结束点x的偏移量
  float endOffsetY = 4;                         // 结束点y的偏移量
}

message WXWBCanvasInfo{
    uint32 canvasId = 1;                        //画板id
    float x = 2;                                //坐标点
    float y = 3;
    float width = 4;                            //宽高
    float height = 5;
    float lineWidth = 6;                        //边框线宽
    uint32 strokeColor = 7;                     //边框线条颜色 带透明度的 0x00ffffff
    uint32 fillColor = 8;                       //填充颜色 带透明度的 0x00ffffff
}
message WXWBUserInfo{
    string uname = 1;                           // 用户对外展示姓名
}
message WXWBRefConfig{
    string refId = 1;
    //增加字段
    string imgUrl = 2;
    string text = 3;                            // 文本
    int32 fontSize = 4;                         // 文本字体大小
    string fontFamily = 5;                      // 文本字体名
    uint32 color = 6;                           // 颜色 带透明度的 0x00ffffff
    int32 lineHeight = 7;                       // 行高
    int32 textAlign = 9;                        // 文本对齐方式(0:左对齐-默认 1:居中 2:右对齐)
    int32 defaultWidth = 10;                    // 文本框原始宽度(超出自动换行)
    int32 defaultHeight = 11;                   // 文本框原始高度
}
message WXWBSelectAreaConfig { //框选
  repeated uint64 lineIndexs = 1; //选中lineIndex集合 删除使用
  repeated uint64 shapeIndexs = 2; //选中shapeIndexs集合 删除使用
  //套索字段
  map<uint64, WXWBOffset> lineOffsets = 5; //选中涂鸦移动位置
  map<uint64, WXWBChooseShapeOffset> layerLocs = 6; //选中形状移动位置（开始点和结束点的中间位置）
  WXWBOffset lassoOffset = 7;              //当前套索的layer的偏移量


}

message WXWBTCPPacketData{
    //sdk内部数据
    WXWBMessageType type = 1;                   //命令类型
    WXTPointType pointType = 2;                 //点位类型
    string courseId = 3;                        //当前课件id
    string pageId = 4;                          //当前页id 用来找db的
    uint64 lineIndex = 5;                        //课件页内唯一id（标记动作，或者一笔、一个图形）
    repeated WXWBPoint pointList = 7;           //控制点集合
    int32 width = 8;                            //画布宽度
    int32 height = 9;                           //画布高度
    uint64 msgId = 11;                           //消息id，严格按照页面递增。用于校验是否丢包或增量同步。
    float lineWidth = 12;                       //线宽
    uint32 strokeColor = 13;                     //线条颜色 带透明度的 0x00ffffff
    uint64 dataCreateTimestamp = 14;            // 涂鸦数据产生的时间戳
    float rotation = 15;                        //旋转弧度
    uint32 fillColor = 16;                      //填充颜色 带透明度的 0x00ffffff
    WXWBLineType lineType = 17;                 //线形

    //培优数据开始
    WXWBPageConfig pageConfig = 18;             // 整体页信息，当前页 id，全部页 id
    WXWBRevokeAndRecoverConfig revokeAndRecoverConfig = 19;
    WXWBChooseConfig chooseConfig = 20;         // 选择数据
    WXWBRulerConfig rulerConfig = 21;           // 直尺配置数据
    WXWBCompassesConfig compassesConfig = 22;   // 圆规配置数据
    WXWBRefConfig  refConfig = 23;              // 外部数据配置
    WXWBUserInfo userInfo = 27;                 // 用户扩展信息

    //新增
    string uid = 30;                            // 用户id
    WXWBCanvasInfo canvasInfo = 31;             //画布信息
    WXWBSelectAreaConfig selectConfig = 32;	    //框选信息
    WXWBPoint cursor_position = 33;       //光标位置
    google.protobuf.Any myBusiness = 1000;      //业务方数据配置
    uint32 businessType = 1001;                 //业务方数据类型
}
