<template>
  <!-- 涂鸦 -->
  <div>
    <div class="nwbBoard">
      <nwb-board ref="nwbRef"></nwb-board>
    </div>
    <!-- 展示画笔 -->
    <div class="authorize-pen" v-if="isAuthorized">
      <img
        v-for="item in penTools"
        :key="item.key"
        :src="item.icon"
        class="eraser"
        @click="selectTool(item.key)"
        :class="`${item.key} ${selectedKey === item.key ? 'select-item' : ''}`"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, watch } from 'vue'
import nwbBoard from './nwbBoard/index.vue'
import yellowPen from './imgs/yellow-pen.png'
import redPen from './imgs/red-pen.png'
import eraser from './imgs/eraser.png'
import { useClassData } from '@/stores/classroom'
import { emitter } from '@/hooks/useEventBus'
import { whiteBoardLog } from '@/utils/web-log/HWLogDefine'

const store = useClassData()
const baseData = computed(() => store.baseData)
const isAuthorized = ref(false) // 是否被授权
const selectedKey = ref('red-pen') // 当前选中的工具
const nwbRef = ref(null)
const currentThickness = ref(4) // 初始化笔迹大小
const currentLineDashed = ref(false) // 是否带有笔锋
const eraserSize = ref(50) // 橡皮大小
const penTools = ref([
  {
    key: 'yellow-pen',
    icon: yellowPen
  },
  {
    key: 'red-pen',
    icon: redPen
  },
  {
    key: 'eraser',
    icon: eraser
  }
])

whiteBoardLog.info('涂鸦WhiteBoard加载')

const selectTool = type => {
  selectedKey.value = type
  if (type === 'yellow-pen' || type === 'red-pen') {
    if (nwbRef.value) {
      emitter.emit('setStroke', {
        color: type === 'yellow-pen' ? '#FFD82D' : '#FF503F',
        thickness: currentThickness.value, // 笔记大小
        lineDashed: currentLineDashed.value // 是否带笔锋
      })
      emitter.emit('setMode', 'free-drawing')
      whiteBoardLog.info(`授权涂鸦选择画笔 ${type}`)
    } else {
      whiteBoardLog.error('授权涂鸦选择画笔失败，未获得涂鸦元素WhiteBoard')
    }
  } else {
    if (nwbRef.value) {
      emitter.emit('setEraserWidth', eraserSize.value)
      emitter.emit('setMode', 'eraser') // 切换到橡皮模式

      whiteBoardLog.info(`授权涂鸦选择橡皮，大小${eraserSize.value}`)
    } else {
      whiteBoardLog.info('授权涂鸦选择橡皮失败，未获得涂鸦元素WhiteBoard')
    }
  }
}

watch(
  () => isAuthorized.value,
  newVal => {
    if (newVal) {
      // 授权成功
      selectTool('yellow-pen') // 设置面板默认选中第一支颜色笔
    } else {
      emitter.emit('setMode', 'none')
    }
  }
)
emitter.on('setIsAuthorizedStatus', noticeContent => {
  setIsAuthorizedStatus(noticeContent)
})
const setIsAuthorizedStatus = noticeContent => {
  whiteBoardLog.info('授权涂鸦', noticeContent)
  try {
    const userId = baseData.value.stuInfo.id
    const userInfo = noticeContent.data.find(item => item.userId === userId)
    whiteBoardLog.info('授权涂鸦userInfo', userInfo)
    if (userInfo && userInfo.isAuthorize === 1) {
      isAuthorized.value = true
    } else {
      isAuthorized.value = false
    }
  } catch (err) {
    whiteBoardLog.error(`JSON.parse异常捕获: ${err}`, 'error')
  }
}
onBeforeUnmount(() => {
  emitter.off('setIsAuthorizedStatus')
})
</script>

<style lang="scss" scoped>
.nwbBoard {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  top: 0;
  left: 0;
  z-index: 10;
}
.classWhiteBoard {
  .authorize-pen {
    position: fixed;
    bottom: 0;
    left: calc(50% - 104px);
    width: 208px;
    height: 60px;
    background: #1a1a1a;
    border-radius: 100px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    z-index: 999;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: auto;
    img {
      width: 25px;
      height: 50px;
      bottom: -22px;
      position: relative;
      cursor: pointer;
    }

    img:nth-child(2) {
      margin: 0 30px;
    }

    .select-item {
      bottom: -6px;
    }
  }
}
.container {
  z-index: 66; // 涂鸦z-index
}
</style>
