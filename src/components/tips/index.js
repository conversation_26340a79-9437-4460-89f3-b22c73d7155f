import { createApp, h } from 'vue'
import TipsOptions from './main.vue'

let tipsInstance = null
let tipsApp = null

/**
 * Tips factory
 *
 * @param {Object} props
 * @return {Object} - 组件实例
 */
const Tips = ({
  msg = '',
  duration = 3000,
  callback = null,
  parentNode = document.getElementById('collectiveSpeechContainer')
}) => {
  // 如果已存在实例，先移除
  if (tipsApp) {
    tipsApp.unmount()
    tipsInstance = null
    tipsApp = null
  }

  // 创建包装器 div 用于挂载组件
  const div = document.createElement('div')
  parentNode.appendChild(div)

  // 创建组件实例
  tipsApp = createApp({
    render() {
      return h(TipsOptions, {
        msg,
        duration,
        callback,
        ref: 'tipsRef'
      })
    }
  })

  // 挂载组件
  const vm = tipsApp.mount(div)
  tipsInstance = vm.$refs.tipsRef

  // 显示提示
  tipsInstance.showTips()

  return tipsInstance
}

// 关闭方法
Tips.close = () => {
  if (tipsInstance && tipsInstance.visible) {
    tipsInstance.closeTips()
  }

  if (tipsApp) {
    tipsApp.unmount()
    tipsInstance = null
    tipsApp = null
  }
}

// 快捷调用方法
Tips.tips = (msg = '', duration = 3000, callback = null) => {
  return Tips({
    msg,
    duration,
    callback
  })
}

// 导出组件
Tips.component = TipsOptions

export default Tips
