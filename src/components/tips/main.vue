<template>
  <transition @after-leave="handleAfterLeave">
    <div class="ne-tips" :id="'toast_' + uuid" v-show="visible">
      <div class="ne-tips__content">
        <p class="ne-tips__content--text" v-if="msg" v-text="msg"></p>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'NeTips',
  data() {
    return {
      uuid: Math.random()
        .toString(36)
        .substring(3, 20)
    }
  },
  props: {
    msg: {
      type: [String, Number],
      default: ''
    },
    duration: {
      type: Number,
      default: 3000
    },
    callback: Function,
    visible: Boolean
  },
  beforeUnmount() {
    if (this.$_timer) {
      clearTimeout(this.$_timer)
    }
    if (this.visible) {
      this.closeTips()
    }
  },
  methods: {
    handleAfterLeave() {
      this.$destroy(true)
      this.$el?.parentNode?.removeChild(this.$el)
      typeof this.callback == 'function' && this.callback()
    },
    fire() {
      if (this.$_timer) {
        clearTimeout(this.$_timer)
      }
      if (this.visible && this.duration > 0) {
        this.$_timer = setTimeout(() => {
          this.closeTips()
        }, this.duration)
      }
    },
    showTips() {
      this.visible = true
      this.fire()
    },
    closeTips() {
      this.visible = false
      this.handleAfterLeave()
    }
  }
}
</script>

<style lang="scss" scoped>
.ne-tips {
  position: absolute;
  z-index: 1000;
  min-width: 110px;
  min-height: 3.6em;
  max-width: 80%;
  box-sizing: border-box;
  border-radius: 16px;
  overflow: hidden;
  backface-visibility: hidden;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, -90px);
  background: rgba(#000, 0.8);
  font-weight: 500;
  color: #fff;
  pointer-events: none;
  text-align: left;
  animation: show-tips 0.5s;
}
.ne-tips__content {
  padding: 10px 15px;
}
.ne-tips__content--text {
  padding: 5px 0 0 0;
  margin: 0;
  font-size: 16px;
  line-height: 20px;
  font-family: Helvetica, PingFang SC, 'sans-serif', Arial, Verdana, Microsoft YaHei;
}
@keyframes show-tips {
  from {
    opacity: 0;
    transform: translate(-50%, -100px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -90px);
  }
}
</style>
