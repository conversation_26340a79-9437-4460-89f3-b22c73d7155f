<template>
  <div class="emoticon-container">
    <EmoticonMsg
      v-if="show && type == 1"
      :name="emoticonName"
      :type="type"
      :width="width"
      :height="height"
    />
    <LottieEmoticon
      v-if="show && type == 2"
      :name="emoticonName"
      :type="type"
      :lottieUrl="lottieUrl"
      :emojiId="emojiId"
      :width="width"
      :height="height"
      @animationComplete="animationComplete"
      :loopLottie="loopLottie"
    />
    <PictureEmoticon
      v-if="show && type == 3"
      :name="emoticonName"
      :type="type"
      :lottieUrl="lottieUrl"
      :emojiId="emojiId"
      :width="width"
      :height="height"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onBeforeUnmount } from 'vue'
import EmoticonMsg from '@/components/EmoticonMessage/components/Emoticon/index.vue'
import LottieEmoticon from '@/components/EmoticonMessage/components/LottieEmoticon/index.vue'
import PictureEmoticon from '@/components/EmoticonMessage/components/PictureEmoticon/index.vue'
defineOptions({
  name: 'EmoticonMessage',
})
// 定义 props
const props = defineProps({
  userId: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  type: {
    type: Number,
    default: 1,
  },
  loopLottie: {
    type: Boolean,
    default: false,
  },
  width: {
    type: Number,
    default: 50,
  },
  height: {
    type: Number,
    default: 50,
  },
  duration: {
    type: Number,
    default: 3,
  },
  lottieUrl: {
    type: String,
    default: '',
  },
  emojiId: {
    type: Number,
    default: 0,
  },
  willAutoClear: {
    type: Boolean,
    default: false,
  },
})

// 定义 emit
const emit = defineEmits(['clearEmoticon'])

// 数据
const emoticonName = ref('')
const timer = ref(null)

// 计算属性
const show = computed(() => {
  return !!emoticonName.value
})

// 方法
const animationComplete = () => {
  if (props.willAutoClear) {
    emoticonName.value = ''
    emit('clearEmoticon', props.userId)
  }
}

const clearTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value)
    timer.value = null
  }
}

// 监听
watch(
  () => props.name,
  (newVal) => {
    if (!newVal) {
      return
    }
    emoticonName.value = newVal
    clearTimer()
    // 处理图片类型表情 图片表情需要 根据传入时间自动消失
    if (props.type === 1 || props.type === 3) {
      timer.value = setTimeout(() => {
        if (props.willAutoClear) {
          emoticonName.value = ''
          emit('clearEmoticon', props.userId)
        }
      }, props.duration * 1000)
    }
  },
  { immediate: true },
)

// 组件销毁前清除定时器
onBeforeUnmount(() => {
  clearTimer()
})
</script>

<style lang="scss" scoped>
.emoticon-container {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
