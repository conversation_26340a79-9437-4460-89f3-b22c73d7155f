<template>
  <div class="lottie-emoticon-icon">
    <lottie-player
      ref="emoji"
      mode="normal"
      autoplay
      :loop="loopLottie"
      @complete="onLottieComplete"
      :src="lottieUrl"
      :style="emoticonStyle"
    >
    </lottie-player>
  </div>
</template>

<script setup>
import { computed, ref, watch, nextTick } from 'vue'
import "@lottiefiles/lottie-player";
defineOptions({
  name: 'LottieEmoticon',
})
// 定义 props
const props = defineProps({
  // 表情类型
  // 2: lottie表情
  type: {
    type: Number,
    default: 2,
  },
  // 表情宽度
  width: {
    type: Number,
    default: 70,
  },
  // 表情高度
  height: {
    type: Number,
    default: 70,
  },
  lottieUrl: {
    type: String,
    default: '',
  },
  loopLottie: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
const emit = defineEmits(['animationComplete'])

// 引用
const emoji = ref(null)

// 计算属性
const emoticonStyle = computed(() => {
  return {
    width: `${props.width}px`,
    height: `${props.height}px`,
  }
})

// 方法
const onLottieComplete = () => {
  emit('animationComplete')
}

// 监听属性变化
watch(
  () => props.lottieUrl,
  (url) => {
    if (url) {
      nextTick(() => emoji.value.load(url))
    }
  },
)
</script>

<style lang="scss" scoped>
.lottie-emoticon-icon {
  display: inline-block;
}
</style>
