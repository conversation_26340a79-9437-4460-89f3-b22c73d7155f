<template>
  <img
    class="emoticon-icon"
    @mouseover="handleMouseover"
    @mouseleave="handleMouseleave"
    @click.stop="handleClick"
    :src="lottieUrl"
    :style="hoverStatus ? emotionHoverStyle : emoticonStyle"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
defineOptions({
  name: 'PictureEmoticon',
})
// 定义 props
const props = defineProps({
  // 表情类型
  // 1: 本地图片表情
  type: {
    type: Number,
    default: 3,
  },
  // 表情符号名称
  // 例: [smile]
  name: {
    type: String,
    default: '',
  },
  // 表情宽度
  width: {
    type: Number,
    default: 30,
  },
  // 表情高度
  height: {
    type: Number,
    default: 30,
  },
  // hover宽度
  hoverWidth: {
    type: Number,
    default: 34,
  },
  // hover高度
  hoverHeight: {
    type: Number,
    default: 34,
  },
  // 是否开启hover效果
  enableHover: {
    type: Boolean,
    default: false,
  },
  lottieUrl: {
    type: String,
    default: '',
  },
})

// 定义 emits
const emit = defineEmits(['handleClick'])

// 响应式状态
const hoverStatus = ref(false)

// 计算属性
const emoticonStyle = computed(() => {
  return {
    width: `${props.width}px`,
    height: `${props.height}px`,
  }
})

const emotionHoverStyle = computed(() => {
  return {
    width: `${props.hoverWidth}px`,
    height: `${props.hoverHeight}px`,
  }
})

// 方法
const handleMouseover = () => {
  if (!props.enableHover) return
  hoverStatus.value = true
}

const handleMouseleave = () => {
  if (!props.enableHover) return
  hoverStatus.value = false
}

const handleClick = () => {
  emit('handleClick', {
    type: props.type,
    name: props.name,
  })
}
</script>

<style lang="scss" scoped>
.emoticon-icon {
  margin-bottom: 4px;
}
</style>
