<template>
  <div
    class="emoticon-icon"
    :style="hoverStatus ? emotionHoverStyle : emoticonStyle"
    :class="[emoticonClass]"
    @mouseover="handleMouseover"
    @mouseleave="handleMouseleave"
    @click.stop="handleClick"
  ></div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { emoticonConfig } from './config'
defineOptions({
  name: 'EmoticonMsg',
})
// 定义 props
const props = defineProps({
  // 表情类型
  // 1: 本地图片表情
  type: {
    type: Number,
    default: 1,
  },
  // 表情符号名称
  // 例: [smile]
  name: {
    type: String,
    default: '',
  },
  // 表情宽度
  width: {
    type: Number,
    default: 30,
  },
  // 表情高度
  height: {
    type: Number,
    default: 30,
  },
  // hover宽度
  hoverWidth: {
    type: Number,
    default: 34,
  },
  // hover高度
  hoverHeight: {
    type: Number,
    default: 34,
  },
  // 是否开启hover效果
  enableHover: {
    type: Boolean,
    default: false,
  },
})

// 定义 emits
const emit = defineEmits(['handleClick'])

// 响应式状态
const hoverStatus = ref(false)

// 计算属性
const emoticonInfo = computed(() => {
  return emoticonConfig[props.name] || {}
})

const emoticonStyle = computed(() => {
  // @log-ignore
  return {
    width: `${props.width}px`, // 将 rem 转换为 px
    height: `${props.height}px`, // 将 rem 转换为 px
  }
})

const emotionHoverStyle = computed(() => {
  return {
    width: `${props.hoverWidth}px`, // 将 rem 转换为 px
    height: `${props.hoverHeight}px`, // 将 rem 转换为 px
  }
})

const emoticonClass = computed(() => {
  if (!emoticonInfo.value.localImageName) {
    return ''
  }
  // 本地图片表情类型
  if (props.type == 1) {
    return `emoticon-icon__${emoticonInfo.value.localImageName}`
  }
  return ''
})

// 方法
const handleMouseover = () => {
  if (!props.enableHover) return
  hoverStatus.value = true
}

const handleMouseleave = () => {
  if (!props.enableHover) return
  hoverStatus.value = false
}

const handleClick = () => {
  emit('handleClick', {
    type: props.type,
    name: props.name,
  })
}
</script>

<style lang="scss" scoped>
.emoticon-icon {
  width: 30px;
  height: 30px;
  background-size: cover;
  transition: all 0.2s;
  cursor: pointer;

  &__e_smile {
    background-image: url('./images/e_smile.png');
  }

  &__e_pleading {
    background-image: url('./images/e_pleading.png');
  }

  &__e_xd {
    background-image: url('./images/e_xd.png');
  }

  &__e_lol {
    background-image: url('./images/e_lol.png');
  }

  &__e_thinking {
    background-image: url('./images/e_thinking.png');
  }

  &__e_the_rock {
    background-image: url('./images/e_the_rock.png');
  }

  &__e_party {
    background-image: url('./images/e_party.png');
  }

  &__e_quiet {
    background-image: url('./images/e_quiet.png');
  }

  &__e_sad {
    background-image: url('./images/e_sad.png');
  }

  &__e_shame {
    background-image: url('./images/e_shame.png');
  }

  &__e_scream {
    background-image: url('./images/e_scream.png');
  }

  &__e_cry {
    background-image: url('./images/e_cry.png');
  }

  &__e_a {
    background-image: url('./images/e_a.png');
  }

  &__e_b {
    background-image: url('./images/e_b.png');
  }

  &__e_true {
    background-image: url('./images/e_true.png');
  }

  &__e_false {
    background-image: url('./images/e_false.png');
  }

  &__e_surprise {
    background-image: url('./images/e_surprise.png');
  }

  &__e_heart {
    background-image: url('./images/e_heart.png');
  }

  &__e_unicorn {
    background-image: url('./images/e_unicorn.png');
  }

  &__e_ghost {
    background-image: url('./images/e_ghost.png');
  }

  &__e_yeah {
    background-image: url('./images/e_yeah.png');
  }

  &__e_like {
    background-image: url('./images/e_like.png');
  }

  &__e_hands_up {
    background-image: url('./images/e_hands_up.png');
  }

  &__e_clap {
    background-image: url('./images/e_clap.png');
  }
}
</style>
