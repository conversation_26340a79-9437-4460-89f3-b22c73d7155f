<template>
  <div class="countDownBox" ref="draggableElement" :style="customStyle">
    <img class="countDownIcon" src="./countDownIcon.png" alt="" />
    <template v-for="(item, index) in countdown">
      <div class="numBox" v-if="index != 2" :key="'numBox-' + index">{{ item }}</div>
      <div class="colon" v-else :key="'colon-' + index">:</div>
    </template>
    <audio :src="popMp3" ref="countDownSound" class="hiden"></audio>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import popMp3 from './audio/pop.mp3'
// 定义 props
const props = defineProps({
  time: {
    type: Number,
    required: true,
    default: 0
  },
  format: {
    type: String,
    default: 'mm:ss'
  }
})

// 设置拖拽
const draggableElement = ref(null)
const positionX = ref(16)
const positionY = ref(1)

// 自定义样式对象
const customStyle = computed(() => {
  return {
    left: `${positionX.value}px`,
    bottom: `${positionY.value}px` // 使用 bottom 而不是 top
  }
})

// 自定义拖拽实现
function setupDrag() {
  if (!draggableElement.value) return

  let startX = 0
  let startY = 0
  let startPosX = 0
  let startPosY = 0
  let isDragging = false

  const onMouseDown = e => {
    isDragging = true
    startX = e.clientX || e.touches?.[0]?.clientX || 0
    startY = e.clientY || e.touches?.[0]?.clientY || 0
    startPosX = positionX.value
    startPosY = positionY.value

    // 防止选中文本
    e.preventDefault()

    // 设置样式
    draggableElement.value.style.transition = 'none'
    draggableElement.value.style.cursor = 'grabbing'

    // 添加全局事件
    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
    document.addEventListener('touchmove', onMouseMove, { passive: false })
    document.addEventListener('touchend', onMouseUp)
  }

  const onMouseMove = e => {
    if (!isDragging) return

    const x = e.clientX || e.touches?.[0]?.clientX || 0
    const y = e.clientY || e.touches?.[0]?.clientY || 0

    // 计算位移
    const deltaX = x - startX
    const deltaY = startY - y // 反转Y方向，因为我们用bottom定位

    // 更新位置
    positionX.value = startPosX + deltaX
    positionY.value = startPosY + deltaY

    // 限制拖拽范围不能出现负值
    if (positionX.value < 0) positionX.value = 0
    if (positionY.value < 0) positionY.value = 0
    // 限制拖拽范围不能超过视图宽度和高度
    const container = document.querySelector('#interactionController')
    if (container) {
      const containerWidth = container.clientWidth
      const containerHeight = container.clientHeight
      if (positionX.value > containerWidth - draggableElement.value.clientWidth) {
        positionX.value = containerWidth - draggableElement.value.clientWidth
      }
      if (positionY.value > containerHeight - draggableElement.value.clientHeight) {
        positionY.value = containerHeight - draggableElement.value.clientHeight
      }
    }

    // 防止滚动
    e.preventDefault()
  }

  const onMouseUp = () => {
    isDragging = false

    // 恢复样式
    if (draggableElement.value) {
      draggableElement.value.style.cursor = 'move'
      draggableElement.value.style.transition = 'transform 0.2s'
    }

    // 移除全局事件
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
    document.removeEventListener('touchmove', onMouseMove)
    document.removeEventListener('touchend', onMouseUp)
  }

  // 添加基础事件监听
  draggableElement.value.addEventListener('mousedown', onMouseDown)
  draggableElement.value.addEventListener('touchstart', onMouseDown, { passive: false })

  // 返回一个清理函数
  return () => {
    if (!draggableElement.value) return

    draggableElement.value.removeEventListener('mousedown', onMouseDown)
    draggableElement.value.removeEventListener('touchstart', onMouseDown)
    document.removeEventListener('mousemove', onMouseMove)
    document.removeEventListener('mouseup', onMouseUp)
    document.removeEventListener('touchmove', onMouseMove)
    document.removeEventListener('touchend', onMouseUp)
  }
}

// 定义 emits
const emit = defineEmits(['complete'])

// 响应式状态
const remainingTime = ref(Math.ceil(props.time / 1000) >= 0 ? Math.ceil(props.time / 1000) : 0)
const countDownSound = ref(null)
let timer = null

// 计算属性
const countdown = computed(() => {
  const minutes = Math.floor(remainingTime.value / 60)
  const seconds = remainingTime.value % 60
  let format = props.format
  format = format.replace('mm', minutes.toString().padStart(2, '0'))
  format = format.replace('ss', seconds.toString().padStart(2, '0'))
  return format
})

// 生命周期钩子
onMounted(() => {
  console.log('this.leftTime 123', props.time)

  // 初始化拖拽功能
  cleanupDrag = setupDrag()

  // 确保倒计时组件初始位置正确
  if (draggableElement.value) {
    const container = document.querySelector('#interactionController')
    if (container) {
      // 初始位置已通过 positionX 和 positionY 设置
    }
  }

  timer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    } else {
      remainingTime.value = 0
      countDownSound.value.play().catch(err => {
        console.error('播放音频失败:', err)
      })

      clearInterval(timer)
      emit('complete')
    }
  }, 1000)
})

// 存储清理函数
let cleanupDrag = null

onBeforeUnmount(() => {
  clearInterval(timer)
  // 清理拖拽相关事件监听器
  if (cleanupDrag) cleanupDrag()
})
</script>

<style lang="scss" scoped>
.countDownBox {
  background: rgba(15, 25, 42, 0.7);
  border-radius: 8px;
  backdrop-filter: blur(4px);
  display: flex;
  padding: 5px 10px;
  align-items: center;
  position: absolute;
  cursor: move;
  user-select: none; /* 防止文本选择干扰拖动 */
  touch-action: none; /* 防止在触摸设备上滚动 */
  z-index: 100; /* 确保在其他元素上面 */

  .countDownIcon {
    width: 26px;
    height: 30px;
    margin-right: 6px;
  }

  .numBox {
    width: 30px;
    height: 30px;
    background: rgba(147, 157, 175, 0.3);
    border-radius: 4px;
    font-size: 26px;
    font-family: SFProRounded-Bold, SFProRounded;
    font-weight: bold;
    color: #ffffff;
    line-height: 28px;
    margin-right: 2px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .colon {
    font-size: 26px;
    font-family: SFProRounded-Bold, SFProRounded;
    font-weight: bold;
    color: #ffffff;
    margin-right: 4px;
    margin-left: 2px;
  }
}
</style>
