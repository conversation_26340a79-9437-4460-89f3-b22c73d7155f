<template>
  <div class="homework">
    <template v-if="homeworkList.length > 0">
      <div class="homework-content">
        <div
          v-for="(item, index) in homeworkList"
          :key="index"
          class="homework-card"
        >
          <div :class="['name', getColorClass(item)]">{{
            item.taskType === 2 ? exerciseTypeMap[0] : homeworkTypeMap[item.type]
          }}</div>
          <div class="homework-body">
            <div class="homework-info">
              <!-- <img
                class="icon"
                :src="item.taskType === 2 ? imgExerciseMap[0] : imgSrcMap[item.type]"
              /> -->
              <div class="homework-text">
               {{ item.title }}
              </div>
              <div v-if="item.taskType === 2" class="tips">
                <!-- <a-icon
                  class="time"
                  :component="item.status === 0 ? timeReadyIcon : timeTowardIcon"
                  v-if="true"
                /> -->
                {{ exerciseStatusMap[item.status] }}
              </div>
              <div v-else class="tips" :class="{ 'color-red': isRemindColor(item) }">
                <i v-if="isShowIcon" class="iconfont icon-shijian time"></i>
                {{ tipsText(item) }}
              </div>
            </div>
            <div :class="['homework-view', getCommonButton(item)['buttonClassName'] || '']" @click="handleJump(item)">{{ getCommonButton(item)['buttonName'] }}</div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="empty">
        <img class="icon-empty" src="@/assets/images/home/<USER>" />
        <span class="empty-tips">- {{ $t('today.homework.noHomeworkTip') }} -</span>
      </div>
    </template>
  </div>
</template>

<script>
import { queryHomeWorkUrl } from '@/api/home/<USER>'
// import logger from 'utils/logger'
// import { todaySensorEvent } from '@/utils/sensorEvent'

export default {
  name: 'HomeworkList',
  props: {
    homeworkList: {
      default: () => [],
      type: Array
    }
  },
  computed: {
    // local() {
    //   return this.$store.state.common.local
    // },
    isRemindColor() {
      return function(item) {
        return item.remainDays < 1 && this.getStatus(item) === this.publishType
      }
    }
  },
  data() {
    return {
      exerciseStatusMap: {
        0: this.$t('today.homework.exercise_not_start'),
        1: this.$t('today.homework.exercise_in_progress')
      },
      exerciseTypeMap: {
        0: this.$t('today.homework.exercise')
      },
      homeworkTypeMap: {
        0: this.$t('today.homework.homework'),
        1: this.$t('today.homework.preview'),
        2: this.$t('today.homework.exam'),
        3: this.$t('today.homework.exam'),
        4: this.$t('today.homework.exam')
      },
      // imgExerciseMap: {
      //   0: require('@/assets/images/today/icon_exercise.png')
      // },
      // imgSrcMap: {
      //   0: require('@/assets/images/today/icon_homework.png'),
      //   1: require('@/assets/images/today/icon_preview.png'),
      //   2: require('@/assets/images/today/icon_exam.png'),
      //   3: require('@/assets/images/today/icon_exam.png'),
      //   4: require('@/assets/images/today/icon_exam.png')
      // },
      isPublished: false,
      isReviewed: false,
      isSubmitted: false,
      backUrl: '/home/<USER>',
      homeWorkTypes: [0],
      previewTypes: [1],
      examTypes: [2, 3, 4],
      publishType: 1,
      reviewedType: 2,
      submittedType: 3
    }
  },
  methods: {
    getColorClass(item) {
       const typeMap = {
        0: 'orange',
        1: 'green',
        2: 'red',
        3: 'red',
        4: 'red',
       }
      return item.taskType === 2 ? 'blue' : typeMap[item.type]
    },
    getStatus(item) {
      // @log-ignore
      const type = item.type
      const status = item.status
      if (status === 4) {
        return this.reviewedType
      }
      const homeworkOrPreivew =
        this.homeWorkTypes.includes(type) || this.previewTypes.includes(type)
      const exam = this.examTypes.includes(type)

      if ((homeworkOrPreivew && status === 3) || (exam && status === 2)) {
        return this.publishType
      }

      if ((homeworkOrPreivew && status === 5) || (exam && status === 3)) {
        return this.submittedType
      }
    },
    isShowIcon(item) {
      // @log-ignore
      const status = this.getStatus(item)
      return status === this.publishType && item.remainDays <= 30
    },
    tipsText(item) {
      // @log-ignore
      const status = this.getStatus(item)
      // console.log(this.local)
      if (status === this.publishType) {
        let endTime = this.$t('today.homework.endTimeTips', {
          remainTime: item.remainDays
        })

        if (item.remainDays < 1) {
          endTime = this.$t('today.homework.unSubmitTips', {
            // todo endTime 时间戳, endTimestamp 应该也不用改 原来是 05/31 12:00
            submitTime: item.endTime
          })
        } else if (item.remainDays > 30) {
          endTime = this.$t('today.homework.unSubmit')
        }

        return endTime
      } else if (status === this.reviewedType) {
        return this.$t('today.homework.reviewed')
      } else if (status === this.submittedType) {
        return this.$t('today.homework.submitted')
      }
    },
    handleJump(item) {
      if (item.taskType === 2) {
        // 巩固练习
        this.handleExercise(item)
      } else if (this.homeWorkTypes.includes(item.type)) {
        this.handleAssignmentJump(item)
      } else if (this.previewTypes.includes(item.type)) {
        this.handlePreviewButton(item)
      } else if (this.examTypes.includes(item.type)) {
        this.handleClassExam(item)
      } else {
        // 本次这里不展示报告的
        this.jumpToLessonReport(item)
      }
      this.setSensorsData(item)
    },
    //跳转巩固练习
    handleExercise(data) {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          assignmentUrl: data.url,
          fromTo: this.$route.path
        }
      })
    },
    //跳转课前测页面
    handlePreviewButton(data) {
      const paperJumpUrl = '/home/<USER>'
      const newParmas = window.JSON.stringify(data.jumpParams)
      window.localStorage.setItem('paperJumpUrlParam', newParmas)
      this.$router.push({
        path: paperJumpUrl,
        query: {
          classId: data.classId,
          lessonId: data.jumpParams.planId,
          planId: data.jumpParams.planId,
          homeworkId: data.jumpParams.homeworkId || '',
          fromTo: this.$route.fullPath,
          status: data ? data.status : 6
        }
      })
    },
    //跳转考试页面
    handleClassExam(data) {
      this.$router.push({
        path: '/home/<USER>',
        query: {
          ...data.jumpParams,
          fromTo: this.backUrl
        }
      })
    },
    // 作业跳转
    handleAssignmentJump(item) {
      const paperJumpUrl = '/home/<USER>'
      const sourceType = item.sourceType //0 作业后台上传，1 内容云

      if (sourceType == 0) {
        this.handleOldAssignmentButton(item)
      } else if (sourceType === 1) {
        this.handleNewAssignmentButton(paperJumpUrl, item)
      }
    },
    handleOldAssignmentButton(data) {
      const assignmentUrl = data.assignment.url
      if (assignmentUrl) {
        this.$router.push({
          path: `/home/<USER>
            this.$route.fullPath
          }`,
          query: {
            assignmentUrl: encodeURIComponent(assignmentUrl)
          }
        })
      }
    },
    // 跳转到作业页面
    handleNewAssignmentButton(paperJumpUrl, data) {
      const { planId, classId, paperId, homeworkType } = data.jumpParams
      queryHomeWorkUrl(data.jumpParams).then(res => {
        if (res) {
          const assignmentUrl = res.data
          console.log('看看执行了么',  res.data, `${paperJumpUrl}=${encodeURIComponent(assignmentUrl)}&backUrl=${
                this.$route.fullPath
              }`)

          if (assignmentUrl) {
            this.$router.push({
              path: '/home/<USER>',
              // `${paperJumpUrl}=${encodeURIComponent(assignmentUrl)}&backUrl=${
              //   this.$route.fullPath
              // }`,
              query: {
                assignmentUrl: assignmentUrl, // 用 query 传
                planId,
                classId,
                homeworkStatus: homeworkType,
                paperId
              }
            })
          }
        }
      })
    },
    // 跳转到课情报告
    jumpToLessonReport(data) {
      const lessonReportUrl = data.report.url
      const canViewReport = data.report.canViewReport
      window.localStorage.setItem('lessonReportUrl', lessonReportUrl)
      window.localStorage.setItem('syllabusDatalist', this.$route.fullPath)
      if (canViewReport && lessonReportUrl) {
        this.$router.push({
          path: `/lessonReport?lessonReportUrl=${encodeURIComponent(lessonReportUrl)}&backUrl=${
            this.$route.fullPath
          }`,
          query: {
            planId: this.syllabusData.planId
          }
        })
      } else {
        // logger.send({
        //   tag: 'classBefore',
        //   level: 'error',
        //   content: {
        //     msg: '无法跳转到课后报告',
        //     canViewReport,
        //     lessonReportUrl
        //   }
        // })
      }
    },
    // todo 巩固练习的文案 =》以及按钮的状态
    // 作业按钮样式信息
    assignmentButton(item) {
      // 按钮状态 1: Hidden 2: Unpublished 3: Submit 4: Reviewed 5: Submitted 6: Finished
      const infoMap = {
        1: {
          buttonClassName: '',
          buttonName: ''
        },
        2: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[0]')
        },
        3: {
          buttonClassName: 'color-green',
          buttonName: this.$t('courses.detail.homework.status[1]')
        },
        4: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[2]')
        },
        5: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[3]')
        },
        6: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[4]')
        }
      }
      return infoMap[item.status]
    },
    // 课中考试报告按钮信息
    examReportButton(item) {
      // status:（1 未发布 2 已发布 3 已提交 4 已批改 5 已过期）
      const infoMap = {
        3: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.reportSubmitText')
        },
        4: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.reportBtnText')
        },
        5: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.reportExpiredText')
        }
      }
      const goTest = {
        buttonName: this.$t('courses.detail.startTest'),
        buttonClassName: 'color-green'
      }
      // 后端确认返回数据代表supportAfterAnswer为true
      const supportAfterAnswer = true
      const status = item.status
      const canViewReport = item?.report?.canViewReport
      const temp = canViewReport ? infoMap[status] : null
      if (supportAfterAnswer) {
        if ([3, 4].includes(status)) {
          return temp
        } else {
          // 考试是否过期用status=5 两种情况：不允许课后作答且课程结束了、 允许课后作答并且超过14天了
          return status === 5 ? temp : goTest
        }
      } else {
        return temp
      }
    },
    // 课前侧按钮样式信息
    previewQuestionButton(item) {
      // 按钮状态 1: Hidden 2: Unpublished 3: Submit 4: Reviewed 5: Submitted 6: Finished
      const infoMap = {
        1: {
          buttonClassName: '',
          buttonName: ''
        },
        2: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[0]')
        },
        3: {
          buttonClassName: 'color-green',
          buttonName: this.$t('courses.detail.homework.status[6]')
        },
        4: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[2]')
        },
        5: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[3]')
        },
        6: {
          buttonClassName: 'color-nobg',
          buttonName: this.$t('courses.detail.homework.status[4]')
        }
      }
      return infoMap[item.status]
    },
    // 阶段考试按钮样式信息
    examButton(item) {
      // examStatus: 1:未发布、2:已发布、3:已作答、4:已批改、5:已过期
      const examMap = {
        1: {
          buttonClassName: 'color-gray',
          buttonName: this.$t('courses.detail.exam.status[0]')
        },
        2: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[1]')
        },
        3: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[2]')
        },
        4: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[3]')
        },
        5: {
          buttonClassName: 'color-orange',
          buttonName: this.$t('courses.detail.exam.status[4]')
        }
      }
      return examMap[item.status]
    },
    getCommonButton(item) {
      // 巩固练习
      if (item.taskType === 2) {
        // todo 待产品确认文案等
        const practiceMap = {
          0: {
            buttonClassName: 'color-gray',
            buttonName: this.$t('courses.detail.exam.status[1]')
          },
          1: {
            buttonClassName: 'color-orange',
            buttonName: this.$t('courses.detail.exam.status[2]')
          },
        }
        return practiceMap[item.status]
      }
      switch (item.type) {
        // 课后作业
        case 0:
          return this.assignmentButton(item)
        // 课前测试
        case 1:
          return this.previewQuestionButton(item)
        // 课前考试
        case 2:
          return this.examReportButton(item)
        // 课中考试
        case 3:
          return this.examReportButton(item)
        // 课后考试即阶段考试
        case 4:
          return this.examButton(item)
        default:
          return null
      }
    },
    setSensorsData(item) {
      let homeworkType = -1
      switch (item.type) {
        case 0:
          homeworkType = 0
          break
        case 1:
          homeworkType = 1
          break
        case 2:
        case 3:
        case 4:
          homeworkType = 2
          break
      }
      if (item.taskType === 2) {
        homeworkType = 3
      }
      homeworkType
      // todaySensorEvent('osta_today_homework_item_click', {
      //   itme_name: item.title,
      //   itme_type: homeworkType
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
.homework {
  //flex-grow: 1;
  //border-radius: 16px;
  //background: #ffffff;

  .homework-content {
    overflow-y: overlay;
    .homework-card {
      position: relative;
      padding: 16px;
      //width: 426px;
      //height: 96px;
      margin-bottom: 12px;
      background: #ffffff;
      border-radius: 12px;
      border: 2px solid #f1f2f4;
      cursor: pointer;
      .name {
        position: absolute;
        top: 0;
        left: 0;
        border-radius: 10px 0px 10px 0px;
        padding: 0px 6px;
        font-size: 12px;
        line-height: 20px;
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
      }
      .name.green {
        background: #F2FCF6;
        color: #4EDA77;
      }
      .name.orange {
        color: #FF9F0A;
        background: #FFF5E6;
      }
      .name.red {
        background: #FFF5F5;
        color: #FF7676;
      }
      .name.blue {
        background: #F5F9FF;
        color: #82B2F9;
      }
      &:hover {
        //border: 2px solid #ffeecd;
      }
      .homework-body {
        margin-top: 2px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .tips {
        display: flex;
        align-items: center;
        margin: 12px 0 0 0px;
        font-size: 14px;
        font-family: Montserrat-Medium, Montserrat;
        font-weight: 500;
        color: #7A7A7A;
        line-height: 18px;
        .time {
          margin-left: 4px;
        }
      }
      .color-red {
        color: rgba(255, 82, 48, 0.8);
      }
      .homework-info {
        max-width: 680px;
        //display: flex;
        //justify-content: space-between;
        //align-items: center;
        .icon {
          width: 40px;
          height: 40px;
          margin-right: 12px;
        }
        .homework-text {
          //height: 40px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 20px;
          font-family: Montserrat-SemiBold, Montserrat;
          font-weight: 600;
          color: #222222;
          line-height: 24px;
        }
      }
      .homework-view {
        margin-left: 20px;
        padding: 0 13px;
        height: 32px;
        line-height: 32px;
        background: #F9F9F9;
        border-radius: 16px;
        font-size: 14px;
        font-family: Montserrat-SemiBold, Montserrat;
        font-weight: 600;
        color: #A6A6A6;
        cursor: pointer;
        min-width: 100px;
        text-align: center;
        box-sizing: border-box;
      }
      .homework-view-active {
        background: #FF9F0A;;
        color: #FFF;
      }
      .color-gray {
        background: #f4f6fa;
        border-color: #f4f6fa;
        color: #a2aab8;
      }

      .color-nobg {
        color: #A6A6A6;
        background: #F9F9F9;
        border-radius: 16px;
        //padding: 0 0 0 16px;
        // cursor: default;
      }

      .color-text {
        background: #ffffff;
        //border-color: #ffffff;
        color: #a2aab8;
        cursor: default;
      }

      .color-orange {
        //background: #fff3dc;
        //border-color: #fff3dc;
        //color: #ffaa0a;
        background: #FF9F0A;;
        color: #FFF;
      }
      .color-green {
        background: #f2fcf8;
        //border-color: #f2fcf8;
        color: #02ca8a;
      }
    }
  }
  .empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: calc(100% - 64px);
    .icon-empty {
      width: 100px;
      height: 84px;
    }
    .empty-tips {
      margin-top: 12px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(23, 43, 77, 0.5);
      line-height: 16px;
    }
  }
}
/* 自定义滚动条 */
.homework::-webkit-scrollbar {
  width: 4px; /* 设置水平滚动条的高度 */
}

.homework::-webkit-scrollbar-thumb {
  background-color: transparent; /* 滚动条的颜色 */
  border-radius: 3px; /* 圆角边框 */
}

.homework::-webkit-scrollbar-track {
  background: transparent; /* 滚动条轨道背景色设为透明 */
}
</style>
