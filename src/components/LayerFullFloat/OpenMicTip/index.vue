<template>
  <div class="open-mic-tip-box" v-if="isVisible">
    <img src="./img/openMic.png" alt="" />
    <span>{{ $t('classroom.smallClass.说出你的答案吧') }}</span>
  </div>
</template>

<script>
import { onMounted, onBeforeUnmount, defineComponent, getCurrentInstance, ref } from 'vue'
export default defineComponent({
  name: 'OpenMicTip',

  setup() {
    const { proxy } = getCurrentInstance()
    let timer = null
    const isVisible = ref(false)
    const closeTimer = () => {
      if (timer) {
        clearTimeout(timer)
        timer = null
      }
    }
    const openMicTipHandle = () => {
      closeTimer()
      isVisible.value = true
      timer = setTimeout(() => {
        isVisible.value = false
      }, 3000)
    }
    onMounted(() => {
      proxy.$bus.$on('open_mic_tip', openMicTipHandle)
    })
    onBeforeUnmount(() => {
      closeTimer()
      isVisible.value = false
      proxy.$bus.$off('open_mic_tip', openMicTipHandle)
    })
    return {
      isVisible
    }
  }
})
</script>
<style scoped lang="scss">
.open-mic-tip-box {
  position: fixed;
  bottom: 0;
  width: 473px;
  height: 80px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 8px 8px 0px 0px;
  backdrop-filter: blur(10px);
  left: 0;
  right: 0;
  margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 30px;
    height: 40px;
    margin-right: 20px;
  }
  span {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    line-height: 25px;
  }
}
</style>
