<template>
  <div class="upload-success-container" v-if="showDialog">
    <div class="container" :class="{ 'no-coins': !data.coins }">
      <div class="bg-img"></div>
      <div class="content" :class="{ 'no-coins': !data.coins }">
        <p class="title" :class="{ 'no-coins': !data.coins }">{{ data.tips }}</p>
        <div class="coins-container" v-if="data.coins">
          <div class="coins"></div>
          <span class="icon-number">+{{ data.coins }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { emitter } from '@/hooks/useEventBus'
const data = ref({})
const showDialog = ref(false)

const handleUploadSuccess = newData => {
  data.value = newData
  showDialog.value = true
  setTimeout(() => {
    showDialog.value = false
  }, 3000)
}

onMounted(() => {
  emitter.on('uploadSuccess', handleUploadSuccess)
})

onBeforeUnmount(() => {
  emitter.off('uploadSuccess')
})
</script>

<style scoped lang="scss">
.upload-success-container {
  width: 100%;
  height: 100%;
  top: 0;
  position: fixed;

  .container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    box-sizing: border-box;
    background: linear-gradient(180deg, #fff0d6 0%, #ffffff 100%);
    border-radius: 12px;
    width: 320px;
    height: 165px;

    &.no-coins {
      height: 115px;
    }

    .content {
      width: 100%;
      height: 90px;
      margin-top: 75px;
      background: url('@/assets/images/icon_upload_success_bg.png');
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;

      &.no-coins {
        margin-top: 25px;
      }
    }

    .title {
      font-weight: bold;
      color: #172b4d;
      line-height: 19px;
      text-align: center;
      font-size: 16px;

      &.no-coins {
        margin-top: 39px;
      }
    }

    .bg-img {
      width: 240px;
      height: 180px;
      background: url('@/assets/images/icon_upload_success.png');
      background-size: 100% 100%;
      position: absolute;
      left: 50%;
      top: -116px;
      transform: translateX(-50%);
    }

    .coins-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      .coins {
        width: 32px;
        height: 32px;
        margin-right: 8px;
        background: url('./icon-coin.png');
        background-size: 100% 100%;
      }

      .icon-number {
        font-weight: bold;
        color: #ff8a3f;
        line-height: 26px;
        font-size: 20px;
      }
    }
  }
}
</style>
