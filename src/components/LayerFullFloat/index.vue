<template>
  <div class="full-float-container">
    <!-- 拍一拍提醒，在互动上层 -->
    <NudgeRemind style="z-index: 96" />
    <!-- 作业盒子z-index: -1使飞金币效果在其上面 -->
    <AssignmentBox
      ref="assignmentBoxRef"
      :plan-id="baseData.planInfo.id"
      :options="baseData.commonOption"
      style="z-index: 98"
    ></AssignmentBox>
    <!-- chatbox -->
    <ChatBox :style="{ right: chatBoxDomRight }" style="z-index: -2"></ChatBox>
    <!-- <GameInteraction
      v-if="isShowGameInteraction"
      :options="gameInteractionOptions"
      @closeGame="isShowGameInteraction = false"
      style="z-index: 97"
    /> -->
    <!-- <CoinsBadge style="z-index: 96" /> -->
    <FlyCoins style="z-index: 99" />
    <UploadSuccessDialog style="z-index: 99" />
    <LiveKickout style="z-index: 100"></LiveKickout>
    <template v-for="interaction in interactions">
      <component
        :ref="
          el => {
            if (el) interactionRefs[interaction.name] = el
          }
        "
        :is="interaction.componentName"
        :key="interaction.name"
        :layout="interaction.layout"
        v-if="interaction.isShow"
        :options="interaction.options"
        @close="interactionClose"
        style="pointer-events: auto"
      ></component>
    </template>
    <!-- <OpenMicTip></OpenMicTip> -->
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, nextTick, reactive, computed } from 'vue'
import { useClassData } from '@/stores/classroom'
// import CoinsBadge from '@/components/ClassHead/Header/Coins/CoinsBadge/index.vue'
import AssignmentBox from '@/components/ClassHead/Header/AssignmentBox/index.vue'
import ChatBox from '../ChatBox/index.vue'
// import GameInteraction from '../../interactions/GameForCourseware'
import FlyCoins from '@/components/ClassHead/Header/Coins/FlyCoins/index.vue'
import UploadSuccessDialog from './UploadSuccessDialog/index.vue'
import LiveKickout from '../liveKickout/index.vue'
import TakePicture from '@/interactions/TakePicture/index.vue'
import RandomCall from '@/interactions/RandomCall/index.vue'
// import OpenMicTip from './OpenMicTip/index.vue'
import NudgeRemind from '@/components/FloatLayer/NudgeRemind/index.vue'
import { emitter } from '@/hooks/useEventBus'
import { getLocalStorageConfig } from '@/utils/initConfig'
import { onClickOutside } from '@vueuse/core'
import { gameLog } from '@/utils/web-log/HWLogDefine'

// import { clearScreenShotTimer, setIntervalScreenShot } from '../../hooks/useInteractScreenShot'

// 获取store
const store = useClassData()
const baseData = computed(() => store.baseData)

// 响应式状态
const assignmentBoxRef = ref(null)
const assignmentEntryDom = ref(null)
const checkBigPictureDom = ref(null)
const isShowGameInteraction = ref(false)
const gameInteractionOptions = ref({})
const chatBoxDomRight = ref('0px')
const interactionRefs = reactive({})

// 点击作业盒子外部处理
onClickOutside(assignmentBoxRef, event => {
  // 排除的dom 1.打开查看作业盒子入口的dom 2.查看大图的dom
  const targetDom = event.target
  const isExcludeDom =
    (assignmentEntryDom.value && assignmentEntryDom.value.contains(targetDom)) ||
    (checkBigPictureDom.value && checkBigPictureDom.value.contains(targetDom))
  if (isExcludeDom) {
    return
  }
  // 隐藏作业盒子
  nextTick(() => {
    assignmentBoxRef.value.hideAssignmentBoxOnly()
  })
})
const openBoxHandle = wrapperDom => {
  assignmentEntryDom.value = wrapperDom
  nextTick(() => {
    assignmentBoxRef.value.handleOpenAssignmentBox()
  })
}
const emitBigImgDomHandle = () => {
  wrapperDom => {
    checkBigPictureDom.value = wrapperDom
  }
}
let game_config = {}

const cloudConfig = getLocalStorageConfig()
if (cloudConfig && cloudConfig.configs) {
  for (let i = 0; i < cloudConfig.configs.length; i++) {
    const element = cloudConfig.configs[i]
    if (element.configKey === 'hw_game_config') {
      game_config = JSON.parse(element.configValue)
      break
    }
  }
}
const gameInteractHandle = noticeContent => {
  gameLog.info('发布游戏gameInteract', noticeContent)
  gameInteractionOptions.value = {
    roomMessage: {
      roomInfo: {
        ...store.state.smallClass.baseData
      }
    },
    ircMsg: noticeContent
  }
  if (noticeContent.jsString && game_config.closeLoadGame !== 1) {
    emitter.emit('coursewarePlayGame', gameInteractionOptions.value)
  } else {
    if (noticeContent.pub) {
      isShowGameInteraction.value = true
    } else {
      isShowGameInteraction.value = false
    }
  }
}
// 监听事件
const listenerEvents = () => {
  emitter.on('handleOpenBox', openBoxHandle)
  // 查看大图dom
  emitter.on('emitBigImgDom', emitBigImgDomHandle)

  console.info('游戏配置云控', game_config)
  //emitter.on('gameInteract', gameInteractHandle)
}

// 互动列表
const interactions = reactive([
  {
    name: 'TakePicture', // 拍照上墙
    componentName: 'TakePicture',
    layout: 'full',
    isShow: false,
    keys: ['take_picture'],
    options: {
      roomMessage: {
        roomInfo: {
          ...store.baseData
        }
      },
      ircMsg: ''
    }
  },
  {
    name: 'RandomCall',
    componentName: 'RandomCall',
    layout: 'full',
    isShow: false,
    keys: ['small_random_call'],
    options: {
      roomMessage: {
        roomInfo: {
          ...store.baseData
        }
      },
      ircMsg: ''
    }
  }
])

// const openScreenShot = ['take_picture']
const noRepeatTrigger = ['small_random_call', 'speedyHand', 'distribute_coins', 'redPacket']

// 打开互动
const handleOpenInteraction = (interaction, options) => {
  interaction.isShow = true
  interaction.options.ircMsg = options.noticeContent
  interaction.options.isHistory = options.isHistory
  interaction.options['sendTime'] = options.sendTime
  nextTick(() => {
    if (interactionRefs[interaction.name]?.receiveMessage) {
      interactionRefs[interaction.name].receiveMessage(options.noticeContent)
    }
  })
  // if (openScreenShot.includes(options.key)) {
  //   const planId = store.state.smallClass.baseData?.planInfo?.id
  //   // setIntervalScreenShot(planId, options?.noticeContent?.interactId, options?.key)
  // }
}

// 查找互动
const findInteraction = key => {
  const interaction = interactions.find(item => item.keys.includes(key))
  return interaction
}

// 处理互动
const handleInteractions = options => {
  const { key, noticeContent, isTrigger } = options
  if (noRepeatTrigger.includes(key) && isTrigger) {
    console.warn(`不恢复的${key}互动，不触发'handleInteractions'函数`)
    return
  }
  const { pub, open, publishTopic } = noticeContent
  const interaction = findInteraction(key)
  if (pub || open || publishTopic) {
    store.updateInteractionStatus({
      interactionName: key,
      interactionStatus: true
    })
    // 如果当前有同一类型互动且不是同一个，则先销毁上一个再打开新的互动
    if (interaction.isShow) {
      if (noticeContent.interactId !== interaction.interactId) {
        interaction.isShow = false
        nextTick(() => {
          handleOpenInteraction(interaction, options)
        })
      } else {
        if (interactionRefs[interaction.name]?.receiveMessage) {
          interactionRefs[interaction.name].receiveMessage(options.noticeContent)
        }
      }
    } else {
      handleOpenInteraction(interaction, options)
    }
    interaction.interactId = noticeContent.interactId
  } else if (
    pub === false ||
    open === false ||
    pub === 0 ||
    open === 0 ||
    publishTopic === false // publishTopic 是答题板私有控制，答题板的关闭，是由端上的倒计时决定的
  ) {
    store.updateInteractionStatus({
      interactionName: key,
      interactionStatus: false
    })

    // 有些互动在收到停止作答和结束互动信令时还需要例如提交答案等操作，因此需要内部处理
    if (interactionRefs[interaction.name]?.destroyInteraction) {
      interactionRefs[interaction.name]?.destroyInteraction(noticeContent)
      // clearScreenShotTimer()
      return
    }
    // clearScreenShotTimer()
    interaction.isShow = false
  }
}

// 关闭互动
const interactionClose = key => {
  const interaction = findInteraction(key)
  interaction.isShow = false
}

// 监听互动事件
emitter.on('take_picture', handleInteractions)

emitter.on('small_random_call', handleInteractions)

// 生命周期钩子
onMounted(() => {
  listenerEvents()
  // 获取 #tools-chatbox dom 在屏幕上的位置
  const chatBoxDom = document.getElementById('tools-chatbox')
  if (chatBoxDom) {
    const chatBoxDomRect = chatBoxDom.getBoundingClientRect()
    // 获取屏幕的宽
    const screenWidth = document.body.clientWidth
    chatBoxDomRight.value = screenWidth - chatBoxDomRect.left + 46 + 'px'
    console.log('chatBoxDomRight', screenWidth, chatBoxDomRect.left, chatBoxDomRight)
  }
})

onBeforeUnmount(() => {
  emitter.off('handleOpenBox', openBoxHandle)
  // 查看大图dom
  emitter.off('emitBigImgDom', emitBigImgDomHandle)
  // emitter.off('gameInteract', gameInteractHandle)
  emitter.off('take_picture', handleInteractions)
  emitter.off('small_random_call', handleInteractions)

  // clearScreenShotTimer()
})

// 组件注册
defineOptions({
  name: 'FullFloatLayer',
  components: {
    AssignmentBox,
    ChatBox,
    // GameInteraction,
    // CoinsBadge,
    FlyCoins,
    UploadSuccessDialog,
    LiveKickout,
    TakePicture,
    RandomCall,
    // OpenMicTip
    NudgeRemind
  }
  // directives: {
  //   Clickoutside
  // }
})
</script>
