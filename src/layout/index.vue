<template>
  <div class="layout-wrapper">
    <CommonHeader />
    <div class="wrapper-body">
      <SideBar />
      <router-view style="flex: 1; margin-left: 20px" />
    </div>
  </div>
</template>
<script setup>
import CommonHeader from '@/components/Common/CommonHeader.vue'
import SideBar from '@/components/Common/SideBar.vue'
</script>
<style lang="scss" scoped>
.layout-wrapper {
  height: 100vh;
  background: #f4f6fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.wrapper-body {
  background: #f4f6fa;
  display: flex;
  height: calc(100vh - 90px);
  min-width: 1240px;
  margin-top: 40px;
}
</style>
