// 业务逻辑
import { submitUserPhoto, interactOpen, getWallStatus, setSubmitStatus } from '@/api/interaction/photoWall'
import { emitter } from '@/hooks/useEventBus'

export default class Main {
  constructor(opts = {}) {
    // @log-ignore
    this.options = opts
    // this.students = opts.ircMsg.students
    // this.myself = opts.stuInfo.id
  }
  clearTipTimer() {
    if (this.tipTimer) {
      clearTimeout(this.tipTimer)
      this.tipTimer = null
    }
  }

  /**
   * 关闭提示
   * @param {*} dom
   * @param {*} duration
   */
  closeTip(dom, duration, callback) {
    if (!dom) return
    this.clearTipTimer()
    // 定时关闭提示
    if (duration > 0) {
      let tipTimer = setTimeout(() => {
        dom.parentNode.removeChild(dom)
        clearTimeout(tipTimer)
        tipTimer = null
        callback && callback()
      }, duration)
    }
  }

  /**
   * 显示提示
   * @param {*} dom
   * @param {*} duration
   */
  showShortCutTip({ duration = 3000, tips }) {
    const needShortCutTip = JSON.parse(window.sessionStorage.getItem('photoWallShortCutTip'))
    if (needShortCutTip) return
    const _tipDom = document.createElement('div')
    _tipDom.setAttribute('class', 'shortCutTip animateTada')
    _tipDom.innerHTML = `
      <p>${tips}</p>
    `
    document.getElementById('graffitiContainer').appendChild(_tipDom)
    window.sessionStorage.setItem('photoWallShortCutTip', true)
    this.closeTip(_tipDom, duration)
  }

  /**
   * 显示未参与提示
   * @param {*} dom
   * @param {*} duration
   */
  showNotjoinTip(options = {}) {
    const { tips, callback, className = null, duration = 3000, dom = 'graffitiContainer' } = options
    const _tipDom = document.createElement('div')
    _tipDom.setAttribute('class', `notJoinTip ${className ? className : ''}`)
    _tipDom.innerHTML = `
      <p>${tips}</p>
    `
    document.getElementById(dom) && document.getElementById(dom).appendChild(_tipDom)
    this.closeTip(_tipDom, duration, callback)
  }

  /**
   * 提交成功，显示金币
   * @param {*} coins
   */
  showCoinsTip(options = {}) {
    const { duration = 3000, callback = null, dom = 'graffitiContainer' } = options
    // 展示金币时，需要关闭所有无关的弹层
    const submitTipContainer = document.getElementById('submitTipContainer')
    if (submitTipContainer) {
      submitTipContainer.parentNode.removeChild(submitTipContainer)
    }
    const _tipDom = document.createElement('div')

    document.getElementById(dom) && document.getElementById(dom).appendChild(_tipDom)
    this.closeTip(_tipDom, duration, callback)
  }

  /**
   * 创建错误提示
   * @param {*} options
   */
  showSubmitTip(options = {}) {
    const { duration = 3000, callback = null, text, icon, dom = 'graffitiContainer' } = options
    const _duration = icon === 'loading' ? 0 : duration
    const submitTipContainer = document.getElementById('submitTipContainer')
    if (submitTipContainer) {
      submitTipContainer.parentNode.removeChild(submitTipContainer)
    }
    const _tipDom = document.createElement('div')
    _tipDom.setAttribute('id', 'submitTipContainer')
    _tipDom.setAttribute('class', 'submitTipContainer')
    _tipDom.innerHTML = `
      <div class="tipContainer">
        ${icon ? '<i class="' + icon + '"></i>' : ''}
        <p>${text}</p>
      </div>
    `
    document.getElementById(dom) && document.getElementById(dom).appendChild(_tipDom)
    this.closeTip(_tipDom, _duration, callback)
  }

  /**
   * 初始化倒计时
   */
  initCountdownTime() {
    const localTime = +new Date() // 本机时间
    const timeOffset = this.options.commonOption.timeOffset // 本机时间和服务器时间的差值
    const times =
      this.options.ircMsg.totalTime * 1000 -
      (localTime + timeOffset - this.options.ircMsg.beginTime)
    return times
  }

  /**
   * 上报开启互动
   */
  async interactOpen() {
    const params = {
      planId: this.options.stuLiveInfo.planId * 1, // 讲次ID
      classId: this.options.stuLiveInfo.classId * 1, // classId
      interactId: this.options.ircMsg.interactId // 互动ID
    }
    await interactOpen(params)
  }

  /**
   * 提交照片
   */
  async submitPhoto(key, isNewGraffiti) {
    const params = {
      planId: this.options.stuLiveInfo.planId * 1, // 讲次ID
      classId: this.options.stuLiveInfo.classId,
      tutorId: this.options.stuLiveInfo.tutorId,
      photoPath: key,
      interactId: this.options.ircMsg.interactId, // 互动ID
      teacherId: this.options.teacherInfo.id // 主讲老师ID（只有拍照上墙传，0722版本新增）
    }
    isNewGraffiti && (params.version = 722) // 需要区分PC和ipad, 小黑板提交传 722、拍照上墙默认0（722代表当前需求版本722，如果为0保持老逻辑加积分，如果为722提交不加金币）
    const res = await submitUserPhoto(params)
    // 通知右侧区域加金币
    if (res.code === 0 && res.data) {
      emitter.emit('updateAchievement', 'update')
    }
    // console.log(res, '互动的金币')
    return res
  }

  async getWallStatus() {
    const params = {
      planId: this.options.stuLiveInfo.planId * 1, // 讲次ID
      interactId: this.options.ircMsg.interactId // 互动ID
    }
    const res = await getWallStatus(params)
    return res
  }

  /**
   * 新版学员提交涂鸦画板（类似举手操作）
   */
  async submitGratiffi() {
    const params = {
      planId: this.options.stuLiveInfo.planId * 1, // 讲次ID
      classId: this.options.stuLiveInfo.classId,
      tutorId: this.options.stuLiveInfo.tutorId,
      interactId: this.options.ircMsg.interactId, // 互动ID
      version: 722 // 传 722（722代表当前需求版本722，如果为0保持老逻辑不加积分，如果为722表示举手要加积分）
    }
    const res = await setSubmitStatus(params)
    return res
  }
}
