@mixin makeBg($value, $width, $height) {
  background: url(../imgs/#{$value}) no-repeat center center;
  background-size: 100% 100%;
  width: #{$width}px;
  height: #{$height}px;
  overflow: hidden;
}

$font-family: -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB',
  'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif !default;

.photoShotContainer {
  flex-direction: column;
  font-size: 14px;
  font-family: $font-family;
  font-weight: 400;
  color: rgba(222, 226, 231, 1);
  line-height: 16px;
}

.photoShotNewContainer {
  width: 100%;
  height: 100%;
}

.graffitiWrapper {
  box-sizing: border-box;
  overflow: hidden;
  margin: 0 auto;
  border-radius: 10px;
  background-size: cover;
  position: relative;
  z-index: 100;
}

.graffitiNewWrapper,
#gratiffiImg {
  width: 100%;
  height: 100%;
  border-radius: 0;
  position: absolute;
  top: 0;
  left: 0;
}

.tools {
  width: 580px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  z-index: 999;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);

  .graffitiTools {
    width: 404px;
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 30px;
    justify-content: center;
    gap: 30px;
    background: #1a1a1a;
    margin-right: 1px;
    border-radius: 100px 0 0 100px;
    position: relative;
    overflow: hidden;
  }

  .userTools {
    width: 175px;
    height: 60px;
    display: flex;
    align-items: center;
    // justify-content: flex-end;
    justify-content: space-around;
    background: #1a1a1a;
    border-radius: 0 100px 100px 0;

    .time {
      display: flex;
      align-items: center;

      .dots {
        display: flex;
        flex-direction: column;
        margin: 0 5px;

        span {
          width: 3px;
          height: 3px;
          border-radius: 50%;
          display: inline-block;
          background: #a2aab8;

          &:first-child {
            margin-bottom: 6px;
          }
        }
      }

      .minutes,
      .seconds {
        width: 28px;
        height: 34px;
        position: relative;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;

        .line {
          width: 100%;
          height: 1px;
          background: #121212;
          position: absolute;
          top: 50%;
          left: 0;
        }

        .num {
          position: absolute;
          font-size: 16px;
          font-weight: 500;
          color: #a2aab8;
          line-height: 19px;
        }
      }
    }
  }
}

.draw {
  background-size: 100%;
  cursor: pointer;
  font-size: 0;
  display: inline-block;
  width: 24px;
  height: 50px;
  margin-bottom: -18px;
  overflow: hidden;
  //  [
  //         {
  //           key: 'red-pen',
  //           color: '#FF3757',
  //           icon: require('./imgs/red-pen.png')
  //         },
  //         {
  //           key: 'orange-pen',
  //           color: '#FF922D',
  //           icon: require('./imgs/orange-pen.png')
  //         },
  //         {
  //           key: 'yellow-pen',
  //           color: '#FAC349',
  //           icon: require('./imgs/yellow-pen.png')
  //         },
  //         {
  //           key: 'green-pen',
  //           color: '#45CD49',
  //           icon: require('./imgs/green-pen.png')
  //         },
  //         {
  //           key: 'blue-pen',
  //           color: '#56A6FF',
  //           icon: require('./imgs/blue-pen.png')
  //         },
  //         {
  //           key: 'purple-pen',
  //           color: '#BB53E3',
  //           icon: require('./imgs/purple-pen.png')
  //         },

  //         {
  //           key: 'eraser',
  //           icon: require('@/components/Classroom/SmallClass/images/eraser.png')
  //         }
  //       ]
  &.draw-red {
    @include makeBg('red-pen.png', 24, 50);

  }

  &.draw-orange {
    @include makeBg('orange-pen.png', 24, 50);

  }

  &.draw-yellow {
    @include makeBg('yellow-pen.png', 24, 50);

  }

  &.draw-green {
    @include makeBg('green-pen.png', 24, 50);

  }

  &.draw-blue {
    @include makeBg('blue-pen.png', 24, 50);
  }

  &.draw-purple {
    @include makeBg('purple-pen.png', 24, 50);
  }

  &.eraser {
    @include makeBg('eraser.png', 24, 50);
  }

  &.select {
    margin-bottom: 0;
    transition: bottom 0.3s;
  }
}

.teacher-watching-label {
  position: absolute;
  top: 20px;
  right: 20px;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  z-index: 1000;
}

.submit-btn {
  padding: 5px 12px;
  height: 32px;
  // margin: 0 0 0 52px;
  border: 0 none;
  background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.eraserCursor) {
  cursor: url('../imgs/eraser_cursor.png') 12 12, pointer;
}

:deep(.red_penCursor) {
  cursor: url('../imgs/red_pen_cursor.png'), pointer;
}

:deep(.yellow_penCursor) {
  cursor: url('../imgs/yellow_pen_cursor.png'), pointer;
}

:deep(.blue_penCursor) {
  cursor: url('../imgs/blue_pen_cursor.png'), pointer;
}

.anticon-caret-right {
  margin-left: 5px;
  margin-top: 2px;
}