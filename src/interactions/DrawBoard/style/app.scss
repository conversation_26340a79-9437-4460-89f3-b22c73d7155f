$namespace: 'graffiti';

.#{$namespace}Container {
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
@mixin makeBg($value, $width, $height) {
  background: url(@/interactions/DrawBoard/imgs/#{$value}) no-repeat center center;
  background-size: 100% 100%;
  width: #{$width}px;
  height: #{$height}px;
  overflow: hidden;
}
:deep(.wallConisTip) {
  @include makeBg('result_toast_bg.png', 260, 120);
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  overflow: hidden;
  box-sizing: border-box;
  padding: 10px 18px 0 18px;
  z-index: 9999;
  p {
    font-size: 18px;
    font-weight: 500;
    color: #fff;
    line-height: 20px;
    text-align: center;
    padding-top: 30px;
  }
  .coins {
    display: flex;
    justify-content: center;
    font-size: 24px;
    color: #fff;
    font-weight: 600;
    align-items: center;
  }
  i {
    display: block;
    @include makeBg('icon_coins.png', 44, 44);
  }
}
:deep(.submitSuccessTip) {
  @include makeBg('submit_success_toast_bg.png', 260, 120);
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  p {
    padding-top: 0;
  }
}
:deep(.notJoinTip) {
  @include makeBg('nojoin_toast_bg.png', 315, 155);
  box-sizing: border-box;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 67; // 高于涂鸦层
  transform: translate(-50%, -50%);
  padding: 75px 50px 0 15px;
  &.notJoin {
    position: absolute;
  }
  p {
    color: #172b4d;
    font-size: 14px;
    line-height: 18px;
    font-weight: 500;
    text-align: center;
  }
}
