<template>
  <div
    v-if="!isSubmit"
    :class="[layout === 'full' ? 'fullContainer' : 'layout4b3container']"
    id="graffitiContainer"
  >
    <!-- 新版本涂鸦画板 -->
    <GraffitiViewNew
      :countDownTime="countDownTime"
      :options="options"
      v-if="isNewGraffiti"
      @submit="submitNotice"
      :isSubmit="GraffitIsSubmit"
    />
    <!-- 兼容ipad主讲发送的涂鸦画板 -->
    <GraffitiView
      @submit="submitPhoto"
      :countDownTime="countDownTime"
      :options="options"
      @photoData="getPhotoData"
      v-else-if="isNewGraffiti !== ''"
      :isSubmit="GraffitIsSubmit"
    />
    <!--上传进度条：拍照、涂鸦 共用-->
    <UploadProgress :classType="classType" :percent="percent" :visible="showUploadProgress" />
  </div>
</template>

<script>
import GraffitiClass from './graffiti.js'
import GraffitiView from './components/graffiti.vue'
import GraffitiViewNew from './components/graffitiNew.vue'
import Upload from '@/utils/upload'
import UploadProgress from './components/progress.vue'
import logger from '@/utils/logger'
import { emitter } from '@/hooks/useEventBus'
export default {
  name: 'DrawBoard',
  props: {
    options: {
      type: Object,
      default: () => {}
    },
    layout: {
      type: String,
      default: 'full'
    }
  },
  components: {
    GraffitiView,
    GraffitiViewNew,
    UploadProgress
  },
  data() {
    return {
      graffitiOptions: null,
      showUploadProgress: false,
      countDownTime: 0,
      classType: 0, // 班级类型
      isSubmit: false, // 是否已提交
      isNewGraffiti: '', // 判断涂鸦画板的新旧版本，默认为''即新旧版本都不加载，通过信令中的dbkey判断
      GraffitIsSubmit: false, // 学生课中练习是否提交
      thinkClass: null
    }
  },
  created() {
    this.graffitiOptions = new GraffitiClass({
      ...this.options
    })
    this.countDownTime = parseFloat(this.graffitiOptions.initCountdownTime() / 1000) || 0
    this.sendLogger(`涂鸦画板互动开始倒计时, ${this.countDownTime}s`)
    this.classType = this.options.commonOption.classType
  },
  mounted() {
    console.log('[DrawBoard] mounted - options:', this.options)
    this.thinkClass = this.options.thinkClass
    this.init()
  },
  computed: {},
  methods: {
    /*
     * 获取图片数据
     */
    getPhotoData(res) {
      // @log-ignore
      this.photoData = res
    },
    /**
     * 提交数据
     */
    async submitPhoto(blob) {
      if (this.submiting) return
      this.dialogVisible = this.showPhotoShotTip = false
      this.submiting = this.showUploadProgress = true
      const upload = new Upload({
        scene: 'pictureWall'
      })
      const { key: uploadKey } = await upload.putFile({
        filePath: 'pictureWall.jpg',
        file: blob ? blob : this.photoData.blob,
        progress: percent => {
          // console.log(percent, 'percent===')
          this.percent = percent
        },
        success: res => {
          // console.log(res, '上传照片')
          this.sendLogger(`上传照片成功, url: ${res.url}`)
        },
        fail: err => {
          this.showUploadProgress = false
          this.submiting = false // 恢复提交状态
          this.percent = 0 // 上传进度清0
          this.graffitiOptions.showSubmitTip({
            text: this.$t('common.uploadFailed'),
            icon: 'error',
            callback: () => {}
          })
          console.error('上传照片失败', err)
          this.sendLogger('上传照片失败')
        }
      })
      // console.log(uploadKey, 'uploadKey')
      // 提交数据到后台
      if (uploadKey) {
        this.showUploadProgress = false
        const submitRes = await this.graffitiOptions.submitPhoto(uploadKey, this.isNewGraffiti)
        if (submitRes.code === 0) {
          this.GraffitIsSubmit = true
          this.isNewGraffiti && (this.isSubmit = true)
          // 如果没有金币或者是PC主讲端停止作答操作，不需要走下面发放金币逻辑，不加日志
          if (!submitRes?.data?.rightCoin || this.isNewGraffiti) return
          const { rightCoin } = submitRes.data
          emitter.emit('uploadSuccess', {
            tips: this.$t('common.sumbittedSuccessfully'),
            coins: rightCoin
          })
          this.graffitiOptions.showCoinsTip({
            callback: () => {
              this.isSubmit = true
            }
          })
          this.sendLogger(`获得金币成功, ${JSON.stringify(submitRes)}`)
        } else {
          this.submiting = false // 恢复提交状态
          this.isSubmit = true
          this.percent = 0 // 上传进度清0
          // 如果是PC主讲端停止作答操作，不需要走下面发放金币逻辑，不加日志
          if (this.isNewGraffiti) return
          this.graffitiOptions.showSubmitTip({
            text: this.$t('common.submissionFailed'),
            icon: 'error',
            callback: () => {}
          })
          this.sendLogger(`获得金币失败, ${JSON.stringify(submitRes)}`)
        }
      }
    },
    // 老师发送停止作答信令，学生需要先上传涂鸦图片再关闭涂鸦层
    async stopGratiffi() {
      try {
        this.sendLogger('老师发送停止作答')
        // 获取内容宽高
        const graffitiDom1 = document.querySelector('#graffitiWrapper')
        if (!graffitiDom1) {
          console.error('[涂鸦画板] 找不到 #graffitiWrapper 元素')
          return
        }
        const containerWidth = graffitiDom1.clientWidth
        const containerHeight = graffitiDom1.clientHeight

        // 创建新canvas
        const canvas = document.createElement('canvas')
        canvas.width = containerWidth
        canvas.height = containerHeight
        const ctx = canvas.getContext('2d')

        // 获取背景图片
        const coursewareImg = document.querySelector('#gratiffiImg')
        if (coursewareImg) {
          // 创建新的图片对象并设置跨域属性
          const img = new Image()
          img.crossOrigin = 'anonymous'
          img.src = coursewareImg.src

          // 等待图片加载完成
          await new Promise((resolve, reject) => {
            img.onload = resolve
            img.onerror = reject
          })

          ctx.drawImage(img, 0, 0, containerWidth, containerHeight)
        }

        // 获取涂鸦canvas
        const whiteboardCanvas = document.querySelector('.canvas-container .pen-canvas')
        if (whiteboardCanvas) {
          ctx.drawImage(whiteboardCanvas, 0, 0, containerWidth, containerHeight)
        }

        // 将canvas生成图片
        const dataUrl = canvas.toDataURL('image/jpeg', 0.8)
        const blob = this.base64ToBlob(dataUrl, 'image/jpeg')
        await this.submitPhoto(blob)
      } catch (error) {
        console.error('[涂鸦画板] stopGratiffi 错误:', error)
        this.sendLogger(`停止作答失败: ${error.message}`, 'error')
      }
    },
    async submitNotice() {
      // console.log('学员提交涂鸦')
      this.sendLogger('学员提交涂鸦')
      //小黑板PC主讲，学生提交不收起小黑板，IPAD主讲，学生提交收起小黑板，停止作答全部收起小黑板
      const submitRes = await this.graffitiOptions.submitGratiffi()
      if (submitRes.code === 0) {
        this.GraffitIsSubmit = true
        if (!submitRes?.data?.rightCoin) return
        const { rightCoin } = submitRes.data
        emitter.emit('uploadSuccess', {
          tips: this.$t('common.sumbittedSuccessfully'),
          coins: rightCoin
        })
        this.graffitiOptions.showCoinsTip({
          callback: () => {}
        })
        this.sendLogger(`获得金币成功, ${JSON.stringify(submitRes)}`)
        // 等待提交成功弹窗消失后再加金币动效
        setTimeout(() => {
          emitter.emit('addCoin', true, rightCoin)
        }, 3000)
      }
    },
    // 将base64转为Blob
    base64ToBlob(base64, mimeType) {
      // @log-ignore
      const bytes = window.atob(base64.split(',')[1])
      const ab = new ArrayBuffer(bytes.length)
      const ia = new Uint8Array(ab)
      for (let i = 0; i < bytes.length; i++) {
        ia[i] = bytes.charCodeAt(i)
      }
      return new Blob([ab], { type: mimeType })
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '') {
      const interactionId = this.options?.ircMsg?.interactId || 'unknown'
      const interactionType = 'DrawBoard'

      logger.send({
        tag: 'student.Interact',
        level: 'info',
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage })
    },
    userTrackLogger(msg) {
      logger.send({
        tag: 'userTrack',
        content: {
          msg,
          tag: 'student.Interact',
          interactType: 'Photopost'
        }
      })
    },
    /**
     * 异常恢复
     */
    async getWallStatus() {
      try {
        const { code, data } = await this.graffitiOptions.getWallStatus()
        if (code === 0) {
          this.isSubmit = data.isSubmit
        }
      } catch (error) {
        console.error('异常恢复error', error)
      }
    },
    /**
     * 关闭互动irc消息
     */
    async destroyInteraction(noticeContent) {
      if (!noticeContent.pub && !noticeContent.isEnd && noticeContent.dbkey) {
        this.stopGratiffi()
        return
      }
      if (!this.photoData && !this.isSubmit && !this.submiting) {
        this.graffitiOptions.showNotjoinTip({
          className: 'notJoin',
          tips: this.$t('classroom.interactions.photoWall.notjoinTip'),
          callback: () => {
            this.destroy()
          }
        })
      }
    },
    destroy() {
      emitter.emit('photoWallInteractId', null)
      this.userTrackLogger('涂鸦画板结束')
      this.$emit('close', 'graffiti_board')
    },
    init() {
      this.userTrackLogger('涂鸦画板启动')
      this.$nextTick(async () => {
        this.isNewGraffiti = this.options.ircMsg.dbkey // 通过有无dbkey判断是新版涂鸦画板还是旧版涂鸦画板
        this.sendLogger(
          `收到涂鸦画板互动, 信令内容为: ${JSON.stringify(this.options.ircMsg)}`,
          'start'
        )
        emitter.emit('photoWallInteractId', this.options.ircMsg.interactId) // 广播互动ID
        // 将互动数据存入在本地，为订正提供互动数据支持
        localStorage.setItem('photoWallOptions', JSON.stringify(this.options))
        this.getWallStatus() // 获取异常恢复数据
        this.graffitiOptions.interactOpen() // 上报互动打开
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@use './style/app.scss' as *;

.fullContainer {
  width: 100%;
  height: 100%;
  position: absolute;
}

.layout4b3container {
  width: calc(100% / 16 * 9 / 3 * 4);
  height: 100%;
  position: absolute;
}
</style>
