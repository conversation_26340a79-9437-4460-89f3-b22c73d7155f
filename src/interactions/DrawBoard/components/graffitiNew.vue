<template>
  <div class="photoShotContainer photoShotNewContainer">
    <GratiffiTools
      ref="GratiffiTools"
      :minutes="minutes"
      :seconds="seconds"
      @graffitiDraw="graffitiDraw"
      @graffitiEraser="graffitiEraser"
      @confirm="confirm"
      :isSubmit="isSubmit"
    />
    <div id="graffitiWrapper" class="graffitiWrapper graffitiNewWrapper">
      <img
        id="gratiffiImg"
        src="../imgs/black_bg.png"
        @load="handleImgLoad($event)"
        @error="handleImgError($event)"
      />
      <canvas id="graffiti-canvas"></canvas>
      <!-- <white-board-tools ref="WhiteBoardTools" /> -->
    </div>
    <div v-if="teacherWatching" class="teacher-watching-label">
      {{ $t('common.teacherWatching') }}
    </div>
  </div>
</template>

<script>
import _debounce from 'lodash/debounce'
import { useClassData } from '@/stores/classroom'
import GratiffiTools from './graffitiTools.vue'
import logger from '@/utils/logger'
import { getHistoryMessage } from '@/core/ThinkService/getHistoryMsg'
// import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { emitter } from '@/hooks/useEventBus'
import { useIrcService } from '@/hooks/useIrcService'
import { DrawingMode, nwb } from '@thinkacademy/nwb'
const { getIrcService } = useIrcService()
export default {
  name: 'smallClassGraffitiNew',
  props: {
    closeInteractive: {
      type: Boolean,
      default: false
    },
    isSubmit: {
      type: Boolean,
      default: false
    },
    countDownTime: {
      type: Number,
      default: 0
    },
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogContent: null,
      dialogDescription: null,
      timer: null,
      minutes: '00',
      seconds: '00',
      isBanned: false,
      currentThickness: 4, // 初始化笔迹大小
      currentLineDashed: false, // 是否带有笔锋
      containerWidth: 0, // 涂鸦区域初始宽高
      containerHeight: 0,
      gratiffiMsgArray: [], // 用于暂存涂鸦消息
      teacherWatching: false, // 老师是否正在查看自己涂鸦
      eraserSize: 50, // 定义橡皮擦大小
      countDownTimeCopy: null,
      SignalService: null,
      boardInstance: null
    }
  },
  components: {
    GratiffiTools
  },
  mounted() {
    // 拿到监听到的[graffiti_board_watching]信令信息 并手动判断一次 老师是否查看自己的画板（防止收到watching信令时该组件还未加载，导致后续操作丢失）
    const store = useClassData()
    const IrcService = getIrcService()
    this.SignalService = IrcService
    console.log('[涂鸦画板] mounted - store:', {
      gratiffiBoardWatchingInfo: store.gratiffiBoardWatchingInfo,
      options: this.options,
      stuInfo: this.options?.stuInfo
    })
    this.teacherIsWatchingMe(store.gratiffiBoardWatchingInfo)
    this.$nextTick(() => {
      this.listenerSignalService()
      // 设置背景为课件图片
      if (this.options.ircMsg.imageUrl) {
        const graffitiBg = document.querySelector('#gratiffiImg')
        // 设置跨域属性
        graffitiBg.crossOrigin = 'anonymous'
        graffitiBg.src = this.options.ircMsg.imageUrl
      }
      this.sendLogger(`this.options, ${JSON.stringify(this.options)}`)
    })
    // 倒计时
    this.renderCountDown()
    window.addEventListener('resize', this.setGraffitiScale)
  },
  watch: {
    // 老师看我的画板时，先发送一波暂存的涂鸦消息
    teacherWatching: {
      handler(val) {
        console.log('[涂鸦画板] teacherWatching watch - 状态变化:', val)
        if (val) {
          this.sendGratiffiMsg()
        }
      },
      immediate: true
    }
  },
  methods: {
    // 图片加载日志
    handleImgLoad(event) {
      this.sendLogger(`涂鸦画板背景图加载成功`, { url: event.target.src })
      // 确保图像加载完成后再初始化画板
      this.$nextTick(() => {
        this.initBoard()
      })
    },
    handleImgError(event) {
      console.error('涂鸦画板背景图加载失败', event.target.src)
      this.sendLogger(`涂鸦画板背景图加载失败`, { url: event.target.src }, 'error')
    },
    /**
     * 初始化涂鸦
     */
    initBoard() {
      // 获取当前画布大小
      this.getCurrentCanvasSize()

      // 初始化画板实例
      this.boardInstance = new nwb({
        id: 'graffiti-canvas',
        getHistoryMessage: this.getHistoryMessage,
        maxChars: 1000,
        messageSender: msg => {
          console.log('[涂鸦画板] messageSender - msg:', msg)
          console.log('[涂鸦画板] messageSender - teacherWatching:', this.teacherWatching)
          console.log('[涂鸦画板] messageSender - SignalService:', this.SignalService)
          // 老师查看我的画板
          if (this.teacherWatching) {
            console.log('[涂鸦画板] messageSender - 发送消息到老师:', {
              roomId: this.options.commonOption.roomlist[0],
              dbKey: `${this.options.ircMsg.dbkey}#${this.options.stuInfo.id}`,
              keyMsgId: msg.keyMsgId
            })
            this.SignalService.sendRoomBinMessage(
              [this.options.commonOption.roomlist[0]],
              `${this.options.ircMsg.dbkey}#${this.options.stuInfo.id}`,
              msg.keyMsgId,
              msg.content
            )
            this.sendLogger(`发送涂鸦画板涂鸦消息:${msg.dbKey} ${msg.keyMsgId}`)
          } else {
            // 老师未查看我的涂鸦画板时先不发送涂鸦消息到irc上，先暂存起来，老师查看我的画板时再发送
            console.log('[涂鸦画板] messageSender - 暂存消息:', msg)
            this.gratiffiMsgArray.push(msg)
            this.sendLogger(`保存涂鸦画板涂鸦消息:${msg.dbKey} ${msg.keyMsgId}`)
          }
        }
      })
      // 监听信令消息
      this.SignalService.on('onRecvRoomBinMessageNotice', this.handleOnRecvRoomBinMessageNotice)
      this.SignalService.on('onSendRoomBinMessageResp', this.handleOnSendRoomBinMessageResp)
      // 设置画板大小
      this.boardInstance.resizeCanvas({
        width: this.containerWidth,
        height: this.containerHeight
      })

      // 设置发送到涂鸦sdk中的dbkey
      const dbkey = `${this.options.stuInfo.id}_${this.options.ircMsg.dbkey}#${this.options.stuInfo.id}`

      // 设置画板模式
      this.boardInstance.setDrawingMode(DrawingMode.none)

      // 设置默认画笔样式
      this.boardInstance.setStroke({
        width: this.currentThickness,
        color: '#FF3757',
        style: this.currentLineDashed ? 'dash' : 'solid'
      })

      // 设置橡皮擦大小
      this.boardInstance.setEraserWidth(this.eraserSize)

      // 设置页面
      this.boardInstance.pageChange(dbkey)

      // 默认选中红色画笔
      this.$refs.GratiffiTools.graffitiDraw('red', '#FF3757')
    },

    sendRoomCanvasMessage(roomId, canvasMessage) {
      // @log-ignore
      // 老师查看我的画板
      if (this.teacherWatching) {
        this.SignalService.sendRoomBinMessage(
          [this.options.commonOption.roomlist[0]],
          `${this.options.ircMsg.dbkey}#${this.options.stuInfo.id}`,
          canvasMessage.keyMsgId,
          canvasMessage.content
        )
        this.sendLogger(`发送涂鸦画板涂鸦消息:${canvasMessage.dbKey} ${canvasMessage.keyMsgId}`)
      } else {
        // 老师未查看我的涂鸦画板时先不发送涂鸦消息到irc上，先暂存起来，老师查看我的画板时再发送
        this.gratiffiMsgArray.push(canvasMessage)
        this.$refs.WhiteBoardNew &&
          this.$refs.WhiteBoardNew.handleSendMessageSuccess(
            canvasMessage.dbKey,
            canvasMessage.keyMsgId
          )
        this.sendLogger(`保存涂鸦画板涂鸦消息:${canvasMessage.dbKey} ${canvasMessage.keyMsgId}`)
      }
    },
    // 拉取自己和老师的历史涂鸦
    getHistoryMessage() {
      // 获取发送到irc上自己和老师的dbkey
      const studentdbkey = `${this.options.ircMsg.dbkey}#${this.options.stuInfo.id}`
      const teacherDbkey = `teacher_${studentdbkey}` // 获取老师dbkey
      // console.log('拉取历史消息dbkey', studentdbkey, teacherDbkey)
      // 发送消息申请拉取自己和老师的数据
      const dkbkeyList = [studentdbkey, teacherDbkey]
      this.sendLogger(`拉取历史消息dbkey :${JSON.stringify(dkbkeyList)}`)
      let userPromise = []
      const store = useClassData()
      dkbkeyList.forEach(key => {
        userPromise.push(
          getHistoryMessage({
            dbkey: key,
            appId: store.baseData.configs.ircAk,
            sk: store.baseData.configs.ircSk,
            businessId: 3,
            liveId: store.baseData.planInfo.id,
            ircApiHost: store.baseData.configs.ircApiHost
          })
        )
      })
      Promise.all(userPromise)
        .then(resAll => {
          // console.log('resAll', resAll)
          let msgs = resAll.flat()
          try {
            this.boardInstance && this.boardInstance.receiveBatchMsgHandle(msgs)
          } catch (e) {
            console.error(e)
          }
        })
        .catch(err => {
          console.error(err)
        })
    },

    listenerSignalService() {
      console.log('[涂鸦画板] listenerSignalService - 开始监听')
      emitter.on('graffiti_board_watching', this.handleGraffitiBoardWatching)
    },
    handleGraffitiBoardWatching(noticeContent) {
      console.log('[涂鸦画板] handleGraffitiBoardWatching - 收到通知:', noticeContent)
      this.sendLogger(`老师正在查看学生涂鸦 :${JSON.stringify(noticeContent)}`)
      this.teacherIsWatchingMe(noticeContent)
    },
    handleOnRecvRoomBinMessageNotice(res) {
      this.boardInstance.receiveMsgHandle(res)
      // this.sendLogger(`收到涂鸦消息 :${res.dbKey}, ${res.msgId}`)
    },
    // 发送涂鸦消息回调
    handleOnSendRoomBinMessageResp(res) {
      // @log-ignore
      // this.sendLogger(`发送涂鸦消息回调 : ${res.dbKey}, ${res.keyMsgId}`)
      if (res.code !== 0) {
        this.sendLogger(
          `涂鸦发送失败:${
            'code:' +
            res.code +
            'msg:' +
            res.msg +
            'dbKey:' +
            res.dbKey +
            'keyMsgId:' +
            res.keyMsgId
          }`,
          {},
          'error'
        )
      }
    },

    teacherIsWatchingMe(noticeContent) {
      console.log('[涂鸦画板] teacherIsWatchingMe - noticeContent:', noticeContent)
      console.log('[涂鸦画板] teacherIsWatchingMe - this.options:', this.options)
      console.log('[涂鸦画板] teacherIsWatchingMe - 当前状态:', {
        studentId: this.options?.stuInfo?.id,
        teacherId: noticeContent?.studentId,
        currentTeacherWatching: this.teacherWatching
      })

      if (!noticeContent || !this.options?.stuInfo?.id) {
        console.log('[涂鸦画板] teacherIsWatchingMe - 参数无效')
        return
      }

      // 判断老师正在是否正在查看我的涂鸦
      const isWatching = noticeContent.studentId === this.options.stuInfo.id
      console.log('[涂鸦画板] teacherIsWatchingMe - 判断结果:', {
        isWatching,
        studentId: this.options.stuInfo.id,
        teacherId: noticeContent.studentId
      })

      this.teacherWatching = isWatching

      this.sendLogger(`老师${isWatching ? '开始' : '停止'}观看我的涂鸦`, {
        studentId: this.options.stuInfo.id,
        teacherId: noticeContent.studentId
      })
    },
    // 老师正在观看我的涂鸦时，将暂存的涂鸦数据以及老师正在查看的实时数据发送到irc上并且将本地该条数据pop
    sendGratiffiMsg() {
      console.log('[涂鸦画板] sendGratiffiMsg - 暂存消息数量:', this.gratiffiMsgArray.length)
      if (!this.gratiffiMsgArray.length) return

      // 记录
      const newArr = [...this.gratiffiMsgArray]
      this.gratiffiMsgArray = [] // 涂鸦数据消费后从暂存数组中删除

      newArr.forEach(canvasMessage => {
        console.log('[涂鸦画板] sendGratiffiMsg - 发送消息:', {
          roomId: this.options.commonOption.roomlist[0],
          dbKey: `${this.options.ircMsg.dbkey}#${this.options.stuInfo.id}`,
          keyMsgId: canvasMessage.keyMsgId
        })
        this.SignalService.sendRoomBinMessage(
          [this.options.commonOption.roomlist[0]],
          `${this.options.ircMsg.dbkey}#${this.options.stuInfo.id}`,
          canvasMessage.keyMsgId,
          canvasMessage.content
        )
        this.sendLogger(
          `发送涂鸦画板保存的涂鸦消息:${canvasMessage.dbKey} ${canvasMessage.keyMsgId}`
        )
      })
    },
    // 获取当前课件区域的宽高
    getCurrentCanvasSize() {
      // 获取当前课件区域的宽高
      const graffitiDom = document.querySelector('#graffitiContainer')
      this.containerWidth = graffitiDom.clientWidth
      this.containerHeight = graffitiDom.clientHeight
    },
    renderCountDown() {
      this.countDownTimeCopy = this.countDownTime
      this.countDown()
      this.timer = setInterval(this.countDown, 1000)
    },
    // 涂鸦画笔
    graffitiDraw(abstractColor, specificColor) {
      if (this.boardInstance) {
        // 设置画笔样式
        this.boardInstance.setStroke({
          width: this.currentThickness,
          color: specificColor,
          style: this.currentLineDashed ? 'dash' : 'solid'
        })
        // 设置画笔模式
        this.boardInstance.setDrawingMode(DrawingMode.freeDrawing)
        this.sendLogger(
          `选择了涂鸦画笔, ${JSON.stringify({
            color: specificColor,
            thickness: this.currentThickness,
            lineDashed: this.currentLineDashed
          })}`
        )
      }
    },
    // 涂鸦橡皮擦
    graffitiEraser() {
      if (this.boardInstance) {
        // 设置橡皮擦大小
        this.boardInstance.setEraserWidth(this.eraserSize)
        // 设置橡皮擦模式
        this.boardInstance.setDrawingMode(DrawingMode.eraser)
        this.sendLogger(
          `选择了涂鸦橡皮擦, ${JSON.stringify({
            eraserSize: this.eraserSize
          })}`
        )
      }
    },
    setGraffitiScale: _debounce(function () {
      this.getCurrentCanvasSize()
      if (this.boardInstance) {
        this.boardInstance.resizeCanvas({
          width: this.containerWidth,
          height: this.containerHeight
        })
      }
    }, 300),
    // 提交确认
    async confirm() {
      // classLiveSensor.osta_ia_graffitiboard_submit(this.options?.ircMsg)
      this.sendLogger('涂鸦画板学生点击提交')
      this.$emit('submit')
    },
    // 倒计时
    countDown() {
      // @log-ignore
      if (this.countDownTimeCopy >= 0) {
        let minutes = Math.floor(this.countDownTimeCopy / 60)
        let seconds = Math.floor(this.countDownTimeCopy % 60)
        this.countDownTimeCopy = this.countDownTimeCopy - 1
        this.minutes = minutes.toString().padStart(2, '0')
        this.seconds = seconds.toString().padStart(2, '0')
      } else {
        clearInterval(this.timer)
      }
    },
    /**
     * 日志上报
     */
    sendLogger(msg, params, level = 'info') {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: msg,
          interactType: 'Graffiti',
          interactId: '',
          ...params
        },
        level
      })
    },
    removeListenerSignalService() {
      emitter.off('graffiti_board_watching', this.handleGraffitiBoardWatching)

      // 监听信令消息
      this.SignalService.off('onRecvRoomBinMessageNotice', this.handleOnRecvRoomBinMessageNotice)
      this.SignalService.off('onSendRoomBinMessageResp', this.handleOnSendRoomBinMessageResp)
    },
    beforeDestroy() {
      if (this.boardInstance) {
        this.boardInstance.destroy()
      }
      this.removeListenerSignalService()
      this.timer && clearInterval(this.timer)
    }
  },
  beforeUnmount() {
    const store = useClassData()
    store.updateGraffitiBoardWatchingStutus({}) // 重置老师观看画板信息
    this.removeListenerSignalService()
    if (this.boardInstance) {
      this.boardInstance.destroy()
    }
    window.removeEventListener('resize', this.setGraffitiScale)
    this.timer && clearInterval(this.timer)
  }
}
</script>

<style lang="scss" scoped>
@use '../style/graffiti.scss' as *;
</style>
