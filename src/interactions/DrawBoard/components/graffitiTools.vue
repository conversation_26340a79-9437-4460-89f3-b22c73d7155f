<template>
  <!-- 涂鸦工具栏 -->
  <div class="tools">
    <div class="graffitiTools">
      <i class="draw draw-red select" @click="graffitiDraw('red', '#FF3757', $event)">
        {{ $t('classroom.interactions.graffiti.red') }}
      </i>
      <i class="draw draw-orange" @click="graffitiDraw('orange', '#FF922D', $event)">
        {{ $t('classroom.interactions.graffiti.orange') }}
      </i>
      <i class="draw draw-yellow" @click="graffitiDraw('yellow', '#FAC349', $event)">
        {{ $t('classroom.interactions.graffiti.yellow') }}
      </i>
      <i class="draw draw-green" @click="graffitiDraw('green', '#45CD49', $event)">
        {{ $t('classroom.interactions.graffiti.green') }}
      </i>
      <i class="draw draw-blue" @click="graffitiDraw('blue', '#56A6FF', $event)">
        {{ $t('classroom.interactions.graffiti.blue') }}
      </i>
      <i class="draw draw-purple" @click="graffitiDraw('purple', '#BB53E3', $event)">
        {{ $t('classroom.interactions.graffiti.purple') }}
      </i>
      <i class="draw eraser" @click="graffitiEraser"></i>
    </div>
    <div class="userTools">
      <div class="time">
        <div class="minutes">
          <span class="line"></span><span class="num">{{ minutes }}</span>
        </div>
        <div class="dots"><span></span><span></span></div>
        <div class="seconds">
          <span class="line"></span><span class="num">{{ seconds }}</span>
        </div>
      </div>
      <a-button v-show="!isSubmit" shape="round" class="submit-btn" @click="confirm">
        {{ $t('common.done') }}
      </a-button>
      <a-button v-show="isSubmit" shape="round" class="is-submit" :disabled="true"> </a-button>
    </div>
  </div>
</template>
<script>
import { addClass, removeClass } from '@/utils/util'
export default {
  name: 'GratiffiTools',
  props: {
    // 倒计时 分
    minutes: {
      type: String,
      default: '00'
    },
    // 倒计时 秒
    seconds: {
      type: String,
      default: '00'
    },
    isSubmit: {
      style: Boolean,
      default: false
    }
  },
  methods: {
    // 涂鸦画笔
    graffitiDraw(abstractColor, specificColor, e) {
      if (e) {
        this.setClass(e.target)
      }
      this.$emit('graffitiDraw', abstractColor, specificColor)
    },
    // 橡皮
    graffitiEraser(e) {
      this.setClass(e.target)
      this.$emit('graffitiEraser')
    },
    // 设置工具选中态
    setClass(target) {
      target.parentNode.children.forEach(item => {
        removeClass(item, 'select')
      })
      addClass(target, 'select')
    },
    // 提交
    confirm() {
      this.$emit('confirm')
    }
  }
}
</script>
<style lang="scss" scoped>
@use '../style/graffiti.scss' as *;
.is-submit {
  background: url('@/assets/images/is-sumbmit.png') no-repeat top center;
  padding: 5px 12px;
  height: 32px;
  width: 70px;
  // margin: 0 0 0 52px;
  border: 0 none;
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
