<template>
  <div class="progressContainer" v-if="visible">
    <div
      class="progress"
      :class="classType == 2 ? 'progress-small-class' : 'progress-normal-class'"
    >
      <div class="progress-text">{{ $t('common.uploading') }}… {{ percent }}%</div>
      <a-progress
        :percent="percent"
        :stroke-color="{
          '0%': 'rgba(255,213,24,1)',
          '100%': 'rgba(255,170,10,1)'
        }"
        :strokeWidth="4"
        :showInfo="false"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'photoWallProgress',
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    percent: {
      type: Number,
      default: 0
    },
    classType: {
      type: Number,
      default: 0
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.progressContainer {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 16;
  background: rgba(0, 0, 0, 0.1);
}
.progress {
  box-sizing: border-box;
  z-index: 18;
  position: fixed;
  left: 36.5%;
  top: 30%;
  transform: translate(-50%, 0%);
  background: rgba(15, 25, 42, 0.95);
  border-radius: 10px;
  width: 200px;
  height: 62px;
  &.progress-normal-class {
    left: 36.5%;
  }
  &.progress-small-class {
    left: 44.5%;
  }
}

.ant-progress-line {
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  width: 160px;
  transform: translate(-50%, 0%);
}
.progress-text {
  display: block;
  color: #fff;
  font-size: 14px;
  text-align: center;
  width: 100%;
  padding-top: 10px;
}
</style>
