<template>
  <div class="photoShotContainer">
    <GratiffiTools
      :minutes="minutes"
      :seconds="seconds"
      @graffitiDraw="graffitiDraw"
      @graffitiEraser="graffitiEraser"
      @confirm="confirm"
      :isSubmit="isSubmit"
    />
    <div id="graffitiWrapper" class="graffitiWrapper"></div>
  </div>
</template>

<script>
import Graffiti from '@neosjs/h5-graffiti'
import _debounce from 'lodash/debounce'
import GratiffiTools from './graffitiTools.vue'
import logger from '@/utils/logger'

export default {
  name: 'smallClassGraffiti',
  props: {
    closeInteractive: {
      style: Boolean,
      default: false
    },
    isSubmit: {
      style: Boolean,
      default: false
    },
    countDownTime: {
      type: Number,
      default: 0
    },
    options: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      graffiti: null,
      dialogVisible: false,
      dialogContent: null,
      dialogDescription: null,
      timer: null,
      minutes: '00',
      seconds: '00',
      localCountDownTime: 0,
      graffitiInit: {
        dom: 'graffitiWrapper',
        width: 840,
        height: 630,
        quality: 0.8,
        lineColor: '#ffcf1b',
        background: '',
        lineSize: 3
      },
      eraserSize: 25 // 定义橡皮擦大小
    }
  },
  components: {
    // photoWallDialog
    GratiffiTools
  },
  mounted() {
    // 倒计时
    this.localCountDownTime = this.countDownTime
    this.renderCountDown()
    this.$nextTick(async () => {
      this.initGraffitiDraw()
    })
    window.addEventListener('resize', this.setGraffitiScale)
  },
  computed: {},
  methods: {
    async initGraffitiDraw() {
      // 初始化涂鸦
      const graffitiDom = document.querySelector('#graffitiContainer')
      this.graffitiInit.width = graffitiDom.clientWidth
      this.graffitiInit.height = graffitiDom.clientHeight

      console.log(this.options, 'this.options')
      this.sendLogger(`涂鸦画板this.options : ${JSON.stringify(this.options)}`)
      this.graffitiInit.background = this.options.ircMsg.imageUrl || '#000000'
      this.graffiti = await Graffiti.init(this.graffitiInit)
      this.graffiti.setAttribute('class', 'yellow_penCursor')
      Graffiti.draw()
    },
    renderCountDown() {
      this.countDown()
      this.timer = setInterval(this.countDown, 1000)
    },
    setGraffitiScale: _debounce(function() {
      const graffitiDom = document.querySelector('#graffitiContainer')
      const graffitiWrapperDom = document.querySelector('#graffitiWrapper')
      if (graffitiDom) {
        const canvasDom = graffitiDom.querySelector('canvas')
        const canvasDomImg = graffitiDom.querySelector('img')
        graffitiWrapperDom.firstChild.style.width = graffitiWrapperDom.style.width = canvasDomImg.style.width = canvasDom.style.width =
          graffitiDom.clientWidth + 'px'
        graffitiWrapperDom.firstChild.style.height = graffitiWrapperDom.style.height = canvasDomImg.style.height = canvasDom.style.height =
          graffitiDom.clientHeight + 'px'
      }
    }, 300),
    // 涂鸦画笔
    graffitiDraw(abstractColor, specificColor) {
      this.graffiti.setAttribute('class', `${abstractColor}_penCursor`)
      Graffiti.lineColor(specificColor)
      Graffiti.draw()
    },
    // 涂鸦橡皮擦
    graffitiEraser() {
      this.graffiti.setAttribute('class', 'eraserCursor')
      Graffiti.eraser(this.eraserSize)
    },
    /**
     * 生成涂鸦图片
     */
    async createImage() {
      const res = await Graffiti.createImage()
      console.log('res')
      if (res.errCode === 0) {
        this.sendLogger(`生成涂鸦图片 : ${JSON.stringify(res.data)}`)
        this.$emit('photoData', res.data)
      }
    },
    // 提交确认
    async confirm() {
      this.sendLogger('涂鸦画板学生点击提交')
      await this.createImage()
      this.$emit('submit')
    },
    // 倒计时
    countDown() {
      if (this.localCountDownTime >= 0) {
        let minutes = Math.floor(this.localCountDownTime / 60)
        let seconds = Math.floor(this.localCountDownTime % 60)
        this.localCountDownTime -= 1
        this.minutes = minutes.toString().padStart(2, '0')
        this.seconds = seconds.toString().padStart(2, '0')
      } else {
        clearInterval(this.timer)
      }
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '') {
      logger.send({
        tag: 'student.Interact',
        content: {
          msg: msg,
          interactType: 'Graffiti',
          interactId: '',
          interactStage: stage
        }
      })
    }
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.setGraffitiScale)
    Graffiti.destroy()
    console.log('Graffiti destroy')
    this.sendLogger('关闭互动')
    this.timer && clearInterval(this.timer)
  },
  watch: {
    closeInteractive(val) {
      val && this.createImage()
    }
  }
}
</script>

<style lang="scss" scoped>
@use '../style/graffiti.scss' as *;
</style>
