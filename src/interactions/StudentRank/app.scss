.class-rank {
  background: url('./imgs/board.png') no-repeat;
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: 0 0;
  width: 266px;
  height: 354px;
  z-index: 999;
  display: flex;
  flex-direction: column;
  padding-top: 54px;
  justify-content: center;
  align-items: center;
  &.large-class {
    position: absolute;
    left: 28%;
    top: 50%;
    transform: translateY(-50%);
  }
  &.small-class {
    position: absolute;
    left: 50%;
    top: 15vh;
    transform: translateX(-50%);
  }
  .close {
    width: 30px;
    height: 30px;
    background-image: url('./imgs/icon-close.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: 0 0;
    position: absolute;
    right: -50px;
    z-index: 5;
    top: 55px;
    cursor: pointer;
  }
  .rank-item {
    width: 220px;
    height: 48px;
    margin-bottom: 6px;
    background-image: url('./imgs/itembg.png');
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: 0 0;
    border-radius: 10px;
    display: flex;
    align-items: center;
    overflow: visible;
    position: relative;
    &.active {
      background-image: url('./imgs/itembg-self.png');
    }
    .current-student {
      position: absolute;
      width: 31px;
      height: 18px;
      left: -8px;
      top: -6px;
    }
    .medal {
      width: 34px;
      height: 34px;
      background-size: 100%;
      background-repeat: no-repeat;
      background-position: 0 0;
      // &.level-1 {
      //   background-image: url('./imgs/level-1.png');
      // }
      // &.level-2 {
      //   background-image: url('./imgs/level-2.png');
      // }
      // &.level-3 {
      //   background-image: url('./imgs/level-3.png');
      // }
      // &.level-4 {
      //   background-image: url('./imgs/level-4.png');
      // }
      // &.level-5 {
      //   background-image: url('./imgs/level-5.png');
      // }
      // &.level-6 {
      //   background-image: url('./imgs/level-6.png');
      // }
      // &.level-7 {
      //   background-image: url('./imgs/level-7.png');
      // }
      // &.no-level {
      //   background-image: url('./imgs/no-level.png');
      // }
      &.level-1 {
        background-image: url('@/assets/images/live/badges/middle/level-1.png');
      }
      &.level-2 {
        background-image: url('@/assets/images/live/badges/middle/level-2.png');
      }
      &.level-3 {
        background-image: url('@/assets/images/live/badges/middle/level-3.png');
      }
      &.level-4 {
        background-image: url('@/assets/images/live/badges/middle/level-4.png');
      }
      &.level-5 {
        background-image: url('@/assets/images/live/badges/middle/level-5.png');
      }
      &.level-6 {
        background-image: url('@/assets/images/live/badges/middle/level-6.png');
      }
      &.level-7 {
        background-image: url('@/assets/images/live/badges/middle/level-7.png');
      }
       &.no-level {
        background-image: url('@/assets/images/live/badges/middle/level-0.png');
      }
    }
    padding: 0 10px 0 8px;
    .modal-icon {
      width: 34px;
      height: 34px;
    }
    .name {
      font-size: 14px;
      font-weight: 500;
      color: #172b4d;
      line-height: 18px;
      width: 90px;
      margin-left: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      -o-text-overflow: ellipsis;
      &.active-name {
        color: #ff850a;
      }
    }
    .coins {
      margin-left: auto;
      display: flex;
      align-items: center;
      margin-right: 10px;
      label {
        width: 14px;
        height: 14px;
        display: inline-block;
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-image: url('./imgs/icon-coin.png');
        margin-right: 3px;
      }
      span {
        font-weight: 600;
        color: #ffaa0a;
        line-height: 14px;
      }
    }
  }
}
