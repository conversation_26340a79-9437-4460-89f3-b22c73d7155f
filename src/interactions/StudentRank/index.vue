<!-- 视图层 -->
<template>
  <div v-if="showRankBox" class="class-rank small-class">
    <div @click="closeRankBox" class="close"></div>
    <div class="rank-container">
      <section
        v-for="item in rankList"
        :key="item.id"
        class="rank-item"
        :class="item.id == common.stuId ? 'active' : ''"
      >
        <img v-if="item.id == common.stuId" class="current-student" src="./imgs/self.png" alt="" />
        <!-- <span v-if="item.level > 0" class="medal" :class="`level-${item.level}`"></span> -->
        <!-- <span v-else class="medal no-level"></span> -->
        <span :class="item.id == common.stuId ? 'active-name' : ''" class="name">{{
          item.name
        }}</span>
        <span class="coins">
          <label></label>
          <span>{{ item.coin }}</span>
        </span>
      </section>
    </div>
    <audio
      :src="getAssetPath('/mp3/StudentRank/show-rank.mp3')"
      class="hide"
      ref="showRankSound"
    ></audio>
  </div>
</template>

<script>
import { querySmallClassRankList } from '@/api/classroom/index.js'
import logger from '@/utils/logger'
import { submitInteractPartakereport } from '@/api/classroom/index.js'
import { getAssetPath } from '@/utils/assetPath'
export default {
  name: 'StudentRank',
  props: {
    options: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      timer: null,
      planId: null,
      classId: null,
      interactId: null,
      currentStudentId: null,
      ircMessage: null,
      showRankBox: false,
      rankList: [],
      common: {}
    }
  },
  created() {
    this.common = this.options.roomMessage.roomInfo.commonOption
    this.sendLogger('学生排行榜组件初始化', 'init')
    this.getStudentRankData()
  },

  methods: {
    getAssetPath,
    receiveMessage(ircMessage) {
      this.showRankBox = true
      this.$nextTick(() => {
        this.$refs.showRankSound.play()
      })
      this.getStudentRankData()
      submitInteractPartakereport({
        planId: this.common.planId,
        classId: this.common.classId,
        interactId: ircMessage.interactId
      })
      this.sendLogger(`收到排行榜互动消息 - 互动ID:${ircMessage.interactId}`, 'start')
    },
    destroyInteraction() {
      this.showRankBox = false
      this.rankList = []
      this.sendLogger('排行榜互动结束', 'end')
      this.$emit('close', 'student_rank')
    },
    /**
     * 获取排行榜数据
     */
    async getStudentRankData() {
      let result = await this.querySmallClassRankList()
      if (result?.stat == 1) {
        this.rankList = result.data
        const rankCount = result.data.length
        const currentStudent = result.data.find(item => item.id == this.common.stuId)
        const currentRank = currentStudent
          ? result.data.findIndex(item => item.id == this.common.stuId) + 1
          : '未上榜'
        this.sendLogger(`获取排行榜成功 - 总人数:${rankCount} 我的排名:${currentRank}`, 'data')
      } else {
        this.sendLogger('获取排行榜失败', 'data', 'error')
      }
    },
    async querySmallClassRankList() {
      const params = {
        planId: this.common.planId * 1,
        classId: this.common.classId * 1
      }
      // 获取定向发送的金币
      const res = await querySmallClassRankList(this, params)
      return res
    },
    closeRankBox() {
      this.showRankBox = false
      this.sendLogger('用户手动关闭排行榜', 'close')
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionId = this.options?.ircMsg?.interactId || 'unknown'
      const interactionType = 'StudentRank'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
    }
  }
}
</script>

<style lang="scss" scoped>
@use './app.scss' as *;
</style>
