<!-- 视图层 -->
<template>
  <CountDown id="classRest" v-if="show" :time="time"></CountDown>
</template>
<script>
import logger from '@/utils/logger'
import CountDown from '@/components/countDown/CountDown.vue'
import { emitter } from '@/hooks/useEventBus'

export default {
  name: 'ClassRestView',
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          ircMsg: {},
          roomMessage: {
            roomInfo: {
              commonOption: {}
            }
          }
        }
      }
    }
  },
  components: {
    CountDown
  },
  data() {
    return {
      // 课件休息剩余时间
      leftTime: 0,
      show: false
    }
  },
  computed: {
    time() {
      return this.leftTime * 1000
    }
  },
  mounted() {
    console.info('课间休息-初始化')
    this.sendLogger('课间休息组件初始化', 'mounted')
    this.getLeftTime()
  },
  methods: {
    receiveMessage() {
      console.info('课间休息-老师开启')
      this.sendLogger('收到老师开启课间休息消息', 'start')
      // emitter.emit('activeTip', true, 'classRest')
      // 修改为
      emitter.emit('activeTip', { pub: true, type: 'classRest' })
    },
    destroyInteraction() {
      console.info('课间休息-老师结束')
      this.sendLogger('收到老师结束课间休息消息', 'destroy')
      // emitter.emit('activeTip', false, 'classRest')
      // 修改为
      emitter.emit('activeTip', { pub: false, type: 'classRest' })
      emitter.emit('close', 'classRest')
      this.show = false
    },
    /**
     * 获取剩余休息时间
     */
    getLeftTime() {
      console.log('课间休息-获取剩余时间', this.options.ircMsg.duration)
      if (isNaN(this.options.ircMsg.type)) {
        let currentTime = new Date().getTime()
        this.leftTime =
          this.options.ircMsg.duration -
          (this.options.roomMessage.roomInfo.nowTime - this.options.ircMsg.beginTime) -
          parseInt((currentTime - window._requestBasicTime) / 1000)
      } else {
        this.leftTime = this.options.ircMsg.duration
      }
      console.log('课间休息-this.leftTime', this.leftTime)
      this.sendLogger(`计算剩余休息时间: ${this.leftTime}秒`, 'calculate_time')
      this.show = true
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionId = this.options?.ircMsg?.interactId || 'unknown'
      const interactionType = 'ClassRest'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
    }
  },
  beforeUnmount() {
    this.sendLogger('老师结束课间休息')
  }
}
</script>

<style lang="scss" scoped></style>
