<!-- 视图层 -->
<template>
  <div v-if="!isAnswer">
    <audio :src="voteKeyTone" class="hide" ref="voteKeyTone"></audio>
    <audio :src="voteSubmit" class="hide" ref="voteSubmit"></audio>
    <audio :src="voteHasCorrectFail" class="hide" ref="voteHasCorrectFail"></audio>
    <audio :src="voteHasCorrectSuccess" class="hide" ref="voteHasCorrectSuccess"></audio>
    <transition name="slide-fade" @after-leave="destroy">
      <div v-if="!getResult || (options.ircMsg.pattern !== 3 && !options.ircMsg.hascorrect)">
        <div class="vote" v-if="!getResult">
          <div
            class="vote-bar-container vote-bar-padding"
            ref="voteBarContainer"
            :class="toShow ? 'ease-in' : 'ease-out'"
          >
            <div class="vote-pri-bar-container vote-pri-bar-size" @click="handleToggleBar">
              <img src="./imgs/ease-out-icon.png" v-if="toShow" class="toggle-icon" />
              <img src="./imgs/ease-in-icon.png" v-else class="toggle-icon" />
            </div>
            <div class="vote-wrapper" data-log="选项box">
              <div class="vote-title text-large">
                <p>{{ $t('classroom.interactions.vote.title') }}</p>
              </div>
              <ul
                data-log="选项列表"
                v-if="optionList.length"
                class="vote-options vote-options-margin"
              >
                <li
                  v-for="item in optionList"
                  :key="item.option"
                  class="option-item text-superLarge"
                  :data-log="`选项-互动vote选项-${item.option}`"
                  :class="[
                    { active: curtOption === item.option },
                    {
                      'panduan-option-item':
                        options.ircMsg.pattern === 2 || options.ircMsg.pattern === 3
                    }
                  ]"
                  @click="handleSelectOption(item)"
                >
                  <span>{{
                    item.option
                      .replace('TRUE', $t('classroom.interactions.vote.true'))
                      .replace('FALSE', $t('classroom.interactions.vote.false'))
                  }}</span>
                </li>
              </ul>
              <div class="vote-submit-container vote-submit-height">
                <div
                  class="vote-submit text-large"
                  :class="curtOption ? 'isSelected' : ''"
                  @click.stop="submit(curtOption)"
                >
                  {{ $t('common.submit') }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 投票结果（没有正确答案的题目且不是懂一懂，有正确的题目不展示投票结果，懂一懂提交后不展示结果） -->
        <div v-if="options.ircMsg.pattern !== 3 && !options.ircMsg.hascorrect">
          <div class="vote-result-inCorrect" v-if="statistics.length">
            <div class="vote-bar-container vote-bar-padding">
              <div class="vote-wrapper">
                <div class="vote-title res text-large">
                  <p>
                    {{ $t('classroom.interactions.vote.voteResult') }}
                  </p>
                </div>

                <ul class="vote-options-res vote-options-margin">
                  <li
                    v-for="item in statistics"
                    :key="item.option"
                    class="option-item-res text-superLarge"
                    :class="{ 'panduan-option-item-res': options.ircMsg.pattern === 2 }"
                  >
                    <div class="option-item-res-desc">
                      <div
                        class="option-name text-superLarge"
                        :class="
                          (isRepeatPrevOption || curtOption) === item.option
                            ? 'user-selected-name'
                            : ''
                        "
                      >
                        {{
                          item.option
                            .replace('TRUE', $t('classroom.interactions.vote.true'))
                            .replace('FALSE', $t('classroom.interactions.vote.false'))
                        }}
                      </div>
                      <div
                        class="option-percent text-large"
                        :class="[
                          {
                            'user-selected-percent':
                              (isRepeatPrevOption || curtOption) === item.option
                          },
                          { 'text-middle': options.ircMsg.pattern === 2 }
                        ]"
                      >
                        {{ item.percent }}%
                      </div>
                    </div>
                  </li>
                </ul>
                <div class="vote-res-close-container vote-res-close-height">
                  <div class="icon-close vote-res-close-size" @click="destroy"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import Vote from './vote'
import logger from '@/utils/logger'
import voteKeyTone from './audio/vote-key-tone.mp3'
import voteSubmit from './audio/vote-submit.mp3'
import voteHasCorrectFail from './audio/vote-has-correct-fail.mp3'
import voteHasCorrectSuccess from './audio/vote-has-correct-success.mp3'
import { emitter } from '@/hooks/useEventBus'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'

export default {
  name: 'VotePanel',
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          ircMsg: {},
          roomMessage: {
            roomInfo: {
              commonOption: {}
            }
          }
        }
      }
    }
  },
  data() {
    return {
      toShow: true,
      // 当前提交选项
      curtOption: '',
      skinType: 1,
      // 选项列表
      optionList: [],
      // 实例
      vote: null,
      // 是否得到结果
      getResult: false,
      // 回答是否正确
      isCorrect: false,
      // 获得金币数量
      getGold: 0,
      // 没有正确答案的占比数据
      statistics: [],
      timer: null,
      unSubmit: true,
      isAnswer: true,
      isRepeatPrevOption: '',
      clickVoteDurationInterval: false, //互动期间是否点击了投票
      voteKeyTone,
      voteSubmit,
      voteHasCorrectFail,
      voteHasCorrectSuccess
    }
  },
  mounted() {
    this.sendLogger('投票组件初始化', 'init')
    this.recoverVoteStatus()
    this.optionList = this.vote.handleOptions()
    this.sendLogger(`投票选项初始化完成 - 选项数量:${this.optionList.length}`, 'init')
    this.sendLogger('收到互动', 'start')
  },
  async created() {
    this.sendLogger('创建投票实例', 'create')
    this.timer && clearTimeout(this.timer)
    this.timer = null
    this.vote = new Vote({
      ...this.options
    })
    this.sendLogger(`投票实例创建完成 - 互动ID:${this.options.ircMsg.interactId}`, 'create')
    this.voteReport()
    // 更新投票比例
    emitter.on('getVoteStatistics', noticeContent => {
      this.getVoteStatistics(noticeContent)
    })
  },
  beforeUnmount() {
    this.sendLogger('投票组件销毁', 'destroy')
    emitter.off('getVoteStatistics')
  },
  methods: {
    /**
     * 投票面板展开折叠切换1
     */
    handleToggleBar() {
      this.toShow = !this.toShow
      this.sendLogger(`投票面板${this.toShow ? '展开' : '折叠'}`, 'toggle')
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: 'Vote',
          interactId: this.options.ircMsg.interactId,
          interactStage: stage
        }
      })
    },
    /**
     * 投票上报
     */
    async voteReport() {
      this.sendLogger('发送投票上报请求', 'report')
      const res = await this.vote.voteReport().catch(err => {
        this.sendLogger(`投票上报失败: ${err.message || err}`, 'report', 'error')
        return { code: -1, msg: err.message || err }
      })
      if (res.code != 0) {
        this.sendLogger(`投票上报失败 - 错误码:${res.code} 错误信息:${res.msg}`, 'report', 'error')
        // this.$Message.info(res.msg)
        return
      }
      this.sendLogger('投票上报成功', 'report')
    },
    /**
     * 投票状态
     */
    async recoverVoteStatus() {
      this.sendLogger('查询投票状态', 'status')
      try {
        const res = await this.vote.getVoteStatus()
        if (res.code != 0) {
          this.sendLogger(`投票状态查询失败 - 错误码:${res.code}`, 'status', 'error')
          // this.$Message.info(res.msg)
          this.sendLogger('互动状态查询接口code不为0', 'start', 'error')
        } else {
          this.sendLogger(
            `投票状态查询成功 - 之前选项:${res.data.option} 投票状态:${res.data.voteState}`,
            'status'
          )
        }
        this.isRepeatPrevOption = res.data.option
        this.isAnswer = res.data.voteState
      } catch (err) {
        this.isAnswer = false
        this.sendLogger(`投票状态查询异常: ${err.message || err}`, 'status', 'error')
        this.sendLogger('互动状态查询接口报错', 'start', 'error')
      }
    },
    /**
     * 当前投票选择项
     */
    handleSelectOption(opt) {
      this.sendLogger(`点击-选择了互动题选项-${opt.option}`)
      if (!this.getResult) {
        this.$refs.voteKeyTone.play()
        this.curtOption = opt.option
        this.sendLogger(`选择投票选项: ${opt.option}`, 'select')
      } else {
        this.sendLogger('投票已结束，忽略选择操作', 'select', 'warn')
      }
    },
    /**
     * 提交投票结果
     */
    async submit(isClick) {
      if (isClick && this.unSubmit) {
        this.sendLogger(`开始提交投票 - 选项:${this.curtOption}`, 'submit')
        this.$refs.voteSubmit.play()
        this.unSubmit = false
        const res = await this.vote.submitVote(this.curtOption).catch(err => {
          this.sendLogger(`投票提交失败: ${err.message || err}`, 'submit', 'error')
          this.unSubmit = true
          return { code: -1, msg: err.message || err }
        })
        classLiveSensor.osta_ia_vote_submit(this.options?.ircMsg, res?.data)
        this.sendLogger(`投票数据提交结果: ${JSON.stringify(res)}`)
        this.clickVoteDurationInterval = true // 互动期间点击了投票
        this.unSubmit = true
        this.getResult = true
        if (res.code != 0) {
          this.sendLogger(
            `投票提交失败 - 错误码:${res.code} 错误信息:${res.msg}`,
            'submit',
            'error'
          )
          // TODO
          // // this.$Message.info(res.msg)
          return
        }
        this.sendLogger('投票提交成功', 'submit')
        this.handleSubmit(res.data)
      } else if (!this.unSubmit) {
        this.sendLogger('重复提交投票，忽略', 'submit', 'warn')
      } else {
        this.sendLogger('无效的提交操作', 'submit', 'warn')
      }
    },
    async handleSubmit(data) {
      let { answerStat, gold, isRepeat } = data
      this.getGold = gold
      this.sendLogger(
        `处理投票结果 - 答案状态:${answerStat} 金币:${gold} 是否重复:${isRepeat}`,
        'result'
      )
      // 判断是不是重复提交过
      if (isRepeat) {
        this.sendLogger('检测到重复提交', 'result', 'warn')
        // this.$Message.warn(this.$t('classroom.interactions.vote.repeatSubmitMessage'))
      }
      // 有正确答案逻辑
      if (this.options.ircMsg.hascorrect) {
        this.sendLogger('题目有正确答案，处理答题结果', 'result')
        // answerStat	答案状态：0-教师未设置正确答案 1-正确 2-错误
        if (!isRepeat) {
          this.isCorrect = answerStat == 1 ? true : false
          const resultText = answerStat == 1 ? '正确' : '错误'
          this.sendLogger(`答题结果: ${resultText}`, 'result')
          answerStat == 1
            ? this.$refs.voteHasCorrectSuccess?.play()
            : this.$refs.voteHasCorrectFail?.play()
          emitter.emit('continuousCorrect', {
            ...data,
            isNoMedal: true // 不展示勋章
          })
          this.sendLogger('触发连续答对事件', 'event')
          setTimeout(() => {
            this.sendLogger('3秒后自动关闭投票', 'timer')
            this.destroy()
          }, 3000)
        } else {
          this.sendLogger('重复提交，直接关闭', 'result')
          this.destroy()
        }
      } else {
        this.sendLogger('题目无正确答案', 'result')
      }
    },
    /**
     * 获取投票结果
     */
    async getVoteStatistics(result) {
      this.sendLogger(`收到投票统计结果: ${JSON.stringify(result)}`, 'statistics')
      this.statistics = []

      if (this.getResult) {
        for (let ind in result) {
          this.statistics.push(result[ind])
        }
        this.getResult = true
        this.sendLogger(`投票统计处理完成 - 选项数量:${this.statistics.length}`, 'statistics')
      } else {
        this.sendLogger('投票未结束，跳过统计处理', 'statistics', 'warn')
      }
    },

    destroy() {
      this.sendLogger('销毁投票组件', 'destroy')
      this.$emit('close', 'vote')
    },
    // 教师端主动关闭互动
    async destroyInteraction() {
      this.sendLogger('教师端主动关闭互动', 'destroy')
      console.log('教师端主动关闭互动')
      // 互动结束未点击投票-上报数据
      if (!this.clickVoteDurationInterval && !this.isAnswer) {
        this.sendLogger('互动结束时处理未提交的投票', 'destroy')
        if (this.curtOption) {
          this.sendLogger(`自动提交选择的选项: ${this.curtOption}`, 'auto_submit')
          await this.submit(this.curtOption)
          this.timer = setTimeout(async () => {
            this.destroy()
          }, 4000)
        } else {
          this.sendLogger('无选择选项，提交未作答状态', 'auto_submit')
          await this.vote.getUnSubmit()
          this.sendLogger(`互动结束，投票未提交`)
          this.destroy()
        }
      } else {
        this.sendLogger('互动期间已有操作，直接关闭', 'destroy')
        this.destroy()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use './style.scss' as *;
</style>
