// 业务逻辑

import {
  voteCommit,
  getVoteStatistics,
  interactVoteReport,
  recoverVoteStatus,
  unSubmmit
} from '@/api/interaction/vote'
/**
 * @description
 */
export default class Main {
  constructor(opts = {}) {
    // @log-ignore
    this.options = opts
    // 临时测试用
    // this.options.ircMsg.pattern = 3
    this.skinType = this.options.roomMessage.roomInfo.commonOption
    console.log('看一下传参值', this.options)
    // {
    //     "roomMessage": {
    //       "roomInfo": {
    //           "nowTime": 1747980901,
    //           "planInfo": {
    //               "id": 222040,
    //               "name": "课堂测试小班(在线)-Num 1",
    //               "type": 1,
    //               "pattern": 13,
    //               "startStampTime": 1747966740,
    //               "endStampTime": 1748015940,
    //               "subjectIds": "8",
    //               "subjectName": "Math",
    //               "gradeIds": "8",
    //               "gradeName": "Math",
    //               "classType": 1,
    //               "packageId": 26787,
    //               "isBuiltinClass": false
    //           },
    //           "stuLiveInfo": {
    //               "stuId": 7594599,
    //               "bizId": 1,
    //               "planId": 222040,
    //               "courseId": 37416,
    //               "classId": 37416,
    //               "tutorId": 1779,
    //               "teamId": 37416,
    //               "isAudition": 0
    //           },
    //           "stuInfo": {
    //               "id": 7594599,
    //               "userName": "tt Hhj",
    //               "nickName": "Hank87",
    //               "englishName": "Hank87",
    //               "sex": 3,
    //               "avatar": "https://download-pa-s3.thethinkacademy.com/415/20201222/1608632880261jJTGYhptsq.png",
    //               "goldNum": 5995,
    //               "level": 7,
    //               "title": "Level 7",
    //               "correctCount": 9,
    //               "prop": {
    //                   "levelId": 1,
    //                   "levelName": "青木",
    //                   "subLevel": 1,
    //                   "head": null,
    //                   "face": null,
    //                   "hand": null,
    //                   "avatarFrame": {
    //                       "resourceId": "",
    //                       "resourceUrl": "https://pa-oss-cn-prod.thethinkacademy.com/multicloud-test/3/20231215110847/0e6bb1eba5a6cdbe1710a86bee882c9e/little_pink_rabbit_avatar_frame.png",
    //                       "resourceMd5": "0eb35ce4d4ce2226a44a1d7b4844e98b",
    //                       "pcResourceUrl": "",
    //                       "pcResourceMd5": ""
    //                   },
    //                   "messageBoxFrame": null,
    //                   "videoFrame": null,
    //                   "inUseFigureGoods": false
    //               }
    //           },
    //           "teacherInfo": {
    //               "id": 1779,
    //               "name": "gsw1",
    //               "nickName": "gsw1",
    //               "sex": 2,
    //               "avatar": "https://download-pa-s3.thethinkacademy.com/us/20201210/1607567234857VNzazvAyb1.png"
    //           },
    //           "counselorInfo": {
    //               "id": 1779,
    //               "name": "gsw1",
    //               "nickName": "gsw1",
    //               "sex": 2,
    //               "avatar": "https://download-pa-s3.thethinkacademy.com/us/20201210/1607567234857VNzazvAyb1.png"
    //           },
    //           "liveStatus": {
    //               "startClass": true,
    //               "streamMode": 1,
    //               "classroomStatus": 0
    //           },
    //           "configs": {
    //               "rtcConfig": {
    //                   "teacherRoomId": 2025560741,
    //                   "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb29tIjoyMDI1NTYwNzQxLCJyb29tU3RyIjoiMjAyNTU2MDc0MSIsInVzZXIiOjc1OTQ1OTksInBzdWVyIjoiNzU5NDU5OSIsImFwcGlkIjoiOTRiYzQwZjZmMTNhNDEwOWJmMWM5NGU1ZTNkOTI2M2IiLCJ0eXBlIjoxLCJ0b2tlbiI6IjAwNjk0YmM0MGY2ZjEzYTQxMDliZjFjOTRlNWUzZDkyNjNiSUFEbkJjRG5YQlVEeGpiZm1zcEZnODRNTFNhK2JoMVpEUXJxbjNtZnBFSUlaaFVISTkxMFJVWUVJZ0JXWnpJQlZTa3hhQVFBQVFBQUFBQUFBZ0FBQUFBQUF3QUFBQUFBQkFBQUFBQUEiLCJhdHRhY2hBcHBpZCI6Ijk0YmM0MGY2ZjEzYTQxMDliZjFjOTRlNWUzZDkyNjNiIiwiYXR0YWNoVG9rZW4iOiIwMDY5NGJjNDBmNmYxM2E0MTA5YmYxYzk0ZTVlM2Q5MjYzYklBRG5CY0RuWEJVRHhqYmZtc3BGZzg0TUxTYStiaDFaRFFycW4zbWZwRUlJWmhVSEk5MTBSVVlFSWdCV1p6SUJWU2t4YUFRQUFRQUFBQUFBQWdBQUFBQUFBd0FBQUFBQUJBQUFBQUFBIiwicGxhbmlkIjoyMjIwNDAsInByb2R1Y2luZyI6ZmFsc2UsInRpbWVzdGFtcCI6MTc0Nzk2NTkwOSwidHRsIjoyNH0.3w8LyLLHij_hgoyZkZMq7d0m06VndEspAIo7IZgL_VQ",
    //                       "teacherVideoUid": "1000011779",
    //                       "teacherAudioUid": "1000021779",
    //                       "teacherUid": "1000051779",
    //                       "teacherShareUid": "1000061779",
    //                       "tutorToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb29tIjozODg0NzUyNDMzLCJyb29tU3RyIjoiMzg4NDc1MjQzMyIsInVzZXIiOjc1OTQ1OTksInBzdWVyIjoiNzU5NDU5OSIsImFwcGlkIjoiOTRiYzQwZjZmMTNhNDEwOWJmMWM5NGU1ZTNkOTI2M2IiLCJ0eXBlIjoxLCJ0b2tlbiI6IjAwNjk0YmM0MGY2ZjEzYTQxMDliZjFjOTRlNWUzZDkyNjNiSUFCdnA5MmdGcW93R0YrYlBNc1BpRFdFMXI2QXArWFUyL2pSRXBQeTQza1ZCcTJENXIxMFJVWUVJZ0EyMXZnQlZTa3hhQVFBQVFBQUFBQUFBZ0FBQUFBQUF3QUFBQUFBQkFBQUFBQUEiLCJhdHRhY2hBcHBpZCI6Ijk0YmM0MGY2ZjEzYTQxMDliZjFjOTRlNWUzZDkyNjNiIiwiYXR0YWNoVG9rZW4iOiIwMDY5NGJjNDBmNmYxM2E0MTA5YmYxYzk0ZTVlM2Q5MjYzYklBQnZwOTJnRnFvd0dGK2JQTXNQaURXRTFyNkFwK1hVMi9qUkVwUHk0M2tWQnEyRDVyMTBSVVlFSWdBMjF2Z0JWU2t4YUFRQUFRQUFBQUFBQWdBQUFBQUFBd0FBQUFBQUJBQUFBQUFBIiwicGxhbmlkIjoyMjIwNDAsInByb2R1Y2luZyI6ZmFsc2UsInRpbWVzdGFtcCI6MTc0Nzk2NTkwOSwidHRsIjoyNH0.WDrBLngAcwoaTaODvfSif0zl6KXU18nffJdWQtSa-0M",
    //                       "tutorUid": "1000031779",
    //                       "tutorRoomId": 3884752433,
    //                       "tutorIsRtc": 1,
    //                       "classToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb29tIjoxODY5MDUwNDgsInJvb21TdHIiOiIxODY5MDUwNDgiLCJ1c2VyIjo3NTk0NTk5LCJwc3VlciI6Ijc1OTQ1OTkiLCJhcHBpZCI6Ijk0YmM0MGY2ZjEzYTQxMDliZjFjOTRlNWUzZDkyNjNiIiwidHlwZSI6MSwidG9rZW4iOiIwMDY5NGJjNDBmNmYxM2E0MTA5YmYxYzk0ZTVlM2Q5MjYzYklBRHVZekZ1b1k5Rk1GbWt5MU95RWVqL1ZwUVJHOXhZL3BKejlLeUx6UWcvN1JaUU9yQjBSVVlFSWdDaXVLb0RWaWt4YUFRQUFRQUFBQUFBQWdBQUFBQUFBd0FBQUFBQUJBQUFBQUFBIiwiYXR0YWNoQXBwaWQiOiI5NGJjNDBmNmYxM2E0MTA5YmYxYzk0ZTVlM2Q5MjYzYiIsImF0dGFjaFRva2VuIjoiMDA2OTRiYzQwZjZmMTNhNDEwOWJmMWM5NGU1ZTNkOTI2M2JJQUR1WXpGdW9ZOUZNRm1reTFPeUVlai9WcFFSRzl4WS9wSno5S3lMelFnLzdSWlFPckIwUlVZRUlnQ2l1S29EVmlreGFBUUFBUUFBQUFBQUFnQUFBQUFBQXdBQUFBQUFCQUFBQUFBQSIsInBsYW5pZCI6MjIyMDQwLCJwcm9kdWNpbmciOmZhbHNlLCJ0aW1lc3RhbXAiOjE3NDc5NjU5MDksInR0bCI6MjR9.Lr_AEGtggzAT0zfWIOC2n1FK17ldYmgUFc8wrN-R0x4",
    //                       "classRoomId": 186905048,
    //                       "stickerToken": "-68,-119,34,-1,-124,109,-20,75,-109,28,27,34,-14,67,117,74,-18,109,-19,46,120,-86,24,-37,-47,-120,-47,111,-99,6,-83,88,85,-45,-36,79,-6,126,-104,-126,-54,-111,-31,-14,33,-69,-81,-105,-92,-105,76,-71,113,66,81,-80,-98,87,54,115,91,106,-97,26,1,112,-104,8,-37,56,-91,-30,55,-97,40,-2,94,94,39,107,-124,-47,8,118,3,-12,-60,-108,122,-45,-71,-69,-19,-81,-1,87,34,56,-75,14,44,-25,-14,122,14,72,-61,-55,-74,-70,28,92,-67,-14,73,59,38,116,-83,99,-47,76,-23,84,21,18,5,-32,-119,1,82,-119,14,-15,81,16,72,-93,-2,102,-45,-89,81,58,-104,94,-51,19,-21,108,118,109,-66,98,-126,91,-72,50,42,-28,16,-40,100,-14,-88,-47,37,93,-95,113,22,-76,77,-53,-108,-56,51,-62,72,32,59,-115,84,25,-18,-107,80,28,102,-43,122,28,-98,-78,22,64,-43,-114,2,-112,70,34,40,48,-87,32,6,42,85,-30,-114,-120,101,127,123,123,42,-16,-51,-77,81,1,-106,-42,80,-114,-79,-114,-14,44,74,27,-13,-33,-107,26,116,64,-124,-115,75,-46,-74,-21,-13,-71,-11,-16,-122,19,49,-94,15,26,-28,126,21,-103,-71,77,-103,-93,122,72,11,-2,0,101,20,24,71,105,-17,-8,-87,78,105,-96,-53,-62,87,55,104,84,99,-87,22,-72,58,111,-85,67,72,76,-68,67,-53,-34,70,-73,-28,40,-57,68,-32,76,-108,-86,-125,-13,-69,-113,-97,-48,104,-103,15,-29,46,47,56,-27,-96,109,6,93,-80,69,75,-67,-11,47,-109,-12,-111,56,50,-79,-100,-85,102,55,-107,104,-94,-10,-104,-125,-42,-4,-2,-31,50,-65,-25,117,-24,106,91,115,-66,13,-93,6,-84,9,97,5,46,-6,-63,-40,57,39,-80,-106,-86,-77,122,-48,57,111,-24,-48,-128,23,52,42,60,-59,-27,93,0,-93,-30,-107,-19,50,-126,-68,-6,17,-49,-123,-70,-40,34,123,89,13,-2,-57,-34,31,114,125,8,41,-32,-29,-65,-89,89,-17,59,-50,-24,54,-12,123,-17,-68,-110,89,-12,-86,-77,21,111,48,98,110,-55,-61,84,-57,50,14,-115,-105,123,26,81,99,-5,9,-76,90,-98,87,34,78,-121,0,-124,20,32,-107,35,-109,41,71,25,-83,10,-11,121,-112,81,-60,-109,90,-12,-51,65,100,91,-33,-50,113,-98,-62,27,-45,-3,92,-54,-78,44,-111,53,-20,117,-120,63,109,-31,-82,54,-113,30,76,65,67,92,-86,84,122,-84,98,80,-41,-30,-49,85,-123,24,7,-82,-20,79,-59,-99,67,2,91,-116,-79,67,-113,-24,63,-15,104,-45,-6,59,-28,106,118,-99,119,-15,100,11,125,-24,104,-80,125,-49,-38,122,121,-4,-28,-76,-55,-101,98,122,15,22,-51,30,-20,107,79,127,-108,64,42,-10,-91,62,-7,64,106,51,21,118,35,8,9,80,-93,90,-98,85,108,11,23,-34,120,-87,86,41,11,24,2,-95,72,40,-115,45,-43,93,6,-30,-84,-109,52,100,-86,-116,99,36,29,99,-67,82,25,-120,42,-70,-6,-13,-11,30,-97,-39,-56,-101,-9,5,97,-116,-35,5,117,-50,-87,-93,-106,-90,17,37,108,-62,58,-76,-121,-22,54,58,-5,78,74,-64,-9,18,-105,-118,-5,-33,77,-32,41,18,113,-26,15,-62,-62,89,77,-103,-95,-90,26,23,-51,-58,125,7,-128,-20,65,102,-110,-6,96,6,91,-116,77,72,110,-56,43,-22,73,54,51,-23,-20,-15,-76,105,-35,-9,-40,-79,107,-92,-95,75,38,-9,63,126,69,105,29,-72,100,126,54,-120,114,-60,12,-30,40,108,87,105,-117,-18,57,99,106,105,82,-26,-99,43,55,123,-36,-69,13,-2,25,6,48,21,2,-62,-57,-13,-97,127,89,11,84,-73,-15,-50,-108,-69,49,-12,75,-2,-61,-22,47,-123,2,122,-78,87,-32,114,-125,-72,127,-104,89,-31,-121,7,-109,70,106,-12,-89,112,-52,-83,56,17,21,62,-34,77,9,-88,30,29,-3,-33,111,-57,-59,-65,-14,63,-88,87,53,-31,76,-82,-41,96,124,62,-16,-54,-117,-91,84,120,-78,83,58,4,-16,62,-57,-20,77,-107,-10,56,65,41,-3,-114,99,9,-64,-35,110,85,-74,68,-31,19,104,34,-66,49,-84,-20,57,56,19,86,-62,89,-85,78,35,117,-26,-79,127,-45,26,-107,-71,-123,-106,-69,77,70,-52,-110,30,11,14,54,-11,-77,63,-30,98,-123,-22,-14,59,110,-24,-98,-73,25,-46,-1,-7,96,58,-50,121,53,-41,54,3,47,113,-57,78,115,57,-46,30,-82,-45,104,-122,-58,-64,127,-54,-47,100,-73,-43,-81,123,-110,-34,-26,87,127,106,42,-65,65,-84,-29,-3,-37,-61,38,-103,92,-85,-53,33,-82,-106,6,-56,114,-49,-43,-32,-94,-90,-121,-40,28,-71,-42,98,85,-89,59,-29,-110,-125,3,-24,117,-10,-85,74,-12,28,82,35,-70,108,-100,-72,60,-12,64,18,-17,25,5,-108,-64,-8,-108,45,61,94,19,-12,-81,-72,-80,63,110,8,101,-103,61,122,-101,-24,90,-33,125,-39,-5,-44,-20,-98,-36,-95,114,85,7,-126,-93,81,1,-61,58,4,-60,-94,-4,-39,-123,85,95,19,-24,38,54,-110,83,-57,105,-48,-8,52,28,-102,43,95,71,42,-74,-74,-91,2,-87,91,109,-26,30,-87,47,-86,-77,-53,24,-59,-104,-8,-55,-100,90,101,87,68,1,89,-80,-27,-114,24,-78,-107,-80,-43,65,-63,48,28,-114,-118,-62,7,-126,109,9,50,-8,12,43,-8,-108,87,-102,84,123,46,24,-99,-114,-42,-25,38,6,53,-101,-99,-4,41,-93,89,95,9,67,87,-60,-86,127,34,57,-68,-24,-25,-25,-15,-59,53,-51,115,-62,125,49,106,89,109,35,-69,33,-50,41,-27,-33,117,-95,-10,-14,90,87,51,86,1,-47,-106,112,75,-94,99,54,-39,108,2,-76,-124,-27,-23,-32,52,124,124,-43,-31,-102,127,3,-39,-70,-50,38,-113,58,-39,-108,27,28,13,-118,51,-20,-123,18,-3,42,79,-58,-88,-73,100,-50,-26,-83,-12,46,121"
    //                   },
    //                   "liveTypeId": "3",
    //                   "appId": "yw10001",
    //                   "appKey": "MmU3MmNjYWIyMjgyYTM0Yg",
    //                   "ircAk": "yw10001",
    //                   "ircSk": "ZmI2YTkzYzU1ZDE4NWRmZA",
    //                   "ircApiHost": "https://public-test.msg.xescdn.com",
    //                   "businessId": 3,
    //                   "stuIrcId": "s_1_222040_7594599",
    //                   "tutorIrcId": "f_1_222040_1779_2",
    //                   "ircRooms": [
    //                       "#1L222040-37416"
    //                   ],
    //                   "urls": {
    //                       "initModuleUrl": ""
    //                   },
    //                   "isForbidden": false,
    //                   "class": {
    //                       "stuList": [
    //                           7606168,
    //                           7595762,
    //                           7594593,
    //                           7594594,
    //                           7594595,
    //                           7594596,
    //                           7594597,
    //                           7594598,
    //                           7594599,
    //                           7592535,
    //                           7594592,
    //                           7595375,
    //                           7596002,
    //                           7606204,
    //                           7606205,
    //                           7606210,
    //                           7606211,
    //                           7606223,
    //                           7606232,
    //                           7606324,
    //                           7607565,
    //                           7607596
    //                       ]
    //                   },
    //                   "wallCanCorrect": true,
    //                   "raiseHand": true,
    //                   "teacherIrcId": "t_1_222040_1779_2",
    //                   "classStudentList": [
    //                       7606168,
    //                       7595762,
    //                       7594593,
    //                       7594594,
    //                       7594595,
    //                       7594596,
    //                       7594597,
    //                       7594598,
    //                       7594599,
    //                       7592535,
    //                       7594592,
    //                       7595375,
    //                       7596002,
    //                       7606204,
    //                       7606205,
    //                       7606210,
    //                       7606211,
    //                       7606223,
    //                       7606232,
    //                       7606324,
    //                       7607565,
    //                       7607596
    //                   ],
    //                   "ircServer": {
    //                       "confService": {
    //                           "protocol": "https",
    //                           "hostname": "chatconf-test.msg.xescdn.com",
    //                           "backupIp": "0.0.0.0",
    //                           "url": "/chat/v1/getConfig",
    //                           "port": 443
    //                       },
    //                       "confServiceV3": {
    //                           "protocol": "https",
    //                           "hostname": "chatconf-test.msg.xescdn.com",
    //                           "backupIp": "0.0.0.0",
    //                           "url": "/v4/proxy/config",
    //                           "port": 443
    //                       },
    //                       "logService": {
    //                           "protocol": "https",
    //                           "hostname": "logirc.thethinkacademy.com",
    //                           "backupIp": "*************",
    //                           "url": "/log",
    //                           "port": 443
    //                       },
    //                       "location": "China-gaap",
    //                       "locationV3": "Philippines",
    //                       "ircLocation": "China-gaap"
    //                   }
    //               },
    //               "classType": 2
    //           }
    //       }
    //   },
    //   "ircMsg": {
    //       "answer": "",
    //       "pattern": 3,
    //       "interactId": "f8e5918f8dc3fd5265bda6a8db79cd3d",
    //       "options": [
    //           {
    //               "option": "YES",
    //               "isAnswer": false,
    //               "gold": 0,
    //               "height": 0,
    //               "percent": 0
    //           },
    //           {
    //               "option": "NO",
    //               "isAnswer": false,
    //               "gold": 0,
    //               "height": 0,
    //               "percent": 0
    //           }
    //       ],
    //       "pub": true,
    //       "hascorrect": false,
    //       "hasCorrect": false,
    //       "correctIndex": null
    //   },
    //   "isHistory": false,
    //   "sendTime": 1747981516760
    // }
  }

  handleOptions() {
    const { ircMsg } = this.options
    ircMsg.options = Object.keys(ircMsg.options).map(key => {
      const item = ircMsg.options[key]
      item.height = 0
      item.percent = 0
      return item
    })
    return ircMsg.options
  }

  async submitVote(option) {
    const { planId, classId } = this.options.roomMessage.roomInfo.stuLiveInfo
    const { interactId } = this.options.ircMsg
    const params = {
      planId: planId * 1,
      classId: classId * 1,
      interactionId: interactId,
      option
    }
    // 提交选择的数据到服务端
    const res = await voteCommit(params)
    return res
  }

  async getVoteStatistics() {
    const { planId } = this.options.roomMessage.roomInfo.stuLiveInfo
    const { interactId } = this.options.ircMsg
    const params = {
      planId: planId * 1,
      interactionId: interactId
    }
    // 提交选择的数据到服务端
    const res = await getVoteStatistics(params)
    return res
  }

  async getVoteStatus() {
    // 投票状态
    const { planId } = this.options.roomMessage.roomInfo.stuLiveInfo
    const { interactId } = this.options.ircMsg
    const params = {
      planId: planId * 1,
      interactionId: interactId
    }
    try {
      const res = await recoverVoteStatus(params)
      return res
    } catch (error) {
      return Promise.reject(error)
    }
  }

  async voteReport() {
    const { planId, classId } = this.options.roomMessage.roomInfo.stuLiveInfo
    const { interactId } = this.options.ircMsg
    const params = {
      planId: planId * 1,
      classId: classId * 1,
      interactId: interactId
    }
    // 上报投票
    const res = await interactVoteReport(params)
    return res
  }

  async getUnSubmit() {
    // 未提交数据收集
    const { planId } = this.options.roomMessage.roomInfo.stuLiveInfo
    const { interactId } = this.options.ircMsg
    const params = {
      planId: planId * 1,
      interactionId: interactId
    }
    const res = await unSubmmit(params)
    return res
  }
}
