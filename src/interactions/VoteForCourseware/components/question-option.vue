<template>
  <div class="vote-options-courseware" data-log="单选多选判断面板">
    <Single
      v-if="quesTypeId === 1"
      :options="optionList"
      v-model="userAnswer"
      @change="handleChange"
    ></Single>
    <Multiple
      v-if="quesTypeId === 2"
      :options="optionList"
      v-model="userAnswer"
      @change="handleChange"
    ></Multiple>
    <Judge
      v-if="quesTypeId === 5"
      :options="judgeOptions"
      @change="handleChange"
      :value="userAnswer"
    ></Judge>
  </div>
</template>

<script>
import Multiple from './Multiple.vue'
import Single from './Single.vue'
import Judge from './Judge.vue'
export default {
  name: 'QuestionOption',
  props: {
    optionList: {
      type: Array,
      default: () => {
        return []
      }
    },
    quesTypeId: {
      type: Number,
      default: 0
    }
  },
  components: {
    Multiple,
    Single,
    Judge
  },
  data() {
    return {
      userAnswer: [],
      judgeOptions: [
        {
          label: this.$t('classroom.interactions.coursewareBoard.true'),
          value: 'T'
        },
        {
          label: this.$t('classroom.interactions.coursewareBoard.false'),
          value: 'F'
        }
      ]
    }
  },
  methods: {
    handleChange(val) {
      this.$emit('change', val)
    }
  }
}
</script>
