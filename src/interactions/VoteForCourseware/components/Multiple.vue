<template>
  <div class="vote-options">
    <div
      v-for="(option, index) in options"
      :key="index"
      class="option-item"
      :class="{ active: selectedOptions.includes(option) }"
      :data-log="`选项-课件内多选选项-${option}`"
      @click="toggleOption(option)"
    >
      <span>{{ option }}</span>
    </div>
  </div>
</template>

<script>
import logger from '@/utils/logger'
export default {
  name: 'MultipleOption',
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  mounted() {
    console.log('optionList', this.optionList)
  },
  data() {
    return {
      selectedOptions: this.value
    }
  },
  watch: {
    value(newValue) {
      this.selectedOptions = newValue
    }
  },
  methods: {
    sendLogger(msg, level = 'info') {
      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: 'Vote'
        }
      })
    },
    toggleOption(option) {
      const index = this.selectedOptions.indexOf(option)
      this.sendLogger(`点击-课件内多选选项-${option},是否选过:${index}`)

      if (index === -1) {
        this.selectedOptions.push(option)
      } else {
        this.selectedOptions.splice(index, 1)
      }
      console.log('this.selectedOptions', this.selectedOptions)
      this.$emit('input', this.selectedOptions)
      this.$emit('change', this.selectedOptions)
    }
  }
}
</script>

<style lang="scss" scoped>
.vote-options {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;

  .option-item {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    border-radius: 12px;
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    border: 2px solid transparent;

    span {
      white-space: nowrap;
      color: #333;
      font-weight: bold;
    }

    &:hover {
      background: rgba(255, 255, 255, 1);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &.active {
      background: #fff !important;
      color: #FF6B00 !important;
      border-color: #fff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      span {
        color: #FF6B00 !important;
        font-weight: bold !important;
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .vote-options {
    gap: 10px;

    .option-item {
      width: 55px;
      height: 55px;
      font-size: 16px;
    }
  }
}
</style>
