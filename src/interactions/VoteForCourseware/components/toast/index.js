import { createApp } from 'vue'
import Toast from './toast.vue'

let instance
let seed = 1

const ToastBox = options => {
  options = options || {}
  if (typeof options === 'string') {
    options = {
      message: options
    }
  }
  let id = 'toast_' + seed++

  // 创建挂载点
  const container = document.createElement('div')
  document.getElementById('interactionController').appendChild(container)

  // 创建应用实例
  const app = createApp(Toast, {
    ...options,
    id,
    visible: true
  })

  // 挂载应用
  instance = app.mount(container)

  return instance
}

ToastBox.close = () => {
  if (instance) {
    instance.visible = false
  }
}

export default ToastBox
