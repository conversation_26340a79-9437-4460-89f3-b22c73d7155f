<template>
  <transition @after-leave="handleAfterLeave">
    <div
      v-if="visible"
      class="coursewae-toast"
      :class="[typeClass]"
      @mouseenter="clearTimer"
      @mouseleave="startTimer"
    >
      <h2>{{ title }}</h2>
      <p>{{ message }}</p>
      <div class="coins-number" v-if="type === 'right'"><i></i> +{{ coin }}</div>
    </div>
  </transition>
</template>
<script>
const typeMap = {
  right: 'right',
  wrong: 'wrong'
}
export default {
  data() {
    return {
      visible: false,
      type: 'right',
      title: '',
      message: '',
      coin: 0,
      duration: 0,
      closed: false,
      onClose: null,
      timer: null
    }
  },
  computed: {
    typeClass() {
      return `is-${typeMap[this.type]}`
    }
  },
  mounted() {
    this.startTimer()
  },
  methods: {
    handleAfterLeave() {
      this.$destroy(true)
      this.$el?.parentNode?.removeChild(this.$el)
    },

    close() {
      this.closed = true
      if (typeof this.onClose === 'function') {
        this.onClose(this)
      }
    },

    clearTimer() {
      clearTimeout(this.timer)
    },

    startTimer() {
      if (this.duration > 0) {
        this.timer = setTimeout(() => {
          if (!this.closed) {
            this.close()
          }
        }, this.duration)
      }
    }
  },
  watch: {
    closed(newVal) {
      if (newVal) {
        this.visible = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.coursewae-toast {
  transform: translate(-50%, -60%);
  width: 260px;
  position: absolute;
  left: 50%;
  top: 50%;
  display: flex;
  justify-content: flex-end;
  flex-direction: column;
  text-align: center;
  &.is-right {
    height: 195px;
    background: url(../../img/toast_right.png) no-repeat center center;
    background-size: 100%;
    p {
      font-size: 20px;
      font-family: 'SFProRounded-Medium', 'SFProRounded', 'PingFang SC', 'sans-serif', 'Arial',
        'Verdana', 'Microsoft YaHei';
      font-weight: 500;
      color: rgba(255, 255, 255, 0.8);
      line-height: 28px;
    }
  }
  &.is-wrong {
    height: 135px;
    background: url(../../img/toast_wrong.png?v=1) no-repeat center center;
    background-size: 100%;
    display: block;
    h2 {
      padding: 25px 0 35px 0;
    }
    p {
      font-size: 14px;
      font-weight: 500;
      color: #ffffff;
      padding: 0 10px;
      line-height: 1.2;
      font-family: 'SFProRounded-Medium', 'SFProRounded', 'PingFang SC', 'sans-serif', 'Arial',
        'Verdana', 'Microsoft YaHei';
    }
  }
  .coins-number {
    i {
      display: inline-block;
      vertical-align: -13px;
      width: 44px;
      height: 44px;
      background: url(../../img/coins.png) no-repeat center center;
      background-size: 100%;
    }
    font-size: 24px;
    font-weight: bold;
    color: #ffffff;
    line-height: 29px;
    margin: 30px auto 12px auto;
  }
  h2 {
    font-size: 26px;
    font-family: 'SFProRounded-Medium', 'SFProRounded', 'Helvetica', 'PingFang SC', 'sans-serif',
      'Arial', 'Verdana', 'Microsoft YaHei';
    font-weight: 600;
    color: #fff;
    line-height: 31px;
    padding: 0 0 25px 0;
    margin: 0;
  }
}
</style>
