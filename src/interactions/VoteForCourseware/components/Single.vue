<template>
  <div class="vote-options">
    <div
      v-for="(option, index) in options"
      :key="index"
      class="option-item"
      :data-log="`选项-课件内单选选项-${option}`"
      :class="{ active: selectedOption === option }"
      @click="selectOption(option)"
    >
      <span>{{ option }}</span>
    </div>
  </div>
</template>

<script>
import logger from '@/utils/logger'
export default {
  name: 'SingleOption',
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedOption: this.value
    }
  },
  watch: {
    value(newValue) {
      this.selectedOption = newValue
    }
  },
  methods: {
    sendLogger(msg, level = 'info') {
      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: 'Vote'
        }
      })
    },
    selectOption(option) {
      this.sendLogger(`点击-课件内选择题选项-${option}`)
      this.selectedOption = option
      this.$emit('input', option)
      this.$emit('change', option)
    }
  }
}
</script>

<style lang="scss" scoped>
.radio-group {
  display: flex;
}

.radio-group button {
  margin: 0 9px;
  border: none;
  cursor: pointer;
  border-radius: 8px;
  background: #f5f5f5;
  color: #a2aab8;
  font-weight: 400;
  line-height: 42px;
  height: 40px;
  min-width: 40px;
  text-align: center;
  padding: 0 15px;
  font-size: 20px;
  white-space: nowrap;
  vertical-align: middle;
  &:first-child {
    margin-left: 0px;
  }
  &:last-child {
    margin-right: 0px;
  }
}

.radio-group button.active {
  background: #3370ff;
  color: #fff;
}
</style>
