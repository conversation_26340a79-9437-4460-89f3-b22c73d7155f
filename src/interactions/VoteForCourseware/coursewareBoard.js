// 业务逻辑
import { submitUserAnswer, interactReport, getQuestionStatus } from '@/api/h5courseware/index'
import { emitter } from '@/hooks/useEventBus'
import Toast from './components/toast'
// import logger from 'utils/logger'

export default class Main {
  constructor(opts = {}) {
    this.options = opts
    this.quesInfo = null
    this.handleQuesInfo()
  }
  /**
   * 初始化倒计时
   */
  initCountdown() {
    const localTime = +new Date() // 本机时间
    const timeOffset = this.options.roomMessage.roomInfo.commonOption.timeOffset // 本机时间和服务器时间的差值
    const times =
      this.options.ircMsg.countDownTime * 1000 -
      (localTime + timeOffset - this.options.ircMsg.currentTime)
    // logger.send({
    //   tag: 'student.Interact',
    //   content: {
    //     msg: `本机时间：${localTime},偏移量:${timeOffset},倒计时${times}`,
    //     interactType: '答题h5互动',
    //     interactId: this.options.ircMsg.interactId
    //   }
    // })
    return times
  }
  /**
   * 答题板详情
   */
  handleQuesInfo() {
    const {
      quesTypeId,
      quesAnswer,
      quesOptions,
      quesId,
      interactId,
      rightCoin,
      countDownTime
    } = this.options.ircMsg
    const { stuInfo, teacherInfo, stuLiveInfo } = this.options.roomMessage.roomInfo

    this.quesInfo = {
      quesTypeId: quesTypeId * 1,
      quesAnswer,
      quesOptions: quesOptions.flat(),
      quesId,
      interactId,
      rightCoin,
      countDownTime,
      stuInfo,
      teacherInfo,
      stuLiveInfo
    }
    return this.quesInfo
  }

  /**
   * 判题
   * @param {*} userAnswer  用户提交的答案
   * @param {*} rightAnswer  正确答案
   */
  handleCorrectQues(userAnswer, rightAnswer) {
    if (!userAnswer.length) return 3
    return [...new Set(userAnswer)].sort().toString() ===
      [...new Set(rightAnswer)].sort().toString()
      ? 1
      : 2
  }

  /**
   * 提交答案
   * @param {*} params
   */
  async submitAnswer(userAnswer) {
    const params = {
      teacherId: this.quesInfo.teacherInfo.id, // 主讲老师ID
      tutorId: this.quesInfo.stuLiveInfo.tutorId, // 辅导老师ID
      planId: this.quesInfo.stuLiveInfo.planId, // 讲次ID
      classId: this.quesInfo.stuLiveInfo.classId, // 班级ID
      interactId: this.quesInfo.interactId, // 互动ID
      questionId: this.quesInfo.quesId, // 试题ID
      userAnswer: [...userAnswer], // 用户答案，当用户未答题时需要提交 null 或空数组
      isRight: this.handleCorrectQues(userAnswer, this.quesInfo.quesAnswer), // 对错，1 - 正确，2 - 错误, 3 - 未答
      userName: this.quesInfo.stuInfo.nickName // 昵称
    }

    const response = await submitUserAnswer(params).catch(err => {
      // logger.send({
      //   tag: 'http',
      //   content: {
      //     msg: `接口报错:提交答案:`,
      //     err,
      //     params
      //   },
      //   level: 'error'
      // })
      return err
    })
    const { code, data } = response
    const { isRight } = params
    const continuousCorrectData = { ...data, isRight, rightCoin: data?.rightCoin }
    let res = {
      params,
      response,
      isSubmit: false,
      isRight
    }
    // 触发连对激励
    emitter.emit('continuousCorrect', continuousCorrectData)
    if (code === 0) {
      res.isSubmit = true
      return res
    }
    return res
  }

  /**
   * 异常恢复
   */
  async questionStatus() {
    try {
      const params = {
        teacherId: this.quesInfo.teacherInfo.id, // 主讲老师ID
        tutorId: this.quesInfo.stuLiveInfo.tutorId, // 辅导老师ID
        planId: this.quesInfo.stuLiveInfo.planId, // 讲次ID
        courseId: this.quesInfo.stuLiveInfo.courseId, // 课程ID
        classId: this.quesInfo.stuLiveInfo.classId, // 班级ID
        interactId: this.quesInfo.interactId // 互动ID
      }
      return await getQuestionStatus(params)
    } catch (error) {
      console.error('getQuestionStatus', error)
    }
  }

  async interactReport() {
    try {
      const params = {
        planId: this.quesInfo.stuLiveInfo.planId, // 讲次ID
        classId: this.quesInfo.stuLiveInfo.classId, // classId
        interactId: this.quesInfo.interactId // 互动ID
      }
      return await interactReport(params)
    } catch (error) {
      console.error('interactReport', error)
    }
  }
  /**
   * 关闭toast
   */
  closeToast() {
    return Toast.close()
  }
}
