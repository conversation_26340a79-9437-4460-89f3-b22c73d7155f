<template>
  <div
    v-if="showContinuousCorrect && isRight != 3"
    v-show="(isRight == 1 && isLoadedCorrectIcon) || isRight != 1"
    class="continus-correct"
    :class="[
      {
        'gameQuestion-style': isGameQuestion,
        'normal-correct-style': !isGameQuestion && !isFillBank,
        'fillbank-style-has-answer': isFillBank,
        'assignment-correct': isAssignmentCorrect
      }
    ]"
  >
    <div
      v-if="isRight == 1"
      class="panel-container"
      :class="{ 'panel-small': isRight == 2 || continuousCorrectData.isNoMedal }"
    >
      <!-- 答对 -->
      <section class="common">
        <canvas id="correctIcon" class="correct-icon"></canvas>
        <!-- 内容 -->
        <section class="content" :class="{ 'noMedal-content': continuousCorrectData.isNoMedal }">
          <span class="continuous-text">
            {{ $t('classroom.modules.continuousCorrect.continuousText') }}
          </span>
          <div class="coins" v-if="continuousCorrectData.rightCoin">
            <img src="./imgs/icon-coin.png" class="icon-coin" />
            <span class="icon-number">+{{ continuousCorrectData.rightCoin }}</span>
          </div>
        </section>
      </section>
    </div>
    <!-- 答错 -->
    <div v-if="isRight == 2" class="panel-container panel-small">
      <section class="common">
        <span class="incorrect-icon"></span>
        <section class="content noMedal-content">
          <span class="wrong-text">
            {{ $t('classroom.modules.continuousCorrect.wrongTitleText') }}
          </span>
          <div class="coins">
            <template v-if="!continuousCorrectData.isNoMedal && continuousCorrectData.rightCoin">
              <img src="./imgs/icon-coin.png" class="icon-coin" />
              <span class="icon-number">+{{ continuousCorrectData.rightCoin }}</span>
            </template>
            <span v-else class="level-desc">
              {{ $t('classroom.modules.continuousCorrect.wrongDescText') }}
            </span>
          </div>
        </section>
      </section>
    </div>
    <audio :src="interactFailAudio" class="hide" ref="interactFail"></audio>
    <audio :src="interactSuccessAudio" class="hide" ref="interactSuccess"></audio>
  </div>
</template>

<script>
import { useClassData } from '@/stores/classroom'
import { useBaseData } from '@/stores/baseData'
import { emitter } from '@/hooks/useEventBus'
import { useIrcService } from '@/hooks/useIrcService'
import logger from '@/utils/logger'
import { getAssetPath } from '@/utils/assetPath'

const { getIrcService } = useIrcService()

export default {
  name: 'continuousCorrect',
  setup() {
    const classStore = useClassData()
    const baseStore = useBaseData()
    return {
      classStore,
      baseStore
    }
  },
  async mounted() {
    this.initEventListeners()
  },
  created() {
    this.loadMedalList()
    this.loadAudioFiles()
  },
  data() {
    return {
      showContinuousCorrect: false,
      continuousCorrectData: {},
      isTopLevel: false,
      MedalImgList: [], // 存储各等级勋章列表
      closeBoardTimer: null, //弹窗关闭定时器
      isGameQuestion: false, // 是否是游戏题
      isFillBank: false, // 填空题
      isAssignmentCorrect: false, // 作业盒子拍照上墙批改正确弹窗
      isRight: 0, // 对错，1 - 正确，2 - 错误, 3 - 未答
      // 发送聊天消息
      sendMsgFrom: 'flv',
      isLoadedCorrectIcon: false, // 等待pag动效加载完再展示弹窗
      interactFailAudio: '',
      interactSuccessAudio: ''
    }
  },
  computed: {
    options() {
      return this.classStore.baseData.commonOption
    },
    currentUserId() {
      // 优先使用 baseData 中的 stuInfo.id，如果没有则使用 baseStore 的 userInfo.uid
      return this.classStore.baseData.stuInfo?.id || this.baseStore.userInfo?.uid
    },
    currentUserName() {
      return (
        this.classStore.baseData.stuInfo?.nickName ||
        this.baseStore.userInfo?.nickName ||
        this.options?.nickName
      )
    }
  },
  unmounted() {
    emitter.off('initClose')
    emitter.off('continuousCorrect')
  },
  methods: {
    // 加载本地勋章图片
    loadMedalList() {
      const MEDAL_LENGTH = 7 // 7个勋章 确保require都能加载
      for (let i = 0; i < MEDAL_LENGTH; i++) {
        this.MedalImgList.push(
          new URL(`../../../assets/images/live/badges/middle/level-${i + 1}.png`, import.meta.url)
            .href
        )
      }
    },
    // 加载音频文件
    loadAudioFiles() {
      this.interactFailAudio = new URL('./audio/interact-fail.mp3', import.meta.url).href
      this.interactSuccessAudio = new URL('./audio/interact-success.mp3', import.meta.url).href
    },
    // 注册监听事件
    initEventListeners() {
      // 填空题打开时，先初始化弹框状态专用事件
      emitter.on('initClose', () => {
        // 如果有定时关闭任务要先清空
        clearTimeout(this.closeBoardTimer)
        this.closeBoardTimer = null
        this.showContinuousCorrect = false
      })
      emitter.on('onlyContinuousCorrect', async data => {
        // 答对后 title勋章
        if (data?.correctCount) {
          // 发送私聊消息-视频回显区域展示勋章
          const uid = this.currentUserId
          if (!uid) {
            console.warn('无法获取用户ID，跳过发送消息')
            return
          }
          const opts = {
            roomlist: this.options.roomlist,
            content: {
              type: 142,
              userId: String(uid),
              level: data.level || 0,
              correctCount: data?.correctCount || 0,
              from: this.sendMsgFrom,
              name: this.currentUserName,
              msg: ''
            },
            chatMsgPriority: 99
          }
          // 真小班发送消息
          const IrcService = getIrcService()

          IrcService.sendRoomMessage(opts)

          // 广播连对称号消息
          emitter.emit('chats.correctSelfMedalData', opts.content)
        }
      })
      emitter.on('continuousCorrect', data => {
        this.isFillBank = this.isGameQuestion = false
        this.isAssignmentCorrect = data.isAssignmentCorrect
        if (!data) {
          this.showContinuousCorrect = false
          return
        }
        // 处理题目答案
        if (data.rightCoin != undefined || data.isNoMedal) {
          this.dealQuestionAnswer(data)
        } else {
          this.autoCloseBoard(0)
        }
      })
    },
    /**
     * 处理答案
     */
    async dealQuestionAnswer(data) {
      this.sendLogger(`金币激励提交-返回答案: ${JSON.stringify(data)}`)
      if (data.isNoMedal) {
        // 处理投票互动答案，不展示勋章
        this.isRight = data.answerStat
        this.continuousCorrectData.rightCoin = data.gold
        this.continuousCorrectData.isNoMedal = data.isNoMedal
        this.continuousCorrectData.level = data?.level
        this.continuousCorrectData.correctCount = data?.correctCount
      } else {
        this.isRight = data.isRight
        this.continuousCorrectData = data
      }
      if (this.isRight == 1) {
        this.initCanvas()
      }
      // 是否是游戏题 且有答案
      if (this.continuousCorrectData.isGameQuestion) {
        this.isGameQuestion = true
      }
      // 是否是填空题 且有答案
      if (this.continuousCorrectData.isFillBank && this.isRight != 3) {
        this.isFillBank = true
      }
      // 答对后 title勋章
      if (this.continuousCorrectData?.correctCount) {
        // 发送私聊消息-视频回显区域展示勋章
        const uid = this.currentUserId
        if (!uid) {
          console.warn('无法获取用户ID，跳过发送消息')
          return
        }
        const opts = {
          roomlist: this.options.roomlist,
          content: {
            type: 142,
            userId: String(uid),
            level: this.continuousCorrectData.level || 0,
            correctCount: this.continuousCorrectData?.correctCount || 0,
            from: this.sendMsgFrom,
            name: this.currentUserName,
            msg: ''
          },
          chatMsgPriority: 99
        }
        // 真小班发送消息
        const IrcService = getIrcService()
        IrcService.sendRoomMessage(opts)

        // 广播连对称号消息
        emitter.emit('chats.correctSelfMedalData', opts.content)
      }
      // 显示勋章modal
      this.showContinuousCorrect = true

      // 为答题结果弹窗增加音效
      this.$nextTick(() => {
        if (this.isRight === 3) return // 未作答不展示
        this.isRight === 1 ? this.$refs.interactSuccess.play() : this.$refs.interactFail.play()
      })
      // 3s后自动关闭
      this.autoCloseBoard(3000)
    },
    async initCanvas() {
      if (!window.PAG) {
        window.PAG = await window.libpag.PAGInit()
      }
      const url = getAssetPath('/pagfiles/correctIconPag.pag')
      fetch(url)
        .then(response => response.blob())
        .then(async blob => {
          const file = new window.File([blob], url.replace(/(.*\/)*([^.]+)/i, '$2'))
          const pagFile = await window.PAG.PAGFile.load(file)
          const pagView = await window.PAG.PAGView.init(pagFile, '#correctIcon')
          this.isLoadedCorrectIcon = true
          pagView.play()
        })
    },
    /**
     * 定时关闭
     */
    autoCloseBoard(time) {
      this.closeBoardTimer = setTimeout(() => {
        this.sendLogger(`3s关闭金币激励`)
        this.closePane()
      }, time)
    },
    // 关闭
    closePane() {
      this.showContinuousCorrect = false
      // 手动点击和自动关闭都要移除关闭弹框定时器
      clearTimeout(this.closeBoardTimer)
      this.closeBoardTimer = null
      // 关闭弹窗的时候更新金币
      if (this.continuousCorrectData.rightCoin) {
        // 游戏互动只加金币，不展示动效
        if (this.continuousCorrectData.isGameQuestion) {
          emitter.emit('updateAchievement')
        } else {
          emitter.emit('addCoin', true, this.continuousCorrectData.rightCoin)
        }
      }

      emitter.emit('closeContinusCorrect', true)
    },
    /**
     * 日志上报
     */
    sendLogger(msg) {
      logger.send({
        tag: 'continuousCorrectData',
        content: {
          msg: msg
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.continus-correct {
  position: absolute;
  z-index: 99; // 默认小于投票互动层级99
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  pointer-events: none;

  &.gameQuestion-style {
    background: rgba(0, 0, 0, 0.7);
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 2001; // 大于游戏互动层级2000
  }

  &.fillbank-style-has-answer {
    z-index: 101; // 高于填空题互动，且没有蒙层
  }

  &.normal-correct-style {
    background: rgba(0, 0, 0, 0.7);
  }

  &.assignment-correct {
    background: none;
    z-index: 2001; // 高于作业盒子查看大图弹窗
  }

  .panel-container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 1200;
    width: 320px;
    height: 200px;

    &.panel-small {
      height: 160px;
    }

    .common {
      background: linear-gradient(180deg, #fff0d6 0%, #ffffff 100%);
      box-shadow: inset -1px 1px 0px 0px #ffffff;
      border-radius: 12px;
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;

      .correct-icon {
        position: absolute;
        left: 50%;
        top: -96px;
        transform: translateX(-50%);
        z-index: 1;
        width: 200px;
        height: 150px;
      }

      .incorrect-icon {
        position: absolute;
        left: 50%;
        top: -96px;
        transform: translateX(-50%);
        z-index: 1;
        width: 200px;
        height: 150px;
        background: url('./imgs/incorrect-icon.png');
        background-size: 100% 100%;
      }

      .content {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        height: 100%;
        margin-top: 40px;

        .continuous-text,
        .wrong-text {
          display: block;
          font-weight: bold;
          color: #172b4d;
          line-height: 19px;
          font-size: 16px;
          margin-bottom: 16px;
          text-align: center;
        }

        .coins {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .icon-coin {
            width: 32px;
            height: 32px;
          }

          .icon-number {
            font-weight: 600;
            color: #ff8a3f;
            line-height: 26px;
            font-size: 18px;
          }
        }

        .level-desc {
          color: #172b4d;
          line-height: 15px;
          font-size: 14px;
          text-align: center;
        }
      }

      .noMedal-content {
        background: url('./imgs/medal-bg.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .hide {
    display: none;
  }

  // 响应式适配
  @media (max-width: 768px) {
    .panel-container {
      width: 280px;
      height: 180px;

      &.panel-small {
        height: 140px;
      }

      .common {
        padding: 16px;

        .correct-icon,
        .incorrect-icon {
          width: 160px;
          height: 100px;
          top: -60px;
        }

        .content {
          margin-top: 30px;

          .continuous-text,
          .wrong-text {
            font-size: 14px;
            margin-bottom: 12px;
          }

          .coins {
            .icon-coin {
              width: 28px;
              height: 28px;
            }

            .icon-number {
              font-size: 16px;
            }
          }

          .level-desc {
            font-size: 12px;
          }
        }
      }
    }
  }
}
</style>
