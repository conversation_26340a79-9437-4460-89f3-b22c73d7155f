<!-- 视图层 -->
<template>
  <div class="orientation-coins">
    <transition name="slide-fade">
      <!-- 此次定向金币互动中包含自己 -->
      <div
        v-if="currentStudentGotCoins && !isShowBox && showCoinsBox"
        class="orientation-coins-content"
      >
        <div class="coins">
          <div class="coins-pag">
            <canvas class="coins-pag" id="orientationCoinsPag"></canvas>
          </div>
          <div class="coins-content">
            <div class="title">
              {{ $t('classroom.modules.orientationCoins.smallCongrats') }}
            </div>
            <div class="icon-img">
              <img class="img-plus" src="./imgs/plus.png" />
              <div
                class="img-num"
                v-for="(i, index) in String(studentCoinsResult.rewardCoinNum).split('')"
                :key="index"
              >
                <img :src="numImgArr[i]" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { emitter } from '@/hooks/useEventBus'
import { getOrientationCoins } from '@/api/interaction/orientationCoins'
import logger from '@/utils/logger'
// import { mapGetters } from 'vuex'
// import { useStore } from 'vuex'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import zero from './imgs/num-0.png'
import one from './imgs/num-1.png'
import two from './imgs/num-2.png'
import three from './imgs/num-3.png'
import four from './imgs/num-4.png'
import five from './imgs/num-5.png'
import six from './imgs/num-6.png'
import seven from './imgs/num-7.png'
import eight from './imgs/num-8.png'
import nine from './imgs/num-9.png'
import { useClassData } from '@/stores/classroom'
import { getAssetPath } from '@/utils/assetPath'
const store = useClassData()
export default {
  name: 'OrientationCoins',
  props: {
    options: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      numImgArr: [zero, one, two, three, four, five, six, seven, eight, nine],
      coins: null,
      timer: null,
      planId: null,
      interactId: null,
      currentStudentId: null,
      ircMessage: null,
      showCoinsBox: false,
      timeDuration: 4000,
      // 获取学生金币的列表
      studentCoinsResult: {},
      lastCoinSum: 0,
      currentStudentGotCoins: false,
      smallCoinsShowTime: 4000 // 小班金币弹窗展示时间
    }
  },
  computed: {
    isShowBox() {
      // return store.chatBox.isShowBox
      return false
    }
    // ...mapGetters({
    //   isShowBox: 'smallClass/isShowBox'
    // }),
  },
  methods: {
    receiveMessage(noticeContent) {
      this.sendLogger(`收到定向金币消息: ${JSON.stringify(noticeContent)}`, 'start')
      console.log('看看手抖定向金币的 kv 信令了么', noticeContent, this.isShowBox, this.isShowBoxCommon, this.options)
      if (noticeContent.duration) {
        this.timeDuration = noticeContent.duration
        this.sendLogger(`设置弹窗时长: ${noticeContent.duration}ms`)
      }
      // 弹窗时间为4000ms
      this.timeDuration = this.smallCoinsShowTime
      // 是否是同一个互动id
      this.getSpecialCoinsStatus(noticeContent)
    },
    /**
     * 判断状态
     */
    async getSpecialCoinsStatus(ircMessage) {
      const interactId = localStorage.getItem('orientationCoinsId')
      if (interactId == ircMessage.interactId) {
        this.sendLogger(`重复的互动ID，跳过处理: ${ircMessage.interactId}`, 'check', 'warn')
        this.showCoinsBox = false
      } else {
        this.sendLogger(`开始处理新的定向金币互动: ${ircMessage.interactId}`, 'process')

        if (!window.PAG) {
          this.sendLogger('初始化PAG库', 'init')
          window.PAG = await window.libpag.PAGInit()
        }
        const url = getAssetPath('/pagfiles/orientationCoinsPag.pag')
        const response = await fetch(url)

        const blob = await response.blob()
        const file = new window.File([blob], url.replace(/(.*\/)*([^.]+)/i, '$2'))
        const pagFile = await window.PAG.PAGFile.load(file)

        const isInterruptedByOther = await this.init(ircMessage)
        if (isInterruptedByOther) {
          this.sendLogger('初始化被其他操作中断', 'init', 'warn')
          return
        }

        this.$nextTick(async () => {
          if (this.currentStudentGotCoins && !this.isShowBoxCommon && this.showCoinsBox) {
            this.sendLogger('开始播放金币动画', 'animation')
            let pagView = await window.PAG.PAGView.init(pagFile, '#orientationCoinsPag', {
              useCanvas2D: true
            })
            pagView.addListener('onAnimationEnd', () => {
              this.sendLogger('金币动画播放完毕', 'animation')
              console.log('播放完毕')
              if (pagView) {
                pagView.destroy()
                pagView = null
              }
            })
            pagView.setRepeatCount(1)
            await pagView.play()
            console.log('play')
            // 小班更新金币数量（动效）
            emitter.emit('addCoin', true, this.studentCoinsResult.rewardCoinNum, true)
            this.sendLogger(`触发金币更新事件 - 金币数量:${this.studentCoinsResult.rewardCoinNum}`, 'update')
            // this.$bus.$emit('addCoin', true, this.studentCoinsResult.rewardCoinNum, true)
          }
        })
        this.sendLogger(`收到定向发金币消息: ${JSON.stringify(ircMessage)}`, 'start')
        localStorage.setItem('orientationCoinsId', ircMessage.interactId)
      }
    },
    /**
     * 初始化
     */
    async init(ircMessage) {
      this.sendLogger('开始初始化定向金币组件', 'init')
      this.ircMessage = ircMessage
      const options = this.options?.roomMessage?.roomInfo?.commonOption
      this.planId = options.planId
      this.currentStudentId = options.stuId
      this.interactId = ircMessage.interactId
      this.sendLogger(`初始化参数 - planId:${this.planId} stuId:${this.currentStudentId} interactId:${this.interactId}`, 'init')
      return await this.getStudentCoins()
    },

    async getOrientationCoins(options) {
      // 互动类型，targerCoin定向金币、classroomPraise课堂表扬榜、schulteGrid 舒尔特方格
      let params = {
        planId: options.planId * 1,
        interactId: options.interactId,
        interactType: this.ircMessage.interactType
      }
      // 从其他互动中使用定向金币动效场景, 目前是仅表扬榜有此逻辑，后期修改需要这里调整
      if (params.interactType === 'classroomPraise') {
        params.interactId = this.ircMessage.parentsInteractId
        this.sendLogger(`课堂表扬榜场景，使用父互动ID: ${this.ircMessage.parentsInteractId}`, 'api')
      }
      this.sendLogger(`调用定向金币接口 - 参数:${JSON.stringify(params)}`, 'api')
      // 获取定向发送的金币
      const res = await getOrientationCoins(params).catch(err => {
        this.sendLogger(`定向金币接口调用失败: ${err.message || err}`, 'api', 'error')
        throw err
      })
      this.sendLogger(`定向金币接口返回: ${JSON.stringify(res)}`, 'api')
      return res
    },
    /**
     * 获取金币
     */
    async getStudentCoins() {
      let result = await this.getOrientationCoins({
        planId: this.planId,
        interactId: this.interactId
      })
      if (result.stat == 1) {
        // 判断是否自己获得金币
        const isMeGetCoins =
          result.data.users && result.data.users[0].userId == this.currentStudentId
        this.interactId && classLiveSensor.osta_ia_coins(this.interactId, result.data.rewardCoinNum)
        this.sendLogger(`定向发金币获取金币: ${JSON.stringify(result.data)}`)

        if (isMeGetCoins) {
          this.sendLogger(`我获得了定向金币 - 金币数量:${result.data.rewardCoinNum}`, 'reward')
          this.currentStudentGotCoins = isMeGetCoins
          // 如果连续获得金币，需要先清除上一定时器
          // 如果存在则不需要再播放一次加金币，直接最终汇总总的金币数通知即可
          if (this.timer) {
            this.lastCoinSum += this.studentCoinsResult.rewardCoinNum
            clearTimeout(this.timer)
            this.timer = null
            this.showCoinsBox = false
            this.sendLogger(`连续获得金币，累计金币数:${this.lastCoinSum + result.data.rewardCoinNum}`, 'reward')
          } else {
            this.lastCoinSum = 0
          }
          this.$nextTick(() => (this.showCoinsBox = true))
          this.timer = setTimeout(() => {
            this.showCoinsBox = false
            this.sendLogger(`关闭定向发金币`, 'end')
            // 小班弹窗提示
            this.smallClassTipsAction(isMeGetCoins)
          }, this.timeDuration)
          this.studentCoinsResult = result.data
        } else {
          this.sendLogger('我没有获得定向金币', 'check')
          if (this.showCoinsBox) return true
          else {
            this.showCoinsBox = false
            this.currentStudentGotCoins = isMeGetCoins
            this.smallClassTipsAction(isMeGetCoins)
          }
        }
        return false
      } else {
        this.sendLogger(`定向金币接口返回失败 - stat:${result.stat}`, 'api', 'error')
      }
    },
    /**
     * 小班金币弹窗后逻辑操作
     */
    smallClassTipsAction(isMeGetCoins) {
      // 如果是自己获得了金币
      if (isMeGetCoins) {
        this.sendLogger('处理获得金币后的小班弹窗逻辑', 'action')
        if (!this.showCoinsBox) {
          this.sendLogger('关闭定向金币组件', 'close')
          this.$emit('close', 'distribute_coins')
        }
      } else {
        this.sendLogger('未获得金币，关闭定向金币组件', 'close')
        this.$emit('close', 'distribute_coins')
      }
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionId = this.interactId || 'unknown'
      const interactionType = 'OrientationCoins'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
    }
  },
  beforeUnmount() {
    clearTimeout(this.timer)
  }
}
</script>

<style lang="scss" scoped>
@use './app.scss' as *;
</style>
