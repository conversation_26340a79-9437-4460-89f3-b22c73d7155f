.orientation-coins {
  position: absolute;
  z-index: 1000;
  width: 100%;
  height: 100%;
  pointer-events: none !important;
}
.orientation-coins-content {
  display: inline-block;
  position: absolute;
  top: 52px;
  right: -120px;
  border-radius: 30px 0 0 30px;
  padding: 1px;
  background: linear-gradient(270deg, rgba(0,0,0,0.3) 0%, #000000 100%);
  border: 1px solid rgba(255,255,255,0.3);
  opacity: 0;
  animation: leftMoveAndShow linear 0.6s forwards;
}
@keyframes leftMoveAndShow {
  0% {
    right: -120px;
    opacity: 0;
  }
  100% {
    right: 0px;
    opacity: 1;
  }
}
@keyframes showfade {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes scaleImg {
  0% {
    transform: scale(1.6);
  }
  33% {
    transform: scale(0.94);
  }
  66% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}
// orientation-coins
.coins-pag {
  width: 136px;
  height: 136px;
}
.coins {
  padding: 0px 16px 0px 66px;
  position: relative;
  .coins-pag {
    position: absolute;
    top: -20px;
    left: -24px;
    width: 136px;
    height: 136px;
  }
  .coins-content {
    display: flex;
    opacity: 0;
    animation: showfade linear 0.2s 0.6s forwards;
  }
  .title {
    font-size: 16px;
    font-family: Montserrat-Bold, Montserrat;
    font-weight: bold;
    color: #FFFFFF;
    line-height: 58px;
    padding-right: 8px;
  }
  .icon-img {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: scaleImg linear 0.6s 0.6s forwards;
    .img-plus {
      margin-right: 2px;
    }
    img {
      height: 18px;
      width: auto;
    }
  }
}
.slide-fade-leave-active {
  animation: 200ms fadeHide linear;
}
@keyframes fadeHide {
   0% {
       opacity: 1;
   }
   100%{
       opacity: 0;
   }
}