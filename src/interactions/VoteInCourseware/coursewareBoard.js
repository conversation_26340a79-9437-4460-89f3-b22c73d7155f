// 业务逻辑
import { submitUserAnswer, interactReport } from '@/api/h5courseware/index'
import { emitter } from '@/hooks/useEventBus'

export default class Main {
  constructor(opts = {}) {
    this.options = opts
    this.vm = opts.vm
    this.quesInfo = null
    this.handleQuesInfo()
  }

  /**
   * 初始化倒计时
   */
  initCountdown() {
    const localTime = +new Date() // 本机时间
    const timeOffset = this.options.roomMessage.roomInfo.commonOption.timeOffset // 本机时间和服务器时间的差值
    let times =
      this.options.ircMsg.countDownTime * 1000 -
      (localTime + timeOffset - this.options.ircMsg.currentTime)

    console.log(`本机时间：${localTime},偏移量:${timeOffset},倒计时${times}`)

    if (times <= 0) {
      times = 0
    }
    return times
  }

  /**
   * 答题板详情
   */
  handleQuesInfo() {
    const {
      quesTypeId,
      quesAnswer,
      quesOptions,
      quesId,
      interactId,
      rightCoin,
      countDownTime
    } = this.options.ircMsg
    const { stuInfo, teacherInfo, stuLiveInfo } = this.options.roomMessage.roomInfo

    this.quesInfo = {
      quesTypeId: quesTypeId * 1,
      quesAnswer,
      quesOptions: quesOptions.flat(),
      quesId,
      interactId,
      rightCoin,
      countDownTime,
      stuInfo,
      teacherInfo,
      stuLiveInfo
    }
    return this.quesInfo
  }

  /**
   * 提交答案
   * @param {*} params
   */
  async submitAnswer(userAnswer, isRight) {
    const params = {
      teacherId: this.quesInfo.teacherInfo.id, // 主讲老师ID
      tutorId: this.quesInfo.stuLiveInfo.tutorId, // 辅导老师ID
      planId: this.quesInfo.stuLiveInfo.planId, // 讲次ID
      classId: this.quesInfo.stuLiveInfo.classId, // 班级ID
      interactId: this.quesInfo.interactId, // 互动ID
      questionId: this.quesInfo.quesId, // 试题ID
      userAnswer: [...userAnswer], // 用户答案，当用户未答题时需要提交 null 或空数组
      isRight: isRight, // 对错，1 - 正确，2 - 错误, 3 - 未答
      userName: this.quesInfo.stuInfo.nickName // 昵称
    }
    let response = {}
    try {
      response = await submitUserAnswer(params)
    } catch (err) {
      console.error('接口报错:提交答案:', err, params)
      return {
        params,
        isRight,
        code: -1
      }
    }
    const { code, data } = response
    let res = {
      params,
      response,
      isRight,
      code
    }
    if (code === 0) {
      const continuousCorrectData = { ...data, isRight, rightCoin: data?.rightCoin }
      // 触发连对激励
      emitter.emit('continuousCorrect', continuousCorrectData)
    }
    return res
  }

  /**
   * 互动上报
   */
  async interactReport() {
    try {
      const params = {
        planId: this.quesInfo.stuLiveInfo.planId, // 讲次ID
        classId: this.quesInfo.stuLiveInfo.classId, // classId
        interactId: this.quesInfo.interactId // 互动ID
      }
      return await interactReport(params)
    } catch (error) {
      console.error('interactReport', error)
    }
  }

  /**
   * 关闭提示
   */
  closeToast() {
    // 如果需要关闭提示，可以在这里实现
  }
}
