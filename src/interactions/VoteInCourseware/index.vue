<template>
  <CountDown v-if="countdownOptions" :time="countdownOptions"></CountDown>
</template>

<script>
import coursewareBoard from './coursewareBoard'
import { coursewareAnswerLog } from '@/utils/web-log/HWLogDefine'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import CountDown from '@/components/countDown/CountDown.vue'
import { emitter } from '@/hooks/useEventBus'

export default {
  name: 'VoteInCourseware',
  props: {
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  components: { CountDown },
  data() {
    return {
      isSubmit: false, // 是否已提交
      quesTypeId: null, // 题目类型ID 1:单选 2:多选 5:判断
      userAnswer: [], // 用户选择的答案
      isRight: 2, // 0错误，1全对，2未作答
      countdownOptions: null, // 倒计时参数
      teacherClosedInteract: false, // 老师主动关闭互动
      resolveFun: null
    }
  },
  created() {
    // 先初始化连对弹框为关闭状态
    emitter.emit('initClose')
    this.coursewareBoard = new coursewareBoard({
      ...this.options
    })
    this.$nextTick(() => {
      // 获取试卷详情
      const quesInfo = this.coursewareBoard.quesInfo
      this.quesTypeId = quesInfo.quesTypeId
      this.sendLogger('收到课件内作答互动', 'start')
      this.coursewareBoard.interactReport() // 开启上报

      this.countdownOptions = this.coursewareBoard.initCountdown()
      this.sendLogger(`互动倒计时${this.countdownOptions}`, 'countdown')
    })
    // 监听作答结果
    emitter.on('sendTestResultToVote', this.getTestResultHandle)
    // 监听课件未开启时，直接结束互动
    emitter.on('cousewareEnd', this.coursewareEnd)

    // 开启作答，发送消息给课件
    emitter.emit('beginTest', this.options.ircMsg)
  },
  methods: {
    coursewareEnd() {
      this.sendLogger('课件未开始时，收到结束，直接结束互动', 'endInteraction')
      this.countdownOptions = 0
      this.destroy()
    },
    endTheInteract(kvContent) {
      // 发送消息给课件
      emitter.emit('endTest', kvContent)
    },
    // 课件内提交答案||(老师主动停止||关闭互动,调用endTest时触发) 均触发
    getTestResultHandle(datas) {
      if (datas && datas.length > 0) {
        const result = datas[0]
        this.userAnswer = result.userAnswerContent.split(',')
        this.isRight = result.isRight
        this.sendLogger(
          `课件返回答题结果 - 答案:${result.userAnswerContent} 正确性:${result.isRight}`,
          'result'
        )
      } else {
        this.$Message.error(this.$t('growthHandbook.systemError', { code: '400' }))
        // 作答结果返回格式错误
        console.error('作答结果返回格式错误', datas)
        this.userAnswer = ''
        this.isRight = 3
        this.sendLogger('课件返回答题结果格式错误', 'result', 'error')
      }
      this.submitAnswer()
      this.countdownOptions = 0
      // 如果老师关闭互动触发，则销毁组件
      if (this.teacherClosedInteract) {
        this.sendLogger('老师主动关闭互动,自动提交答案后销毁', 'endInteraction')
        this.destroy()
      } else {
        console.log('vote-未关闭互动，等待关闭互动后销毁')
      }
    },
    /**
     * 提交答案
     */
    async submitAnswer() {
      this.isSubmit = true
      const { code } = await this.coursewareBoard.submitAnswer(this.userAnswer, this.isRight)

      // 获取答题结果描述
      const resultText = this.isRight === 1 ? '正确' : this.isRight === 0 ? '错误' : '未作答'
      const answerText = Array.isArray(this.userAnswer)
        ? this.userAnswer.join(',')
        : this.userAnswer

      if (code === 0) {
        this.sendLogger(`提交答案成功 - 答案:${answerText} 结果:${resultText}`, 'submit', 'info')

        // 在 this.coursewareBoard.submitAnswer 方法中，触发了 Vue.prototype.$bus.$emit('continuousCorrect', continuousCorrectData),
        classLiveSensor.osta_ia_base_interaction_submit(
          this.options?.ircMsg,
          this.quesTypeId,
          this.isRight
        )
      } else if (code === 30005) {
        this.sendLogger(`重复提交 - 答案:${answerText} 结果:${resultText}`, 'submit', 'warn')
      } else {
        this.$Message.error(this.$t('growthHandbook.systemError', { code }))
        this.sendLogger(
          `提交答案失败 - 答案:${answerText} 结果:${resultText} 错误码:${code}`,
          'submit',
          'error'
        )
      }
    },
    // 连续收到两次不同互动id的开启，先调用次方法强制结束
    forceEndInteract() {
      const promiseFun = new Promise(resolve => {
        this.resolveFun = resolve
      })
      this.sendLogger('连续收到两次不同互动id的开启，强制结束互动', 'forceEnd', 'error')
      this.destroyInteraction({ jsString: this.options.ircMsg.endJsString })

      return promiseFun
    },
    // 教师端主动关闭互动
    destroyInteraction(kvContent) {
      this.sendLogger('收到停止作答', 'endInteraction', 'info')
      this.teacherClosedInteract = true
      this.endTheInteract(kvContent)
      // 如果已触发过答案回调，代表手动提交过，直接销毁组件
      if (this.isSubmit) {
        this.destroy()
        this.sendLogger('已触发过答案回调，代表手动提交过，直接销毁组件', 'endInteraction', 'info')
      } else {
        console.log('vote-未提交，等待提交后销毁')
      }
    },

    /**
     * 获取题目类型名称
     */
    getQuesTypeName(quesTypeId) {
      const typeMap = {
        1: '单选题',
        2: '多选题',
        4: '填空题',
        5: '判断题'
      }
      return typeMap[quesTypeId] || '未知题型'
    },

    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const quesTypeName = this.getQuesTypeName(this.quesTypeId)
      const interactType = this.quesTypeId ? `Vote-${quesTypeName}` : 'Vote'
      coursewareAnswerLog.info(`[${interactType}][${this.options.ircMsg.interactId}] ${msg}`, {
        stage,
        level
      })
    },

    /**
     * 销毁组件
     */
    destroy() {
      this.sendLogger('结束互动', 'end', 'info')
      this.$emit('close', 'interact_in_courseware')
      emitter.off('sendTestResultToVote', this.getTestResultHandle)
      emitter.off('cousewareEnd', this.coursewareEnd)
      if (this.resolveFun) {
        this.sendLogger('强制结束，回调结束成功', 'end', 'info')
        this.resolveFun()
      }
    }
  }
}
</script>
