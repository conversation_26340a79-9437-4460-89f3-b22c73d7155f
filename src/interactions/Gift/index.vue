<template>
  <div style="width: 100%; height: 100%; top: 0; left: 0; position: absolute">
    <audio :src="gift_select" class="hiden" ref="giftSelect"></audio>
    <audio :src="gift_show" class="hiden" ref="giftShow"></audio>
    <audio :src="gift_submit" class="hiden" ref="giftSubmit"></audio>
    <GiftBarrage ref="giftBarrage" />
    <div class="live-gifts-container">
      <!-- <template v-if="sendStatusData.isSuccessful"> -->
      <div
        class="toast-item-container hover-trainsition"
        :class="{ show: sendStatusData.isSuccessful }"
      >
        <img class="gift-tost-icon" height="30px" :src="submitGitPic" />
        <span>{{ sendStatusData.successfulMsg }}</span>
      </div>
      <!-- </template> -->
      <template v-if="showGiftsPannel && queryGiftDataSuccess">
        <div class="gifts-pannel-container" @click.stop>
          <div class="close-icon-container" @click.stop="closeGiftsPannel">
            <CloseOutlined />
          </div>
          <div
            class="operate-text-container"
            v-if="sendStatusData.sendMsg != '' && sendStatusData.showSendMsg"
          >
            {{ sendStatusData.sendMsg }}
          </div>
          <div class="gifts-pannel-body-container">
            <section>
              <button
                :disabled="leftArrowDisabled"
                @click.stop="handleLeftScroll"
                class="arrow-left"
              >
                <span :class="leftArrowDisabled ? 'arrow-disabled' : 'arrow'"></span>
              </button>
              <div class="gifts-list-wrapper">
                <div ref="scrollWrapper" class="scroll-wrapper">
                  <div
                    v-for="(giftItem, index) in giftData.giftList"
                    :key="index"
                    class="gift-item hover-trainsition"
                    :class="{ selected: giftItem.giftId == selectedGift.giftId }"
                    @click.stop="chooseGift(giftItem)"
                  >
                    <div class="gift-image">
                      <img :src="giftItem.imagePc" />
                    </div>
                    <div class="gift-name text-overflow-ellipsis">{{ giftItem.giftName }}</div>
                    <div class="gift-price">
                      <!-- <a-icon :component="coinsSvg" class="icon-coins" />{{ giftItem.coin }} -->
                      <img :src="coinsSvgUrl" class="icon-coins" alt="coins" />
                      {{ giftItem.coin }}
                    </div>
                  </div>
                </div>
              </div>
              <button
                :disabled="rightArrowDisabled"
                @click.stop="handleRightScroll"
                class="arrow-right"
              >
                <span :class="rightArrowDisabled ? 'arrow-disabled' : 'arrow'"></span>
              </button>
            </section>
            <div class="account-operate-pannel-container">
              <div class="coins-item">
                <div class="header">{{ $t('common.coins') }}:</div>
                <div class="account-conins-number">
                  <!-- <a-icon :component="coinsSvg" class="icon-coins" />{{ userCoin }} -->
                  <img :src="coinsSvgUrl" class="icon-coins" alt="coins" />
                  {{ userCoin }}
                </div>
              </div>

              <button
                class="btn btn-send-gift hover-trainsition"
                @click.stop="sendSubmit"
                :disabled="sendStatusData.sendTimes >= 3 || sendStatusData.sendStatus == 2"
              >
                <a-icon v-if="isSending" type="loading" />
                <LoadingOutlined v-if="isSending" />

                <span class="btn-text">{{ $t('common.send') }}</span>
              </button>
            </div>
          </div>
        </div>
      </template>
      <template v-if="!showGiftsPannel && queryGiftDataSuccess">
        <div class="icon-btn btn-open-gifts-pannel" @click.stop="openGiftsPannel"></div>
      </template>
    </div>
  </div>
</template>

<script setup>
import gift_select from './audio/gift-select.mp3'
import gift_show from './audio/gift-show.mp3'
import gift_submit from './audio/gift-submit.mp3'
import { CloseOutlined, LoadingOutlined } from '@ant-design/icons-vue'
</script>
<script>
import {
  queryCoinsCount,
  queryGiftsListApi,
  sendGiftApi,
  recoverGifteStatusApi,
  submitInteractPartakereport
} from '@/api/classroom/index.js'
import GiftBarrage from './components/GiftBarrage/index.vue'
import { sensorEvent } from '@/utils/sensorEvent'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { emitter } from '@/hooks/useEventBus'
import logger from '@/utils/logger'
import coinsSvgUrl from '@/assets/svg/icon-coins.svg?url'
import { message } from 'ant-design-vue'
export default {
  name: 'GiftInteraction',
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          ircMsg: {},
          roomMessage: {
            roomInfo: {
              commonOption: {}
            }
          }
        }
      }
    }
  },
  components: { GiftBarrage, CloseOutlined, LoadingOutlined },
  data() {
    return {
      isSending: false,
      myOptions: {},
      // coin图标常亮
      coinsSvgUrl,
      // 送礼物面板是否显示
      showGiftsPannel: true,
      // 互动初始化数据
      interactionData: {
        interactId: '',
        planId: '',
        classId: ''
      },
      // 送礼物互动配置数据
      giftData: {
        activityId: '',
        activityName: '',
        giftList: []
      },
      queryGiftDataSuccess: false,
      // 送礼物状态
      sendStatusData: {
        sendMsg: '',
        showSendMsg: false,
        isSuccessful: false,
        successfulMsg: '',
        // 已送礼物次数
        sendTimes: 0,
        // 是否可以发送礼物 1: 可发送 2: 达到次数上限，不可发送
        sendStatus: 1
      },
      // 已选择的礼物
      selectedGift: {
        coin: '',
        giftId: ''
      },
      // 是否显示弹幕
      submitGitPic: '',
      userCoin: 0,
      elementScrollLeft: 0,
      arrowLeftDisabled: false,
      arrowRightDisabled: false
    }
  },
  computed: {
    leftArrowDisabled() {
      return this.arrowLeftDisabled || this.giftData.giftList.length < 6
    },
    rightArrowDisabled() {
      return this.arrowRightDisabled || this.giftData.giftList.length < 6
    }
  },

  unmounted() {
    emitter.off('sendGiftBarrage')
    this.sendLogger('结束互动', 'end')
  },
  mounted() {
    emitter.on('sendGiftBarrage', this.receiveGiftBarrage)
    this.init()
    this.$refs.giftBarrage.init()
  },
  methods: {
    /**
     * 初始化数据
     */
    init() {
      console.log('init gift interaction', this.$refs.giftShow)
      // this.$refs.giftShow.play()
      this.interactionData.interactId = this.options.ircMsg.interactId
      this.interactionData.planId = this.options.roomMessage.roomInfo.commonOption.planId
      this.interactionData.classId = this.options.roomMessage.roomInfo.commonOption.classId
      this.myOptions = this.options
      // 上报互动
      submitInteractPartakereport({
        planId: this.interactionData.planId,
        classId: this.interactionData.classId,
        interactId: this.interactionData.interactId
      })
      this.queryCoinsCount()
      this.queryGiftsList()
      this.recoverGiftStatus()
      this.sendLogger('收到互动', 'start')
    },
    /**
     * 接收弹幕互动消息
     */
    receiveGiftBarrage(notice) {
      console.log('接收弹幕互动消息', notice)
      if (notice.userId == this.options.roomMessage.roomInfo.stuInfo.id) {
        return
      }
      this.$refs.giftBarrage.addMessage({
        icon_pc: notice.icon_pc,
        message: notice.message,
        time: 10
      })
    },
    /**
     * 获取礼物数据
     */
    queryGiftsList() {
      queryGiftsListApi(this, {
        planId: this.interactionData.planId
      })
        .then(res => {
          if (res.code != 0) {
            this.queryGiftDataSuccess = false
            message(res.msg)
            this.sendLogger('获取礼物数据失败')
            return
          }
          const resData = res.data || {}

          if (
            'giftList' in resData &&
            Object.prototype.toString.call(resData.giftList) === '[object Array]' &&
            resData.giftList.length > 0
          ) {
            this.giftData = Object.assign(this.giftData, resData)
            this.queryGiftDataSuccess = true
            return
          }

          this.sendLogger(`获取礼物数据格式错误:${JSON.stringify(resData)}`)
          this.queryGiftDataSuccess = false
        })
        .catch(err => {
          this.queryGiftDataSuccess = false
          this.sendLogger(`获取礼物数据接口报错:${err}`, '', 'error')
        })
    },
    /**
     * 数据恢复
     */
    recoverGiftStatus() {
      let data = {
        // 讲次ID
        planId: this.interactionData.planId,
        // 互动ID
        interactId: this.interactionData.interactId
      }
      recoverGifteStatusApi(this, data)
        .then(res => {
          if (res.code != 0) {
            message.info(res.msg)
            return
          }

          const resData = res.data || {}
          // 互动未进行
          if (resData.interactStatus != 1) {
            this.$emit('close', 'openGift')
          }

          this.sendStatusData.sendStatus = resData.sendStatus
          this.sendStatusData.sendTimes = resData.sendTimes

          if (this.sendStatusData.sendStatus == 2 || this.sendStatusData.sendTimes >= 3) {
            this.sendStatusToast(this.$t('classroom.interactions.gift.notEnoughTimes'))
          }
          this.sendLogger(`获取互动恢复数据状态:${JSON.stringify(resData)}`)
        })
        .catch(err => {
          this.sendLogger(`获取互动恢复数据失败:${err}`, '', 'error')
        })
    },
    /**
     * send提交
     */
    sendSubmit() {
      // 送礼物埋点
      sensorEvent('hw_classroom_interact_gift', this.options.roomMessage.roomInfo.commonOption, {
        interact_id: this.interactionData.interactId,
        gift_id: !this.selectedGift.giftId ? -1 : this.selectedGift.giftId,
        gift_price: !this.selectedGift.coin ? -1 : this.selectedGift.coin,
        click_button: 1
      })
      this.sendLogger(`点击送礼物按钮,selectedGift:${JSON.stringify(this.selectedGift)}`)
      if (this.selectedGift == null || this.selectedGift.giftId == '') {
        message.info(this.$t('classroom.interactions.gift.pleaseChooseGiftNotice'))
        return
      }
      if (this.isSending) {
        return
      }
      this.sendGift()
    },
    /**
     * 发送礼物
     */
    sendGift() {
      let data = {
        // 讲次ID
        planId: this.interactionData.planId,
        // 互动ID
        interactId: this.interactionData.interactId,
        // 礼物ID
        giftId: this.selectedGift.giftId,
        // 课堂ID
        classId: this.interactionData.classId
      }
      this.isSending = true
      classLiveSensor.osta_ia_gifts(this.interactionData.interactId, this.selectedGift)
      sendGiftApi(this, data)
        .then(res => {
          this.isSending = false

          // 达到上限不允许继续发送礼物
          if (res.code == '42001') {
            this.sendStatusToast(this.$t('classroom.interactions.gift.notEnoughTimes'))
            this.sendLogger('送礼物失败, 原因: 次数达到上限', '', 'error')
            return
          }

          // 金币数量不够
          if (res.code == '42002') {
            this.sendStatusToast(this.$t('classroom.interactions.gift.notEnoughCoin'), 3000)
            this.sendLogger('送礼物失败, 原因: 金币数量不够', '', 'error')
            return
          }

          // 接口返回异常处理
          if (res.code != 0) {
            message.info(res.msg)
            this.sendLogger(`送礼物失败, 原因: ${res.msg}`, '', 'error')
            return
          }

          const resData = res.data || {}
          this.$refs.giftSubmit.play()
          this.$refs.giftBarrage.addToList(this.selectedGift)
          this.sendGiftSuccessHandler(resData)
          this.sendLogger(`送礼物成功, params: ${JSON.stringify(data)} res: ${JSON.stringify(res)}`)
        })
        .catch(err => {
          this.sendLogger(`送礼物失败, 原因: ${JSON.stringify(err)}`, '', 'error')
          this.isSending = false
        })
    },
    /**
     * 发送礼物成功处理
     */
    sendGiftSuccessHandler(resData) {
      this.myOptions.roomMessage.roomInfo.stuInfo.goldNum = resData.userLatestCoin

      // 更新金币数量
      emitter.emit('updateAchievement', ['update', resData.userLatestCoin])
      this.userCoin = resData.userLatestCoin
      this.sendStatusData.sendStatus = resData.sendStatus
      this.sendStatusData.sendTimes = resData.sendTimes

      // this.sendSuccessToast()

      if (this.sendStatusData.sendTimes >= 3 || this.sendStatusData.sendStatus == 2) {
        this.sendStatusToast(this.$t('classroom.interactions.gift.notEnoughTimes'))
      }
    },
    /**
     * 送礼物成功的toast
     */
    sendSuccessToast() {
      var showSuccessToastTmer = null
      clearTimeout(showSuccessToastTmer)

      this.sendStatusData.isSuccessful = true
      this.submitGitPic = this.selectedGift.iconPc
      this.sendStatusData.successfulMsg = this.selectedGift.text.replace(
        /#.*?#/,
        this.$t('common.i')
      )

      showSuccessToastTmer = setTimeout(() => {
        this.sendStatusData.isSuccessful = false
      }, 3000)

      // 重置发送状态
      this.sendStatusData.sendMsg = ''
      this.sendStatusData.showSendMsg = false
    },
    /**
     * 展示发送礼物toast
     */
    sendStatusToast(msg = '', timing = 0) {
      var showErrorToastTimer = null
      clearTimeout(showErrorToastTimer)
      this.sendStatusData.sendMsg = msg
      this.sendStatusData.showSendMsg = true

      if (timing > 0) {
        showErrorToastTimer = setTimeout(() => {
          this.sendStatusData.showSendMsg = false
        }, 3000)
      }
    },
    chooseGift(giftItem) {
      this.sendLogger('点击选择礼物')
      this.$refs.giftSelect.play()
      this.selectedGift = giftItem
    },
    closeGiftsPannel() {
      sensorEvent('hw_classroom_interact_gift', this.options.roomMessage.roomInfo.commonOption, {
        interact_id: this.interactionData.interactId,
        gift_id: this.selectedGift.giftId == '' ? -1 : this.selectedGift.giftId,
        gift_price: this.selectedGift.coin == '' ? -1 : this.selectedGift.coin,
        click_button: 0
      })
      this.showGiftsPannel = false
      this.sendLogger('关闭送礼物窗口')
    },
    openGiftsPannel() {
      sensorEvent(
        'hw_classroom_interact_gift_icon',
        this.options.roomMessage.roomInfo.commonOption,
        {
          interact_id: this.interactionData.interactId
        }
      )
      this.sendLogger('打开送礼物窗口')
      this.showGiftsPannel = true
      this.arrowLeftDisabled = true
    },
    async queryCoinsCount() {
      const { code, data } = await queryCoinsCount(this).catch(err => {
        this.sendLogger(`接口报错:获取金币数量:${JSON.stringify(err)}`, '', 'error')
      })
      if (code != 0) {
        return
      }
      const userCoin = data.latestCoin || 0
      this.userCoin = userCoin
    },
    /**
     * 左右滚动
     */
    handleLeftScroll() {
      this.sendLogger('点击礼物窗口左边滚动')
      let scrollElement = this.$refs.scrollWrapper
      scrollElement.scrollLeft -= 124
      this.arrowLeftDisabled = true
      setTimeout(() => {
        this.elementScrollLeft = scrollElement.scrollLeft
        this.arrowLeftDisabled = false
      }, 300)
    },
    handleRightScroll() {
      this.sendLogger('点击礼物窗口右边滚动')
      let scrollElement = this.$refs.scrollWrapper
      scrollElement.scrollLeft += 124
      this.arrowRightDisabled = true
      setTimeout(() => {
        this.arrowRightDisabled = false
        this.elementScrollLeft = scrollElement.scrollLeft
      }, 300)
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionId = this.options?.ircMsg?.interactId || 'unknown'
      const interactionType = 'Gift'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
    }
  },
  watch: {
    elementScrollLeft(newVal) {
      console.log(newVal, 'newVal')
      if (newVal == 0) {
        this.arrowLeftDisabled = true
        this.arrowRightDisabled = false
      } else {
        this.arrowLeftDisabled = false
        let listWidh = 0
        const lists = Array.from(document.getElementsByClassName('gift-item'))
        lists.forEach(gift => {
          listWidh += gift.clientWidth + 20
        })
        // 首屏展示五个 需要减去这个宽度
        const scrollWidh = listWidh - 5 * 124
        if (newVal >= scrollWidh) {
          this.arrowRightDisabled = true
        } else {
          this.arrowRightDisabled = false
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use './gift.scss' as *;
</style>
