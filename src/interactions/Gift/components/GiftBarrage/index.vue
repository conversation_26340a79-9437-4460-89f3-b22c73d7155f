<template>
  <div class="stage" ref="stage">
    <vueBaberrage
      :isShow="barrageIsShow"
      :barrageList="messageList"
      :loop="barrageLoop"
      :lanesCount="3"
      :messageGap="10"
      ref="vueBaberrageRefRef"
    >
    </vueBaberrage>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue'
import vueBaberrage from '@/components/VueBaberrage/vue-baberrage.vue'
import _debounce from 'lodash/debounce'
import { useI18n } from 'vue-i18n'

// 获取国际化函数
const { t } = useI18n()

// 定义响应式状态
const barrageIsShow = ref(true)
const barrageLoop = ref(false)
const messageList = ref([])
const currentId = ref(0)
const timer = ref(null)

// DOM引用
const stage = ref(null)
const vueBaberrageRef = ref(null)

// 初始化方法
function init() {
  window.addEventListener('resize', resetWidth)

  // 确保组件已挂载
  if (vueBaberrageRef.value) {
    vueBaberrageRef.value.replay()
    resetWidth()
  } else {
    nextTick(() => {
      if (vueBaberrageRef.value) {
        vueBaberrageRef.value.replay()
        resetWidth()
      }
    })
  }
}

// 重设宽度方法
const resetWidth = _debounce(() => {
  nextTick(() => {
    const contentElement = document.querySelector('.class-content')
    if (contentElement && vueBaberrageRef.value) {
      vueBaberrageRef.value.boxWidthVal = contentElement.offsetWidth
    }
  })
}, 300)

// 添加消息
function addMessage(message) {
  console.log('添加弹幕消息', message)
  messageList.value.push(message)
}

// 清空列表
function removeList() {
  messageList.value = []
}

// 添加礼物到弹幕列表
function addToList(giftItem) {
  console.log('添加礼物到弹幕列表', giftItem)
  const text = giftItem.text.replace('#name#', t('common.i'))
  this.addMessage({
    icon_pc: giftItem.imagePc,
    message: `<span style='color:#FFDC0A'>${text}</span>`,
    time: 10
  })
}

// 卸载事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', resetWidth)
})

// 暴露方法给父组件
defineExpose({
  init,
  addMessage,
  removeList,
  addToList
})
</script>

<style lang="scss">
.stage {
  height: 300px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.baberrage-stage {
  top: 0;
  margin-top: 30px;
  z-index: 999;
  overflow: visible !important;
}

.baberrage-stage .baberrage-item.normal {
  height: 25px;
  padding: 0 12px !important;
  color: #fff;
  border-radius: 100px;
  background: rgba(15, 25, 42, 0.9);
}

.baberrage-item .normal {
  background: transparent !important;
  border-radius: 0;
  height: 25px;
  padding: 0 !important;
  align-items: center;
}

.baberrage-item .baberrage-msg {
  font-size: 12px;
  line-height: 25px;
}

.baberrage-item .normal .baberrage-avatar {
  position: relative;
  width: 30px !important;
  height: 30px !important;
  border-radius: 50%;
  top: -8px;

  img {
    width: 100% !important;
  }
}
</style>
