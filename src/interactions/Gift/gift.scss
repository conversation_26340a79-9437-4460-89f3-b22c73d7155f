@keyframes imgZoom {
  0% {
    transform: scale(0.9);
  }

  25% {
    transform: scale(1.1);
  }

  50% {
    transform: scale(0.9);
  }

  75% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(0.9);
  }
}

@keyframes show {
  0% {
    display: none;
  }

  100% {
    display: inline-block;
  }
}

// 单行文本溢出
.text-overflow-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.live-gifts-container {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  padding-bottom: 20px;
  min-height: 180px;
  text-align: center;
  background: transparent;

  .hover-trainsition {
    transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
}
.hiden {
  display: none;
}
.live-gifts-container .icon-btn {
  position: absolute;
  bottom: 13px;
  right: 66px;
  width: 39px;
  height: 39px;
  background: url('./imgs/gift-btn-bg.png');
  background-size: cover;
  cursor: pointer;
}

.live-gifts-container .toast-item-container {
  position: relative;
  display: inline-block;
  margin: 0 auto;
  padding: 3px 18px 3px 36px;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
  color: #fff;
  background: linear-gradient(222deg, #ffcd66 0%, #ff7c66 0%, #ff5d23 100%);
  border-radius: 13px;
  visibility: hidden;
  opacity: 0;

  &.show {
    visibility: visible;
    opacity: 1;
  }

  .gift-tost-icon {
    position: absolute;
    left: 6px;
    bottom: 2px;
    // width: ;
  }

  span {
    margin-left: 8px;
  }
}

.live-gifts-container .gifts-pannel-container {
  position: relative;
  // bottom: 0;
  margin: 11px auto 0;
  padding: 9px 20px 12px;
  width: 720px;
  height: 212px;
  background: #0f192a;
  color: #fff;
  border-radius: 14px;
  z-index: 99;

  .operate-text-container {
    position: absolute;
    left: 36px;
    right: 36px;
    text-align: center;
    color: #ffdc0a;
  }

  .close-icon-container {
    position: absolute;
    right: 0;
    top: 0;
    width: 32px;
    height: 22px;
    font-size: 12px;
    font-weight: 500;
    line-height: 22px;
    color: #fff;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 0px 14px 0px 14px;
    cursor: pointer;
  }

  .gifts-pannel-body-container {
    margin-top: 27px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    section {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    .arrow-left,
    .arrow-right {
      width: 24px;
      height: 46px;
      background: #162030;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      span {
        width: 8px;
        height: 12px;
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 100%;
        display: inline-block;
      }
    }
    .arrow-left {
      // margin-right: 20px;
      .arrow {
        background-image: url('./imgs/left-arrow.png');
      }
      .arrow-disabled {
        background-image: url('./imgs/left-arrow-disabled.png');
      }
    }
    .arrow-right {
      // margin-left: 20px;
      .arrow {
        background-image: url('./imgs/right-arrow.png');
      }
      .arrow-disabled {
        background-image: url('./imgs/right-arrow-disabled.png');
      }
    }
    .gifts-list-wrapper {
      // margin-left: 5px;
      width: 600px;
      .scroll-wrapper {
        // display: flex;
        display: -webkit-box;
        align-items: center;
        flex-direction: row;
        -webkit-overflow-scrolling: touch;
        // overflow-x: scroll;
        overflow: hidden;
        white-space: nowrap;
        scroll-behavior: smooth;
        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    .gifts-list-wrapper .gift-item {
      margin-right: 20px;
      padding: 10px 0 8px;
      width: 104px;
      height: 112px;
      text-align: center;
      font-size: 12px;
      line-height: 14px;
      background: rgba(255, 255, 255, 0.03);
      border-radius: 8px;
      border: 1px solid transparent;
      cursor: pointer;

      // &:last-child {
      //   margin-right: 0;
      // }

      .gift-image {
        margin: 0 auto;
        width: 52px;
        height: 50px;
      }
      .gift-image img {
        max-width: 100%; /* 图片不超过容器的宽度 */
        max-height: 100%; /* 图片不超过容器的高度 */
        width: auto; /* 等比例缩放 */
        height: auto; /* 等比例缩放 */
      }

      .gift-name {
        margin: 5px 8px;
        color: rgba(255, 255, 255, 0.3);
      }

      .gift-price {
        color: #fff;

        .icon-coins {
          margin-right: 5px;
          font-size: 18px;
          vertical-align: top;
        }
      }

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        .gift-name {
          color: rgba(255, 255, 255, 0.8);
        }
      }

      &.selected {
        border: 1px solid #ffaa0a;
      }
    }

    .account-operate-pannel-container {
      margin-top: 20px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .coins-item {
        display: flex;
        align-items: center;
      }
      .coins-item .header {
        width: 50px;
        height: auto !important;
        margin-right: 10px;
        font-size: 14px;
        // line-height: 24px;
        text-align: center;
        color: rgba(255, 255, 255, 0.3);
        // background: rgba(255, 255, 255, 0.03);
        border-radius: 5px;
      }

      .coins-item .account-conins-number {
        margin-top: 1px;
        // width: 100px;
        // height: 34px;
        font-size: 14px;
        // line-height: 34px;
        text-align: center;
        color: #fff;
        // background: rgba(255, 255, 255, 0.03);
        border-radius: 5px;

        .icon-coins {
          font-size: 18px;
          margin-right: 10px;
          vertical-align: middle;
        }
      }

      .btn {
        position: relative;
        // margin-top: 17px;
        width: 88px;
        height: 32px;
        font-size: 14px;
        line-height: 30px;
        text-align: center;
        background: #fff url('./imgs/btn-send-bg.png') center no-repeat;
        background-size: cover;
        border-radius: 18px;
        cursor: pointer;

        .btn-text {
          // margin-left: 5px;
          color: white;
        }

        &:disabled {
          &::after {
            content: '';
            position: absolute;
            top: -2px;
            bottom: -1px;
            left: -2px;
            right: -1px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 18px;
            cursor: not-allowed;
            z-index: 2;
          }
        }
      }
    }
  }
}

.btn-open-gifts-pannel {
  z-index: 99;
}
