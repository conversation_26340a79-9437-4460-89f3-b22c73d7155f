<template>
  <div v-if="show" class="class-praise">
    <audio src="./audio/show.mp3" class="hiden" ref="praiseShow"></audio>
    <audio src="./audio/click.mp3" class="hiden" ref="praiseClick"></audio>
    <audio src="./audio/open.mp3" class="hiden" ref="praiseOpen"></audio>
    <audio src="./audio/success.mp3" class="hiden" ref="praiseSuccess"></audio>
    <audio src="./audio/fail.mp3" class="hiden" ref="praiseFail"></audio>
    <div v-if="showStatus === 'large'" class="praise-large coins-small-class">
      <div class="treasure-box">
        <div v-if="showAnimation" class="status-unclaimed">
          <div v-if="openStatus">
            <lottie-player
              autoplay
              mode="normal"
              :src="getAssetPath('/lottiefiles/treasure/open/index.json')"
              class="lottie-player"
            >
            </lottie-player>
          </div>
          <div v-else @click="openTreasure('large')">
            <lottie-player
              autoplay
              loop
              mode="normal"
              :src="getAssetPath('/lottiefiles/treasure/normal/index.json')"
              class="lottie-player"
            >
            </lottie-player>
          </div>
        </div>
        <div v-if="showResult" class="status-result" :class="resultClassName">
          <div class="icon-close" @click="closeTreasure"></div>
          <div class="result-head">
            <div class="student-info-wrapper">
              <div class="student-info">
                <div class="student-avatar">
                  <img :src="resultData.avatar" />
                </div>
                <div class="student-name">{{ curOptions.nickName }}</div>
              </div>
            </div>
            <div class="message-wrapper">
              <div class="message-title">{{ resultData.messageTitle }}</div>
              <div class="message-content">{{ resultData.messageContent }}</div>
            </div>
          </div>
          <div class="coin-wrapper">
            <div class="coin-num">+{{ resultData.rewardCoin }}</div>
          </div>
          <div v-if="hasBadge" class="icon-badge" :class="badgeClassName"></div>
        </div>
      </div>
    </div>
    <div
      v-if="showStatus === 'small'"
      class="praise-small coins-small-class"
      @click="openTreasure('small')"
    ></div>
  </div>
</template>

<script>
import { emitter } from '@/hooks/useEventBus'
import { submitPraiseApi } from '@/api/classroom'
import { submitInteractPartakereport } from '@/api/classroom'
import logger from '@/utils/logger'
import { message } from 'ant-design-vue'
import '@lottiefiles/lottie-player'
import { sensorEvent } from '@/utils/sensorEvent'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { useClassData } from '@/stores/classroom'
import { getAssetPath } from '@/utils/assetPath'

export default {
  name: 'ClassPraiseComponent',
  props: {
    options: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      show: false, // 是否打开课堂表扬宝箱
      showStatus: 'large', // 宝箱显示状态，large: 大宝箱 small: 缩略小宝箱
      showAnimation: true, // 显示宝箱动画
      showResult: false, // 显示领取结果
      openStatus: false, // 宝箱打开状态
      timer: null, // 定时器
      interactId: '', // 互动ID
      planId: 0, // planId
      classId: '', // classId
      loading: false, // 请求loading
      // 结果数据
      resultData: {
        avatar: '', // 头像
        level: 0, // 等级
        title: '', // 称号
        messageTitle: '', // 描述标题
        messageContent: '', // 描述内容
        rewardCoin: 0, // 奖励金币数
        userLatestCoin: 0 // 最新金币数
      },
      curOptions: {},
      rewardCoin: 0
    }
  },
  computed: {
    hasBadge() {
      return this.resultData.level > 0
    },
    resultClassName() {
      return this.hasBadge ? 'result-success' : 'result-fail'
    },
    badgeClassName() {
      return this.hasBadge ? `level-${this.resultData.level}` : ''
    }
  },
  beforeUnmount() {
    this.sendLogger('课堂表扬组件销毁', 'destroy')
    this.timer && clearTimeout(this.timer)
  },
  methods: {
    getAssetPath,
    destroyInteraction() {
      this.sendLogger('收到销毁互动指令', 'destroy')
    },
    receiveMessage(ircMessage) {
      this.init(ircMessage)
      this.sendLogger(`收到课堂表扬消息: ${JSON.stringify(ircMessage)}`, 'start')
    },
    /**
     * 初始化
     */
    async init(ircMessage) {
      this.sendLogger('开始初始化课堂表扬', 'init')
      this.curOptions = this.options?.roomMessage?.roomInfo?.commonOption
      this.planId = this.curOptions.planId
      this.classId = this.curOptions.classId
      this.interactId = ircMessage?.interactId || ''

      this.sendLogger(
        `初始化参数 - planId:${this.planId} classId:${this.classId} interactId:${this.interactId}`,
        'init'
      )

      /**
      @疑问 window.thinkApi.ipc.invoke 以前的逻辑
      @答 未确认
      @解答人 未知
      @问题创建人 白海禄
      @创建时间 2025-05-25 15:11:23
      @是否解决 未解决
      */
      const store = useClassData()
      // 查询是否收到本次课堂表扬提醒
      const classPraiseInteractId = store.classPraiseInteractId
      // 收到课堂表扬提醒则不再显示
      if (classPraiseInteractId == this.interactId) {
        this.sendLogger(`重复的课堂表扬互动，跳过处理: ${this.interactId}`, 'check', 'warn')
        return
      }
      store.updateClassPraiseInteractId(this.interactId)
      this.sendLogger(`更新课堂表扬互动ID: ${this.interactId}`, 'update')
      // 记录已收到课堂表扬提醒
      // await window.thinkApi.ipc.invoke(
      //   'setStoreValue',
      //   `classPraiseInteractId_${this.planId}`,
      //   this.interactId
      // )

      // 上报互动
      this.sendLogger('上报互动参与', 'report')
      submitInteractPartakereport({
        planId: this.planId,
        classId: this.classId,
        interactId: this.interactId
      }).catch(err => {
        this.sendLogger(`上报互动参与失败: ${err.message || err}`, 'report', 'error')
      })

      this.show = true
      this.resetData()
      this.sendLogger('显示课堂表扬宝箱', 'show')
      this.$nextTick(() => {
        this.$refs.praiseShow.play()
        this.sendLogger('播放表扬音效', 'audio')
      })
      // 5秒后最小化宝箱
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.minimizeTreasure()
      }, 5000)
    },

    /**
     * 打开宝箱
     */
    openTreasure(type) {
      this.sendLogger(`点击打开宝箱 - 入口:${type}`, 'click')
      sensorEvent('hw_classroom_interact_treasure_click', this.options, {
        interact_id: this.interactId,
        click_button: type == 'large' ? 1 : 0
      })
      this.showStatus = 'large'
      this.openStatus = true
      this.$refs.praiseClick.play()
      this.sendLogger('播放点击音效', 'audio')
      this.timer && clearTimeout(this.timer)
      setTimeout(() => {
        this.showAnimation = false
        this.sendLogger('开始提交表扬请求', 'submit')
        this.submitPraise()
          .then(async data => {
            this.sendLogger(
              `表扬请求成功 - 奖励金币:${data.rewardCoin} 等级:${data.level}`,
              'success'
            )
            this.delayClose(10000) // 5s后自动关闭
            this.showResult = true
            if (this.hasBadge) {
              this.$refs.praiseSuccess.play()
              this.sendLogger('播放成功音效', 'audio')
            } else {
              this.$refs.praiseFail.play()
              this.sendLogger('播放失败音效', 'audio')
            }
            const { rewardCoin } = data
            this.rewardCoin = rewardCoin
            Object.keys(data).forEach(key => {
              this.resultData[key] = data[key]
            })
            classLiveSensor.osta_ia_praise(this.interactId, data.rewardCoin)
          })
          .catch(error => {
            this.sendLogger(`表扬请求失败: ${error.message || error}`, 'submit', 'error')
            this.closeTreasure()
            console.error('打开宝箱报错', error)
          })
      }, 2200)
      this.sendLogger(`点击打开宝箱, 入口来自: ${type}`)
    },
    /**
     * 关闭宝箱
     */
    closeTreasure() {
      this.sendLogger('关闭课堂表扬宝箱', 'close')
      this.show = false
      this.resetData()
      this.sendLogger('完课宝箱关闭')
      // 更新金币
      if (this.rewardCoin) {
        this.sendLogger(`更新金币数量: ${this.rewardCoin}`, 'coin')
        emitter.emit('addCoin', true, this.rewardCoin, true)
        // this.$bus.$emit('addCoin', true, this.rewardCoin, true)
      }
      this.$emit('close', 'classroom_praise')
    },
    /**
     * 重置数据
     */
    resetData() {
      this.sendLogger('重置宝箱数据', 'reset')
      this.showAnimation = true
      this.showResult = false
      this.openStatus = false
      this.showStatus = 'large'
    },

    /**
     * 最小化宝箱
     */
    minimizeTreasure() {
      this.sendLogger('最小化宝箱', 'minimize')
      this.showStatus = 'small'
      this.delayClose(60000 * 5) // 5分钟后自动关闭
      this.sendLogger('宝箱最小化')
    },

    /**
     * 延迟关闭
     */
    delayClose(time) {
      this.sendLogger(`设置延迟关闭 - 延迟时间:${time}ms`, 'timer')
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.closeTreasure()
        this.timer = null
        this.sendLogger(`宝箱延迟关闭, 延迟时间: ${time}ms`)
      }, time)
    },

    /**
     * 领取奖励
     */
    async submitPraise() {
      if (this.loading) {
        this.sendLogger('请求进行中，跳过重复请求', 'submit', 'warn')
        return
      }
      this.loading = true
      const params = {
        classId: this.classId,
        planId: Number(this.planId),
        interactId: String(this.interactId)
      }
      this.sendLogger(`发送领取奖励请求 - 参数:${JSON.stringify(params)}`, 'api')
      const res = await submitPraiseApi(params).catch(err => {
        this.sendLogger(`领取奖励接口调用失败: ${err.message || err}`, 'api', 'error')
        throw err
      })
      this.loading = false
      this.sendLogger(
        `提交领取奖励接口, params: ${JSON.stringify(params)} res: ${JSON.stringify(res)}`,
        'submit'
      )
      if (res.code != 0) {
        this.sendLogger(`领取奖励失败 - 错误码:${res.code} 错误信息:${res.msg}`, 'api', 'error')
        message.info(res.msg)
        return Promise.reject(res.msg)
      }
      const data = res.data || {}
      this.sendLogger(`领取奖励成功 - 数据:${JSON.stringify(data)}`, 'api')
      return Promise.resolve(data)
    },

    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionId = this.interactId || 'unknown'
      const interactionType = 'ClassPraise'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
    }
  }
}
</script>

<style lang="scss" scoped>
.class-praise {
  pointer-events: none !important;
  width: 100%;
  height: 100%;
}
.praise-large {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 998;
  background: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  pointer-events: auto;
  &.coins-small-class {
    width: 100%;
    height: 100%;
    bottom: 0;
  }
  .treasure-box {
    position: relative;
    width: 500px;
    height: 500px;
    .lottie-player {
      margin: 0 auto;
    }
    .status-unclaimed {
      cursor: pointer;
      .notice {
        max-height: 60px;
        margin: 0 40px;
        text-align: center;
        color: #172b4d;
        line-height: 20px;
        overflow: hidden;
      }
    }
    .status-result {
      position: absolute;
      left: 79px;
      top: 79px;
      width: 342px;
      height: 338px;
      background-size: cover;
      animation: treasureMove 0.5s ease;
      &.result-success {
        background-image: url('./imgs/bg-success.png');
        .coin-wrapper {
          display: flex;
          justify-content: center;
          position: absolute;
          left: 126px;
          top: 265px;
          width: 90px;
          height: 28px;
          overflow: hidden;
          &::before {
            content: '';
            display: block;
            width: 24px;
            height: 24px;
            background: url('./imgs/icon-coin.png') no-repeat;
            background-size: cover;
            background-position: 0 -2px;
            margin-top: 2px;
          }
          .coin-num {
            margin-left: 5px;
            line-height: 28px;
            color: #ff850a;
            font-size: 18px;
            font-weight: 500;
            max-width: 50px;
            overflow: hidden;
          }
        }
      }
      &.result-fail {
        background-image: url('./imgs/bg-fail.png');
        .coin-wrapper {
          position: absolute;
          right: 36px;
          top: 155px;
          width: 62px;
          height: 60px;
          background-image: url('./imgs/bg-coins.png');
          background-size: cover;
          overflow: hidden;
          .coin-num {
            width: 45px;
            margin: 12px auto 0 auto;
            color: #fff;
            font-size: 18px;
            font-weight: 500;
            text-align: center;
          }
        }
      }
      .icon-close {
        position: absolute;
        right: -54px;
        top: 0;
        width: 34px;
        height: 34px;
        background: url('./imgs/icon-close.png') no-repeat;
        background-size: cover;
        cursor: pointer;
      }
      .result-head {
        overflow: hidden;
      }
      .student-info-wrapper {
        display: flex;
        justify-content: center;
      }
      .student-info {
        display: flex;
        height: 34px;
        margin: 33px auto 0 auto;
        padding-right: 5px;
        background: #ff9c02;
        border: 3px solid #ffe548;
        border-radius: 17px;
      }
      .student-avatar {
        width: 28px;
        img {
          width: 28px;
          height: 28px;
          border-radius: 14px;
        }
      }
      .student-name {
        flex: 1;
        max-width: 110px;
        min-width: 50px;
        height: 28px;
        line-height: 28px;
        font-size: 16px;
        color: #fff;
        text-align: center;
        font-weight: 500;
        overflow: hidden;
        word-break: break-word;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .message-wrapper {
        width: 300px;
        margin: 5px auto 0 auto;
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
      }
      .message-title,
      .message-content {
        height: 20px;
        line-height: 20px;
        overflow: hidden;
      }
      .icon-badge {
        width: 144px;
        height: 144px;
        margin: 0 auto;
        background-repeat: no-repeat;
        background-size: cover;
        &.level-1 {
          background-image: url('@/assets/images/live/badges/primary/level-1.png');
        }
        &.level-2 {
          background-image: url('@/assets/images/live/badges/primary/level-2.png');
        }
        &.level-3 {
          background-image: url('@/assets/images/live/badges/primary/level-3.png');
        }
        &.level-4 {
          background-image: url('@/assets/images/live/badges/primary/level-4.png');
        }
        &.level-5 {
          background-image: url('@/assets/images/live/badges/primary/level-5.png');
        }
        &.level-6 {
          background-image: url('@/assets/images/live/badges/primary/level-6.png');
        }
        &.level-7 {
          background-image: url('@/assets/images/live/badges/primary/level-7.png');
        }
      }
    }
  }
}
.praise-small {
  position: absolute;
  z-index: 998;
  width: 39px;
  height: 39px;
  cursor: pointer;
  background: url('./imgs/treasure-thumbnail.png') no-repeat;
  background-size: cover;
  pointer-events: auto;
  &.coins-large-class {
    right: 27.5%;
    bottom: 138px;
  }
  &.coins-small-class {
    bottom: 115px;
    right: 66px;
  }
}
@keyframes treasureMove {
  from {
    top: -50px;
  }
  to {
    top: 79px;
  }
}
</style>
