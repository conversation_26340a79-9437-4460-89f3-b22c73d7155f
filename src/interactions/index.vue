<template>
  <div class="class-interaction" id="interactionController">
    <template v-for="interaction in interactions">
      <component
        :ref="interaction.name"
        :is="interaction.componentName"
        :key="interaction.name"
        :layout="interaction.layout"
        v-if="interaction.isShow"
        :options="interaction.options"
        @close="interactionClose"
        style="pointer-events: auto"
      ></component>
    </template>
  </div>
</template>

<script>
import {
  defineComponent,
  getCurrentInstance,
  nextTick,
  onBeforeUnmount,
  reactive,
  computed
} from 'vue'
import DrawBoard from './DrawBoard/index.vue'
import ClassPraiseList from './ClassPraiseList/index.vue'
// import RandomCall from './RandomCall'
import Gift from '@/interactions/Gift/index.vue'
import Vote from './vote/index.vue'
import NonpresetFillBlank from './NonpresetFillBlank/index.vue'
import VoteForCourseware from './VoteForCourseware/index.vue'
import VoteInCourseware from './VoteInCourseware/index.vue'
import StudentRank from './StudentRank/index.vue'
import FillBlankForCourseware from './FillBlankForCourseware/index.vue'
import OrientationCoins from './orientationCoins/index.vue'
import ClassPraise from './classPraise/index.vue'
import SchulteTable from './schulteTable/index.vue'
import RedPacket from './redPacket/index.vue'
import RedPacketRain from './redPacketRain/index.vue'
import SpeedyHand from './speedyHand/index.vue'
import ClassExamination from './ClassExamination/index.vue'
import ClassRest from './classRest/index.vue'
import logger from '@/utils/logger'
// import { clearScreenShotTimer, setIntervalScreenShot } from '../hooks/useInteractScreenShot.js'
import { useClassData } from '@/stores/classroom'
import { emitter } from '@/hooks/useEventBus'

export default defineComponent({
  name: 'intera-ctions',
  components: {
    VoteForCourseware,
    VoteInCourseware,
    SchulteTable,
    RedPacket,
    RedPacketRain,
    DrawBoard,
    ClassPraiseList,
    // RandomCall,
    Gift,
    Vote,
    NonpresetFillBlank,
    StudentRank,
    FillBlankForCourseware,
    OrientationCoins,
    ClassPraise,
    SpeedyHand,
    ClassExamination,
    ClassRest
  },
  setup(props) {
    const VOTE_FOR_COURSEWARE = 'interact'
    const VOTE_IN_COURSEWARE = 'interact_in_courseware'
    const isCourseware4b3 = computed(() => props.isCourseware4b3)
    isCourseware4b3
    const { proxy } = getCurrentInstance()
    const store = useClassData()
    const baseData = computed(() => store.baseData)
    const getOtherOpenVote = key => {
      let otherKey = ''
      if (key === VOTE_FOR_COURSEWARE) {
        otherKey = VOTE_IN_COURSEWARE
      } else if (key === VOTE_IN_COURSEWARE) {
        otherKey = VOTE_FOR_COURSEWARE
      } else {
        return false
      }
      console.log('otherKey', otherKey)
      const interact = findInteraction(otherKey)
      console.log('interact', interact)
      if (interact.isShow) {
        return interact
      } else {
        return false
      }
    }
    const interactions = reactive([
      {
        name: 'TakePicture', // 拍照上墙
        componentName: 'TakePicture',
        layout: 'full',
        isShow: false,
        keys: ['take_picture'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'DrawBoard', // 涂鸦画板
        componentName: 'DrawBoard',
        layout: isCourseware4b3.value ? 'isFourToThree' : 'full',
        isShow: false,
        keys: ['graffiti_board'],
        options: {
          ...baseData.value,
          ircMsg: ''
        }
      },
      {
        name: 'ClassPraiseList', // 课堂表扬榜
        componentName: 'ClassPraiseList',
        layout: 'isFourToThree',
        isShow: false,
        keys: ['class_praise_list'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      // {
      //   name: 'RandomCall',
      //   componentName: 'RandomCall',
      //   layout: 'full',
      //   isShow: false,
      //   keys: ['small_random_call'],
      //   options: {
      //     roomMessage: {
      //       roomInfo: {
      //         ...baseData.value
      //       }
      //     },
      //     ircMsg: ''
      //   }
      // },
      {
        name: 'Gift',
        componentName: 'Gift',
        layout: 'full',
        isShow: false,
        keys: ['openGift'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'Vote', // 投票
        componentName: 'Vote',
        layout: 'full',
        isShow: false,
        keys: ['vote'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'NonpresetFillBlank', // 投票
        componentName: 'NonpresetFillBlank',
        layout: 'full',
        isShow: false,
        keys: ['nonpreset_fill_blank'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'VoteForCourseware', // 课件内互动
        componentName: 'VoteForCourseware',
        layout: 'full',
        isShow: false,
        keys: [VOTE_FOR_COURSEWARE],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'VoteInCourseware', // 课件答题内互动
        componentName: 'VoteInCourseware',
        layout: 'full',
        isShow: false,
        keys: [VOTE_IN_COURSEWARE],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'StudentRank',
        componentName: 'StudentRank',
        layout: 'full',
        isShow: false,
        keys: ['student_rank'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'FillBlankForCourseware',
        componentName: 'FillBlankForCourseware',
        layout: 'isFourToThree',
        isShow: false,
        keys: ['fill_blank'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'OrientationCoins', // 定向金币
        componentName: 'OrientationCoins',
        layout: 'full',
        isShow: false,
        keys: ['distribute_coins'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'ClassPraise', // 完课宝箱
        componentName: 'ClassPraise',
        layout: 'full',
        isShow: false,
        keys: ['classroom_praise'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'SchulteTable', // 舒尔特方格
        componentName: 'SchulteTable',
        layout: 'full',
        isShow: false,
        keys: ['schulte_table'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'RedPacket', // 普通红包
        componentName: 'RedPacket',
        layout: 'full',
        isShow: false,
        keys: ['redPacket'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'RedPacketRain', // 红包雨
        componentName: 'RedPacketRain',
        layout: 'full',
        isShow: false,
        keys: ['red_packet_rain'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'SpeedyHand',
        componentName: 'SpeedyHand',
        layout: 'full',
        isShow: false,
        keys: ['speedyHand'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      },
      {
        name: 'ClassExamination', // 课中考试
        componentName: 'ClassExamination',
        layout: 'isFourToThree',
        isShow: false,
        keys: ['class_examination'],
        options: {}
      },
      {
        name: 'ClassRest',
        componentName: 'ClassRest',
        layout: 'full',
        isShow: false,
        keys: ['classRest'],
        options: {
          roomMessage: {
            roomInfo: {
              ...baseData.value
            }
          },
          ircMsg: ''
        }
      }
    ])
    const noRepeatTrigger = ['small_random_call', 'speedyHand', 'distribute_coins', 'redPacket']

    // const openScreenShot = [
    //   'red_packet_rain',
    //   'schulte_table',
    //   'fill_blank',
    //   'interact',
    //   'nonpreset_fill_blank',
    //   'vote',
    //   'take_picture'
    // ]

    const handleOpenInteraction = (interaction, options) => {
      sendLogger(`准备开启互动 - 类型:${interaction.name} 键值:${options.key}`, 'open')
      console.log('🔍 [DEBUG] handleOpenInteraction被调用:', {
        interactionName: interaction.name,
        key: options.key,
        noticeContent: options.noticeContent
      })

      interaction.isShow = true
      interaction.options.ircMsg = options.noticeContent
      interaction.options.isHistory = options.isHistory
      interaction.options['sendTime'] = options.sendTime
      // interaction.options.roomMessage.roomInfo = { ...baseData.value }
      console.log('看一下传参值', baseData.value, interaction, options)
      sendLogger(`互动参数设置完成 - 互动ID:${options.noticeContent?.interactId}`, 'open')

      if (interaction.name === 'NonpresetFillBlank') {
        sendLogger('正在打开非预设填空组件', 'open')
        console.log('🔍 [DEBUG] 正在打开NonpresetFillBlank组件')
      }

      nextTick(() => {
        if (proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0].receiveMessage) {
          sendLogger(`调用组件receiveMessage方法 - 组件:${interaction.name}`, 'message')
          console.log('🔍 [DEBUG] 调用组件的receiveMessage方法:', {
            componentName: interaction.name,
            noticeContent: options.noticeContent
          })
          proxy.$refs[interaction.name][0].receiveMessage(options.noticeContent)
        } else {
          sendLogger(
            `组件ref或receiveMessage方法不存在 - 组件:${interaction.name}`,
            'message',
            'warn'
          )
          console.log('🔍 [DEBUG] 组件ref或receiveMessage方法不存在:', {
            componentName: interaction.name,
            hasRef: !!proxy.$refs[interaction.name],
            hasReceiveMessage: !!(
              proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0].receiveMessage
            )
          })
        }
      })
      // if (openScreenShot.includes(options.key)) {
      //   const planId = baseData.value?.planInfo?.id
      //   setIntervalScreenShot(planId, options?.noticeContent?.interactId, options?.key)
      // }
    }
    emitter.on('interactions', options => {
      const { key, noticeContent, isTrigger } = options
      sendLogger(`收到互动消息 - 键值:${key} 是否触发:${isTrigger}`, 'receive')
      console.log('[互动排查] 收到互动消息:', { key, noticeContent, isTrigger })

      // 如果当前key是课件内作答，还要判断课件外作答是否打开，如果key是课件外作答，还要判断课件内作答是否打开
      const voteInteract = getOtherOpenVote(key)
      console.log('[互动排查] voteInteract:', voteInteract)
      if (voteInteract) {
        sendLogger(
          `检测到其他投票互动正在开启，需要关闭 - 当前:${key} 冲突:${voteInteract.name}`,
          'conflict'
        )
        console.error(`vote-另一种投票正在开启中，需要关闭，当前key:${key}`)
        // 如果获取到了另一个开启中的投票，则先把当前关闭，再继续运行逻辑
        if (proxy.$refs[voteInteract.name] && proxy.$refs[voteInteract.name][0].forceEndInteract) {
          proxy.$refs[voteInteract.name][0].forceEndInteract()
          sendLogger(`强制结束冲突互动 - 互动:${voteInteract.name}`, 'conflict')
        } else {
          voteInteract.isShow = false
          sendLogger(`直接关闭冲突互动 - 互动:${voteInteract.name}`, 'conflict')
        }
      }

      if (noRepeatTrigger.includes(key) && isTrigger) {
        sendLogger(`检测到重复触发互动 - 键值:${key}`, 'repeat', 'warn')
        // logger.send({
        //   tag: 'student.Interact',
        //   level: 'warn',
        //   content: {
        //     msg: '重复触发互动',
        //     key
        //   }
        // })
        return
      }

      const { pub, open, publishTopic } = noticeContent
      console.log('[互动排查] pub:', pub, 'open:', open, 'publishTopic:', publishTopic)
      const interaction = findInteraction(key)
      if (!interaction) {
        sendLogger(`未找到对应互动 - 键值:${key}`, 'error', 'error')
        console.info(`未找到互动, key:${key}`)
        return
      }

      if (pub || open || publishTopic) {
        sendLogger(`触发互动开启 - 互动:${interaction.name} 键值:${key}`, 'trigger')
        console.log('[互动排查] 进入触发互动分支')
        store.updateInteractionStatus({
          interactionName: key,
          interactionStatus: true
        })

        // 如果当前有同一类型互动且不是同一个，则先销毁上一个再打开新的互动
        if (interaction.isShow) {
          if (noticeContent.interactId !== interaction.interactId && key !== 'distribute_coins') {
            sendLogger(
              `检测到重复互动，关闭旧互动 - 旧ID:${interaction.interactId} 新ID:${noticeContent.interactId}`,
              'replace'
            )
            console.error(
              `重复打开互动, 关闭上一个互动,oldInteractId:${interaction.interactId},newInteractId:${noticeContent.interactId},key:${key}`
            )
            console.log('观察传值', interaction.interactId, noticeContent.interactId, key)
            if (
              proxy.$refs[interaction.name] &&
              proxy.$refs[interaction.name][0].forceEndInteract
            ) {
              proxy.$refs[interaction.name][0].forceEndInteract().then(() => {
                nextTick(() => {
                  handleOpenInteraction(interaction, options)
                })
              })
              sendLogger('强制结束旧互动成功，准备开启新互动', 'replace')
              console.error(`重复打开互动, 关闭上一个互动成功后运行打开`)
              return
            }
            interaction.isShow = false
            nextTick(() => {
              handleOpenInteraction(interaction, options)
            })
          } else {
            sendLogger(`互动已存在，直接发送消息 - 互动:${interaction.name}`, 'update')
            if (proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0].receiveMessage) {
              interaction.options.isHistory = options.isHistory
              proxy.$refs[interaction.name][0].receiveMessage(options.noticeContent)
            }
          }
        } else {
          handleOpenInteraction(interaction, options)
        }
        interaction.interactId = noticeContent.interactId
        sendLogger(
          `互动ID已更新 - 互动:${interaction.name} ID:${noticeContent.interactId}`,
          'update'
        )
      } else if (
        pub === false ||
        open === false ||
        pub === 0 ||
        open === 0 ||
        publishTopic === false // publishTopic 是答题板私有控制，答题板的关闭，是由端上的倒计时决定的
      ) {
        sendLogger(`触发互动关闭 - 互动:${interaction.name} 键值:${key}`, 'close')
        console.log('[互动排查] 未进入触发互动分支，pub/open/publishTopic都为false')
        store.updateInteractionStatus({
          interactionName: key,
          interactionStatus: false
        })
        // console.log('@@@@', interaction)

        // 有些互动在收到停止作答和结束互动信令时还需要例如提交答案等操作，因此需要内部处理
        if (proxy.$refs[interaction.name] && proxy.$refs[interaction.name][0]?.destroyInteraction) {
          sendLogger(`调用互动销毁方法 - 互动:${interaction.name}`, 'destroy')
          console.log('[互动排查] 进入destroyInteraction分支')
          proxy.$refs[interaction.name][0]?.destroyInteraction(noticeContent)
          // clearScreenShotTimer()
          return
        }
        // clearScreenShotTimer()
        interaction.isShow = false
        sendLogger(`互动已关闭 - 互动:${interaction.name}`, 'close')
      }
    })

    function findInteraction(key) {
      // @log-ignore
      const interaction = interactions.find(item => item.keys.includes(key))
      return interaction
    }

    function interactionClose(key) {
      sendLogger(`手动关闭互动 - 键值:${key}`, 'manual_close')
      console.log('关闭互动', key)
      const interaction = findInteraction(key)
      interaction.isShow = false
      sendLogger(`互动关闭完成 - 互动:${interaction?.name}`, 'manual_close')
    }

    /**
     * 日志上报
     */
    const sendLogger = (msg, stage = '', level = 'info') => {
      const interactionType = 'InteractionManager'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: 'manager',
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][manager] ${msg}`, { stage, level })
    }

    onBeforeUnmount(() => {
      sendLogger('互动管理器开始销毁', 'destroy')
      // clearScreenShotTimer()
      emitter.off('interactions')
      sendLogger('互动管理器销毁完成', 'destroy')
    })
    return {
      baseData,
      interactions,
      interactionClose
    }
  }
})
</script>
<style scoped>
.class-interaction {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
