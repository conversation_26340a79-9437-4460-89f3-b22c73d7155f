// .class-content {
//     height: 100%;
//     width: 100%;
//     background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAP4AAADwBAMAAADSssQSAAAAAXNSR0IArs4c6QAAABtQTFRFMjIyMzMzNDQ0NTU1NjY2Nzc3ODg4OTk5Ojo6QUDc4AAAA9ZJREFUeNrtnSt320AQhUeSHdOwGAYaFhoaGgYWGgo6zDROU9+fXaC2lmOvtI/Z2SfbE7Dn3M3cydWnWYWAHRER0U8Av4blAgDehvUWwO9h2QHAYVhvAFyGZQMAx2H9AgDPw/oE4GNYrgDgdVj3AD6H5RKgR7usAVymdzldd3kCgB+TIvZKEWS4y9f9LtvrUbTfRfzVphZBDruMTug8+vFIxPWc//+av4mgmV362V0UxaISAQDv12IhjZI7GpXcea7kbkSQfuGalZymb8iq5BRHMfJNoymCbAv33zkrjmJp5JubXTRL7qBdcvO+sShc+5J7IMJwF82S0/eNfcNfG/lm+bhYdAv37NqqFSLuCnfnr+E/EqFRchoN39o3PA2/s/aNY8P/cPWN74xh4j6pkrtzn3avfDLOGDMivGeMGd+0EyXH3vDfFO5T7MKSMaZ94z9jTIuQyhit4gFDs3CdM4aO+8wKl8U3YhlD4Ruehn+y9Y1jw3f2TYiGP/m3z1PGUIkgoYyhEkFCGUPlG/LxUGngG3q4S2ve8C2zOclkDKVvyLbhM2VzEskYat9QEfxvG4r/zfqGl/9NBaWI+N9IRDH871j5XxH8z0pEuvzvpfK/dPhfV/mfP/63DcX/XH2TJ//Tf6bNh/9ZBaVS+F9TKv87VP4XF/+jnPlfV/lfQvyvT4P/+cvmZfC/lo//MWfzzOf/SuF/Zr5Jnv8to+d/FJj/LXzyvy4H/teF4n/vlf9V/nfjPsmB79D87yu7+T9XGOM9YzDxP+uMUfmfH/5HgfnfovI/96AkljEq/yuS/104+V9bOP/bx8v/LH2TNP9j8E3m/G9WRF7zf02B/M8tKJFExqj8L0b+t2Hjf86+KWr+T5r/9Wz8z1c2Z8wYVhCzFP638sL/zHzjjf9Jzf81BfI/rzDGkf+5+qbyP/kXPhb8z1c2z3P+T1+Enxc+WfM/zhel2fG/VQT8T3+IMZ/5P0sRkvzvmZP/MV2UIPEPPlT+NxZB0h98KH3+T5T/zYsg6Rc+ifA/2WGsxPgfe1Aqiv/tBPifmQhR/vdpxv8ELmMWw//2kvzP7vZ9gMuY+c3/5cP/BGBM5X9R8j+py5h58j9tEWID32uzr29uhWAMmRVuqfzP2zAWCWUMM/7XVP7nbeA7T/5nO4xVBP+jOv9X+V8c/K9n43+cvpHlf6858D/WixKCH3yo/E+f/wnBmFT4n6+LElnwP4qf/23q/F8R/K9n438S2TzEBx9C8T8D95lljI21iHT5n9f/vhMp/2MPSrHxP+nLmEnxP4qf/23Y+J8MjAnN//4Ax3H9/N6BeDoAAAAASUVORK5CYII=);
//     background-repeat: repeat;
//     position: absolute;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     overflow: hidden;
//     pointer-events: auto;
// }
// .schulte-table-bg {
//     background: url('./imgs/bg-normal.png') no-repeat center center;
//     background-size: 100% 100%;
// }
// .schulte-table {
//     position: absolute;
//     bottom: 0;
//     z-index: 699;
//     width: calc(100% / 16 * 9 / 3 * 4);
//     height: 100%;
//     text-align: center;
//     .box-title {
//         position: absolute;
//         top: 40px;
//         left: 50%;
//         transform: translateX(-50%);
//         width: 444px;
//         height: 187px;
//         background: url('./imgs/bg-box-title.png') no-repeat center center;
//         background-size: 100% 100%;
//         color: #FFFFFF;
//         text-align: center;
//         &-main {
//             margin-top: 68px;
//             width: 444px;
//             height: 72px;
//             line-height: 72px;
//             font-size: 52px;
//             font-weight: bold;
//         }
//         &-sub {
//             margin-top: 14px;
//             width: 444px;
//             height: 25px;
//             line-height: 25px;
//             font-size: 18px;
//             font-weight: 500;
//         }
//     }
//     // 两行问题
//     .box-bottom {
//         position: absolute;
//         bottom: 55px;
//         left: 50%;
//         transform: translateX(-50%);
//         width: 480px;
//         height: 56px;
//         padding: 6px 0px;
//         background: url('./imgs/bg-bottom.png') no-repeat center center;
//         background-size: 100% 100%;
//         color: #FFFFFF;
//         text-align: center;
//         font-size: 16px;
//         font-weight: 500;
//         p: {
//           line-height: 22px;
//         }
//     }
//     // 用百分比形式保证相对位置不变
//     .playing {
//         position: absolute;
//         top: 0;
//         width: 100%;
//         height: 100%;
//         background: rgba(255,255,255,0.1);
//         backdrop-filter: blur(1.774418604651163px);
//         display: flex;
//         flex-direction: row;
//         .playing-left {
//             width: 58.72%;
//             // margin: 5.77% 0 0 5.77%;
//             margin: 20% 0 0 5.77%;
//             height: 0;
//             padding-bottom: calc(64.52%);
//             position: relative;
//             .game-box{
//                 position: absolute;
//                 width: 100%;
//                 height: 100%;
//                 left: 0;
//                 top:0;
//                 display: flex;
//                 flex-direction: column;
//             }
//             .top-tip {
//                 height: 0;
//                 padding-bottom: 7.59%;
//                 background: rgba(255, 255, 255, 0.6);
//                 border-radius: 7px;
//                 border: 1px solid #93A19A;
//                 backdrop-filter: blur(5.323255813953488px);
//                 font-size: 14px;
//                 color: #783B00;
//                 margin-bottom: 1.78%;
//                 z-index: 10;
//                 p{
//                     position: absolute;
//                     width: 100%;
//                     height: 100%;
//                     left: 0;
//                     top: 0;
//                     display: flex;
//                     align-items: center;
//                     justify-content: center;
//                 }
//             }
//             .top-tip-light {
//                 background: #FFFFFF;
//                 border: 1px solid #C3C3C3;
//                 backdrop-filter: blur(5.323255813953488px);
//                 color: #783B00;
//             }
//             .content {
//                 flex:1;
//                 background: rgba(255, 255, 255, 0.5);
//                 border-radius: 14px;
//                 border: 1px solid #93A19A;
//                 backdrop-filter: blur(5.323255813953488px);
//                 .mode-disabled {
//                     pointer-events: none;
//                     cursor: not-allowed;
//                 }
//                 .mode-9, .mode-16, .mode-25 {
//                     z-index: 1;
//                     display: flex;
//                     flex-wrap: wrap;
//                     height: 100%;
//                     padding: 2%;
//                     .mode-item {
//                         position: relative;
//                         opacity: 1;
//                         display: flex;
//                         justify-content: center;
//                         align-items: center;
//                         color: #FFF9E9;
//                         text-align: center;
//                         vertical-align: middle;
//                         background-image: url(./imgs/box.png);
//                         background-position: 0 0;
//                         background-size: auto 100%;
//                         .bg-mask {
//                             position: fixed;
//                             z-index: 3;
//                             border-radius: 50%;
//                             filter: blur(6px);
//                             box-shadow: 0 0 0 10000px rgba(0, 0, 0, 0.7);
//                             opacity: 0;
//                             animation: opacShow 400ms 400ms forwards;
//                         }
//                         .bg-mask-lottie {
//                             position: fixed;
//                             z-index: 10;
//                             width: 16.40625%;
//                             height: 8.4375%;
//                             opacity: 0;
//                             animation: opacShow 400ms 800ms forwards;
//                         }
//                         .num{
//                             z-index: 2;
//                             display: flex;
//                             align-items: center;
//                             justify-content: center;
//                             font-weight: bold;
//                         }
//                         .error-zadan {
//                             // 注意不同场景的宽高比例值不同
//                             position: absolute;                  
//                             left: 50%;
//                             top: 50%;
//                             transform: translate(-50%, -50%);
//                             background-image: url(./imgs/zadan.png);
//                             background-position: 0 0;
//                             background-size: cover;
//                             animation: bbb 0.6s steps(14) forwards;
//                         }
//                         .correct-flower {
//                             width: 150%;
//                             height: 150%;
//                             position: absolute;
//                             left: 50%;
//                             top: 50%;
//                             transform: translate(-50%, -50%);
//                             background-image: url(./imgs/correct.png);
//                             background-position: 0 0;
//                             background-size: cover;
//                             animation: bbb 0.28s steps(6) forwards;
//                         }
//                     }
//                     .mode-item-up {
//                         z-index: 1;
//                     }
//                     .press-box {
//                         animation: pressBox 120ms linear forwards;
//                     }
//                 }
//                 .mode-reversal {
//                     .mode-item {
//                         animation: bbb 0.28s steps(4) forwards;
//                     }
//                     .mode-item {
//                         .num{
//                            opacity: 0;
//                            animation: opacShow 200ms 200ms linear forwards; 
//                         }
//                     }
//                 }
//                 .mode-moving {
//                     .mode-item {
//                         animation: moving 0.2s linear forwards;
//                     }
//                 }
//                 .mode-9 {
//                     .mode-item {
//                         flex: 1 0 28%;
//                         margin: 2%;
//                         font-size: 56px;
//                         position: relative;
//                         .bg-mask {
//                             width: 30%;
//                             height: 30%;
//                         }
//                         .bg-mask-nth0 {
//                             left: 34%;
//                         }
//                         .bg-mask-nth1 {
//                             left: 67%;
//                         }
//                         .bg-mask-nth2 {
//                             transform: rotate(180deg);
//                             left: 48%;
//                         }
//                         .error-zadan {
//                             width: 218%;
//                             height: 218%;
//                         }
//                     }
//                 }
//                 .mode-16 {
//                     .mode-item {
//                         flex: 1 0 21%;
//                         margin: 1%;                    
//                         font-size: 40px;
//                         .bg-mask {
//                             width: 23%;
//                             height: 23%;
//                         }
//                         .bg-mask-nth0 {
//                             left: 26%;
//                         }
//                         .bg-mask-nth1 {
//                             left: 51%;
//                         }
//                         .bg-mask-nth2 {
//                             left: 76%;
//                         }
//                         .bg-mask-nth3 {
//                             transform: rotate(180deg);
//                             left: 56%;
//                         }
//                         .error-zadan {
//                             width: 277%;
//                             height: 277%;
//                         }
//                     }
//                 }
//                 .mode-25 {
//                     .mode-item {
//                         flex: 1 0 18%;
//                         margin: 1%;
//                         font-size: 32px;
//                         .bg-mask {
//                             width: 18.5%;
//                             height: 18.5%;
//                         }
//                         .bg-mask-nth0 {
//                             left: 21%;
//                         }
//                         .bg-mask-nth1 {
//                             left: 41%;
//                         }
//                         .bg-mask-nth2 {
//                             left: 61%;
//                         }
//                         .bg-mask-nth3 {
//                             left: 81%;
//                         }
//                         .bg-mask-nth4 {
//                             transform: rotate(180deg);
//                             left: 62%;
//                         }
//                         .error-zadan {
//                             width: 359%;
//                             height: 359%;
//                         }
//                     }
//                 }
//             }
//         }
//         &-right {
//             position: relative;
//             width: 34.86%;
//             background: url('./imgs/play-right.png') no-repeat center center;
//             background-size: 100% 100%;
//             z-index: -1;
//             animation: 400ms shiftIn linear forwards;
//             @keyframes shiftIn {
//                 0% {
//                     opacity: 0;
//                     transform: translate(0, -100%);
//                 }
//                 100%{
//                     opacity: 1;
//                     transform: translate(0, 0%);
//                 }
//             }
//             .title {
//                 // margin百分比基数取的是父元素宽度
//                 // margin-top: 34.2%;
//                 height: 5.24%;
//                 font-size: 23px;
//                 color: #FFFFFF;
//                 line-height: 5.24%;
//                 text-shadow: 0px 2px 4px rgba(141,39,0,0.3);
//                 font-weight: bold;
//                 position: absolute;
//                 top: 16.285%;
//                 left: 50%;
//                 transform: translateX(-50%);
//             }
//             .mode {
//                 // margin-top: 11.5%;
//                 height: 3.5%;
//                 font-size: 14px;
//                 color: #994112;
//                 font-weight: bold;
//                 position: absolute;
//                 top: 26.285%;
//                 left: 50%;
//                 transform: translateX(-50%);
//             }
//             .mode-shuffle::before, .mode-normal::before {
//                 content: '';
//                 display: inline-block;
//                 width: 14px;
//                 height: 14px;
//                 vertical-align: -9.6666%;
//             }
//             .mode-shuffle::before {
//                 background: url('./imgs/shuffle-mode.png') no-repeat;
//                 background-size: cover;
//             }
//             .mode-normal::before {
//                 background: url('./imgs/normal-mode.png') no-repeat;
//                 background-size: cover;
//             }
//             .next-number {
//                 text-align: center;
//                 .next-number-title {
//                     // margin-top: 30%;
//                     height: 22px;
//                     font-size: 16px;
//                     color: #B47142;
//                     line-height: 22px;
//                     font-weight: bold;
//                     position: absolute;
//                     top: 35.285%;
//                     left: 50%;
//                     transform: translateX(-50%);
//                 }
//                 .number {
//                     // height: 62%;
//                     vertical-align: middle;
//                     font-size: 72px;
//                     color: #994112;
//                     font-weight: bold;
//                     position: absolute;
//                     top: 48%;
//                     left: 37.45%;
//                     // transform: translate(-50%, -50%);
//                 }
//                 .animate-number {
//                     animation: fontNext 0.24s linear forwards;
//                 }
//             }
//             .time {
//                 position: absolute;
//                 top: 79.285%;
//                 left: 50%;
//                 transform: translateX(-50%);
//                 width: 120px;
//                 height: 30px;
//                 background: url('./imgs/bg-time.png') no-repeat;
//                 background-size: contain;
//                 vertical-align: middle;
                
//                 .time-desc {
//                     display: flex;
//                     align-items: center;
//                     justify-content: center;
//                     margin-top: 2px;
//                     font-size: 16px;
//                     color: #A14A1B;
//                     font-weight: bold;
//                 }
//                 .time-desc::before {
//                     content: '';
//                     display: inline-block;
//                     width: 16px;
//                     height: 14px;
//                     background: url('./imgs/time-clock.png') no-repeat;
//                     background-size: contain;
//                     margin-right: 4px;
//                 }
//             }
//         }
//     }
//     .caidai {
//         z-index: 1000;
//         position: absolute;
//         opacity: 0;
//         animation: opacShow 0.2s linear forwards;
//         width: 100%;
//     }
//     .caidai-mask {
//         // background: rgba(0,0,0,0.7);
//         opacity: 0;
//         animation: opacShow 0.2s linear forwards;
//     }
//     .result-mask {
//         position: absolute;
//         bottom: 0;
//         right: 0;
//         left: 0;
//         width: 100%;
//         height: 100%;
//         z-index: 999;
//         background: rgba(0,0,0,0.7);
//         opacity: 0;
//         animation: opacShow 0.2s linear forwards;
//         .result-mask-bg {
//             position: absolute;
//             top: 50%;
//             left: 50%;
//             transform: translate(-50%, -50%);
//             width:100%;
//             height: 88px;
//             line-height: 88px;
//             background: url('./imgs/youxidi.png') no-repeat;
//             background-size: 100% 100%;
//             opacity: 0;
//             animation: opacShow 0.2s 0.2s linear forwards;
//             .tip {
//                 color: #FFFFFF;
//                 font-size: 36px;
//                 animation: fontResult 0.44s 0.2s linear forwards;
//                 font-weight: bold;
//             }
//         }
//         .new-record {
//             animation: opacShow 0.2s 0.4s linear;
//         }
//     }
    
//     .personal-result {
//         position: absolute;
//         top: 60%;
//         left: 50%;
//         transform: translate(-50%, -50%);
//         opacity: 0;
//         animation: opacShow 0.2s linear forwards;
//         .result-content {
//             font-weight: 500;
//             .current-result, .best-result {
//                 width: 386px;
//                 height: 70px;
//                 line-height: 70px;
//                 background: url('./imgs/bg-result.png') no-repeat;
//                 background-size: 100% 100%;
//                 display: flex;
//                 justify-content: space-between;
//                 .left, .right {
//                     line-height: 70px;
//                     font-size: 18px;
//                     color: #78390C;
//                 }
//                 .left {
//                     padding-left: 24px
//                 }
//                 .right {
//                     padding-right: 24px
//                 }
//             }
//             .current-result {
//                 position: relative;
//                 .right {
//                     color: #FF7D21;
//                 }
//                 .current-result-tag {
//                     position: absolute;
//                     top: -3px;
//                     right: 12px;
//                     font-size: 12px;
//                     font-weight: 600;
//                     color: #FFFFFF;
//                     line-height: 23px;
//                     height: 23px;
//                     width: 110px;
//                     background: url('./imgs/result-tag.png') no-repeat;
//                     background-size: 100% 100%;
//                 }
//             }
//             .best-result {
//                 margin-top: 14px;
//             }
//         }
//     }
//     .paihangbang {
//         position: absolute;
//         z-index: 1003;
//     }
//     .rank-list {
//         position: absolute;
//         bottom: 0;
//         right: 0;
//         left: 0;
//         width: 100%;
//         height: 100%;
//         z-index: 1001;
//         opacity: 0;
//         animation: opacShow 0.2s linear forwards;
//         .penzi-box {
//             position: relative;
//             height: 100%;
//             top: -100%;
//             #penzi {
//                 position: absolute;
//             }
//         }
//         .rank-list-content {
//             font-weight: 500;
//             .title {
//                 position: absolute;
//                 top: 17%;
//                 left: 50%;
//                 transform: translateX(-50%);
//                 font-size: 36px;
//                 color: #A4512C;
//                 text-shadow: 0px 2px 2px rgba(255,255,255,0.7);
//             }
//             .tip {
//                 position: absolute;
//                 top: 25.5%;
//                 left: 50%;
//                 width: 524px;
//                 height: 56px;
//                 line-height: 56px;
//                 transform: translateX(-50%);
//                 font-size: 18px;
//                 color: #FFFFFF;
//                 z-index: 1002;
//             }
//             .tip-in-rank {
//                 background: url('./imgs/bg-in-rank.png') no-repeat;
//                 background-size: 100%;
//             }
//             .tip-not-in-rank {
//                 background: url('./imgs/bg-not-in-rank.png') no-repeat;
//                 background-size: 100%;
//             }
//             .rank-list-show .content, .rank-list-owner .content {
//                 height: 60px;
//                 font-size: 14px;
//                 display: flex;
//                 align-items: center;
//                 .left {
//                     flex: 1;
//                     text-align: left;
//                     overflow: hidden;
//                     word-break: break-all;
//                     text-overflow: ellipsis;
//                     display: -webkit-box;
//                     -webkit-line-clamp: 1;
//                     -webkit-box-orient: vertical;
//                     display: inline-flex;
//                     align-items: center;
//                 }
//                 .right {
//                     .card-clock, .card-coin {
//                         display: inline-block;
//                     }
//                     .card-coin {
//                         margin-left: 26px;
//                     }
//                     .card-clock::before, .card-coin::before {
//                         content: '';
//                         display: inline-block;
//                         width: 16px;
//                         height: 16px;
//                         vertical-align: middle;
//                         margin-right: 8px; 
//                     }
//                     .card-coin::before {
//                         height: 18px;
//                         background: url('./imgs/gold-coin.png') no-repeat;
//                         background-size: cover;
//                     }
//                     .card-clock::before {
//                         background: url('./imgs/clock-light.png') no-repeat;
//                         background-size: cover;
//                     }
//                 }
//             }
//             .rank-list-owner .content {
//                 color: #FFFFFF;
//                 padding: 8px 20px 0;
//             }
//             .rank-list-show {
//                 position: absolute;
//                 // top: 27%;
//                 top: 32.5%;
//                 left: 50%;
//                 transform: translateX(-50%);
//                 width: 56%;
//                 height: 0;
//                 padding-bottom: 46%;
//                 #show-box{
//                     height: 94%;
//                     position: absolute;
//                     left: 0;
//                     top: 0;
//                     overflow-y: auto;
//                     width: 100%;
//                     margin-top: 5.6666%;
//                 }
//                 .rank-top3 {
//                     background: url('./imgs/bg-bangdan-top3.png') no-repeat;
//                     background-size: cover;
//                     display: flex;
//                     flex-direction: row;
//                     justify-content: space-around;
//                     font-size: 14px;
//                     height: 71%;
//                     .rank-top3-item {
//                         position: relative;
//                         margin-top: 10.915555%;
//                         width: 33%;
//                         .name {
//                             color: #894321;
//                             overflow: hidden;
//                             word-break: break-all;
//                             text-overflow: ellipsis;
//                             display: -webkit-box;
//                             -webkit-line-clamp: 1;
//                             -webkit-box-orient: vertical;
//                             padding: 0 3%;                      
//                         }
//                         .duration {
//                             color: #FF8939;
//                             margin-top: 4%;
//                         }
//                         .img-avatar {
//                             width: 52px;
//                             height: 52px;
//                             position: relative;
//                         }
//                         .rank-bg-1, .rank-bg-2, .rank-bg-3 {
//                             margin-top: -6px;
//                         }
//                         .rank-bg-1 {
//                             width: 116px;
//                             height: 52px;
//                             background: url('./imgs/1.png') no-repeat;
//                             background-size: 100% 100%;
//                         }
//                         .rank-bg-2 {
//                             width: 116px;
//                             height: 43px;
//                             background: url('./imgs/2.png') no-repeat;
//                             background-size: 100% 100%;
//                         }
//                         .rank-bg-3 {
//                             width: 116px;
//                             height: 43px;
//                             background: url('./imgs/3.png') no-repeat;
//                             background-size: 100% 100%;
//                         }
//                         .opacity0 {
//                             opacity: 0;
//                         }
//                         .bg-in-list{
//                             display: flex;
//                             justify-content: center;
//                             align-items: center;
//                             flex-direction: column;
//                             margin-top: 8%;
//                         }
//                         .gold-coin {
//                             margin-top: 8px;
//                             color: #B16743;
//                         }
//                         .gold-coin::before {
//                             content: '';
//                             display: inline-block;
//                             width: 16px;
//                             height: 18px;
//                             vertical-align: middle;
//                             margin-right: 8px; 
//                             background: url('./imgs/gold-coin.png') no-repeat;
//                             background-size: contain;
//                         }
//                     }
//                     .rank-top3-1 {
//                         order: 2;
//                         margin-top: 9.1555555%;
//                     }
//                     .rank-top3-2 {
//                         order: 1;   
//                     }
//                     .rank-top3-3 {
//                         order: 3;
//                     }
//                 }
//                 .rank-top3-only {                 
//                     height: 96%;
//                     overflow: hidden;
//                     .rank-top3-item {
//                         margin-top: 19.9555555%;
//                     }
//                     .rank-top3-1 {
//                         margin-top: 17.9555555%;
//                     }
//                 }
//                 .content {
//                     background: #FFF7EB;
//                     border: 4px solid #FFFFFF;
//                     color: #894321;
//                     width: 100%;
//                     padding: 0 12px;
//                     margin-top: 10px;
//                     .right .card-clock::before {
//                         background: url('./imgs/clock-dark.png') no-repeat;
//                         background-size: cover;
//                     }
//                 }
//                 .content:last-child {
//                     margin-bottom: 10px;
//                 }
//             }
//             .rank-list-owner {
//                 position: absolute;
//                 bottom: 0;
//                 left: 50%;
//                 transform: translateX(-50%);
//                 width: 65%;
//                 border-bottom: 0px;
//                 background: url('./imgs/bg-rank-has-own.png') no-repeat;
//                 background-size: 100%;
//                 background-position: 0 100%;
//             }
//         }
//     }
//     .ml12 {
//         margin-left: 12px;
//     }
//     .mr8 {
//         margin-right: 8px;
//     }
//     // 原始头像是圆形的 + 展示图像需要圆角加边框，此时的宽高是包含了圆型图像的透明部分，
//     // 其实际部分小于设定的宽度，因此有间隙，可加背景色解决
//     .img-avatar {
//         width: 36px;
//         height: 36px;
//         border-radius: 50%;
//         border: 1.5px solid #FFFFFF;
//         background: #fff;
//         display: inline-block;
//     }
// }

// .cursor-hand {
//     cursor: url(./imgs/hand1.png), auto;
// }
// .cursor-hand90{
//   cursor: url(./imgs/hand90_.png), auto;
// }                      
// .hidden {
//     display: none;
// }
// .show {
//     display: block;
// }
// @keyframes pressBox{
//     0%{
//         transform: scale(1);
//     }
//     100%{
//         transform: scale(0.9);
//     }
// }
// @keyframes moving{
//     12.5%{
//         transform: scale(0.9) rotate(-6deg);
//     }
//     25%{
//         transform: scale(1) rotate(0deg);
//     }
//     37.5%{
//         transform: scale(1.04) rotate(4deg);
//     }
//     50%{
//         transform: scale(1) rotate(0deg);
//     }
//     62.5%{
//         transform: scale(1.02) rotate(-2deg);
//     }
//     75%{
//         transform: scale(1) rotate(0deg);
//     }
//     87.5%{
//         transform: scale(1.01) rotate(1deg);
//     }
//     100%{
//         transform: scale(1) rotate(0deg);
//     }
// }
// @keyframes fontResult{
//     0%{
//         transform: scale(1.5);
//     }
//     45.45%{
//         transform: scale(0.8);
//     }
//     81.81%{
//         transform: scale(1.05);
//     }
//     100%{
//         transform: scale(1);
//     }
// }
// @keyframes fontNext{
//     50%{
//         transform: scale(1.2);
//     }
//     100%{
//         transform: scale(1);
//     }
// }
// @keyframes bbb{
//     0%{
//         background-position: 0 0;
//     }
//     100%{
//         background-position: 100% 0;
//     }
// }
// @keyframes moveBox {
//     0%{
//         transform: scale(1) rotateY(0deg);
//     }
//     25%{
//          transform: scale(.8,1) rotateY(-22.5deg);
//     }
//     50%{
//         transform: scale(.8,1) rotateY(-45deg);
//     }
//     75%{
//         transform: scale(.8,1) rotateY(-67.5deg);
//     }
//     100%{
//         transform: scale(1) rotateY(-90deg);
//     }
// }
// .slide-show-enter-active {
//     animation: 400ms backdrop-filter linear;
//     animation: 400ms opacShow linear;
// }

// @keyframes opacShow {
//     0% {
//         opacity: 0;
//     }
//     100%{
//         opacity: 1;
//     }
// }
// .slide-fade-leave-active {
//    animation: 200ms fadeHide linear;
// }
// @keyframes fadeHide {
//     0% {
//         opacity: 1;
//     }
//     100%{
//         opacity: 0;
//     }
// }
.class-content {
    height: 100%;
    width: 100%;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAP4AAADwBAMAAADSssQSAAAAAXNSR0IArs4c6QAAABtQTFRFMjIyMzMzNDQ0NTU1NjY2Nzc3ODg4OTk5Ojo6QUDc4AAAA9ZJREFUeNrtnSt320AQhUeSHdOwGAYaFhoaGgYWGgo6zDROU9+fXaC2lmOvtI/Z2SfbE7Dn3M3cydWnWYWAHRER0U8Av4blAgDehvUWwO9h2QHAYVhvAFyGZQMAx2H9AgDPw/oE4GNYrgDgdVj3AD6H5RKgR7usAVymdzldd3kCgB+TIvZKEWS4y9f9LtvrUbTfRfzVphZBDruMTug8+vFIxPWc//+av4mgmV362V0UxaISAQDv12IhjZI7GpXcea7kbkSQfuGalZymb8iq5BRHMfJNoymCbAv33zkrjmJp5JubXTRL7qBdcvO+sShc+5J7IMJwF82S0/eNfcNfG/lm+bhYdAv37NqqFSLuCnfnr+E/EqFRchoN39o3PA2/s/aNY8P/cPWN74xh4j6pkrtzn3avfDLOGDMivGeMGd+0EyXH3vDfFO5T7MKSMaZ94z9jTIuQyhit4gFDs3CdM4aO+8wKl8U3YhlD4Ruehn+y9Y1jw3f2TYiGP/m3z1PGUIkgoYyhEkFCGUPlG/LxUGngG3q4S2ve8C2zOclkDKVvyLbhM2VzEskYat9QEfxvG4r/zfqGl/9NBaWI+N9IRDH871j5XxH8z0pEuvzvpfK/dPhfV/mfP/63DcX/XH2TJ//Tf6bNh/9ZBaVS+F9TKv87VP4XF/+jnPlfV/lfQvyvT4P/+cvmZfC/lo//MWfzzOf/SuF/Zr5Jnv8to+d/FJj/LXzyvy4H/teF4n/vlf9V/nfjPsmB79D87yu7+T9XGOM9YzDxP+uMUfmfH/5HgfnfovI/96AkljEq/yuS/104+V9bOP/bx8v/LH2TNP9j8E3m/G9WRF7zf02B/M8tKJFExqj8L0b+t2Hjf86+KWr+T5r/9Wz8z1c2Z8wYVhCzFP638sL/zHzjjf9Jzf81BfI/rzDGkf+5+qbyP/kXPhb8z1c2z3P+T1+Enxc+WfM/zhel2fG/VQT8T3+IMZ/5P0sRkvzvmZP/MV2UIPEPPlT+NxZB0h98KH3+T5T/zYsg6Rc+ifA/2WGsxPgfe1Aqiv/tBPifmQhR/vdpxv8ELmMWw//2kvzP7vZ9gMuY+c3/5cP/BGBM5X9R8j+py5h58j9tEWID32uzr29uhWAMmRVuqfzP2zAWCWUMM/7XVP7nbeA7T/5nO4xVBP+jOv9X+V8c/K9n43+cvpHlf6858D/WixKCH3yo/E+f/wnBmFT4n6+LElnwP4qf/23q/F8R/K9n438S2TzEBx9C8T8D95lljI21iHT5n9f/vhMp/2MPSrHxP+nLmEnxP4qf/23Y+J8MjAnN//4Ax3H9/N6BeDoAAAAASUVORK5CYII=);
    background-repeat: repeat;
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    pointer-events: auto;
}
.schulte-table-bg {
    background: url('./imgs/bg-normal.png') no-repeat center center;
    background-size: 100% 100%;
}
.schulte-table {
    position: absolute;
    bottom: 0;
    z-index: 699;
    width: calc(100% / 16 * 9 / 3 * 4);
    height: 100%;
    text-align: center;
    .box-title {
        position: absolute;
        top: 40px;
        left: 50%;
        transform: translateX(-50%);
        width: 444px;
        height: 187px;
        background: url('./imgs/bg-box-title.png') no-repeat center center;
        background-size: 100% 100%;
        color: #FFFFFF;
        text-align: center;
        &-main {
            margin-top: 68px;
            width: 444px;
            height: 72px;
            line-height: 72px;
            font-size: 52px;
            font-weight: bold;
        }
        &-sub {
            margin-top: 14px;
            width: 444px;
            height: 25px;
            line-height: 25px;
            font-size: 18px;
            font-weight: 500;
        }
    }
    // 两行问题
    .box-bottom {
        position: absolute;
        bottom: 55px;
        left: 50%;
        transform: translateX(-50%);
        width: 480px;
        height: 56px;
        padding: 6px 0px;
        background: url('./imgs/bg-bottom.png') no-repeat center center;
        background-size: 100% 100%;
        color: #FFFFFF;
        text-align: center;
        font-size: 16px;
        font-weight: 500;
        p: {
          line-height: 22px;
        }
    }
    // 用百分比形式保证相对位置不变
    .playing {
        position: absolute;
        top: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(1.774418604651163px);
        display: flex;
        flex-direction: row;
        .playing-left {
            width: 58.72%;
            margin: 5.77% 0 0 5.77%;
            height: 0;
            padding-bottom: calc(64.52%);
            position: relative;
            .game-box{
                position: absolute;
                width: 100%;
                height: 100%;
                left: 0;
                top:0;
                display: flex;
                flex-direction: column;
            }
            .top-tip {
                height: 0;
                padding-bottom: 7.59%;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 7px;
                border: 1px solid #93A19A;
                backdrop-filter: blur(5.323255813953488px);
                font-size: 14px;
                color: #783B00;
                margin-bottom: 1.78%;
                z-index: 10;
                p{
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    left: 0;
                    top: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
            .top-tip-light {
                background: #FFFFFF;
                border: 1px solid #C3C3C3;
                backdrop-filter: blur(5.323255813953488px);
                color: #783B00;
            }
            .content {
                flex:1;
                background: rgba(255, 255, 255, 0.5);
                border-radius: 14px;
                border: 1px solid #93A19A;
                backdrop-filter: blur(5.323255813953488px);
                .mode-disabled {
                    pointer-events: none;
                    cursor: not-allowed;
                }
                .mode-9, .mode-16, .mode-25 {
                    z-index: 1;
                    display: flex;
                    flex-wrap: wrap;
                    height: 100%;
                    padding: 2%;
                    .mode-item {
                        position: relative;
                        opacity: 1;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        color: #FFF9E9;
                        text-align: center;
                        vertical-align: middle;
                        background-image: url(./imgs/box.png);
                        background-position: 0 0;
                        background-size: auto 100%;
                        .bg-mask {
                            position: fixed;
                            z-index: 3;
                            border-radius: 50%;
                            filter: blur(6px);
                            box-shadow: 0 0 0 10000px rgba(0, 0, 0, 0.7);
                            opacity: 0;
                            animation: opacShow 400ms 400ms forwards;
                        }
                        .bg-mask-lottie {
                            position: fixed;
                            z-index: 10;
                            width: 16.40625%;
                            height: 8.4375%;
                            opacity: 0;
                            animation: opacShow 400ms 800ms forwards;
                        }
                        .num{
                            z-index: 2;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-weight: bold;
                        }
                        .error-zadan {
                            // 注意不同场景的宽高比例值不同
                            position: absolute;                  
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            background-image: url(./imgs/zadan.png);
                            background-position: 0 0;
                            background-size: cover;
                            animation: bbb 0.6s steps(14) forwards;
                        }
                        .correct-flower {
                            width: 150%;
                            height: 150%;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            background-image: url(./imgs/correct.png);
                            background-position: 0 0;
                            background-size: cover;
                            animation: bbb 0.28s steps(6) forwards;
                        }
                    }
                    .mode-item-up {
                        z-index: 1;
                    }
                    .press-box {
                        animation: pressBox 120ms linear forwards;
                    }
                }
                .mode-reversal {
                    .mode-item {
                        animation: bbb 0.28s steps(4) forwards;
                    }
                    .mode-item {
                        .num{
                           opacity: 0;
                           animation: opacShow 200ms 200ms linear forwards; 
                        }
                    }
                }
                .mode-moving {
                    .mode-item {
                        animation: moving 0.2s linear forwards;
                    }
                }
                .mode-9 {
                    .mode-item {
                        flex: 1 0 28%;
                        margin: 2%;
                        font-size: 56px;
                        position: relative;
                        .bg-mask {
                            width: 30%;
                            height: 30%;
                        }
                        .bg-mask-nth0 {
                            left: 34%;
                        }
                        .bg-mask-nth1 {
                            left: 67%;
                        }
                        .bg-mask-nth2 {
                            transform: rotate(180deg);
                            left: 48%;
                        }
                        .error-zadan {
                            width: 218%;
                            height: 218%;
                        }
                    }
                }
                .mode-16 {
                    .mode-item {
                        flex: 1 0 21%;
                        margin: 1%;                    
                        font-size: 40px;
                        .bg-mask {
                            width: 23%;
                            height: 23%;
                        }
                        .bg-mask-nth0 {
                            left: 26%;
                        }
                        .bg-mask-nth1 {
                            left: 51%;
                        }
                        .bg-mask-nth2 {
                            left: 76%;
                        }
                        .bg-mask-nth3 {
                            transform: rotate(180deg);
                            left: 56%;
                        }
                        .error-zadan {
                            width: 277%;
                            height: 277%;
                        }
                    }
                }
                .mode-25 {
                    .mode-item {
                        flex: 1 0 18%;
                        margin: 1%;
                        font-size: 32px;
                        .bg-mask {
                            width: 18.5%;
                            height: 18.5%;
                        }
                        .bg-mask-nth0 {
                            left: 21%;
                        }
                        .bg-mask-nth1 {
                            left: 41%;
                        }
                        .bg-mask-nth2 {
                            left: 61%;
                        }
                        .bg-mask-nth3 {
                            left: 81%;
                        }
                        .bg-mask-nth4 {
                            transform: rotate(180deg);
                            left: 62%;
                        }
                        .error-zadan {
                            width: 359%;
                            height: 359%;
                        }
                    }
                }
            }
        }
        &-right {
            position: relative;
            width: 34.86%;
            background: url('./imgs/play-right.png') no-repeat center center;
            background-size: 100% 100%;
            z-index: -1;
            animation: 400ms shiftIn linear forwards;
            @keyframes shiftIn {
                0% {
                    opacity: 0;
                    transform: translate(0, -100%);
                }
                100%{
                    opacity: 1;
                    transform: translate(0, 0%);
                }
            }
            .title {
                // margin百分比基数取的是父元素宽度
                margin-top: 34.2%;
                height: 5.24%;
                font-size: 23px;
                color: #FFFFFF;
                line-height: 5.24%;
                text-shadow: 0px 2px 4px rgba(141,39,0,0.3);
                font-weight: bold;
            }
            .mode {
                margin-top: 11.5%;
                height: 3.5%;
                font-size: 14px;
                color: #994112;
                font-weight: bold;
            }
            .mode-shuffle::before, .mode-normal::before {
                content: '';
                display: inline-block;
                width: 14px;
                height: 14px;
                vertical-align: -9.6666%;
            }
            .mode-shuffle::before {
                background: url('./imgs/shuffle-mode.png') no-repeat;
                background-size: cover;
            }
            .mode-normal::before {
                background: url('./imgs/normal-mode.png') no-repeat;
                background-size: cover;
            }
            .next-number {
                text-align: center;
                .next-number-title {
                    margin-top: 30%;
                    height: 22px;
                    font-size: 16px;
                    color: #B47142;
                    line-height: 22px;
                    font-weight: bold;
                }
                .number {
                    height: 62%;
                    vertical-align: middle;
                    font-size: 72px;
                    color: #994112;
                    font-weight: bold;
                }
                .animate-number {
                    animation: fontNext 0.24s linear forwards;
                }
            }
            .time {
                position: absolute;
                top: 79.285%;
                left: 50%;
                transform: translateX(-50%);
                width: 120px;
                height: 30px;
                background: url('./imgs/bg-time.png') no-repeat;
                background-size: contain;
                vertical-align: middle;
                
                .time-desc {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-top: 2px;
                    font-size: 16px;
                    color: #A14A1B;
                    font-weight: bold;
                }
                .time-desc::before {
                    content: '';
                    display: inline-block;
                    width: 16px;
                    height: 14px;
                    background: url('./imgs/time-clock.png') no-repeat;
                    background-size: contain;
                    margin-right: 4px;
                }
            }
        }
    }
    .caidai {
        z-index: 1000;
        position: absolute;
        opacity: 0;
        animation: opacShow 0.2s linear forwards;
        width: 100%;
    }
    .caidai-mask {
        background: rgba(0,0,0,0.7);
        opacity: 0;
        animation: opacShow 0.2s linear forwards;
    }
    .result-mask {
        position: absolute;
        bottom: 0;
        right: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 999;
        background: rgba(0,0,0,0.7);
        opacity: 0;
        animation: opacShow 0.2s linear forwards;
        .result-mask-bg {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width:100%;
            height: 88px;
            line-height: 88px;
            background: url('./imgs/youxidi.png') no-repeat;
            background-size: 100% 100%;
            opacity: 0;
            animation: opacShow 0.2s 0.2s linear forwards;
            .tip {
                color: #FFFFFF;
                font-size: 36px;
                animation: fontResult 0.44s 0.2s linear forwards;
                font-weight: bold;
            }
        }
        .new-record {
            animation: opacShow 0.2s 0.4s linear;
        }
    }
    
    .personal-result {
        position: absolute;
        top: 60%;
        left: 50%;
        transform: translate(-50%, -50%);
        opacity: 0;
        animation: opacShow 0.2s linear forwards;
        .result-content {
            font-weight: 500;
            .current-result, .best-result {
                width: 386px;
                height: 70px;
                line-height: 70px;
                background: url('./imgs/bg-result.png') no-repeat;
                background-size: 100% 100%;
                display: flex;
                justify-content: space-between;
                .left, .right {
                    line-height: 70px;
                    font-size: 18px;
                    color: #78390C;
                }
                .left {
                    padding-left: 24px
                }
                .right {
                    padding-right: 24px
                }
            }
            .current-result {
                position: relative;
                .right {
                    color: #FF7D21;
                }
                .current-result-tag {
                    position: absolute;
                    top: -3px;
                    right: 12px;
                    font-size: 12px;
                    font-weight: 600;
                    color: #FFFFFF;
                    line-height: 23px;
                    height: 23px;
                    width: 110px;
                    background: url('./imgs/result-tag.png') no-repeat;
                    background-size: 100% 100%;
                }
            }
            .best-result {
                margin-top: 14px;
            }
        }
    }
    .paihangbang {
        position: absolute;
        z-index: 1003;
    }
    .rank-list {
        position: absolute;
        bottom: 0;
        right: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1001;
        opacity: 0;
        animation: opacShow 0.2s linear forwards;
        .penzi-box {
            position: relative;
            height: 100%;
            top: -100%;
            #penzi {
                position: absolute;
            }
        }
        .rank-list-content {
            font-weight: 500;
            .title {
                position: absolute;
                top: 17%;
                left: 50%;
                transform: translateX(-50%);
                font-size: 36px;
                color: #A4512C;
                text-shadow: 0px 2px 2px rgba(255,255,255,0.7);
            }
            .tip {
                position: absolute;
                top: 25.5%;
                left: 50%;
                width: 524px;
                height: 56px;
                line-height: 56px;
                transform: translateX(-50%);
                font-size: 18px;
                color: #FFFFFF;
                z-index: 1002;
            }
            .tip-in-rank {
                background: url('./imgs/bg-in-rank.png') no-repeat;
                background-size: 100%;
            }
            .tip-not-in-rank {
                background: url('./imgs/bg-not-in-rank.png') no-repeat;
                background-size: 100%;
            }
            .rank-list-show .content, .rank-list-owner .content {
                height: 60px;
                font-size: 14px;
                display: flex;
                align-items: center;
                .left {
                    flex: 1;
                    text-align: left;
                    overflow: hidden;
                    word-break: break-all;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                    -webkit-line-clamp: 1;
                    -webkit-box-orient: vertical;
                    display: inline-flex;
                    align-items: center;
                }
                .right {
                    .card-clock, .card-coin {
                        display: inline-block;
                    }
                    .card-coin {
                        margin-left: 26px;
                    }
                    .card-clock::before, .card-coin::before {
                        content: '';
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        vertical-align: middle;
                        margin-right: 8px; 
                    }
                    .card-coin::before {
                        height: 18px;
                        background: url('./imgs/gold-coin.png') no-repeat;
                        background-size: cover;
                    }
                    .card-clock::before {
                        background: url('./imgs/clock-light.png') no-repeat;
                        background-size: cover;
                    }
                }
            }
            .rank-list-owner .content {
                color: #FFFFFF;
                padding: 8px 20px 0;
            }
            .rank-list-show {
                position: absolute;
                top: 27%;
                left: 50%;
                transform: translateX(-50%);
                width: 56%;
                height: 0;
                padding-bottom: 46%;
                #show-box{
                    height: 94%;
                    position: absolute;
                    left: 0;
                    top: 0;
                    overflow-y: auto;
                    width: 100%;
                    margin-top: 5.6666%;
                }
                .rank-top3 {
                    background: url('./imgs/bg-bangdan-top3.png') no-repeat;
                    background-size: cover;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-around;
                    font-size: 14px;
                    height: 71%;
                    .rank-top3-item {
                        position: relative;
                        margin-top: 10.915555%;
                        width: 33%;
                        .name {
                            color: #894321;
                            overflow: hidden;
                            word-break: break-all;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            -webkit-box-orient: vertical;
                            padding: 0 3%;                      
                        }
                        .duration {
                            color: #FF8939;
                            margin-top: 4%;
                        }
                        .img-avatar {
                            width: 52px;
                            height: 52px;
                            position: relative;
                        }
                        .rank-bg-1, .rank-bg-2, .rank-bg-3 {
                            margin-top: -6px;
                        }
                        .rank-bg-1 {
                            width: 116px;
                            height: 52px;
                            background: url('./imgs/1.png') no-repeat;
                            background-size: 100% 100%;
                        }
                        .rank-bg-2 {
                            width: 116px;
                            height: 43px;
                            background: url('./imgs/2.png') no-repeat;
                            background-size: 100% 100%;
                        }
                        .rank-bg-3 {
                            width: 116px;
                            height: 43px;
                            background: url('./imgs/3.png') no-repeat;
                            background-size: 100% 100%;
                        }
                        .opacity0 {
                            opacity: 0;
                        }
                        .bg-in-list{
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            flex-direction: column;
                            margin-top: 8%;
                        }
                        .gold-coin {
                            margin-top: 8px;
                            color: #B16743;
                        }
                        .gold-coin::before {
                            content: '';
                            display: inline-block;
                            width: 16px;
                            height: 18px;
                            vertical-align: middle;
                            margin-right: 8px; 
                            background: url('./imgs/gold-coin.png') no-repeat;
                            background-size: contain;
                        }
                    }
                    .rank-top3-1 {
                        order: 2;
                        margin-top: 9.1555555%;
                    }
                    .rank-top3-2 {
                        order: 1;   
                    }
                    .rank-top3-3 {
                        order: 3;
                    }
                }
                .rank-top3-only {                 
                    height: 96%;
                    overflow: hidden;
                    .rank-top3-item {
                        margin-top: 19.9555555%;
                    }
                    .rank-top3-1 {
                        margin-top: 17.9555555%;
                    }
                }
                .content {
                    background: #FFF7EB;
                    border: 4px solid #FFFFFF;
                    color: #894321;
                    width: 100%;
                    padding: 0 12px;
                    margin-top: 10px;
                    .right .card-clock::before {
                        background: url('./imgs/clock-dark.png') no-repeat;
                        background-size: cover;
                    }
                }
                .content:last-child {
                    margin-bottom: 10px;
                }
            }
            .rank-list-owner {
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 65%;
                border-bottom: 0px;
                background: url('./imgs/bg-rank-has-own.png') no-repeat;
                background-size: 100%;
                background-position: 0 100%;
            }
        }
    }
    .ml12 {
        margin-left: 12px;
    }
    .mr8 {
        margin-right: 8px;
    }
    // 原始头像是圆形的 + 展示图像需要圆角加边框，此时的宽高是包含了圆型图像的透明部分，
    // 其实际部分小于设定的宽度，因此有间隙，可加背景色解决
    .img-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: 1.5px solid #FFFFFF;
        background: #fff;
        display: inline-block;
    }
}

.cursor-hand {
    cursor: url(./imgs/hand1.png), auto;
}
.cursor-hand90{
  cursor: url(./imgs/hand90_.png), auto;
}                      
.hidden {
    display: none;
}
.show {
    display: block;
}
@keyframes pressBox{
    0%{
        transform: scale(1);
    }
    100%{
        transform: scale(0.9);
    }
}
@keyframes moving{
    12.5%{
        transform: scale(0.9) rotate(-6deg);
    }
    25%{
        transform: scale(1) rotate(0deg);
    }
    37.5%{
        transform: scale(1.04) rotate(4deg);
    }
    50%{
        transform: scale(1) rotate(0deg);
    }
    62.5%{
        transform: scale(1.02) rotate(-2deg);
    }
    75%{
        transform: scale(1) rotate(0deg);
    }
    87.5%{
        transform: scale(1.01) rotate(1deg);
    }
    100%{
        transform: scale(1) rotate(0deg);
    }
}
@keyframes fontResult{
    0%{
        transform: scale(1.5);
    }
    45.45%{
        transform: scale(0.8);
    }
    81.81%{
        transform: scale(1.05);
    }
    100%{
        transform: scale(1);
    }
}
@keyframes fontNext{
    50%{
        transform: scale(1.2);
    }
    100%{
        transform: scale(1);
    }
}
@keyframes bbb{
    0%{
        background-position: 0 0;
    }
    100%{
        background-position: 100% 0;
    }
}
@keyframes moveBox {
    0%{
        transform: scale(1) rotateY(0deg);
    }
    25%{
         transform: scale(.8,1) rotateY(-22.5deg);
    }
    50%{
        transform: scale(.8,1) rotateY(-45deg);
    }
    75%{
        transform: scale(.8,1) rotateY(-67.5deg);
    }
    100%{
        transform: scale(1) rotateY(-90deg);
    }
}
.slide-show-enter-active {
    animation: 400ms backdrop-filter linear;
    animation: 400ms opacShow linear;
}

@keyframes opacShow {
    0% {
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}
.slide-fade-leave-active {
   animation: 200ms fadeHide linear;
}
@keyframes fadeHide {
    0% {
        opacity: 1;
    }
    100%{
        opacity: 0;
    }
}