<template>
  <div class="class-content">
    <div :class="['schulte-table', step > 0 ? 'schulte-table-bg' : '']">
      <transition name="slide-fade">
        <div v-if="[1, 3].includes(step)">
          <div class="box-title">
            <div class="box-title-main">
              {{ $t('classroom.interactions.schulteTable.schulteTable') }}
            </div>
            <div class="box-title-sub">
              {{ subTitle }}
            </div>
          </div>
          <div class="box-bottom">
            <p>
              {{
                step === 1
                  ? $t('classroom.interactions.schulteTable.gameObjectives')[0]
                  : $t('classroom.interactions.schulteTable.leaderboardTip')[0]
              }}
            </p>
            <p>
              {{
                step === 1
                  ? $t('classroom.interactions.schulteTable.gameObjectives')[1]
                  : $t('classroom.interactions.schulteTable.leaderboardTip')[1]
              }}
            </p>
          </div>
        </div>
      </transition>
      <transition name="slide-fade">
        <div v-if="step === 1" class="ready">
          <!-- ready-->
          <lottie-player
            id="ready"
            ref="ready"
            autoplay
            mode="normal"
            :src="getAssetPath('/lottiefiles/schulteTable/daojishi.json')"
            class="lottie-player"
            @complete="onLottieReadyComplete"
          />
          <audio src="./audio/readygo.mp3" class="hide" autoplay ref="readygoAudio"></audio>
        </div>
      </transition>
      <div v-if="step === 2">
        <audio src="./audio/play.mp3" class="hide" loop autoplay ref="playAudio"></audio>
      </div>
      <transition name="slide-show">
        <div id="playing" v-if="step === 2" @click="handlerPlayMask" class="playing cursor-hand">
          <div class="playing-left">
            <div class="game-box">
              <div :class="['top-tip', isShowPlayMask ? 'top-tip-light' : '']">
                <p>
                  {{
                    $t(
                      `classroom.interactions.schulteTable.gameInstructions[${
                        category === 1 ? 0 : 1
                      }]`,
                      {
                        start: playNumberOrderArr[0],
                        end: playNumberOrderArr.at(-1)
                      }
                    )
                  }}
                </p>
              </div>
              <div class="content">
                <div
                  id="play-moudle"
                  :class="[`mode-${level * level}`, isDisabled ? 'mode-disabled' : '']"
                >
                  <div
                    v-for="(item, index) in playNumberShowArr"
                    :key="item"
                    :id="item"
                    :class="[
                      'mode-item',
                      curClickPlayNumber > 0 && curClickPlayNumber === item ? 'mode-item-up' : ''
                    ]"
                    @click.stop="clickCheck(item)"
                    @mousedown="addPressBox(item)"
                    @mouseup="removePressBox(item)"
                  >
                    <div v-if="isShowPlayMask && nextPlayNumber === item" class="bg-mask"></div>
                    <!-- 避免点击位于其他元素的lottie，触发点击成功，应该算错，传值0 -->
                    <div
                      v-if="isShowPlayMask && nextPlayNumber === item"
                      @click.stop="clickCheck(0)"
                      :class="['bg-mask-lottie', `bg-mask-nth${index % level}`]"
                    >
                      <lottie-player
                        id="jiantou"
                        ref="jiantou"
                        autoplay
                        loop
                        mode="normal"
                        :src="getAssetPath('/lottiefiles/schulteTable/jiantou.json')"
                        class="lottie-player"
                      />
                    </div>
                    <div
                      v-if="curClickPlayNumber > 0 && curClickPlayNumber === item"
                      :class="curClickCorrectMap[curClickCorrectStatus]"
                    ></div>
                    <div class="num">{{ item }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="playing-right">
            <div class="title">
              {{ $t('classroom.interactions.schulteTable.schulteTable') }}
            </div>
            <div :class="['mode', random ? 'mode-shuffle' : 'mode-normal']">
              {{
                random
                  ? $t('classroom.interactions.schulteTable.shuffleMode')
                  : $t('classroom.interactions.schulteTable.normalMode')
              }}
            </div>
            <div class="next-number">
              <div class="next-number-title">
                {{ $t('classroom.interactions.schulteTable.nextNumber') }}
              </div>
              <div
                :class="['number', isChangeNextNumber ? 'animate-number' : '']"
                :style="{ left: nextPlayNumber > 9 ? '37.45%' : '42.45%' }"
              >
                {{ nextPlayNumber }}
              </div>
            </div>
            <div class="time">
              <div class="time-desc">
                {{
                  $t('classroom.interactions.schulteTable.duration', {
                    duration: `${seconds}.${milliSeconds}`
                  })
                }}
              </div>
            </div>
          </div>
        </div>
      </transition>
      <div v-if="[3, 4].includes(step)" :class="['caidai', step === 4 ? 'caidai-mask' : '']">
        <lottie-player
          id="caidai"
          ref="caidai"
          autoplay
          loop
          mode="normal"
          :src="getAssetPath('/lottiefiles/schulteTable/caidai.json')"
          class="lottie-player"
        />
      </div>
      <transition name="slide-fade">
        <div v-if="step === 3 && ownResultShow" class="result-mask">
          <div class="result-mask-bg">
            <p class="tip">{{ resultTip }}</p>
          </div>
          <div v-if="isShowBestTime && +curDuration < +bestDuration" class="new-record">
            <lottie-player
              id="newRecord"
              ref="newRecord"
              autoplay
              mode="normal"
              :src="getAssetPath('/lottiefiles/schulteTable/new_record.json')"
              class="lottie-player"
            />
          </div>
        </div>
      </transition>
      <!-- 个人结果页 -->
      <div v-if="step === 3" class="personal-result">
        <div class="result-content">
          <div class="current-result">
            <div class="left">{{ $t('classroom.interactions.schulteTable.yourCurrentTime') }}</div>
            <div class="right">
              {{
                $t('classroom.interactions.schulteTable.duration', {
                  duration: curDuration
                })
              }}
            </div>
            <div
              v-if="!(isShowBestTime && +curDuration >= +bestDuration)"
              class="current-result-tag"
              v-text="
                !isShowBestTime
                  ? $t('classroom.interactions.schulteTable.firstChallenge')
                  : Number(curDuration) < Number(bestDuration)
                    ? $t('classroom.interactions.schulteTable.newRecord')
                    : ''
              "
            ></div>
          </div>
          <div v-if="isShowBestTime" class="best-result">
            <div class="left">{{ $t('classroom.interactions.schulteTable.yourBestTime') }}</div>
            <div class="right">
              {{
                $t('classroom.interactions.schulteTable.duration', {
                  duration: bestDuration
                })
              }}
            </div>
          </div>
        </div>
        <audio src="./audio/own-result.mp3" class="hide" autoplay ref="resultAudio"></audio>
      </div>
      <!-- 榜单页音效， 需要立即展示 -->
      <div v-if="step === 4" class="paihangbang">
        <audio src="./audio/paihangbang.mp3" class="hide" autoplay ref="paihangbangAudio"></audio>
      </div>
      <!-- 榜单页 -->
      <div v-if="step === 4" class="rank-list">
        <lottie-player
          id="paihangbang"
          ref="paihangbang"
          autoplay
          mode="normal"
          :src="getAssetPath('/lottiefiles/schulteTable/paihangbang.json')"
          class="lottie-player"
        />
        <div class="penzi-box">
          <lottie-player
            v-if="isOnRankList"
            id="penzi"
            ref="penzi"
            autoplay
            loop
            mode="normal"
            :src="getAssetPath('/lottiefiles/schulteTable/penzi.json')"
            class="lottie-player"
          />
        </div>
        <div class="rank-list-content">
          <div class="title">
            {{ $t('classroom.interactions.schulteTable.leaderboard') }}
          </div>
          <div
            v-if="ownRankResult"
            :class="['tip', isOnRankList ? 'tip-in-rank' : 'tip-not-in-rank']"
          >
            {{
              isOnRankList
                ? $t('classroom.interactions.schulteTable.congratulations')
                : $t('classroom.interactions.schulteTable.tryAgain')
            }}
          </div>
          <div class="rank-list-show">
            <div id="show-box" @mouseenter="listEnterHandler" @mouseleave="listLeaveHandler">
              <!-- 仅前三名，特殊展示 -->
              <div
                id="top3"
                :class="['rank-top3', showRankList.length === 3 ? 'rank-top3-only' : '']"
              >
                <div
                  v-for="(item, index) in showRankList.slice(0, 3)"
                  :key="index"
                  :class="['rank-top3-item', `rank-top3-${index + 1}`]"
                >
                  <div :class="['name', !item.nickName ? 'opacity0' : '']">
                    {{ item.nickName || '-' }}
                  </div>
                  <div :class="['duration', !item.duration ? 'opacity0' : '']">
                    {{
                      item.duration
                        ? $t('classroom.interactions.schulteTable.duration', {
                            duration: item.duration
                          })
                        : '-'
                    }}
                  </div>
                  <div class="bg-in-list">
                    <img :src="item.avatar" class="img-avatar" />
                    <div :class="`rank-bg-${index + 1}`"></div>
                    <div v-if="item.coin" class="gold-coin">+{{ item.coin }}</div>
                  </div>
                </div>
              </div>
              <div id="rank-normal" v-if="showRankList.length > 3">
                <div
                  v-for="(item, index) in showRankList.slice(3)"
                  :key="item.userId"
                  class="content"
                >
                  <div class="left">
                    {{ index + 4 }}.
                    <img :src="item.avatar" class="img-avatar ml12 mr8" />
                    {{ item.nickName }}
                  </div>
                  <div class="right">
                    <span class="card-clock">{{
                      $t('classroom.interactions.schulteTable.duration', {
                        duration: item.duration
                      })
                    }}</span>
                    <span class="card-coin">+{{ item.coin }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="ownRankResult" class="rank-list-owner">
            <div class="content">
              <div class="left">
                {{ ownRankIndex }}.
                <img :src="ownRankResult.avatar" class="img-avatar ml12 mr8" />
                {{ ownRankResult.nickName }}
              </div>
              <div class="right">
                <span class="card-clock">{{
                  $t('classroom.interactions.schulteTable.duration', {
                    duration: ownRankResult.duration
                  })
                }}</span>
                <span class="card-coin">+{{ ownRankResult.coin }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import { emitter } from '@/hooks/useEventBus'
import '@lottiefiles/lottie-player'
// import logger from 'utils/logger'
import { checkSubmitSchulteTable, submitSchulteTable } from '@/api/interaction/redPackage'
import { submitInteractPartakereport } from '@/api/interaction/index'
import { px2vh } from '@/utils/util'
// 点击箱子就播放音效无论对错
import pressAudio from './audio/press.mp3'
// 点击箱子错误音效
import clickErrAudio from './audio/click-error.mp3'
import defaultAvatarIcon from './imgs/normal-avatar.png'
import { message } from 'ant-design-vue'
import logger from '@/utils/logger'
import { getAssetPath } from '@/utils/assetPath'

export default {
  name: 'SchulteTableComponent',
  data() {
    return {
      step: 0, // 0未开始 1 初始化倒计时, 2个人游戏页, 3个人结果页, 4榜单展示, 5关闭
      subTitle: '',
      bestDuration: 0,
      curDuration: 0,
      resultTip: '',
      isShowBestTime: false,
      isShowPlay: false,
      // 游戏相关参数
      level: 3, // 3,4,5
      category: 1, // 1自然数 2质数
      random: false, // 是否实时随机
      playNumberOrderArr: [], // 游戏的按顺序数组
      playNumberShowArr: [], // 游戏的当前展示乱序数组
      nextPlayNumber: 0,
      nextPlayIndex: 0,
      curClickPlayNumber: -1, // 当前点击值
      curClickCorrectStatus: 0, // 当前点击状态
      curClickCorrectMap: {
        0: '',
        1: 'correct-flower',
        2: 'error-zadan'
      },
      isChangeNextNumber: false, // 控制nextNumber动效
      isDisabled: false,
      isShowPlayMask: false,
      showPlayMaskEle: null,
      time: 0,
      pre_time: 0,
      intervals: 0,
      pre_intervals: 0,
      secondsTimer: null,
      milliSeconds: '00',
      seconds: 0,
      playDuration: 0, // 保留两位小数点的计时（以秒为单位）
      showTopNum: 3,
      showRankList: [], // 群发的展示榜单
      ownRankResult: null, // 个人的榜单排名结果
      ownResultShow: true, // 个人结果页蒙层
      isOnRankList: false,
      ownRankIndex: -1,
      playing: 'playing', // 游戏中
      halfWay: 'halfway', // 中途结束
      endNormalWay: 'normal', // 正常流程结束
      rotationListTimer: null, // 榜单轮播定时器
      hasSubmitted: false, // 避免重复提交
      loadStartTime: Date.now(),
      initAutoScroll: false //自动轮播初始开启
    }
  },
  props: {
    options: {
      type: Object,
      default: () => {}
    }
  },
  beforeUnmount() {
    clearInterval(this.secondsTimer)
    clearInterval(this.rotationListTimer)
  },
  updated() {
    // 此处调用保证能获取到dom元素，避免dom取不到问题
    // 保证仅在处于榜单页、且排行榜超过3人时、初始未播放过三个条件都满足时才开启播放
    if (this.step === 4 && this.showRankList.length > 3 && !this.initAutoScroll) {
      let parent = document.getElementById('show-box')
      let child = document.getElementById('rank-normal')
      this.rotationListHandler(parent, child)
      this.initAutoScroll = true
    }
  },
  mounted() {
    this.sendLogger('舒尔特方格组件挂载完成', 'mount')
    console.log('观察传值 9999')
  },
  methods: {
    getAssetPath,
    whenClick(inputValue) {
      this.sendLogger(`计时器操作 - 动作:${inputValue}`, 'timer')
      //  开始/暂停
      if (inputValue == 'start') {
        this.time = +new Date().getTime()
        this.pre_time = this.time
        this.startTimer()
      } else if (inputValue == 'end') {
        this.stopTimer()
      } else {
        this.clearTimer()
      }
    },
    // 清空计时器
    clearTimer() {
      this.sendLogger('清空计时器', 'timer')
      this.stopTimer()
      this.milliSeconds = '00'
      this.seconds = 0
      this.time = 0
      this.pre_time = 0
      this.intervals = 0
      this.pre_intervals = 0
    },
    // 开始计时
    startTimer() {
      this.sendLogger('开始计时', 'timer')
      this.secondsTimer = setInterval(this.timeIncrement, 10)
    },
    timeIncrement() {
      // @log-ignore
      const date = new Date()
      this.intervals = +date.getTime() - this.time + this.pre_intervals
      let mills = (this.intervals % 1000) / 10
      let seconds = (this.intervals % 60000) / 1000
      let minute = Math.floor((this.intervals % 3600000) / 60000)
      let hour = Math.floor(this.intervals / 3600000)
      this.milliSeconds = mills < 10 ? '0' + Math.floor(mills) : Math.floor(mills)
      const totalSeconds = seconds + minute * 60 + hour * 3600
      this.seconds = totalSeconds < 10 ? '0' + Math.floor(totalSeconds) : Math.floor(totalSeconds)
    },
    // 暂停
    stopTimer() {
      this.sendLogger(`暂停计时 - 当前用时:${this.seconds}.${this.milliSeconds}秒`, 'timer')
      this.pre_intervals += new Date().getTime() - this.pre_time
      clearInterval(this.secondsTimer)
    },
    listEnterHandler() {
      if (this.showRankList.length > 3) {
        if (this.rotationListTimer > 0) {
          this.rotationListTimer && window.clearInterval(this.rotationListTimer)
          this.rotationListTimer = null
          this.sendLogger('鼠标进入榜单，暂停轮播', 'rotation')
        } else {
          this.rotationListTimer = -1
        }
      }
    },
    listLeaveHandler() {
      if (this.showRankList.length > 3) {
        let parent = document.getElementById('show-box')
        let child = document.getElementById('rank-normal')
        !this.rotationListTimer && this.rotationListHandler(parent, child)
        this.sendLogger('鼠标离开榜单，恢复轮播', 'rotation')
      }
    },
    // 初始化监听信令触发及历史消息，初始化数据及确定处于相应第几步
    receiveMessage(noticeContent) {
      const isHistory = this.options.isHistory
      const { actionType } = noticeContent
      const data = noticeContent
      this.sendLogger(
        `收到舒尔特方格消息 - 动作类型:${actionType} 是否历史:${isHistory}`,
        'receive'
      )
      console.log('观察传值 000', noticeContent, actionType)

      // 若irc断连又重连，则不需要再次重复渲染，导致游戏反复开始
      if (isHistory && [1, 2, 3].includes(this.step) && actionType === 'schulte_table_start') {
        this.sendLogger('IRC重连，跳过重复渲染', 'skip')
        return
      }
      if (actionType === 'schulte_table_start') {
        const { level, category, random } = data
        this.level = level
        this.category = category
        this.random = random
        this.sendLogger(
          `游戏参数设置 - 等级:${level}x${level} 类型:${category === 1 ? '自然数' : '质数'} 随机:${random}`,
          'config'
        )

        // 历史消息
        if (isHistory) {
          this.sendLogger('处理历史消息，检查是否已玩过', 'history')
          // 是否玩过当前互动
          checkSubmitSchulteTable({
            planId: this.options.roomMessage.roomInfo.planInfo.id,
            interactId: this.options.ircMsg.interactId
          })
            .then(res => {
              if (res.code === 0) {
                if (res.data.curDuration) {
                  this.sendLogger(
                    `历史记录存在，显示个人结果 - 用时:${res.data.curDuration}`,
                    'history'
                  )
                  this.initPersonalResult(res.data)
                } else {
                  this.sendLogger('历史记录不存在，从头开始游戏', 'history')
                  // 未玩过则展示，初始状态，从倒计时从头来过
                  this.initReady(data)
                }
              } else {
                // code 不为 0 , 可能interactId id 0
                this.sendLogger(
                  `查询历史提交接口异常 - code:${res.code} msg:${res.msg}`,
                  'checkSubmitSchulteTable',
                  'error'
                )
              }
            })
            .catch(err => {
              this.sendLogger(
                `查询历史提交接口报错 - ${err.message || err}`,
                'checkSubmitSchulteTable',
                'error'
              )
            })
        } else {
          this.sendLogger('非历史消息，直接初始化游戏', 'start')
          // 初始化相关数据，并开始倒计时
          this.initReady(data)
        }
        this.$sensors.track('pc_student_schulte_table_load', {
          result: 'start',
          load_duration: Date.now() - this.loadStartTime
        })
        this.sendLogger(`埋点上报 - 加载时长:${Date.now() - this.loadStartTime}ms`, 'track')
      } else if (actionType === 'show_rank_list') {
        this.sendLogger(
          `收到榜单展示消息 - 展示人数:${data.showTopNum} 榜单人数:${data.rankList.length}`,
          'rank'
        )
        // 不论是否历史消息，都要进行榜单展示，且若处于游戏中及倒计时场景，需要关闭相关lottie等操作
        this.showTopNum = data.showTopNum
        this.showRankList = data.rankList.slice(0, data.showTopNum)

        // 需要特别处理下前三名，若发的榜单人数不足前三名，则需要进行空头像补位处理
        const len = this.showRankList.length
        // 教师端加限制了，至少会有一名同学上榜才能发榜单
        if (len < 3) {
          const obj = {
            avatar: defaultAvatarIcon
          }
          let needAddLen = 3 - len
          if (needAddLen === 1) this.showRankList.splice(2, 0, obj)
          if (needAddLen === 2) this.showRankList.splice(1, 0, obj, obj)
          this.sendLogger(`榜单补位处理 - 原人数:${len} 补位:${needAddLen}`, 'rank')
        }

        const index = data.rankList.findIndex(
          item => item.userId === this.options.roomMessage.roomInfo.stuInfo.id
        )
        if (index > -1) {
          this.ownRankResult = data.rankList[index]
          this.isOnRankList = index > this.showTopNum - 1 ? false : true
          this.ownRankIndex = index + 1
          this.sendLogger(
            `找到自己的排名 - 排名:${this.ownRankIndex} 是否在榜:${this.isOnRankList}`,
            'rank'
          )
          this.isOnRankList && this.$refs?.penziAudio?.play()
        } else {
          this.sendLogger('未找到自己的排名', 'rank')
        }

        // 发布榜单时，若处于游戏中则需提示
        if (this.step === 2) {
          this.sendLogger('游戏进行中收到榜单，显示游戏结束提示', 'interrupt')
          message.info(this.$t('classroom.interactions.schulteTable.gameOver'))
        }
        this.step = 4
        console.log('观察传值 000 33333', noticeContent, actionType)
      }
    },
    destroyInteraction() {
      this.sendLogger('收到销毁互动指令', 'destroy')
      this.destoryLottie()
      if (this.step === 2) {
        this.sendLogger('游戏进行中被销毁，显示游戏结束提示', 'interrupt')
        message.info(this.$t('classroom.interactions.schulteTable.gameOver'))
      }
      this.step = 0
      this.sendLogger('舒尔特方格互动已销毁', 'destroy')
      this.$emit('close', 'schulte_table')
    },
    // 设置轮播定时器
    rotationListHandler(parent, child) {
      this.sendLogger('设置榜单轮播定时器', 'rotation')
      console.log('看下触发设置轮播定时器')
      if (!parent || !child) {
        this.sendLogger('榜单DOM元素不存在，无法设置轮播', 'rotation', 'warn')
        return
      }
      let parentScrollTop = -1
      let vm = this
      console.log('看下触发设置轮播定时器 inin', parent.scrollTop, child.scrollHeight)
      vm.rotationListTimer = setInterval(() => {
        if (parent.scrollTop >= child.scrollHeight) {
          parent.scrollTop = 0
        } else {
          // 如果存在网页缩放，很有可能没有效果，但是else部分的代码会执行
          // 原因：刚才讲到的scrollTop三个注意中标黄的一条
          // 设置scrollTop的值小于0，即scrollTop被设为0
          // 可以缩放跑一下，然后不刷新的状态下恢复百分之百跑一下，再缩放，打印scrollTop的值
          // 你会发现正常尺寸执行时打印的第一个值不是加法，而是减法，即scrollTop++增加负值
          // 这样的话就对应上了scrollTop的注意点了，增加的值小于0，就被设为0
          parent.scrollTop++
        }
        if (parent.scrollTop == parentScrollTop) {
          vm.rotationListTimer && window.clearInterval(vm.rotationListTimer)
          vm.rotationListTimer = -1
          vm.sendLogger('榜单轮播结束', 'rotation')
        } else {
          parentScrollTop = parent.scrollTop
        }
      }, 62.5)
      this.sendLogger('榜单轮播定时器启动', 'rotation')
    },
    /**
     * 倒计时ready准备
     */
    initReady() {
      this.sendLogger('初始化倒计时准备阶段', 'ready')
      this.step = 1
      this.getSubTitle()
    },
    // 生成副标题
    getSubTitle() {
      const sequenceTypeStr =
        this.category === 1
          ? this.$t('classroom.interactions.schulteTable.naturalNumber')
          : this.$t('classroom.interactions.schulteTable.primeNumber')
      const randomStr = this.random
        ? this.$t('classroom.interactions.schulteTable.shuffleMode')
        : this.$t('classroom.interactions.schulteTable.normalMode')
      this.subTitle = `${this.level}×${this.level} · ${sequenceTypeStr} · ${randomStr}`
      this.sendLogger(`副标题生成 - ${this.subTitle}`, 'config')
    },
    /**
     * ready 播放完
     */
    onLottieReadyComplete() {
      if (this.step >= 2) {
        this.sendLogger('ready动画已完成，跳过重复处理', 'ready')
        return
      }

      this.sendLogger('倒计时ready动画播放完成', 'ready')
      // document.getElementById('ready')?.destroy && document.getElementById('ready')?.destroy()
      console.log('看看refs', this.$refs)
      // this.$refs?.ready?.destroy()

      // 上报互动
      submitInteractPartakereport({
        planId: this.options.roomMessage.roomInfo.planInfo.id,
        interactId: this.options.ircMsg.interactId,
        classId: this.options.roomMessage.roomInfo.commonOption.classId
      })
      this.sendLogger('上报互动参与记录', 'report')

      // 引导层逻辑，标识是否首次进入舒尔特方格游戏
      if (!localStorage.getItem('hasPlayedSchulteTable')) {
        this.isShowPlayMask = true
        localStorage.setItem('hasPlayedSchulteTable', true)
        this.sendLogger('首次游戏，显示引导层', 'guide')
      } else {
        this.sendLogger('非首次游戏，跳过引导层', 'guide')
      }

      this.step = 2
      this.isShowPlay = true
      this.initPlayDate()
      this.whenClick('start')
      this.sendLogger('游戏正式开始', 'start')
    },
    // 数组乱序排列
    shuffle(arr) {
      let len = arr.length
      for (let i = 0; i < len - 1; i++) {
        let index = parseInt(Math.random() * (len - i))
        let temp = arr[index]
        arr[index] = arr[len - i - 1]
        arr[len - i - 1] = temp
      }
      return arr
    },
    // 构建
    initPlayDate() {
      this.sendLogger(`初始化游戏数据 - 等级:${this.level} 类型:${this.category}`, 'init')
      // 自然数数组
      const naturalNumberArr = [
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25
      ]
      // 质数数组
      const primeNumberArr = [
        2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89,
        97
      ]
      const { level, category } = this
      const len = level * level
      this.playNumberOrderArr =
        category === 1 ? naturalNumberArr.slice(0, len) : primeNumberArr.slice(0, len)
      this.sendLogger(
        `数字序列生成 - 数量:${len} 范围:${this.playNumberOrderArr[0]}-${this.playNumberOrderArr[len - 1]}`,
        'init'
      )

      this.playModeReversal('mode-reversal')
      this.playNumberShowArr = this.shuffle([...this.playNumberOrderArr])
      this.nextPlayIndex = 0
      this.nextPlayNumber = this.playNumberOrderArr[0]
      this.sendLogger(`游戏初始化完成 - 下一个数字:${this.nextPlayNumber}`, 'init')
    },
    addPressBox(key) {
      document.getElementById(key)?.classList?.add('press-box')
      document.getElementById('playing')?.classList?.add('cursor-hand90')
    },
    removePressBox(key) {
      document.getElementById(key)?.classList?.remove('press-box')
      document.getElementById('playing')?.classList?.remove('cursor-hand90')
    },
    handlerPlayMask() {
      this.sendLogger('关闭引导层', 'guide')
      this.isShowPlayMask = false
    },
    // 处理每次点击, 正在点击处理则应该锁住游戏，避免连续触发
    clickCheck(number) {
      if (this.isDisabled) {
        this.sendLogger('游戏已锁定，忽略点击', 'click')
        this.curClickPlayNumber = -1
        this.curClickCorrectStatus = 0
        return
      }

      this.sendLogger(
        `用户点击数字 - 点击:${number} 期望:${this.playNumberOrderArr[this.nextPlayIndex]} 进度:${this.nextPlayIndex + 1}/${this.playNumberOrderArr.length}`,
        'click'
      )
      this.isDisabled = true
      this.isChangeNextNumber = false
      this.curClickPlayNumber = number
      new Audio(pressAudio).play()
      let isDealClickError = true

      // 存在引导层且第一次点击
      if (this.isShowPlayMask && !this.nextPlayIndex) {
        // 销毁相关音效 lottie
        this.isShowPlayMask = false
        isDealClickError = false
        this.sendLogger('引导层首次点击，关闭引导层', 'guide')
      }

      // 正确
      if (number === this.playNumberOrderArr[this.nextPlayIndex]) {
        this.sendLogger(
          `点击正确 - 数字:${number} 进度:${this.nextPlayIndex + 1}/${this.playNumberOrderArr.length}`,
          'correct'
        )
        this.curClickCorrectStatus = 1

        // 是最后一次点击，需提交结果
        if (this.nextPlayIndex === this.playNumberOrderArr.length - 1) {
          this.stopTimer()
          this.playDuration = Number(this.seconds) + Number(this.milliSeconds) / 100
          this.sendLogger(`游戏完成 - 总用时:${this.playDuration}秒`, 'complete')
          this.submitPlay()
        } else {
          // 实时随机，每次洗牌
          // 300保证每次动效播放完毕
          if (this.random) {
            this.sendLogger('实时随机模式，重新洗牌', 'shuffle')
            this.playModeReversal('mode-reversal')
            const timerRandom = setTimeout(() => {
              this.curClickPlayNumber = -1
              this.playNumberShowArr = this.shuffle([...this.playNumberOrderArr])
              clearTimeout(timerRandom)
            }, 300)
          }
          this.nextPlayIndex++
          this.nextPlayNumber = this.playNumberOrderArr[this.nextPlayIndex]
          this.isChangeNextNumber = true
          this.sendLogger(
            `进入下一步 - 下一个数字:${this.nextPlayNumber} 进度:${this.nextPlayIndex + 1}/${this.playNumberOrderArr.length}`,
            'next'
          )

          const changeNumberTimer = setTimeout(() => {
            this.isChangeNextNumber = false
            this.isDisabled = false
            clearTimeout(changeNumberTimer)
          }, 300)
        }
      } else {
        // 错误情况
        if (isDealClickError) {
          this.sendLogger(
            `点击错误 - 点击:${number} 期望:${this.playNumberOrderArr[this.nextPlayIndex]}`,
            'error'
          )
          this.playModeReversal('mode-moving')
          this.curClickCorrectStatus = 2
          new Audio(clickErrAudio).play()
          const timer = setTimeout(() => {
            this.curClickPlayNumber = -1
            this.isDisabled = false
            clearTimeout(timer)
          }, 610)
        } else {
          this.curClickCorrectStatus = 0
          this.isDisabled = false
        }
      }
    },
    submitPlay() {
      if (!this.hasSubmitted) {
        this.sendLogger(`开始提交游戏结果 - 用时:${this.playDuration}秒`, 'submit')
        submitSchulteTable({
          planId: this.options.roomMessage.roomInfo.planInfo.id,
          interactId: this.options.ircMsg.interactId,
          duration: this.playDuration
        })
          .then(res => {
            if (res.code === 0) {
              this.sendLogger(`提交成功 - 当前用时:${this.playDuration}秒`, 'submit')
              this.initPersonalResult(res.data)
              this.hasSubmitted = true
              this.$sensors.track('pc_student_schulte_table_load', {
                result: 'submitSuccess',
                playDuration: this.playDuration
              })
            } else {
              this.sendLogger(`提交失败 - code:${res.code} msg:${res.msg}`, 'submit', 'error')
              this.$sensors.track('pc_student_schulte_table_load', {
                result: 'submitFail'
              })
              // 触发重复提交场景
            }
          })
          .catch(err => {
            // 仅处于游戏中场景才会重试，若信令触发到排行榜了则停止二次确认
            if (this.step === 2) {
              this.sendLogger('提交失败，显示重试确认框', 'retry')
              // 测试断网场景，二次确认提示, 点确认再请求接口重试
              this.$Modal.info({
                class: 'modal-simple apu-header center-modal',
                title: this.$t('classroom.interactions.schulteTable.submitFailTitle'),
                content: this.$t('classroom.interactions.schulteTable.submitFailTip'),
                okText: this.$t('classroom.interactions.schulteTable.submitFailBtn'),
                width: px2vh(416),
                centered: true,
                closable: false,
                zIndex: 3000,
                onOk: () => {
                  this.sendLogger('用户确认重试提交', 'retry')
                  this.submitPlay()
                }
              })
            }
            this.sendLogger(`提交接口报错 - ${err.message || err}`, 'submit', 'error')
          })
          .finally(() => {
            this.isDisabled = false
            this.curClickCorrectStatus = 0
            this.curClickPlayNumber = -1
          })
      } else {
        this.sendLogger('重复提交，已忽略', 'submit', 'warn')
      }
    },
    // 操作小盒子，翻转或者偏移
    playModeReversal(className) {
      this.sendLogger(`执行游戏动效 - 类型:${className}`, 'animation')
      this.$nextTick(() => {
        let ele = document.getElementById('play-moudle')
        ele?.classList?.add(className)
        let duration = className === 'mode-moving' ? 1020 : 400
        const clearClass = setTimeout(() => {
          ele?.classList?.remove(className)
          clearTimeout(clearClass)
        }, duration)
      })
    },
    initPersonalResult(data) {
      this.sendLogger(
        `初始化个人结果 - 当前用时:${data.curDuration || this.playDuration} 最佳用时:${data.bestDuration || 0} 是否首次:${data.isFirst}`,
        'result'
      )
      this.curDuration = data.curDuration || this.playDuration
      this.bestDuration = data.bestDuration || 0
      this.isShowBestTime = !data.isFirst
      if (data.isFirst) {
        this.resultTip = this.$t('classroom.interactions.schulteTable.firstChallenge')
        this.sendLogger('首次挑战', 'result')
      } else if (+this.curDuration < +this.bestDuration) {
        this.resultTip =
          this.$t('classroom.interactions.schulteTable.newRecord') +
          '  ' +
          this.$t('classroom.interactions.schulteTable.duration', {
            duration: this.curDuration
          })
        this.sendLogger(
          `创造新纪录 - 新纪录:${this.curDuration} 旧纪录:${this.bestDuration}`,
          'result'
        )
      } else {
        this.resultTip = this.$t('classroom.interactions.schulteTable.gameCompleted')
        this.sendLogger('游戏完成，未破纪录', 'result')
      }

      this.getSubTitle()
      this.step = 3
      this.ownResultShow = true
      const timer = setTimeout(() => {
        this.ownResultShow = false
        clearTimeout(timer)
      }, 3000)
      this.sendLogger('个人结果页显示完成', 'result')
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionId = this.options?.ircMsg?.interactId || 'unknown'
      const interactionType = 'SchulteTable'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
    },
    /**
     * 摧毁lottie
     */
    destoryLottie() {
      this.sendLogger('开始摧毁Lottie动画', 'cleanup')
      // 音效 readygoAudio、resultAudio、paihangbangAudio、clickError、clickNumber
      // switch (this.step) {
      //   case 1:
      //     // document.getElementById('ready')?.destroy()
      //     this.$refs?.ready?.destroy()
      //     break
      //   case 2:
      //     // document.getElementById('jiantou')?.destroy()
      //     this.$refs?.jiantou?.destroy()
      //     break
      //   case 3:
      //     // document.getElementById('caidai')?.destroy()
      //     // document.getElementById('newRecord')?.destroy()
      //     this.$refs?.caidai?.destroy()
      //     this.$refs?.newRecord?.destroy()
      //     break
      //   case 4:
      //     // document.getElementById('caidai')?.destroy()
      //     // document.getElementById('paihangbang')?.destroy()
      //     // document.getElementById('penzi')?.destroy()
      //     this.$refs?.caidai?.destroy()
      //     this.$refs?.paihangbang?.destroy()
      //     this.$refs?.penzi?.destroy()
      //     break
      //   default:
      //     return
      // }
      this.sendLogger('Lottie动画摧毁完成', 'cleanup')
    }
  }
}
</script>
<style lang="scss" scoped>
@use './app.scss';
</style>
