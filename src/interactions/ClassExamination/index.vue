<!-- h5考试exam -->
<template>
  <div class="exam-container" ref="exam-wrapper" v-if="showEaxmIframe">
    <iframe id="class-examination" :src="examUrl" @onload="handleIframeLoad" />
    <ExamRank class="close-exam" :list="rankList" v-if="showRankList"></ExamRank>
  </div>
</template>
<script>
import bindEventMixin from '@/mixins/h5BindEvent.js'
import { emitter } from '@/hooks/useEventBus'
import Cookies from 'js-cookie'
import { useIrcService } from '@/hooks/useIrcService'
import { inClassExamLog } from '@/utils/web-log/HWLogDefine'
import { useI18n } from 'vue-i18n'
import ExamRank from './ExamRank/ExamRank.vue'
import { useClassData } from '@/stores/classroom'
const { getIrcService } = useIrcService()
export default {
  mixins: [bindEventMixin],
  components: {
    ExamRank
  },
  computed: {
    // 使用 computed 从 Pinia store 获取数据
    baseData() {
      return useClassData().baseData
    },
    // 添加学生列表计算属性
    studentList() {
      return useClassData().studentList
    }
  },
  data() {
    return {
      examUrl: '',
      showEaxmIframe: false, // 是否展示考试弹窗
      showRankList: false, // 是否展示考试弹窗
      rankList: []
    }
  },
  // 在 setup() 中获取 locale
  setup() {
    const { locale } = useI18n()
    // 返回给组件实例使用
    return {
      locale
    }
  },
  methods: {
    receiveMessage(noticeContent) {
      this.sendLogger('class_examination 课中考试', { noticeContent })
      this.setExaminationInfo(noticeContent)
    },

    mergeRankListData(list) {
      // 将store中的学生列表转为以uid为key的映射表
      const currentDataMap = this.studentList.reduce((map, student) => {
        map[student.userId] = student
        return map
      }, {})

      let listData = list.map(item => {
        const storeStudent = currentDataMap[item.userId]
        return {
          ...item,
          avatar: storeStudent?.avatar,
          effect: storeStudent?.prop?.avatarFrame?.resourceUrl,
          name: storeStudent.nickName,
          isCurrentUser: item.userId === this.baseData.stuInfo.id + ''
        }
      })
      // return this.generateDebugData()
      return listData
    },

    generateDebugData() {
      let data = []
      for (let i = 1; i < 51; i++) {
        if (i === 25) {
          data.push({
            avatar:
              'https://download-pa-s3.thethinkacademy.com/415/20201222/1608632911194U2fMdYEfeN.png',
            coin: 100,
            effect: undefined,
            isCurrentUser: true,
            name: `83海滨公园地点上海国际艺术节${i}`,
            ranking: `${i}`,
            score: 5,
            studentName: `83海滨公园地点上海国际艺术节${i}`,
            userId: `${i}`
          })
        } else {
          data.push({
            avatar:
              'https://download-pa-s3.thethinkacademy.com/415/20201222/1608632911194U2fMdYEfeN.png',
            coin: 100,
            effect: undefined,
            isCurrentUser: false,
            name: `83海滨公园地点上海国际艺术节${i}`,
            ranking: `${i}`,
            score: 5,
            studentName: `83海滨公园地点上海国际艺术节${i}`,
            userId: `${i}`
          })
        }
      }
      return data
    },

    async setExaminationInfo(noticeContent) {
      inClassExamLog.info('课中考试noticeContent', noticeContent.examUrl)
      emitter.emit('setExaminationStatus', true) // 开始考试时将其他学生摄像头变为头像
      const timeOffset = this.baseData.commonOption.timeOffset // 获取服务器与本机时间差值
      const localTime = +new Date() // 本机时间
      const localRealTime = parseInt((localTime + timeOffset) / 1000)

      // 判断学生是否迟到(精确到秒)  isLate: 0 未迟到，1迟到。服务器当前时间 - 考试考试时间 >1分钟为迟到
      const isLate = localRealTime - noticeContent.beginTime - 60
      // 剩余考试时间 = 考试开始时间 + 考试总时长 - 当前时间，如果未迟到，则剩余考试时间=总时长
      let remainSeconds = noticeContent.beginTime + noticeContent.totaltime - localRealTime
      const token = Cookies.get('_official_token') || ''

      // 开始考试时如果未迟到，则剩余考试时间=总时长
      // 1: 开始考试
      if (noticeContent.status === 1 && isLate <= 0) {
        remainSeconds = noticeContent.totaltime
      }

      if (!this.examUrl) {
        // 判断examUrl是否有值是为了防止status变化时，examUrl会变化
        // 课堂相关信息
        const classInfo = `from=live&classId=${this.baseData.commonOption.classId}&studentId=${this.baseData.commonOption.stuId}&token=${token}`
        // 考试相关信息 completed=1提交作答，0未提交
        const testInfo = `duration=${noticeContent.totaltime}&completed=${
          noticeContent.status === 3 ? 1 : 0
        }&isLate=${isLate > 0 ? 1 : 0}&remainSeconds=${remainSeconds}`
        // iframe拼接url参数  platform=3表示pc端
        const language = this.locale // 获取当前语言
        this.examUrl = `${noticeContent.examUrl}&platform=3&language=${language}&${classInfo}&${testInfo}`
        inClassExamLog.info('课中考试this.examUrl', this.examUrl)
      }

      this.showEaxmIframe = true // 打开iframe答题弹窗
      this.showRankList = (noticeContent?.rankStatus ?? 0) === 1
      const _rankList = this.mergeRankListData(noticeContent?.rankInfo ?? [])
      this.rankList = _rankList ?? []

      // 2: 更改考试时间
      if (noticeContent.status === 2) {
        // 传递获取剩余考试时间
        this.$nextTick(() => {
          document.getElementById('class-examination').contentWindow.postMessage(
            {
              type: 'updateRemainSecondsTo',
              data: {
                remainSeconds: remainSeconds
              }
            },
            '*'
          )
          this.sendLogger(`class_examination 课中考试更改考试时间`, { remainSeconds })
        })
      }
      // 3: 老师收卷结束考试
      else if (noticeContent.status === 3) {
        this.$nextTick(() => {
          document.getElementById('class-examination').contentWindow.postMessage(
            {
              type: 'completeExam'
            },
            '*'
          )
        })
        this.sendLogger(`class_examination 课中考试老师收卷结束考试`)
      }
    },

    // 监听学生交卷时，发送给教师端
    sendSubmitEaxmToTeacher() {
      const opts = {
        nickname: this.baseData.configs.teacherIrcId,
        content: {
          type: 128, // 学生交卷后给主讲发送消息
          submit: true
        },
        chatMsgPriority: 99 // 私聊类型
      }
      const IrcService = getIrcService()
      IrcService.sendPeerMessage(opts)
    },

    // 主讲端关闭互动
    destroyInteraction() {
      // 主讲端关闭考试窗口，则学生端直接关闭iframe不需要通知h5
      this.showEaxmIframe = false
      this.examUrl = '' // 关闭iframe后清空url，再次打开考试后重新加载新的url
      emitter.emit('setExaminationStatus', false)
      this.sendLogger('class_examination 关闭课中考试')
      this.$emit('close', 'class_examination')
    },

    handleIframeLoad() {
      inClassExamLog.success('加载H5成功-小班考试')
    },
    sendLogger(msg) {
      inClassExamLog.info(msg)
    }
  },
  mounted() {
    this.bindEvent()
  }
}
</script>
<style scoped lang="less">
.exam-container {
  width: 100%;
  height: 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAP4AAADwBAMAAADSssQSAAAAAXNSR0IArs4c6QAAABtQTFRFMjIyMzMzNDQ0NTU1NjY2Nzc3ODg4OTk5Ojo6QUDc4AAAA9ZJREFUeNrtnSt320AQhUeSHdOwGAYaFhoaGgYWGgo6zDROU9+fXaC2lmOvtI/Z2SfbE7Dn3M3cydWnWYWAHRER0U8Av4blAgDehvUWwO9h2QHAYVhvAFyGZQMAx2H9AgDPw/oE4GNYrgDgdVj3AD6H5RKgR7usAVymdzldd3kCgB+TIvZKEWS4y9f9LtvrUbTfRfzVphZBDruMTug8+vFIxPWc//+av4mgmV362V0UxaISAQDv12IhjZI7GpXcea7kbkSQfuGalZymb8iq5BRHMfJNoymCbAv33zkrjmJp5JubXTRL7qBdcvO+sShc+5J7IMJwF82S0/eNfcNfG/lm+bhYdAv37NqqFSLuCnfnr+E/EqFRchoN39o3PA2/s/aNY8P/cPWN74xh4j6pkrtzn3avfDLOGDMivGeMGd+0EyXH3vDfFO5T7MKSMaZ94z9jTIuQyhit4gFDs3CdM4aO+8wKl8U3YhlD4Ruehn+y9Y1jw3f2TYiGP/m3z1PGUIkgoYyhEkFCGUPlG/LxUGngG3q4S2ve8C2zOclkDKVvyLbhM2VzEskYat9QEfxvG4r/zfqGl/9NBaWI+N9IRDH871j5XxH8z0pEuvzvpfK/dPhfV/mfP/63DcX/XH2TJ//Tf6bNh/9ZBaVS+F9TKv87VP4XF/+jnPlfV/lfQvyvT4P/+cvmZfC/lo//MWfzzOf/SuF/Zr5Jnv8to+d/FJj/LXzyvy4H/teF4n/vlf9V/nfjPsmB79D87yu7+T9XGOM9YzDxP+uMUfmfH/5HgfnfovI/96AkljEq/yuS/104+V9bOP/bx8v/LH2TNP9j8E3m/G9WRF7zf02B/M8tKJFExqj8L0b+t2Hjf86+KWr+T5r/9Wz8z1c2Z8wYVhCzFP638sL/zHzjjf9Jzf81BfI/rzDGkf+5+qbyP/kXPhb8z1c2z3P+T1+Enxc+WfM/zhel2fG/VQT8T3+IMZ/5P0sRkvzvmZP/MV2UIPEPPlT+NxZB0h98KH3+T5T/zYsg6Rc+ifA/2WGsxPgfe1Aqiv/tBPifmQhR/vdpxv8ELmMWw//2kvzP7vZ9gMuY+c3/5cP/BGBM5X9R8j+py5h58j9tEWID32uzr29uhWAMmRVuqfzP2zAWCWUMM/7XVP7nbeA7T/5nO4xVBP+jOv9X+V8c/K9n43+cvpHlf6858D/WixKCH3yo/E+f/wnBmFT4n6+LElnwP4qf/23q/F8R/K9n438S2TzEBx9C8T8D95lljI21iHT5n9f/vhMp/2MPSrHxP+nLmEnxP4qf/23Y+J8MjAnN//4Ax3H9/N6BeDoAAAAASUVORK5CYII=);
  background-repeat: repeat;
  display: flex;
  justify-content: center;
  position: relative;
  iframe {
    width: calc(100% / 16 * 9 / 3 * 4);
    height: 100%;
    border: none;
    background: white;
    position: relative;
  }
  .close-exam {
    width: calc(100% / 16 * 9 / 3 * 4);
    height: 100%;
    position: absolute;
  }
}
</style>
