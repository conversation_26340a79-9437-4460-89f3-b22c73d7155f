<template>
  <div class="panel">
    <div class="header">
      <img src="./sources/header_left.png" alt="Exam Rank Board" />
      <div class="title">
        {{ $t('classroom.modules.ClassExam.rankHeaderTitle') }}
      </div>
      <img src="./sources/header_right.png" alt="Exam Rank Board" />
    </div>
    <ul class="list" ref="listRef">
      <li v-for="item in list" :key="item.uid" :class="{ 'current-user-li': item.isCurrentUser }">
        <RankCell class="cell" :cellData="item"></RankCell>
      </li>
    </ul>
    <RankCell
      v-show="flow"
      class="cell flow"
      :cellData="list.find(item => item.isCurrentUser) || {}"
    ></RankCell>
  </div>
</template>

<script>
import RankCell from './RankCell.vue'
import { debounce } from 'lodash' // 建议使用lodash的防抖函数

export default {
  components: { RankCell },
  props: {
    list: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      flow: null,
      localList: [...this.list], // 使用本地副本
      observer: null,
      currentLi: null
    }
  },

  watch: {
    list(newVal) {
      this.localList = [...newVal]
      this.handleFlowView()
    }
  },

  mounted() {
    this.$refs.listRef.addEventListener('scroll', this.handleListScroll)
    this.handleFlowView()
  },

  beforeUnmount() {
    this.$refs.listRef.removeEventListener('scroll', this.handleListScroll)
  },

  methods: {
    // 处理flowview方法
    handleFlowView() {
      const listRef = this.$refs.listRef
      const listItems = listRef.querySelectorAll('li')
      const visibleList = []
      listItems.forEach((item, index) => {
        const rect = item.getBoundingClientRect()
        // 获取列表容器相对于视口的位置
        const listRect = listRef.getBoundingClientRect()

        if (rect.bottom > listRect.top && rect.top < listRect.bottom) {
          visibleList.push(index)
        }
      })
      // console.log('visibleList', visibleList)
      // 检查当前用户条目是否在可视区域
      const currentUserIndex = this.localList.findIndex(item => item.isCurrentUser)
      // 如果currentUserIndex 大于visibleList的最后一个元素，显示flow
      if (currentUserIndex > visibleList[visibleList.length - 1]) {
        this.flow = true
      } else {
        this.flow = false
      }
    },

    // 原有的 handleListScroll 方法，使用箭头函数包裹
    handleListScroll: debounce(function () {
      // console.log('ul 滚动事件触发')
      this.handleFlowView()
    }, 100) // 这里设置防抖时间为 300 毫秒，可根据实际情况调整
  }
}
</script>

<style lang="scss" scoped>
.header {
  margin-top: 3.2%;
  display: flex;
  justify-content: center;
  align-items: center;
  div {
    overflow: no-wrap;
  }
}

.panel {
  background-image: url('./sources/exam_rank_bg.png');
  background-color: rgb(51, 51, 51);
  background-repeat: no-repeat;
  background-size: contain;
  overflow: hidden;
  position: relative;
  background-position: center;
}

.title {
  color: #ffffff;
  text-align: center;
  /// 使用百分比设置font-size
  font-size: 2.5vh; /* 根据屏幕宽度动态调整 */

  text-shadow: 0.2px 2px 3px rgba(134, 66, 12, 1);
  margin-left: 24px;
  margin-right: 24px;
  white-space: nowrap;
  /* 可选附加样式 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block; /* 或 flex 根据布局需要 */
}

.list {
  list-style: none;
  margin-top: 6.5%;
  margin-left: 6.5%;
  margin-right: 6.5%;
  margin-bottom: 3.25%;
  overflow: auto;
  height: 78%;
  // 隐藏滚动条
  scrollbar-width: none;
  &::-webkit-scrollbar {
    display: none;
  }
  width: calc(100% - 13%);
}

.cell {
  margin: auto 0;
  width: 100%;
}

.flow {
  position: absolute;
  bottom: 15px;
  width: 100%;
  z-index: 1000;
  left: 40px;
  width: calc(100% - 80px);
  margin-right: 40px;
}
</style>
