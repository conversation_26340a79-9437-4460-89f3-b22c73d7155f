<template>
  <div class="container">
    <div>
      <img src="./sources/icon_coin.png" />
    </div>
    <span
      class="text"
      :class="{
        'text--selected': isCurrentUser
      }"
      >{{ '+' + coin }}</span
    >
  </div>
</template>

<script>
export default {
  props: {
    coin: {
      type: String,
      required: true
    },
    isCurrentUser: {
      type: Boolean,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 27px;
  position: relative;

  img {
    width: 27px;
    height: 27px;
    position: absolute;
    object-fit: contain;
    top: 50%;
    transform: translate(0%, -50%); /* 图片居中 */
    left: -4px;
    z-index: 1;
  }

  .text {
    font-size: 18px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background-color: #e18d30;
    color: white;
    padding-left: 30px;
    padding-right: 10px;
    z-index: 0;
    ///设置为圆角
    border-radius: 20px;
    text-align: center;
    background-color: #e18d30;
    color: white;

    // 选中模式
    &--selected {
      color: #e18d30;
      background-color: white;
    }
  }
}
</style>
