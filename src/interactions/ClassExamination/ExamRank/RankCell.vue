<template>
  <!-- 主界面 -->
  <div class="container">
    <div
      class="content"
      :class="{
        'content--selected': cellData.isCurrentUser
      }"
    >
      <RankTag
        class="rankTag"
        :rank="cellData.ranking"
        :isCurrentUser="cellData.isCurrentUser"
      ></RankTag>
      <RankUserAvatar
        class="headImage"
        :avatar="cellData.avatar"
        :effect="cellData.effect"
      ></RankUserAvatar>
      <div class="name">{{ cellData.name }}</div>
      <div class="score">{{ cellData.score }}</div>
      <RankCoin
        class="coin"
        :coin="cellData.coin"
        :isCurrentUser="cellData.isCurrentUser"
      ></RankCoin>
    </div>
  </div>
</template>

<script>
import RankTag from './RankTag.vue'
import RankUserAvatar from './RankUserAvatar.vue'
import RankCoin from './RankCoin.vue'

export default {
  components: {
    RankTag,
    RankUserAvatar,
    RankCoin
  },
  props: {
    cellData: {
      type: Object,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  font-family: PingFangSC-Semibold, PingFang SC;
  background-color: transparent;
  border: 2px solid transparent;
  color: #994112;
  margin-left: 20px;
  margin-right: 20px;
  width: 100%;
}

$selected: #ff9f0a;
$gradient-normal: linear-gradient(to right, rgba(255, 247, 240, 1), rgba(248, 229, 207, 1));

.content {
  display: flex;
  align-items: center;
  height: 70px;
  /* 渐变色 */
  font-size: 18px;
  border-radius: 16px;
  gap: 0; // 添加这行，移除flex项目之间的间隙
  width: 100%;

  // 默认背景
  background: $gradient-normal;

  // 渐变模式
  &--gradient {
    background: $gradient-normal;
    color: #994112;
  }

  // 选中模式
  &--selected {
    background: $selected;
    color: white;
  }
}

.rankTag {
  padding-left: 0px;
  margin-left: 0px;
  height: 36px;
  width: 36px;
  // background-color: red;
  padding: 0; // 修改padding
  margin: 0; // 修改margin
  display: flex; // 添加这行
  align-items: center; // 添加这行
  justify-content: center; // 添加这行
  margin-right: 20px;
  margin-left: 20px;
}

.headImage {
  width: 54px;
  height: 54px;
  margin: 0; // 添加这行
}

.name {
  max-width: 333px;
  // 不换行，超出部分显示省略号
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  font-size: 16px;
  margin-left: 20px;
  margin: 0;
  width: 333px;
}

.score {
  width: 60px;
  margin-right: 60px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  /* 添加水平居中 */
  margin-left: 20px;
  text-align: center;
  /* 添加文本居中 */
  line-height: 1;
  /* 确保行高正确 */
}

.coin {
  width: 180px;
  margin-right: 0px;
  /* 添加右边距 */
  margin-left: auto;
}
</style>
