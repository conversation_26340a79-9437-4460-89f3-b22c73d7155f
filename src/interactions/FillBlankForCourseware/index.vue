<template>
  <div class="fillBlanksBox" v-if="visible">
    <FillBlanksComponent
      :userConfig="userConfig"
      :judgeConfig="judgeConfig"
      :quesContent="quesContent"
      :countDownTime="countDownTime"
      :disableSubmit="disableSubmit"
      :showResultToast="showResultToast"
      :coin="rightCoin"
      :submitText="$t('common.submit')"
      @submit="submitAnswer"
      @changeAnswer="changeAnswer"
      @countDownComplete="countDownComplete"
      class="fillBlanksContainer"
    />
  </div>
</template>

<script>
import FillBlanks from './fillBlanks'
import FillBlanksComponent from './FillBlanksComponent.vue'
import logger from '@/utils/logger'
import { addClass } from '@/utils/util'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { emitter } from '@/hooks/useEventBus'

export default {
  name: 'FillBlankForCourseware',
  components: {
    FillBlanksComponent
  },
  props: {
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      showResultToast: false, // 是否显示结果面板
      judgeConfig: {
        halfRight: false
      }, // 判题配置
      userConfig: null, // 用户配置
      quesContent: null, // 题目内容
      disableSubmit: true, // 禁用提交按钮
      userAnswerData: null, // 用户作答内容
      countDownTime: 0, // 倒计时
      isSubmit: false,
      isRight: 3,
      rightCoin: 0,
      visible: false,
      talqsLoaded: false, // 标记talqs是否已加载
      keyboardObserver: null // 键盘监听器
    }
  },
  async created() {
    // 先初始化连对弹框为关闭状态
    emitter.emit('initClose')
    this.fillBlanks = new FillBlanks({
      ...this.options
    })
    this.countDownTime = this.fillBlanks.initCountdownTime() || 0
    const { data, code } = await this.fillBlanks.getStatus() // 获取互动所需数据
    if (code !== 0 || !data) return // 接口出错、已经作答 均不显示互动
    if (data.questionType === 4) this.quesContent = JSON.parse(data.questionContent)
    this.isSubmit = data.isSubmit
    // 监听
    this.initEventListener()
  },
  async mounted() {
    console.log(this.options, 'options')
    this.userConfig = {
      org: 'haiwaifenxiao',
      sys: 'zaixianketang',
      stuId: this.options.roomMessage.roomInfo.stuInfo.id
    }

    // 先加载talqs文件
    await this.loadTalqs()

    this.$nextTick(() => {
      addClass(document.getElementById('interactionController'), 'index-99')
      // 确保talqs已加载后再设置语言
      if (window.TalqsInteraction) {
        window.TalqsInteraction.lang = 'en'
      }
      this.fillBlanks.interactOpen() // 上报互动打开
      this.sendLogger('收到互动', 'start')

      // 监听键盘创建
      this.observeKeyboard()
    })
  },
  watch: {
    quesContent: {
      handler(newVal) {
        if (newVal && !this.isSubmit) {
          this.visible = true
        }
      },
      deep: true
    }
  },
  methods: {
    /**
     * 动态加载talqs相关文件
     */
    async loadTalqs() {
      if (this.talqsLoaded) return

      try {
        // 创建并加载CSS文件
        const cssLink = document.createElement('link')
        cssLink.rel = 'stylesheet'
        cssLink.href = new URL('./talqs/index.min.css', import.meta.url).href
        document.head.appendChild(cssLink)

        // 动态加载jQuery
        await this.loadScript(new URL('./talqs/jquery.min.js', import.meta.url).href)

        // 确保window对象存在必要的属性
        if (!window.TalqsInteraction) {
          window.TalqsInteraction = {}
        }
        if (!window.TalqsTemplate) {
          window.TalqsTemplate = {}
        }
        if (!window.judge) {
          window.judge = {}
        }

        // 动态加载talqs主文件
        await this.loadScript(new URL('./talqs/index.min.js', import.meta.url).href)

        // 等待一小段时间确保talqs完全初始化
        await new Promise(resolve => setTimeout(resolve, 100))

        this.talqsLoaded = true
        console.log('Talqs files loaded successfully')
      } catch (error) {
        console.error('Failed to load talqs files:', error)
        // 即使加载失败也标记为已尝试，避免重复尝试
        this.talqsLoaded = true
      }
    },

    /**
     * 加载脚本文件
     */
    loadScript(src) {
      return new Promise((resolve, reject) => {
        // 检查是否已经加载过该脚本
        const existingScript = document.querySelector(`script[src="${src}"]`)
        if (existingScript) {
          resolve()
          return
        }

        const script = document.createElement('script')
        script.src = src
        script.onload = () => {
          console.log(`Script loaded: ${src}`)
          resolve()
        }
        script.onerror = error => {
          console.error(`Failed to load script: ${src}`, error)
          reject(error)
        }
        document.head.appendChild(script)
      })
    },
    countDownComplete() {
      this.destroyInteraction()
    },
    /**
     * 注册监听事件
     */
    initEventListener() {
      emitter.on('closeContinusCorrect', data => {
        if (data) {
          this.showResultToast = false
          this.destroyInteraction()
        }
      })
    },
    /**
     * 提交答案
     *
     * 这里得到的答案，已经在组件里把数组拍扁了，并且已经判断了对错。
     * 直接提交给后端即可
     */
    async submitAnswer(userAnswerData) {
      this.disableSubmit = true
      const { code, rightCoin, isSubmit } = await this.fillBlanks.submitAnswer(userAnswerData)

      // 获取答题结果描述
      const { isRight, answerData } = userAnswerData
      const resultText = isRight === 1 ? '正确' : isRight === 0 ? '错误' : '未作答'
      const answerText = Array.isArray(answerData)
        ? answerData.filter(item => item).join(',')
        : '无答案'
      const coinText = rightCoin > 0 ? ` 获得金币:${rightCoin}` : ''

      this.sendLogger(
        `提交填空答案 - 答案:${answerText} 结果:${resultText}${coinText}`,
        'submit',
        'info'
      )
      if (code !== 0) return
      this.showResultToast = true
      this.rightCoin = rightCoin
      this.isRight = isRight
      this.isSubmit = isSubmit
      classLiveSensor.osta_ia_base_interaction_submit(this.options?.ircMsg, '4', isRight)
    },

    /**
     * 答案更改 (其实这个没啥用，主要是更改提交按钮的状态)
     */
    changeAnswer(userAnswerData) {
      this.disableSubmit = userAnswerData.answerData.every(item => !item)
      const answerText = userAnswerData.answerData.filter(item => item).join(',') || '空'
      this.sendLogger(`填空答案变更 - ${answerText}`, 'change')
    },

    /**
     * 获取题目类型名称
     */
    getQuesTypeName() {
      // 填空题组件固定为填空题类型
      return '填空题'
    },

    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const quesTypeName = this.getQuesTypeName()
      const interactType = `FillBlank-${quesTypeName}`

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactType,
          interactId: this.options.ircMsg.interactId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })
      console.log(`[${interactType}][${this.options.ircMsg.interactId}] ${msg}`, { stage, level })
    },
    /**
     * 监听键盘元素创建
     */
    observeKeyboard() {
      // 创建 MutationObserver 监听 body 的子元素变化
      this.keyboardObserver = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
          if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(node => {
              if (node.nodeType === 1 && node.classList) {
                // 检查是否是键盘元素
                if (
                  node.classList.contains('keyboardLayerOfPad') ||
                  node.classList.contains('blankOptionKeypad')
                ) {
                  // 键盘被创建，设置宽度
                  this.setKeyboardWidth(node)
                }
              }
            })
          }
        })
      })

      // 开始监听 body 的变化
      this.keyboardObserver.observe(document.body, {
        childList: true,
        subtree: false
      })
    },

    /**
     * 设置键盘宽度与 fillBlanksContent 一致
     */
    setKeyboardWidth(keyboardElement) {
      this.$nextTick(() => {
        const fillBlanksContent = document.getElementById('fillBlanksContent')
        if (fillBlanksContent && keyboardElement) {
          const rect = fillBlanksContent.getBoundingClientRect()
          const windowWidth = window.innerWidth

          // 计算百分比
          const widthPercent = (rect.width / windowWidth) * 100
          const leftPercent = (rect.left / windowWidth) * 100

          keyboardElement.style.width = widthPercent + '%'
          keyboardElement.style.left = leftPercent + '%'

          const keyboardType = keyboardElement.classList.contains('keyboardLayerOfPad')
            ? 'keyboardLayerOfPad'
            : 'blankOptionKeypad'
          this.sendLogger(
            `${keyboardType} 宽度已设置为: ${widthPercent.toFixed(2)}% (${rect.width}px)`,
            'keyboard'
          )
        }
      })
    },

    destroyInteraction() {
      this.sendLogger('结束互动', 'end')

      // 停止监听键盘创建
      if (this.keyboardObserver) {
        this.keyboardObserver.disconnect()
        this.keyboardObserver = null
      }

      const keyboard = document.getElementsByClassName('keyboardLayerOfPad')[0]
      if (keyboard) document.body.removeChild(keyboard)
      const addKeyboard = document.getElementsByClassName('blankOptionKeypad')[0]
      if (addKeyboard) document.body.removeChild(addKeyboard)
      this.$emit('close', 'fill_blank')
    }
  },
  beforeUnmount() {
    // 如果没有参与作答，则提交一条未作答数据
    if (!this.isSubmit) {
      this.submitAnswer({
        isRight: 3,
        answerData: [],
        judgeData: []
      })
    }
  },
  /**
   * 解绑事件
   */
  unmounted() {
    emitter.off('closeContinusCorrect')
  }
}
</script>

<style scoped lang="scss">
.fillBlanksBox {
  color: #333;
  height: 100%;
  width: 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAP4AAADwBAMAAADSssQSAAAAAXNSR0IArs4c6QAAABtQTFRFMjIyMzMzNDQ0NTU1NjY2Nzc3ODg4OTk5Ojo6QUDc4AAAA9ZJREFUeNrtnSt320AQhUeSHdOwGAYaFhoaGgYWGgo6zDROU9+fXaC2lmOvtI/Z2SfbE7Dn3M3cydWnWYWAHRER0U8Av4blAgDehvUWwO9h2QHAYVhvAFyGZQMAx2H9AgDPw/oE4GNYrgDgdVj3AD6H5RKgR7usAVymdzldd3kCgB+TIvZKEWS4y9f9LtvrUbTfRfzVphZBDruMTug8+vFIxPWc//+av4mgmV362V0UxaISAQDv12IhjZI7GpXcea7kbkSQfuGalZymb8iq5BRHMfJNoymCbAv33zkrjmJp5JubXTRL7qBdcvO+sShc+5J7IMJwF82S0/eNfcNfG/lm+bhYdAv37NqqFSLuCnfnr+E/EqFRchoN39o3PA2/s/aNY8P/cPWN74xh4j6pkrtzn3avfDLOGDMivGeMGd+0EyXH3vDfFO5T7MKSMaZ94z9jTIuQyhit4gFDs3CdM4aO+8wKl8U3YhlD4Ruehn+y9Y1jw3f2TYiGP/m3z1PGUIkgoYyhEkFCGUPlG/LxUGngG3q4S2ve8C2zOclkDKVvyLbhM2VzEskYat9QEfxvG4r/zfqGl/9NBaWI+N9IRDH871j5XxH8z0pEuvzvpfK/dPhfV/mfP/63DcX/XH2TJ//Tf6bNh/9ZBaVS+F9TKv87VP4XF/+jnPlfV/lfQvyvT4P/+cvmZfC/lo//MWfzzOf/SuF/Zr5Jnv8to+d/FJj/LXzyvy4H/teF4n/vlf9V/nfjPsmB79D87yu7+T9XGOM9YzDxP+uMUfmfH/5HgfnfovI/96AkljEq/yuS/104+V9bOP/bx8v/LH2TNP9j8E3m/G9WRF7zf02B/M8tKJFExqj8L0b+t2Hjf86+KWr+T5r/9Wz8z1c2Z8wYVhCzFP638sL/zHzjjf9Jzf81BfI/rzDGkf+5+qbyP/kXPhb8z1c2z3P+T1+Enxc+WfM/zhel2fG/VQT8T3+IMZ/5P0sRkvzvmZP/MV2UIPEPPlT+NxZB0h98KH3+T5T/zYsg6Rc+ifA/2WGsxPgfe1Aqiv/tBPifmQhR/vdpxv8ELmMWw//2kvzP7vZ9gMuY+c3/5cP/BGBM5X9R8j+py5h58j9tEWID32uzr29uhWAMmRVuqfzP2zAWCWUMM/7XVP7nbeA7T/5nO4xVBP+jOv9X+V8c/K9n43+cvpHlf6858D/WixKCH3yo/E+f/wnBmFT4n6+LElnwP4qf/23q/F8R/K9n438S2TzEBx9C8T8D95lljI21iHT5n9f/vhMp/2MPSrHxP+nLmEnxP4qf/23Y+J8MjAnN//4Ax3H9/N6BeDoAAAAASUVORK5CYII=);
  background-repeat: repeat;
  display: flex;
  justify-content: center;
}
.fillBlanksContainer {
  background: #fff9ec;
  height: 100%;
  width: calc(100% / 16 * 9 / 3 * 4);
  overflow: hidden;
  position: relative;
  border-radius: 16px;
  z-index: 100;
  font-family: Helvetica, 'PingFang SC', sans-serif, Arial, Verdana, 'Microsoft YaHei';
  :deep(.ne-fillblanks__countdown) {
    top: 15px;
    .item {
      height: 26px;
    }
  }
  :deep(.ne-fillblanks__content) {
    overflow-y: auto;
  }
  :deep(.carousel__arrow i) {
    width: 11px;
    height: 11px;
  }
  :deep(.ne-fillblanks__board) {
    bottom: 0 !important;
  }
  :deep(.ne-fillblanks__board--title) {
    padding: 0 55px;
    text-align: center;
  }
  :deep(.ne-carousel) {
    position: absolute;
    bottom: 2px;
    left: 2px;
    width: calc(100% - 4px);
    background: #ffffff;
    border-radius: 0 0 15px 15px;
  }
  :deep(.carousel__arrow) {
    border-radius: 5px;
    width: 24px;
    height: 46px;
    color: #333;
  }
  :deep(.carousel__item) {
    padding: 0px 55px;
    background: #fff;
    ul {
      padding: 0;
      margin: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      border-radius: 8px;
    }
    li {
      text-align: center;
      height: 40px;
      float: left;
      line-height: 40px;
      padding: 0;
      list-style-type: none;
      background: #fff9ec;
      margin: 0 1px 1px 0;
      width: calc(50% - 1px);
      position: relative;
      color: #ff850a;
      font-size: 16px;
      font-weight: 500;
      i {
        display: block;
        width: 24px;
        height: 24px;
        position: absolute;
        right: 0;
        top: 8px;
      }
      &.wrong {
        background: #fff5f4;
        color: #ff503f;
        i {
          background: url(./imgs/wrong-icon.png) no-repeat center center;
          background-size: 100%;
        }
      }
      &.correct {
        background: #f0fcf8;
        color: #02ca8a;
        i {
          background: url(./imgs/correct-icon.png) no-repeat center center;
          background-size: 100%;
        }
      }
      &.nojoin {
        background: #f4f6fa;
        color: #a2aab8;
      }
    }
  }
  :deep(.talqs_main) {
    padding: 52px 32px 32px 28px;
    text-align: left;
    // font-size: 16px;
    // line-height: 1.5;
    // color: #333;
    // 确保所有文本元素都有合适的字体大小
    // div,
    // span,
    // text,
    // p {
    //   // font-size: 16px;
    // }
    .talqs_content {
      padding: 0 10px;
    }
    .clearfix {
      zoom: 1;
    }
    .clearfix::before {
      display: table;
      content: '';
    }
  }
  :deep(.talqs) {
    p {
      padding: 1rem 0;
      font-size: 1.5rem;
      line-height: 2rem;
    }
  }
  :deep(svg) {
    display: inline-block;
  }
  :deep(.mq-math-mode) {
    border: 0 none;
  }
  :deep(span.talqs_blank_span),
  :deep(span.talqs_math_input) {
    outline: 0 none;
    color: #ffaa0a;
    background: transparent;
    border-bottom: 1px solid #333;
    &.mq-focused {
      outline: 0 none;
      box-shadow: none;
    }
  }
  :deep(.talqs_blank_span) {
    vertical-align: initial;
  }
}
</style>
<style lang="scss">
body {
  .keyboardLayerOfPad,
  .blankOptionKeypad {
    border-radius: 16px !important;
    z-index: 1001 !important;
    width: 50%;
    height: 40vh !important;
    .word-btn {
      line-height: 1 !important;
    }
  }
  .keyBtn {
    &:active {
      background-color: rgba(0, 0, 0, 0.1) !important;
    }
  }
}
</style>
