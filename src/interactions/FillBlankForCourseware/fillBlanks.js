// 业务逻辑
import { getQuestionStatus, submitUserAnswer, interactReport } from '@/api/h5courseware'
import { emitter } from '@/hooks/useEventBus'
import logger from '@/utils/logger'

export default class FillBlanks {
  constructor(opts = {}) {
    this.options = opts
  }

  /**
   * 初始化倒计时
   */
  initCountdownTime() {
    const localTime = +new Date() // 本机时间
    const timeOffset = this.options.roomMessage.roomInfo.commonOption.timeOffset // 本机时间和服务器时间的差值
    const times =
      this.options.ircMsg.countDownTime * 1000 -
      (localTime + timeOffset - this.options.ircMsg.currentTime)
    return times
  }

  /**
   * 提交答案
   * @param {*} userAnswerData
   * @returns
   */
  async submitAnswer(userAnswerData) {
    try {
      const params = {
        planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次ID
        interactId: this.options.ircMsg.interactId, // 互动ID
        questionId: this.options.ircMsg.quesId,
        classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId,
        isRight: userAnswerData.isRight,
        userAnswer: [...userAnswerData.answerData],
        userAnswerResult: {
          userAnswer: [...userAnswerData.answerData],
          userAnswerResult: [...userAnswerData.judgeData]
        },
        userName: this.options.roomMessage.roomInfo.stuInfo.nickName
      }
      const response = await submitUserAnswer(params).catch(err => {
        logger.send({
          tag: 'http',
          content: {
            msg: `接口报错:提交答案:`,
            err,
            params
          },
          level: 'error'
        })
      })
      const { code, data } = response
      const continuousCorrectData = { ...data, isRight: userAnswerData.isRight, isFillBank: true }
      // 触发连对激励
      emitter.emit('continuousCorrect', continuousCorrectData)
      let res = {
        params,
        response,
        code,
        isSubmit: code === 0 ? true : false,
        rightCoin: data.rightCoin || 0
      }
      return res
    } catch (error) {
      console.error('submitAnswer', error)
      return {
        code: -1,
        errMsg: error
      }
    }
  }

  /**
   * 上报开启互动
   */
  async interactOpen() {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次ID
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId * 1, // classID
      interactId: this.options.ircMsg.interactId // 互动ID
    }
    await interactReport(params)
  }

  /**
   * 异常恢复
   * 同时获取题目数据
   * @returns
   */
  async getStatus() {
    try {
      const params = {
        planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次ID
        interactId: this.options.ircMsg.interactId, // 互动ID
        classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId, // 班级ID
        courseId: this.options.roomMessage.roomInfo.stuLiveInfo.courseId // 课程ID
      }
      const res = await getQuestionStatus(params)
      return res
    } catch (error) {
      console.error('获取题目数据', error)
      return {
        code: -1,
        errMsg: error
      }
    }
  }
}
