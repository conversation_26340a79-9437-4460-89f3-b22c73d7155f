<template>
  <div class="ne-fillblanks" ref="fillBlanks" :class="customClass" id="fillBlanksContainer">
    <!--倒计时-->
    <div class="ne-fillblanks__countdown">
      <NeClockCountdown :options="clockCountdownOptions" />
      <NeCountdown :time="countDownTime" format="mm:ss" @complete="$emit('countDownComplete')" />
    </div>

    <!--题目渲染区域-->
    <div class="ne-fillblanks__content" id="fillBlanksContent" talqs-app></div>

    <!--提交按钮-->
    <button
      class="ne-fillblanks__submit"
      :class="{ 'is-disabled': disableSubmit }"
      @click.stop="submit"
      v-if="!isSubmit"
    >
      <span>{{ submitText }}</span>
      <svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1">
        <defs>
          <linearGradient x1="50%" y1="35.4886634%" x2="50%" y2="168.492388%" id="linearGradient-1">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFEA5C" offset="100%"></stop>
          </linearGradient>
          <linearGradient x1="0%" y1="79.5857988%" x2="100%" y2="20.4142012%" id="linearGradient-2">
            <stop stop-color="#FFD518" offset="0%"></stop>
            <stop stop-color="#FFAA0A" offset="100%"></stop>
          </linearGradient>
        </defs>
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
          <g transform="translate(-459.000000, -611.000000)">
            <g transform="translate(375.000000, 603.000000)">
              <g transform="translate(84.000000, 8.000000)">
                <circle fill="url(#linearGradient-1)" cx="14" cy="14" r="12"></circle>
                <path
                  d="M13.8095546,18.7004623 C13.4554695,19.0713185 12.8970984,19.0978082 12.5138296,18.7799315 L12.4286639,18.7004623 L8.28599183,14.361574 C7.90466939,13.9621904 7.90466939,13.3146616 8.28599183,12.9152779 L8.97643718,12.1921299 C9.35775962,11.7927463 9.97600545,11.7927463 10.3573279,12.1921299 L13.1191093,15.0847221 L18.6426721,9.29953773 C19.0239946,8.90015409 19.6422404,8.90015409 20.0235628,9.29953773 L20.7140082,10.0226858 C21.0953306,10.4220694 21.0953306,11.0695982 20.7140082,11.4689819 L13.8095546,18.7004623 Z"
                  fill="url(#linearGradient-2)"
                ></path>
              </g>
            </g>
          </g>
        </g>
      </svg>
    </button>

    <div class="ne-fillblanks-result-mask" v-if="showResultToast"></div>
    <!-- 作答详情、正确答案 面板 -->
    <div
      class="ne-fillblanks__board"
      id="ne-fillblanks__board"
      v-if="showResultToast && (judge.data || userAnswer['data'])"
    >
      <div class="ne-fillblanks__board--title">
        <span>My answer</span>
        <span>Correct answer</span>
      </div>
      <Carousel
        height="82px"
        :autoplay="false"
        :loop="false"
        arrow="always"
        indicatorPosition="none"
      >
        <CarouselItem v-for="item in answer['length']" :key="item">
          <ul v-if="judge['data'][item - 1]">
            <template v-for="i in judge['data'][item - 1].length" :key="'judge_' + i">
              <li
                :class="
                  judge['data'][item - 1][i - 1] === 1
                    ? 'correct'
                    : judge['data'][item - 1][i - 1] === 0
                      ? 'wrong'
                      : 'nojoin'
                "
              >
                <i /><span :id="'judge_' + item + i">{{
                  renderMathJax(userAnswer['data'][item - 1][i - 1])
                }}</span>
              </li>
              <li v-html="answer['data'][item - 1][i - 1][0]"></li>
            </template>
          </ul>
        </CarouselItem>
      </Carousel>
    </div>
  </div>
</template>

<script>
import { chunk, flatten } from '@/utils/util'
import NeCountdown from '@/components/NeCountdown/index.vue'
import NeClockCountdown from './clock-countdown/src/main.vue'
import { Carousel, CarouselItem } from './carousel-component/index'
import logger from '@/utils/logger'

export default {
  name: 'FillBlanksComponent',
  components: {
    NeCountdown,
    NeClockCountdown,
    Carousel,
    CarouselItem
  },
  props: {
    userConfig: {
      type: Object,
      default: () => {}
    },
    judgeConfig: {
      type: Object,
      default: () => {}
    },
    renderConfig: {
      type: Object,
      default: () => {}
    },
    quesContent: {
      type: Object,
      default: () => {}
    },
    countDownTime: {
      type: Number,
      default: 0
    },
    submitText: {
      type: String,
      default: 'Submit'
    },
    disableSubmit: {
      type: Boolean,
      default: true
    },
    showResultToast: {
      type: Boolean,
      default: false
    },
    customClass: {
      type: String,
      default: ''
    },
    coin: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      defaultRenderConfig: {
        hideSource: true, // 隐藏试题来源
        hideDifficulty: true // 隐藏试题难度
      }, // 试题渲染配置
      userAnswerData: {}, // 用户答题数据
      isSubmit: false, // 是否已提交
      answer: {},
      judge: {},
      userAnswer: [],
      needShowRight: false,
      clockCountdownOptions: {
        ratio: 3,
        size: 26,
        borderWidth: 3,
        borderColor: '#FFAA0A',
        background: ['#ffffff', '#fff6b2'],
        pointerColor: '#ffc613',
        pointerWidth: 2
      }
    }
  },
  computed: {
    qsRenderConfig() {
      return Object.assign({}, this.defaultRenderConfig, this.renderConfig)
    },
    isRightToast() {
      return this.needShowRight === 1 || this.needShowRight === 4
    }
  },
  mounted() {
    this.sendLogger('填空组件初始化', 'init')
    this.$nextTick(() => {
      if (!this.quesContent || !this.userConfig.stuId) {
        this.sendLogger('缺少必要参数，跳过初始化', 'init', 'warn')
        return
      }
      // 确保talqs已加载后再设置用户配置
      if (window.TalqsInteraction) {
        this.sendLogger('设置用户配置', 'config')
        window.TalqsInteraction.setUserConfig(this.userConfig)
      } else {
        this.sendLogger('TalqsInteraction未加载', 'config', 'error')
      }
      this.renderQS()
      this.judgeQS()
      this.questionAnswerChange()
      this.answer = this.chunkArray(this.quesContent.answer, 2)
      this.sendLogger(`题目初始化完成 - 答案数量:${this.quesContent.answer?.length || 0}`, 'init')
    })
  },
  beforeUnmount() {
    this.sendLogger('填空组件销毁', 'destroy')
    // 清理工作
  },
  updated() {
    this.$nextTick(() => {
      if (window.MathJax && window.MathJax.Hub) {
        console.log('MathJax re-rendering...')
        window.MathJax.Hub.Queue([
          'Typeset',
          window.MathJax.Hub,
          document.getElementById('ne-fillblanks__board')
        ])
      }
    })
  },
  methods: {
    /**
     * 拆分数组
     */
    chunkArray(array, size) {
      if (!array || array.length === 0) return
      const newArrayLength = Math.ceil(array.length / size)
      const newArray = chunk(array, size)
      return {
        length: newArrayLength,
        data: newArray
      }
    },
    /**
     * 提交答案
     * isRight: 1 - 正确，2 - 错误, 3 - 未答, 4 - 半对
     * judgeData: SDK判题结果
     * answerData: 用户答题内容
     */
    submit() {
      if (this.isSubmit) {
        this.sendLogger('重复提交，忽略', 'submit', 'warn')
        return
      }

      this.sendLogger('开始提交填空答案', 'submit')
      let isRight = 3
      let judgeData = []

      // 安全检查：如果没有答题数据，直接提交未答状态
      if (
        !this.userAnswerData ||
        !this.userAnswerData.answerData ||
        !this.userAnswerData.judgeData
      ) {
        this.sendLogger('无答题数据，提交未答状态', 'submit', 'warn')
        const submitData = {
          judgeData: [],
          answerData: [],
          isRight: 3
        }
        this.$emit('submit', submitData)
        this.isSubmit = true
        this.needShowRight = 3
        return
      }

      const answerData = flatten(this.userAnswerData.answerData)
      this.userAnswerData.judgeData.forEach(item => {
        judgeData.push(item.judge)
      })
      judgeData = flatten(judgeData) // 把多维数组拍扁

      this.sendLogger(
        `答题数据处理 - 答案:${JSON.stringify(answerData)} 判题结果:${JSON.stringify(judgeData)}`,
        'submit'
      )

      // 判断对错
      if (judgeData.includes(1) && (judgeData.includes(0) || judgeData.includes(-2))) {
        // 半对
        isRight = 4
        this.sendLogger('答题结果：半对', 'result')
      } else if (judgeData.includes(0)) {
        // 答错
        isRight = 2
        this.sendLogger('答题结果：错误', 'result')
      } else if (judgeData.every(item => item === 1)) {
        // 全对
        isRight = 1
        this.sendLogger('答题结果：正确', 'result')
      } else {
        this.sendLogger('答题结果：未作答', 'result')
      }

      const submitData = {
        judgeData,
        answerData,
        isRight: isRight === 4 ? 2 : isRight
      }

      this.sendLogger(
        `提交填空答案 - 答案:${answerData.join(',')} 结果:${isRight === 1 ? '正确' : isRight === 2 ? '错误' : isRight === 4 ? '半对' : '未作答'}`,
        'submit'
      )

      // 抛出判题数据
      this.$emit('submit', submitData)
      // 这里为了结果面板数组准备
      this.judge = this.chunkArray(judgeData, 2) // 判题结果
      this.userAnswer = this.chunkArray(answerData, 2) // 用户答题内容
      console.log(1111111, this.judge, this.userAnswer)
      this.isSubmit = true
      this.needShowRight = isRight
    },
    /**
     * 渲染试题
     */
    renderQS() {
      this.sendLogger('开始渲染试题', 'render')
      const renderDom = document.getElementById('fillBlanksContent')
      if (!renderDom || !window.TalqsTemplate) {
        this.sendLogger('渲染失败：缺少DOM或TalqsTemplate', 'render', 'error')
        return
      }
      renderDom.innerHTML = window.TalqsTemplate.render(this.quesContent, this.qsRenderConfig)
      this.sendLogger('试题渲染完成', 'render')
      this.$nextTick(() => {
        if (window.TalqsTemplate) {
          window.TalqsTemplate.autoLayout()
          this.sendLogger('试题自动布局完成', 'render')
        }
      })
    },
    /**
     * 判题
     */
    judgeQS() {
      this.sendLogger('初始化判题系统', 'judge')
      if (window.TalqsInteraction) {
        window.TalqsInteraction.initTqiQs() // 初始化判题
        this.sendLogger('判题系统初始化完成', 'judge')
      } else {
        this.sendLogger('TalqsInteraction未加载，判题初始化失败', 'judge', 'error')
      }
    },
    /**
     * 监听作答
     */
    questionAnswerChange() {
      this.sendLogger('开始监听答题变化', 'listen')
      if (window.TalqsInteraction && window.judge) {
        window.TalqsInteraction.onChange = res => {
          this.sendLogger(
            `答题内容变化 - 题目ID:${res.queId} 答案:${JSON.stringify(res.data)}`,
            'change'
          )
          const judgeRes = window.judge.byId([res.queId], this.userConfig, this.judgeConfig)
          this.userAnswerData = {
            answerData: res.data,
            judgeData: judgeRes
          }
          this.sendLogger(`实时判题结果: ${JSON.stringify(judgeRes)}`, 'judge')
          this.$emit('changeAnswer', this.userAnswerData)
        }
      } else {
        this.sendLogger('缺少判题依赖，无法监听答题变化', 'listen', 'error')
      }
    },
    renderMathJax(value) {
      if (!value) return '-'
      console.log('renderMathJax input:', value)

      // 根据 talqs 中的 MathJax 配置：inlineMath: [["$", "$"], ["\\", "\\"]]
      // 直接返回原值，MathJax 会自动识别 $...$ 格式
      // 对于 $$+$$，这已经是正确的内联数学格式
      console.log('renderMathJax output:', value)
      return value
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionType = 'FillBlanks'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: 'fillblanks-component',
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][fillblanks-component] ${msg}`, { stage, level })
    }
  }
}
</script>

<style lang="scss" scoped>
.ne-fillblanks-result-mask {
  background: rgba(0, 0, 0, 0.8);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 12;
}

.ne-fillblanks {
  position: relative;
  width: 100%;
  height: 100%;

  &__countdown {
    position: absolute;
    box-sizing: border-box;

    z-index: 11;
    display: flex;
    border-radius: 100em;
    background: rgba(0, 0, 0, 0.5);
    left: 50%;
    transform: translate(-50%, 0);
    -webkit-box-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    align-items: center;
    padding: 4px 12px 4px 4px;
    overflow: hidden;

    .item {
      height: 26px;
    }
  }

  &__content {
    position: relative;

    // 确保所有文本元素都有合适的字体大小
    div,
    span,
    text {
      font-size: 16px;
    }

    svg {
      display: inline-block;
    }

    .mq-math-mode {
      border: 0 none;
    }

    span.talqs_blank_span,
    span.talqs_math_input {
      outline: 0 none;
      color: #ffaa0a;
      background: transparent;
      border-bottom: 1px solid #333;

      &.mq-focused {
        outline: 0 none;
        box-shadow: none;
      }
    }

    .talqs_blank_span {
      vertical-align: initial;
    }
  }

  &__submit {
    border: 0;
    outline: 0;
    position: absolute;
    cursor: pointer;
    box-sizing: border-box;
    height: 44px;
    line-height: 44px;
    padding: 0 8px 0 22px;
    color: #fff;
    border-radius: 100em;
    z-index: 12;
    bottom: 30px;
    transform: translate(-50%, 0);
    background: linear-gradient(45deg, #ffd518 0, #ffaa0a 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    left: 50%;
    font-weight: 500;
    font-size: 18px;

    &.is-disabled {
      background: #dee2e7;
      color: #fff;
      pointer-events: none;
      cursor: not-allowed;
      svg {
        path {
          fill: #dee2e7;
        }
        circle {
          fill: #fff;
        }
      }
    }
    svg {
      margin-left: 10px;
    }
  }

  &__board {
    box-sizing: border-box;
    position: absolute;
    z-index: 13;
    left: 50%;
    bottom: 20px;
    transform: translate(-50%, 0);
    border-radius: 15px;
    width: 500px;
    height: 115px;
    padding: 2px;
    color: #a2aab8;
    font-size: 16px;
    font-weight: 400;

    &--title {
      position: relative;
      width: 100%;
      height: 34px;
      background: #fff;
      border-radius: 15px 15px 0 0;
      display: flex;
      justify-content: space-between;
      color: #a2aab8;
      font-size: 16px;
      list-style: none;
      font-weight: 500;
      line-height: 30px;
      span {
        width: 50%;
      }
    }

    .answer-display {
      max-height: 200px;
      overflow-y: auto;
      padding: 16px 0;

      // 自定义滚动条样式
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }

      .answer-page {
        padding: 0 32px;
        background: #fff;

        .answer-comparison {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .answer-pair {
          display: flex;
          align-items: stretch;
          gap: 12px;
          width: 100%;
          border-radius: 12px;
          overflow: hidden;
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
          }

          .user-answer {
            flex: 1;
            text-align: center;
            height: 56px;
            line-height: 56px;
            padding: 0 16px;
            font-size: 16px;
            font-weight: 500;
            position: relative;
            transition: all 0.3s ease;

            .status-icon {
              display: block;
              width: 20px;
              height: 20px;
              position: absolute;
              right: 12px;
              top: 18px;
            }

            .answer-text {
              display: block;
              padding-right: 32px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &.wrong {
              background: linear-gradient(135deg, #fff5f4 0%, #ffe6e6 100%);
              color: #dc3545;
              border-right: 3px solid #dc3545;

              .status-icon {
                background: url('./imgs/wrong-icon.png') no-repeat center center;
                background-size: 100%;
              }
            }

            &.correct {
              background: linear-gradient(135deg, #f0fcf8 0%, #e6f7f1 100%);
              color: #28a745;
              border-right: 3px solid #28a745;

              .status-icon {
                background: url('./imgs/correct-icon.png') no-repeat center center;
                background-size: 100%;
              }
            }

            &.nojoin {
              background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
              color: #6c757d;
              border-right: 3px solid #6c757d;
            }
          }

          .correct-answer {
            flex: 1;
            padding: 0 16px;
            height: 56px;
            line-height: 56px;
            background: linear-gradient(135deg, #fff9e6 0%, #fff3cd 100%);
            color: #856404;
            font-size: 16px;
            font-weight: 600;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            border-left: 3px solid #ffc107;
          }

          // 移动端适配
          @media (max-width: 768px) {
            flex-direction: column;
            gap: 0;

            .user-answer,
            .correct-answer {
              height: 48px;
              line-height: 48px;
              font-size: 14px;
            }

            .user-answer {
              border-right: none;
              border-bottom: 3px solid;
              border-radius: 12px 12px 0 0;

              &.wrong {
                border-bottom-color: #dc3545;
              }

              &.correct {
                border-bottom-color: #28a745;
              }

              &.nojoin {
                border-bottom-color: #6c757d;
              }

              .status-icon {
                top: 14px;
              }
            }

            .correct-answer {
              border-left: none;
              border-top: 3px solid #ffc107;
              border-radius: 0 0 12px 12px;
            }
          }
        }
      }
    }
  }
}
</style>
