import Carousel from './src/Carousel.vue'
import CarouselItem from './src/CarouselItem.vue'

// 单独导出组件
export { Carousel, CarouselItem }

// 插件安装函数
const install = (app) => {
  // 注册为你习惯使用的组件名
  app.component('ne-carousel', Carousel)
  app.component('ne-carousel-item', CarouselItem)
  // 同时也注册通用名称
  app.component('Carousel', Carousel)
  app.component('CarouselItem', CarouselItem)
}

// 默认导出插件对象
export default {
  install,
  Carousel,
  CarouselItem
}
