<template>
  <div
    :class="carouselClasses"
    @mouseenter.stop="handleMouseEnter"
    @mouseleave.stop="handleMouseLeave"
  >
    <div class="carousel__container" :style="{ height: height }">
      <transition v-if="arrowDisplay" name="carousel-arrow-left">
        <button
          type="button"
          v-show="(arrow === 'always' || hover) && (loop || activeIndex > 0)"
          @mouseenter="handleButtonEnter('left')"
          @mouseleave="handleButtonLeave"
          @click.stop="throttledArrowClick(activeIndex - 1)"
          class="carousel__arrow carousel__arrow--left"
        >
          <CarouselIcon name="arrow-left" />
        </button>
      </transition>
      <transition v-if="arrowDisplay" name="carousel-arrow-right">
        <button
          type="button"
          v-show="(arrow === 'always' || hover) && (loop || activeIndex < items.length - 1)"
          @mouseenter="handleButtonEnter('right')"
          @mouseleave="handleButtonLeave"
          @click.stop="throttledArrowClick(activeIndex + 1)"
          class="carousel__arrow carousel__arrow--right"
        >
          <CarouselIcon name="arrow-right" />
        </button>
      </transition>
      <slot></slot>
    </div>
    <ul v-if="indicatorPosition !== 'none'" :class="indicatorsClasses">
      <li
        v-for="(item, index) in items"
        :key="index"
        :class="[
          'carousel__indicator',
          'carousel__indicator--' + direction,
          { 'is-active': index === activeIndex }
        ]"
        @mouseenter="throttledIndicatorHover(index)"
        @click.stop="handleIndicatorClick(index)"
      >
        <button class="carousel__button">
          <span v-if="hasLabel">{{ item.label }}</span>
        </button>
      </li>
    </ul>
  </div>
</template>

<script>
import { addResizeListener, removeResizeListener } from './utils/resize-event'
import CarouselIcon from './Icon.vue'

// 简单的节流函数实现
function throttle(delay, callback) {
  let timeoutId = null
  let lastExecTime = 0
  return function (...args) {
    const currentTime = Date.now()
    if (currentTime - lastExecTime > delay) {
      callback.apply(this, args)
      lastExecTime = currentTime
    } else {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        callback.apply(this, args)
        lastExecTime = Date.now()
      }, delay - (currentTime - lastExecTime))
    }
  }
}

export default {
  name: 'Carousel',
  components: { CarouselIcon },

  props: {
    initialIndex: {
      type: Number,
      default: 0
    },
    height: String,
    trigger: {
      type: String,
      default: 'hover'
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    interval: {
      type: Number,
      default: 3000
    },
    indicatorPosition: String,
    indicator: {
      type: Boolean,
      default: true
    },
    arrow: {
      type: String,
      default: 'hover'
    },
    type: String,
    loop: {
      type: Boolean,
      default: true
    },
    direction: {
      type: String,
      default: 'horizontal',
      validator(val) {
        return ['horizontal', 'vertical'].indexOf(val) !== -1
      }
    }
  },

  data() {
    return {
      items: [],
      carouselItems: [],
      activeIndex: -1,
      containerWidth: 0,
      timer: null,
      hover: false
    }
  },

  provide() {
    return {
      carousel: this
    }
  },

  computed: {
    arrowDisplay() {
      return this.arrow !== 'never' && this.direction !== 'vertical'
    },

    hasLabel() {
      return this.items.some(item => item.label.toString().length > 0)
    },

    carouselClasses() {
      const classes = ['carousel', 'carousel--' + this.direction]
      if (this.type === 'card') {
        classes.push('carousel--card')
      }
      return classes
    },

    indicatorsClasses() {
      const classes = ['carousel__indicators', 'carousel__indicators--' + this.direction]
      if (this.hasLabel) {
        classes.push('carousel__indicators--labels')
      }
      if (this.indicatorPosition === 'outside' || this.type === 'card') {
        classes.push('carousel__indicators--outside')
      }
      return classes
    }
  },

  watch: {
    items(val) {
      if (val.length > 0) this.setActiveItem(this.initialIndex)
    },

    activeIndex(val, oldVal) {
      this.resetItemPosition(oldVal)
      if (oldVal > -1) {
        this.$emit('change', val, oldVal)
      }
    },

    autoplay(val) {
      val ? this.startTimer() : this.pauseTimer()
    },

    loop() {
      this.setActiveItem(this.activeIndex)
    }
  },

  created() {
    this.throttledArrowClick = throttle(300, (index) => {
      this.setActiveItem(index)
    })
    this.throttledIndicatorHover = throttle(300, (index) => {
      this.handleIndicatorHover(index)
    })
  },

  mounted() {
    this.updateItems()
    this.$nextTick(() => {
      addResizeListener(this.$el, this.resetItemPosition)
      if (this.initialIndex < this.items.length && this.initialIndex >= 0) {
        this.activeIndex = this.initialIndex
      }
      this.startTimer()
    })
  },

  beforeUnmount() {
    if (this.$el) removeResizeListener(this.$el, this.resetItemPosition)
    this.pauseTimer()
  },

  methods: {
    handleMouseEnter() {
      this.hover = true
      this.pauseTimer()
    },

    handleMouseLeave() {
      this.hover = false
      this.startTimer()
    },

    itemInStage(item, index) {
      const length = this.items.length
      if (index === length - 1 && item.inStage && this.items[0].active ||
        (item.inStage && this.items[index + 1] && this.items[index + 1].active)) {
        return 'left'
      } else if (index === 0 && item.inStage && this.items[length - 1].active ||
        (item.inStage && this.items[index - 1] && this.items[index - 1].active)) {
        return 'right'
      }
      return false
    },

    handleButtonEnter(arrow) {
      if (this.direction === 'vertical') return
      this.items.forEach((item, index) => {
        if (arrow === this.itemInStage(item, index)) {
          item.hover = true
        }
      })
    },

    handleButtonLeave() {
      if (this.direction === 'vertical') return
      this.items.forEach(item => {
        item.hover = false
      })
    },

    updateItems() {
      // Vue3兼容：使用provide/inject来获取子组件
      this.items = this.carouselItems || []
    },

    addItem(item) {
      this.carouselItems.push(item)
    },

    removeItem(item) {
      const index = this.carouselItems.indexOf(item)
      if (index > -1) {
        this.carouselItems.splice(index, 1)
      }
    },

    resetItemPosition(oldIndex) {
      this.items.forEach((item, index) => {
        item.translateItem(index, this.activeIndex, oldIndex)
      })
    },

    playSlides() {
      if (this.activeIndex < this.items.length - 1) {
        this.activeIndex++
      } else if (this.loop) {
        this.activeIndex = 0
      }
    },

    pauseTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    startTimer() {
      if (this.interval <= 0 || !this.autoplay || this.timer) return
      this.timer = setInterval(this.playSlides, this.interval)
    },

    setActiveItem(index) {
      if (typeof index === 'string') {
        const filteredItems = this.items.filter(item => item.name === index)
        if (filteredItems.length > 0) {
          index = this.items.indexOf(filteredItems[0])
        }
      }
      index = Number(index)
      if (isNaN(index) || index !== Math.floor(index)) {
        console.warn('[Carousel]index must be an integer.')
        return
      }
      let length = this.items.length
      const oldIndex = this.activeIndex
      if (index < 0) {
        this.activeIndex = this.loop ? length - 1 : 0
      } else if (index >= length) {
        this.activeIndex = this.loop ? 0 : length - 1
      } else {
        this.activeIndex = index
      }
      if (oldIndex === this.activeIndex) {
        this.resetItemPosition(oldIndex)
      }
    },

    prev() {
      this.setActiveItem(this.activeIndex - 1)
    },

    next() {
      this.setActiveItem(this.activeIndex + 1)
    },

    handleIndicatorClick(index) {
      this.activeIndex = index
    },

    handleIndicatorHover(index) {
      if (this.trigger === 'hover' && index !== this.activeIndex) {
        this.activeIndex = index
      }
    }
  }
}
</script>

<style scoped>
/* Carousel 轮播组件样式 - 完整迁移版本 */
.carousel {
  position: relative;
  box-sizing: border-box;
}

/* 确保所有子元素也使用border-box */
.carousel *,
.carousel *::before,
.carousel *::after {
  box-sizing: border-box;
}

.carousel--horizontal {
  overflow-x: hidden;
}

.carousel--vertical {
  overflow-y: hidden;
}

.carousel--card {
  /* Card模式的特殊样式 */
}

.carousel__container {
  position: relative;
  height: 100%;
}

.carousel__arrow {
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  height: 24px; /* $--carousel-arrow-size */
  width: 24px; /* $--carousel-arrow-size */
  cursor: pointer;
  transition: .3s;
  border-radius: 50%;
  background-color: #f4f6fa; /* $--carousel-arrow-background */
  color: #ffffff; /* $--color-white */
  position: absolute;
  top: 50%;
  z-index: 10;
  transform: translateY(-50%);
  text-align: center;
}

.carousel__arrow--left {
  left: 10px;
}

.carousel__arrow--right {
  right: 10px;
}

.carousel__arrow:hover {
  background-color: rgba(31, 45, 61, 0.23); /* $--carousel-arrow-hover-background */
}

.carousel__arrow i {
  width: 24px; /* $--carousel-arrow-icon-size */
  height: 24px; /* $--carousel-arrow-icon-size */
  cursor: pointer;
}

.carousel__indicators {
  position: absolute;
  list-style: none;
  margin: 0;
  padding: 0;
  z-index: 2; /* $--index-normal + 1 */
}

.carousel__indicators--horizontal {
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.carousel__indicators--vertical {
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.carousel__indicators--outside {
  bottom: 26px; /* $--carousel-indicator-height + $--carousel-indicator-padding-vertical * 2 = 2 + 12*2 */
  text-align: center;
  position: static;
  transform: none;
}

.carousel__indicators--outside .carousel__indicator:hover button {
  opacity: 0.64;
}

.carousel__indicators--outside button {
  background-color: #c0c4cc; /* $--carousel-indicator-out-color */
  opacity: 0.24;
}

.carousel__indicators--labels {
  left: 0;
  right: 0;
  transform: none;
  text-align: center;
}

.carousel__indicators--labels .carousel__button {
  height: auto;
  width: auto;
  padding: 2px 18px;
  font-size: 12px;
}

.carousel__indicators--labels .carousel__indicator {
  padding: 6px 4px;
}

.carousel__indicator {
  background-color: transparent;
  cursor: pointer;
}

.carousel__indicator:hover button {
  opacity: 0.72;
}

.carousel__indicator--horizontal {
  display: inline-block;
  padding: 12px 4px; /* $--carousel-indicator-padding-vertical $--carousel-indicator-padding-horizontal */
}

.carousel__indicator--vertical {
  padding: 4px 12px; /* $--carousel-indicator-padding-horizontal $--carousel-indicator-padding-vertical */
}

.carousel__indicator--vertical .carousel__button {
  width: 2px; /* $--carousel-indicator-height */
  height: 15px; /* $--carousel-indicator-width / 2 = 30/2 */
}

.carousel__indicator.is-active button {
  opacity: 1;
}

.carousel__button {
  display: block;
  opacity: 0.48;
  width: 30px; /* $--carousel-indicator-width */
  height: 2px; /* $--carousel-indicator-height */
  background-color: #ffffff; /* $--color-white */
  border: none;
  outline: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  transition: .3s;
}

/* Vue3 过渡动画类名 */
.carousel-arrow-left-enter-active,
.carousel-arrow-left-leave-active,
.carousel-arrow-right-enter-active,
.carousel-arrow-right-leave-active {
  transition: all .3s;
}

.carousel-arrow-left-enter-from,
.carousel-arrow-left-leave-to {
  transform: translateY(-50%) translateX(-10px);
  opacity: 0;
}

.carousel-arrow-right-enter-from,
.carousel-arrow-right-leave-to {
  transform: translateY(-50%) translateX(10px);
  opacity: 0;
}

/* 兼容Vue2的过渡类名 */
.carousel-arrow-left-enter,
.carousel-arrow-left-leave-active {
  transform: translateY(-50%) translateX(-10px);
  opacity: 0;
}

.carousel-arrow-right-enter,
.carousel-arrow-right-leave-active {
  transform: translateY(-50%) translateX(10px);
  opacity: 0;
}
</style>
