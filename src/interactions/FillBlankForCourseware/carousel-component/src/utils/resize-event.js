const isServer = typeof window === 'undefined'

// 使用原生方法替代ResizeObserver
const resizeHandler = () => {
  // 触发所有注册的resize监听器
  if (typeof window !== 'undefined') {
    const elements = document.querySelectorAll('[data-resize-listener]')
    elements.forEach(element => {
      const listeners = element.__resizeListeners__ || []
      listeners.forEach(fn => fn())
    })
  }
}

// 全局resize监听器，只注册一次
let globalResizeListenerAdded = false

export const addResizeListener = (element, fn) => {
  if (isServer) return

  if (!element.__resizeListeners__) {
    element.__resizeListeners__ = []
    element.setAttribute('data-resize-listener', 'true')
  }
  element.__resizeListeners__.push(fn)

  // 只添加一次全局监听器
  if (!globalResizeListenerAdded) {
    window.addEventListener('resize', resizeHandler)
    globalResizeListenerAdded = true
  }
}

export const removeResizeListener = (element, fn) => {
  if (!element || !element.__resizeListeners__) return

  const index = element.__resizeListeners__.indexOf(fn)
  if (index > -1) {
    element.__resizeListeners__.splice(index, 1)
  }

  if (element.__resizeListeners__.length === 0) {
    element.removeAttribute('data-resize-listener')
  }
}
