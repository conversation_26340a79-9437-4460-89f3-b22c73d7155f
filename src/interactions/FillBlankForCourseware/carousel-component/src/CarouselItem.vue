<template>
  <div
    v-show="ready"
    class="carousel__item"
    :class="{
      'is-active': active,
      'carousel__item--card': carousel.type === 'card',
      'is-in-stage': inStage,
      'is-hover': hover,
      'is-animating': animating
    }"
    @click="handleItemClick"
    :style="itemStyle"
  >
    <div v-if="carousel.type === 'card'" v-show="!active" class="carousel__mask"></div>
    <slot></slot>
  </div>
</template>

<script>
import { autoprefixer } from './utils/util'

const CARD_SCALE = 0.83

export default {
  name: 'CarouselItem',

  props: {
    name: String,
    label: {
      type: [String, Number],
      default: ''
    }
  },

  inject: ['carousel'],

  data() {
    return {
      hover: false,
      translate: 0,
      scale: 1,
      active: false,
      ready: false,
      inStage: false,
      animating: false
    }
  },

  computed: {
    parentDirection() {
      return this.carousel.direction
    },

    itemStyle() {
      const translateType = this.parentDirection === 'vertical' ? 'translateY' : 'translateX'
      const value = `${translateType}(${this.translate}px) scale(${this.scale})`
      const style = {
        transform: value
      }
      return autoprefixer(style)
    }
  },

  created() {
    this.carousel && this.carousel.addItem(this)
  },

  beforeUnmount() {
    this.carousel && this.carousel.removeItem(this)
  },

  methods: {
    processIndex(index, activeIndex, length) {
      if (activeIndex === 0 && index === length - 1) {
        return -1
      } else if (activeIndex === length - 1 && index === 0) {
        return length
      } else if (index < activeIndex - 1 && activeIndex - index >= length / 2) {
        return length + 1
      } else if (index > activeIndex + 1 && index - activeIndex >= length / 2) {
        return -2
      }
      return index
    },

    calcCardTranslate(index, activeIndex) {
      const parentWidth = this.carousel.$el.offsetWidth
      if (this.inStage) {
        return parentWidth * ((2 - CARD_SCALE) * (index - activeIndex) + 1) / 4
      } else if (index < activeIndex) {
        return -(1 + CARD_SCALE) * parentWidth / 4
      } else {
        return (3 + CARD_SCALE) * parentWidth / 4
      }
    },

    calcTranslate(index, activeIndex, isVertical) {
      const distance = this.carousel.$el[isVertical ? 'offsetHeight' : 'offsetWidth']
      return distance * (index - activeIndex)
    },

    translateItem(index, activeIndex, oldIndex) {
      const parentType = this.carousel.type
      const parentDirection = this.parentDirection
      const length = this.carousel.items.length
      if (parentType !== 'card' && oldIndex !== undefined) {
        this.animating = index === activeIndex || index === oldIndex
      }
      if (index !== activeIndex && length > 2 && this.carousel.loop) {
        index = this.processIndex(index, activeIndex, length)
      }
      if (parentType === 'card') {
        this.inStage = Math.round(Math.abs(index - activeIndex)) <= 1
        this.active = index === activeIndex
        this.translate = this.calcCardTranslate(index, activeIndex)
        this.scale = this.active ? 1 : CARD_SCALE
      } else {
        this.active = index === activeIndex
        const isVertical = parentDirection === 'vertical'
        this.translate = this.calcTranslate(index, activeIndex, isVertical)
      }
      this.ready = true
    },

    handleItemClick() {
      const parent = this.carousel
      if (parent && parent.type === 'card') {
        const index = parent.items.indexOf(this)
        parent.setActiveItem(index)
      }
    }
  }
}
</script>

<style scoped>
/* CarouselItem 样式 - 完整迁移版本 */
.carousel__item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: inline-block;
  overflow: hidden;
  z-index: 0; /* $--index-normal - 1 = 1 - 1 = 0 */
  box-sizing: border-box;
}

/* 确保子元素也使用border-box */
.carousel__item *,
.carousel__item *::before,
.carousel__item *::after {
  box-sizing: border-box;
}

.carousel__item.is-active {
  z-index: 2; /* $--index-normal + 1 = 1 + 1 = 2 */
}

.carousel__item.is-animating {
  transition: transform .4s ease-in-out;
}

.carousel__item--card {
  width: 50%;
  transition: transform .4s ease-in-out;
}

.carousel__item--card.is-in-stage {
  cursor: pointer;
  z-index: 1; /* $--index-normal = 1 */
}

.carousel__item--card.is-in-stage:hover .carousel__mask,
.carousel__item--card.is-in-stage.is-hover .carousel__mask {
  opacity: 0.12;
}

.carousel__item--card.is-active {
  z-index: 2; /* $--index-normal + 1 = 1 + 1 = 2 */
}

.carousel__mask {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background-color: #ffffff; /* $--color-white */
  opacity: 0.24;
  transition: .2s;
}
</style>
