<template>
  <i class="carousel-icon" :class="[iconName, iconRotate, customClass]" :style="styles"></i>
</template>

<script>
export default {
  name: 'CarouselIcon',
  props: {
    name: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    },
    size: {
      type: Number,
      default: 0
    },
    rotate: {
      type: Boolean,
      default: false
    },
    customClass: {
      type: String,
      default: ''
    }
  },
  computed: {
    iconName() {
      return `carousel-icon--${this.name}`
    },
    iconRotate() {
      return this.rotate && `is-rotate`
    },
    styles() {
      let _style = {}
      if (this.color) _style.color = this.color
      if (this.size) {
        _style.width = `${this.size}px`
        _style.height = `${this.size}px`
      }
      return _style
    }
  }
}
</script>

<style scoped>
.carousel-icon {
  display: inline-block;
  font-style: normal;
  vertical-align: baseline;
  text-align: center;
  text-transform: none;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.carousel-icon--arrow-left::before {
  content: '‹';
  font-size: 24px;
  font-weight: bold;
}

.carousel-icon--arrow-right::before {
  content: '›';
  font-size: 24px;
  font-weight: bold;
}

.is-rotate {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  0% {
    transform: rotateZ(0deg);
  }
  100% {
    transform: rotateZ(360deg);
  }
}
</style>
