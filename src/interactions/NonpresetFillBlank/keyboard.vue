<template>
  <div class="keyboard-container">
    <!-- 键盘 -->
    <div class="keyboard-panel" v-if="panelType == 'N+1'">
      <!-- N+1类型键盘 -->
      <div
        class="keyboard-item"
        v-for="(item, index) in keypanelConfig[panelType].keyList"
        :key="index"
        @click.stop="insertar(item.v, `key-${index}`)"
        :id="'key-' + index"
        @mouseout="removeKeyDownClass(`key-${index}`)"
        @mousemove="addHoverClass(`key-${index}`)"
      >
        <span class="text" v-if="item.v === 'del'">{{
          $t('classroom.interactions.nonPersetFillBlank.clear')
        }}</span>
        <img class="key-icon" :src="item.i" alt="" />
      </div>
    </div>
    <audio :src="keyDownSound" ref="keyDownSound" />
  </div>
</template>

<script>
import keyDownSound from './audio/keyDown.mp3'
import icon1 from './imgs/icon_1.png'
import icon2 from './imgs/icon_2.png'
import icon3 from './imgs/icon_3.png'
import icon4 from './imgs/icon_4.png'
import icon5 from './imgs/icon_5.png'
import iconFen from './imgs/icon_fen.png'
import iconAdd from './imgs/icon_add.png'
import iconReduce from './imgs/icon_reduce.png'
import iconMuti from './imgs/icon_muti.png'
import iconDivide from './imgs/icon_divide.png'
import iconForward from './imgs/icon_forward.png'
import icon6 from './imgs/icon_6.png'
import icon7 from './imgs/icon_7.png'
import icon8 from './imgs/icon_8.png'
import icon9 from './imgs/icon_9.png'
import icon0 from './imgs/icon_0.png'
import iconPoint from './imgs/icon_point.png'
import iconLeft from './imgs/icon_left.png'
import iconRight from './imgs/icon_right.png'
import iconPercent from './imgs/icon_percent.png'
import iconEqual from './imgs/icon_equal.png'
import iconClear from './imgs/icon_clear.png'

export default {
  name: 'MathKeyboard',
  data() {
    return {
      mathField: null,
      panelType: 'N+1', // 键盘类型
      keypanelConfig: {
        // 键盘配置
        'N+1': {
          keyList: [
            { k: '1', v: '1', i: icon1 },
            { k: '2', v: '2', i: icon2 },
            { k: '3', v: '3', i: icon3 },
            { k: '4', v: '4', i: icon4 },
            { k: '5', v: '5', i: icon5 },
            { k: '', v: '/', i: iconFen },
            { k: '+', v: '+', i: iconAdd },
            { k: '-', v: '-', i: iconReduce },
            { k: '×', v: '\\times', i: iconMuti },
            { k: '÷', v: '\\div', i: iconDivide },
            { k: '', v: 'Backspace', i: iconForward },
            { k: '6', v: '6', i: icon6 },
            { k: '7', v: '7', i: icon7 },
            { k: '8', v: '8', i: icon8 },
            { k: '9', v: '9', i: icon9 },
            { k: '0', v: '0', i: icon0 },
            { k: '.', v: '.', i: iconPoint },
            { k: '<', v: '<', i: iconLeft },
            { k: '>', v: '>', i: iconRight },
            { k: '%', v: '%', i: iconPercent },
            { k: '=', v: '=', i: iconEqual },
            { k: '清空', v: 'del', i: iconClear }
          ]
        }
      },
      controller: null,
      keyDownSound
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initMathQuill()
    })
  },
  methods: {
    // 初始化数学键盘输入
    initMathQuill() {
      var that = this
      var mathFieldSpan = document.getElementById('mathinput')
      // eslint-disable-next-line no-undef
      var MQ = MathQuillTal.getInterface(2)
      this.mathField = MQ.MathField(mathFieldSpan, {
        restrictMismatchedBrackets: true,
        handlers: {
          edit: function() {
            console.log('输入的内容--------', that.mathField.latex())
            that.$emit('updateInputValue', that.mathField.latex())
            //显示光标
            that.controller = that.mathField.__controller
            that.controller.cursor.show()
            that.controller.blurred = false
          }
        }
      })
    },
    // 设置输入框失去焦点
    setMathFieldBlur() {
      this.mathField.blur()
    },
    setMathFieldFocus() {
      this.mathField.focus()
    },
    insertar(valor, id) {
      this.addKeyDownClass(id)
      if (valor == 'Backspace') {
        // 记录删除前的内容
        const deleteBeforeValue = this.mathField.latex()
        // 删除光标前一位
        this.mathField.keystroke(valor)
        // 记录删除后的内容
        let deleteAfterValue = this.mathField.latex()
        // 分式删除
        // 如果删除前包含分式，删除后不包含分式，则需要清除整个分式，直接截取分式前的内容并复制给当前输入框
        if (deleteBeforeValue.indexOf('frac') > -1 && deleteAfterValue.indexOf('frac') === -1) {
          const newVal = deleteBeforeValue.split('\\frac')[0]
          this.mathField.latex(newVal)
        }
      } else if (valor == 'del') {
        // 清除
        this.mathField.latex('')
      } else {
        if (this.mathField.latex().indexOf('frac') == -1 && valor == '/') {
          // 点击了分式键且当前没有分式
          this.mathField.cmd('1')
          if (!this.filterStrLength(this.mathField.latex())) {
            this.mathField.keystroke('Backspace')
            return
          }
          this.mathField.keystroke('Backspace') // 删除计数数字
          this.mathField.latex(this.mathField.latex() + '\\frac{}{1}')
          this.mathField.keystroke('Backspace')
          this.mathField.keystroke('Backspace')
        } else if (valor != '/') {
          if (valor == '=') {
            // 解决>=自动合并为≧的问题
            this.mathField.latex(this.mathField.latex() + '=')
          } else {
            this.mathField.cmd(valor)
          }
          if (!this.filterStrLength(this.mathField.latex())) {
            this.mathField.keystroke('Backspace')
          }
        }
      }
    },
    // 判断答案长度
    filterStrLength(val, len = 20) {
      let str = '',
        totalLength = 0,
        isAfter = false
      // eg: val = 33\frac{4}{5}
      if (val.indexOf('frac') != -1) {
        str = val.replace('\\frac', '')
        let arr = str.split('}{') // ['33{4', '5}']
        let arr1 = arr[0].split('{') // ['33', '4']
        let numup = this.latexToOne(arr1[1]) // 分子长度
        let arr2 = arr[1].split('}') // ['5', '']
        let numdown = this.latexToOne(arr2[0]) // 分母长度
        let orStr = this.latexToOne(arr1[0] + arr2[1]) // 其他答案长度
        if (arr2[1].length > 0 || isNaN(arr1[1]) || isNaN(arr2[0])) {
          // 判断分数后面是否有输入/判断分数中是否有非数字（限制分数后面输入内容/限制分子和分母输入非数字）
          isAfter = true
        }
        if (numup > numdown) {
          totalLength = numup + orStr
        } else {
          totalLength = numdown + orStr
        }
      } else {
        totalLength = this.latexToOne(val)
      }
      if (totalLength > len || isAfter) {
        return false
      }
      return true
    },
    // latex符号转为单字符计算长度
    latexToOne(str) {
      return str
        .replace('\\%', '1')
        .replace('\\times', '1')
        .replace('\\div', '1')
        .replace('\\ge', '1')
        .replace('\\le', '1').length
    },
    // 添加鼠标按下事件
    addKeyDownClass(id) {
      this.$refs.keyDownSound.play()
      document.getElementById(id).classList.remove('hover_style')
      document.getElementById(id).classList.add('click-style')
    },
    // 添加鼠标移除事件
    removeKeyDownClass(id) {
      document.getElementById(id).classList.remove('hover_style')
      document.getElementById(id).classList.remove('click-style')
    },
    // 添加鼠标hover事件
    addHoverClass(id) {
      document.getElementById(id).classList.add('hover_style')
    }
  }
}
</script>

<style lang="scss" scoped>
@use './style.scss' as *;
</style>
