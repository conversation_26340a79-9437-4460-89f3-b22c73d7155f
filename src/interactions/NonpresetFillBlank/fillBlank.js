// 业务逻辑

import { answerCheck, submitOneAnswer, interactVoteReport } from '@/api/interaction/vote'
/**
 * @description
 */
export default class Main {
  constructor(opts = {}) {
    // @log-ignore
    this.options = opts
    this.skinType = this.options.roomMessage.roomInfo.commonOption
    this.vm = opts.vm
  }

  handleOptions() {
    const { ircMsg } = this.options
    ircMsg.options = Object.keys(ircMsg.options).map(key => {
      const item = ircMsg.options[key]
      item.height = 0
      item.percent = 0
      return item
    })
    return ircMsg.options
  }
  // 非预置填空题查询学生作答结果
  async checkAnswerResult() {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1,
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId * 1,
      interactionId: this.options.ircMsg.interactId
    }
    const res = await answerCheck(params)
    return res
  }
  // 非预置填空题提交学生作答信息
  async submitAnswerResult(answer) {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1,
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId * 1,
      interactionId: this.options.ircMsg.interactId,
      option: answer
    }
    const res = await submitOneAnswer(params)
    return res
  }
  async fillBlankReport() {
    console.log('fillBlankReport', this.options)
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1,
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId * 1,
      interactId: this.options.ircMsg.interactId
    }
    // 上报投票
    return interactVoteReport(params)
  }
}
