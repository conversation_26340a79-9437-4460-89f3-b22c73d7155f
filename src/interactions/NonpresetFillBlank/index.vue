<!-- 视图层 -->
<template>
  <div>
    <!--显示键盘作答弹窗-->
    <div v-show="isOpenKeyBoardContainer" class="fill-blank-container">
      <div class="keyboard-input">
        <img
          :src="isShowKeyBoard ? iconBack : iconExpand"
          class="expand-btn"
          @click.stop="expandKeyBoard('expand-btn')"
          id="expand-btn"
          @mouseout="removeKeyDownClass('expand-btn')"
          @mousemove="addHoverClass('expand-btn')"
        />
        <div class="input-container" @click.stop="clickInputBox">
          <span class="placeholder" v-if="!inputValue && !isClicked">{{
            $t('classroom.interactions.nonPersetFillBlank.inputPlaceholder')
          }}</span>
          <span id="mathinput" class="math-input"></span>
        </div>
        <div
          class="submit-btn"
          @click.stop="clickSubmitBtn('submit-btn')"
          id="submit-btn"
          @mouseout="removeKeyDownClass('submit-btn')"
          @mousemove="addHoverClass('submit-btn')"
        >
          <img :src="inputValue && inputValue !== emptyFenshi ? submitBtn : submitBtnDisabled" />
          <span id="submit-text">{{ $t('classroom.interactions.nonPersetFillBlank.submit') }}</span>
        </div>
      </div>
      <KeyBoard v-show="isShowKeyBoard" ref="keyBoard" @updateInputValue="updateInputValue" />
    </div>
    <!-- 答案已提交且作答正确弹窗 -->
    <div v-show="isOpenAnswerCorrectDialog" class="vote-result-isCorrect">
      <div class="myAnswer-container">
        <div class="note-container">
          <img :src="noteImg" class="note-img" />
          <span class="answer-desc">{{
            $t('classroom.interactions.nonPersetFillBlank.myAnswer')
          }}</span>
        </div>
        <span class="answer-text" id="myAnswer">{{ this.myAnswer }}</span>
        <img :src="correctLottie" class="lottie-img" />
      </div>
    </div>
    <div class="toast-tips" v-if="submitStatus">
      <span v-if="submitStatus === 1">
        {{ $t('classroom.interactions.nonPersetFillBlank.submitSuccessfully') }}
      </span>
      <span v-else> {{ $t('classroom.interactions.nonPersetFillBlank.submitFailed') }} </span>
    </div>
  </div>
</template>

<script>
import './mathquill/jquery.js'
import './mathquill/mathquill.css'
import './mathquill/mathquill.js'
import './mathquill/mathquill.css'
import NonPersetFillBlank from './fillBlank.js'
import KeyBoard from './keyboard.vue'
import iconBack from './imgs/icon_back.png'
import iconExpand from './imgs/icon_expand.png'
import submitBtn from './imgs/submit_btn.png'
import submitBtnDisabled from './imgs/submit_btn_disabled.png'
import noteImg from './imgs/note.png'
import correctLottie from './imgs/correct_lottie.png'
import { useIrcService } from '@/hooks/useIrcService'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { emitter } from '@/hooks/useEventBus'
import { fillblankLog } from '@/utils/web-log/HWLogDefine.js'

const { getIrcService } = useIrcService()
export default {
  name: 'NonpresetFillBlank',
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          ircMsg: {},
          roomMessage: {
            roomInfo: {
              commonOption: {}
            }
          }
        }
      }
    }
  },
  components: {
    KeyBoard
  },
  data() {
    return {
      iconBack,
      iconExpand,
      submitBtn,
      submitBtnDisabled,
      noteImg,
      correctLottie,
      // 实例
      nonPersetFillBlank: null,
      fillBlankAnswerState: 0, // 学员填一填作答状态: 0-未作答 1-正确 2-错误
      fillBlankAnswerIsSubmit: false, // 学员填一填是否点击提交
      timer: null,
      isShowKeyBoard: true,
      myAnswer: '',
      isStopAnswer: false, // 是否是停止作答状态
      inputValue: '', // 输入框的值
      rewardCoins: 0, // 获得的金币奖励
      submitStatus: 0, // 提交状态: 1 提交成功，2 提交失败
      isSubmitting: false, // 是否正在提交
      isClicked: false,
      emptyFenshi: '\\frac{ }{ }', // 空的分式
      durationTime: 3000
    }
  },
  mounted() {
    fillblankLog.info('收到填一填互动', 'start')
    this.listenerSignalService()
    this.recoverHistory()
    console.log('🔍 [DEBUG] NonpresetFillBlank组件mounted完成')
  },
  unmounted() {
    this.removeSignalService()
    if (this.timer) {
      clearTimeout(this.timer)
      this.timer = null
    }
  },
  computed: {
    // 是否打开键盘容器
    // 未提交答案且没有停止作答
    isOpenKeyBoardContainer() {
      return this.fillBlankAnswerIsSubmit === false && !this.isStopAnswer
    },
    // 答案已提交且作答正确弹窗
    isOpenAnswerCorrectDialog() {
      return this.fillBlankAnswerIsSubmit && this.fillBlankAnswerState === 1
    }
  },
  async created() {
    this.timer && clearTimeout(this.timer)
    this.timer = null
    this.nonPersetFillBlank = new NonPersetFillBlank({
      ...this.options
    })
    // 上报收到互动
    this.nonPersetFillBlank.fillBlankReport()
  },
  watch: {
    isOpenAnswerCorrectDialog: {
      handler: function (val) {
        if (val) {
          console.log('打开作答正确结果弹窗')
          // 打开回答正确弹窗时设置里面字体
          this.$nextTick(() => {
            // 正确回显答案
            var answerSpan = document.getElementById('myAnswer')
            // eslint-disable-next-line no-undef
            var MQ = MathQuillTal.getInterface(2)
            MQ.StaticMath(answerSpan)
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    receiveMessage(noticeContent) {
      // 填一填互动点击【停止作答】不会关闭互动，其内容 {pub: true, isStop: true}
      // 监听停止作答信令
      if (noticeContent.isStop) {
        fillblankLog.info('收到老师停止作答消息')
        this.isStopAnswer = true
        // 如果未提交强制学员提交答案
        if (!this.fillBlankAnswerIsSubmit) {
          this.submitFillBlankAnswer()
        }
      }
    },
    listenerSignalService() {
      console.log('🔍 [DEBUG] 开始注册信号监听器')
      // 监听群聊消息
      const IrcService = getIrcService()
      if (IrcService) {
        console.log('🔍 [DEBUG] thinkClass存在，注册onRecvRoomMessage监听器')

        console.log('🔍 [DEBUG] SignalService:', IrcService)
        IrcService.on('onRecvRoomMessage', this.markCorrectHandler)
        console.log('🔍 [DEBUG] 已注册onRecvRoomMessage监听器，回调函数:', this.markCorrectHandler)
      } else {
        console.log('🔍 [DEBUG] thinkClass不存在，无法注册监听器')
      }
    },
    markCorrectHandler(res) {
      console.log('🔍 [DEBUG] NonpresetFillBlank markCorrectHandler 被调用:', {
        res,
        content: res.content,
        ircType: res.content?.ircType,
        currentAnswerState: this.fillBlankAnswerState
      })

      // 如果该消息为老师标记正确群聊消息 且 当前学生提交的答案错误
      // 学员填一填作答状态: 0-未作答 1-正确 2-错误
      if (res.content && res.content.ircType === 'fill_blank_mark_correct') {
        console.log('🔍 [DEBUG] 确认收到标记正确群聊消息')
        fillblankLog.info(`收到标记正确群聊消息, 当前作答状态: ${this.fillBlankAnswerState}`)
        if (this.fillBlankAnswerState === 2) {
          console.log('🔍 [DEBUG] 当前作答状态为错误(2)，调用checkAnswerResult')
          // 如果回答错误，每次调用check接口查看老师是否将自己标记正确
          this.checkAnswerResult()
        } else {
          console.log('🔍 [DEBUG] 当前作答状态不是错误，状态为:', this.fillBlankAnswerState)
        }
      } else {
        console.log('🔍 [DEBUG] 消息不是fill_blank_mark_correct类型或content为空:', {
          hasContent: !!res.content,
          ircType: res.content?.ircType
        })
      }
    },
    /**
     * 移除IRC消息
     */
    removeSignalService() {
      console.log('🔍 [DEBUG] 开始移除信号监听器')
      // 监听群聊消息
      const IrcService = getIrcService()
      if (IrcService) {
        console.log('🔍 [DEBUG] IrcService存在，移除onRecvRoomMessage监听器')

        IrcService.off('onRecvRoomMessage', this.markCorrectHandler)
        console.log('🔍 [DEBUG] 已移除onRecvRoomMessage监听器')
      } else {
        console.log('🔍 [DEBUG] thinkClass不存在，无法移除监听器')
      }
    },
    // 互动恢复
    recoverHistory() {
      // 互动恢复
      if (this.options?.isHistory) {
        // 调用check接口查询学生作答信息
        // 1-作答正确----> 弹出金币奖励弹窗以及我的作答结果弹窗
        // 2-作答错误  （不处理）
        // 0-未作答----> 调起键盘弹窗
        fillblankLog.info(`填一填历史互动恢复`)
        this.fillBlankAnswerIsSubmit = null // 历史恢复时，将其置为null，防止开始就弹出键盘
        this.checkAnswerResult()
      }
    },
    // 非预置填空题查询学生作答结果
    async checkAnswerResult() {
      console.log('🔍 [DEBUG] 开始查询学生作答结果')
      const res = await this.nonPersetFillBlank.checkAnswerResult()
      console.log('🔍 [DEBUG] checkAnswerResult API返回结果:', res)
      fillblankLog.info(`查询【填一填】学员作答结果: ${JSON.stringify(res)}`)
      this.setAnswerResultState(res)
      console.log('🔍 [DEBUG] 完成setAnswerResultState调用')
    },
    // 点击提交按钮
    clickSubmitBtn(id) {
      fillblankLog.action('点击提交按钮')
      if (this.isSubmitting) return
      this.$refs.keyBoard.addKeyDownClass(id) // 添加按键动效
      this.submitFillBlankAnswer()
    },
    // 提交填一填作答结果
    async submitFillBlankAnswer() {
      // 如果内容为空或为空分式
      if (!this.inputValue || this.inputValue === this.emptyFenshi) {
        fillblankLog.error(`提交内容为空或空分式, inputValue: ${this.inputValue}`)
        return
      }
      window.$sensors.track('keyboard_submit')
      this.formatAnswerHandler() // 处理提交的答案格式
      fillblankLog.info(`学员提交答案: ${this.inputValue}`)
      this.isSubmitting = true
      try {
        const res = await this.nonPersetFillBlank.submitAnswerResult(this.inputValue)
        // 设置答案提交状态
        if (res.code === 0) {
          // 如果提交成功且答案正确，需要展示金币奖励弹窗，此时不需要展示该toast
          this.submitStatus = res?.data?.answerStat === 1 ? 0 : 1
          this.setAnswerResultState(res, true)
          classLiveSensor.osta_ia_vote_submit(this.options?.ircMsg, res.data)
        } else {
          // code不为0返回提交失败
          this.submitStatus = 2
          fillblankLog.error(`[接口报错]学员提交答案失败${res.code}`)
        }
      } catch (e) {
        console.log('学员提交答案失败', e)
        this.submitStatus = 2
        fillblankLog.error(`[接口报错]学员提交答案失败`)
      }
      this.isSubmitting = false
      // 3s后toast消失
      setTimeout(() => {
        this.submitStatus = 0
      }, this.durationTime)
    },
    // 处理提交的答案格式
    formatAnswerHandler() {
      // 如果被容包含空分式，则将空分式过滤掉
      if (this.inputValue.includes(this.emptyFenshi)) {
        this.inputValue = this.inputValue.split(this.emptyFenshi)[0]
      }
      // 1、对输入的"."的进行处理   将"\." 转化成"."
      // 2、对分式中分子或分母为空的情况进行处理  eg: 54\frac{5}{ } 为和其他端保持一致，需要将空括号中的空格去除
      this.inputValue = this.inputValue.replace(/\\\./g, '.').replace('{ }', '{}')
    },
    // 设置作答状态
    setAnswerResultState(res, isClickSubmit = false) {
      console.log('🔍 [DEBUG] setAnswerResultState被调用:', {
        res,
        isClickSubmit,
        currentState: this.fillBlankAnswerState,
        currentSubmitStatus: this.fillBlankAnswerIsSubmit
      })

      if (res.code === 0) {
        // 如果点击提交按钮，标记已作答
        if (isClickSubmit) {
          this.fillBlankAnswerIsSubmit = true
        } else {
          //check接口需要根据答案状态：0-未作答 1-正确 2-错误 判断是否提交
          this.fillBlankAnswerIsSubmit = !!res?.data?.answerStat
        }
        // 获取学员作答状态
        const oldState = this.fillBlankAnswerState
        this.fillBlankAnswerState = res?.data?.answerStat

        console.log('🔍 [DEBUG] 状态更新:', {
          oldState,
          newState: this.fillBlankAnswerState,
          submitStatus: this.fillBlankAnswerIsSubmit,
          answerData: res?.data
        })

        console.log('当前学员作答状态', this.fillBlankAnswerIsSubmit)
        fillblankLog.info(`当前学员作答状态: ${this.fillBlankAnswerState}`)
        this.myAnswer = res?.data?.option
        this.rewardCoins = res?.data?.addCoin

        const currentInteractId = this.options?.ircMsg?.interactId
        // 打开作答正确弹窗
        if (this.fillBlankAnswerState === 1) {
          fillblankLog.info(
            '🔍 [DEBUG] 作答状态为正确(1)，触发continuousCorrect事件',
            res,
            currentInteractId
          )

          // 检查localStorage中是否有该互动的标记
          const localInteractId = localStorage.getItem(`fillblankGoldCoinTag`)
          ///如果interactId存在并且和当前互动ID相同，不执行continuousCorrect
          if (localInteractId && localInteractId === currentInteractId) {
            fillblankLog.info('当前互动添加过金币，不执行加金币动画', currentInteractId)
          } else {
            emitter.emit('continuousCorrect', {
              ...res.data,
              answerStat: this.fillBlankAnswerState,
              gold: res?.data?.addCoin,
              isNoMedal: true // 不展示勋章
            })
            //将互动id记录在本地
            localStorage.setItem('fillblankGoldCoinTag', currentInteractId)
          }

          // 4s消失
          setTimeout(() => {
            this.destroy()
          }, this.durationTime)
        } else {
          console.log('🔍 [DEBUG] 作答状态不是正确，状态为:', this.fillBlankAnswerState)
        }
      } else {
        console.log('🔍 [DEBUG] API返回code不为0:', res.code)
      }
    },
    // 伸缩键盘
    expandKeyBoard(id) {
      this.$refs.keyBoard.addKeyDownClass(id) // 添加按键动效
      this.isShowKeyBoard = !this.isShowKeyBoard
      window.$sensors.track('click_expand_keyboard_btn', {
        isShowKeyBoard: this.isShowKeyBoard
      })
      fillblankLog.info(`伸缩键盘, 当前键盘是否展开: ${this.isShowKeyBoard}`)
      // 当前键盘收起时，取消输入框焦点
      if (!this.isShowKeyBoard) {
        this.$refs.keyBoard.setMathFieldBlur()
      }
    },
    // 点击输入框，如果此时键盘处于隐藏状态，则调起键盘
    clickInputBox() {
      if (!this.isClicked) {
        this.isClicked = true
        this.$refs.keyBoard.setMathFieldFocus()
      }
      if (!this.isShowKeyBoard) {
        this.isShowKeyBoard = true
      }
    },
    // 添加鼠标移除事件
    removeKeyDownClass(id) {
      this.$refs.keyBoard.removeKeyDownClass(id)
    },
    // 添加鼠标hover事件
    addHoverClass(id) {
      this.$refs.keyBoard.addHoverClass(id)
    },
    // 获取输入框的值
    updateInputValue(val) {
      this.isClicked = true
      this.inputValue = val
    },

    destroy() {
      this.$emit('close', 'nonpreset_fill_blank')
    },
    // 教师端主动关闭互动
    async destroyInteraction() {
      this.isStopAnswer = true // 立即关闭键盘
      fillblankLog.info(`互动结束，教师端主动关闭互动`)
      // 如果未提交，则强制提交
      if (!this.fillBlankAnswerIsSubmit) {
        await this.submitFillBlankAnswer()
      }
      // 互动结束，强制提交
      this.timer = setTimeout(async () => {
        this.destroy()
      }, this.durationTime)
    }
  }
}
</script>

<style lang="scss" scoped>
@use './style.scss' as *;
</style>
