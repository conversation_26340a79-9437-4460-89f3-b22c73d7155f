<template>
  <div class="praise-container" ref="praiseContainer">
    <PraiseList
      role="student"
      :praiseInfo="praiseInfo"
      :planId="planId"
      :savingText="t('classroom.interactions.praiseList.savingText')"
      ref="praiseList"
      v-if="Object.keys(praiseInfo).length > 0"
      :getThumbInfo="getThumbInfo"
      @sendThumbNumMsg="sendThumbNumMsg"
      @composeImgSuccess="composeImgSuccess"
      :class="{ 'class-interaction__fourToThree': layout !== 'full' }"
    >
      <template v-slot:empty>
        <span>{{ t('classroom.interactions.praiseList.emptyText') }}</span>
      </template>
    </PraiseList>
    <Transition name="fade">
      <div class="save-path-toast" v-if="showSavePathToast">
        <span>{{ t('classroom.interactions.praiseList.saveText') }}</span>
        <span class="path"> {{ t('classroom.interactions.praiseList.savePath') }}</span>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import PraiseList from './components/praiseList/index.vue'
import logger from '@/utils/logger'
import { submitInteractPartakereport } from '@/api/interaction'
import { useIrcService } from '@/hooks/useIrcService'
import { useBaseData } from '@/stores/baseData'
defineOptions({
  name: 'ClassPraiseListPage'
})

const store = useBaseData()
const { getIrcService } = useIrcService()

const props = defineProps({
  options: {
    type: Object,
    default: () => ({
      ircMsg: {},
      roomMessage: {
        roomInfo: {
          commonOption: {}
        }
      }
    })
  },
  layout: {
    type: String,
    default: 'full'
  }
})

const { t } = useI18n()

// refs
const praiseContainer = ref(null)
const praiseList = ref(null)

// reactive state
const praiseInfo = ref({})
const getThumbInfo = ref({})
const showSavePathToast = ref(false)
const useInfo = computed(() => store.userInfo)
const planId = ref(0)

// 接收消息处理
const receiveMessage = noticeContent => {
  // 翻转卡片(// 如果是历史恢复，不需要再执行一次 update_praise_list)
  if (!props.options.isHistory && noticeContent.isUpdate) {
    // 监听更新学生列表操作
    praiseInfo.value = { ...noticeContent }
    const position = praiseInfo.value.position // 判断向前翻还是向后翻
    sendLogger(`收到翻转卡片操作, 方向：${position}`)
    nextTick(() => {
      console.log(11111, position)
      praiseList.value?.reverseCardHandler(position) // 翻转卡片
    })
  }
}

// 监听信号服务
const listenerSignalService = () => {
  // 监听群聊消息
  const IrcService = getIrcService()
  IrcService.on('onRecvRoomMessage', getThumbNumMsg)
}

// 获取其他学生点赞数
const getThumbNumMsg = res => {
  // 小班传res.content， 大班只传content
  const content = res.content || res
  if (content && content.ircType === 'praise_list_point_like') {
    sendLogger(`收到其他人点赞群聊消息, ${JSON.stringify(res.content)}`)
    getThumbInfo.value = content
  }
}

// 自己点赞发送群聊消息
const sendThumbNumMsg = count => {
  const opts = {
    roomList: props.options.roomMessage.roomInfo.commonOption.roomlist,
    content: {
      ircType: 'praise_list_point_like',
      interactId: praiseInfo.value.interactId,
      userId: useInfo.value.uid || '',
      avatar: useInfo.value.avatar || '',
      likeNum: count
    },
    chatMsgPriority: 99
  }
  sendLogger(`自己点赞, ${JSON.stringify(opts.content)}`)
  const IrcService = getIrcService()
  IrcService.sendRoomMessage(opts)
}

// 图片合成成功进行图片下载
const composeImgSuccess = url => {
  // url 是 base64 图片字符串
  // 生成文件名
  const fileName = `${t('classroom.interactions.praiseList.fileName', {
    firstTitle: praiseInfo.value.firstTitle,
    lessonName: praiseInfo.value.lessonName,
    courseName: praiseInfo.value.courseName
  })}_${new Date().getTime()}.png`

  // 创建 a 标签并自动点击下载
  const link = document.createElement('a')
  link.href = url
  link.download = fileName
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  sendLogger(`图片下载成功, base64: ${url}`)
  showSavePathToast.value = true
  setTimeout(() => {
    showSavePathToast.value = false
  }, 2000)
}

/**
 * 日志上报
 */
const sendLogger = (msg, stage = '') => {
  const interactionId = props.options?.ircMsg?.interactId || 'unknown'
  const interactionType = 'ClassPraiseList'

  logger.send({
    tag: 'student.Interact',
    level: 'info',
    content: {
      msg: msg,
      interactType: interactionType,
      interactId: interactionId,
      interactStage: stage,
      params: '',
      response: '',
      timestamp: Date.now()
    }
  })

  console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage })
}
// 上报互动
const reportInterct = () => {
  submitInteractPartakereport({
    planId: planId.value,
    classId: props.options.roomMessage.roomInfo.commonOption.classId,
    interactId: props.options.ircMsg.interactId || ''
  })
}

onMounted(() => {
  sendLogger(`收到课堂表扬榜互动, 内容为: ${JSON.stringify(props.options.ircMsg)}`)
  planId.value = props.options.roomMessage.roomInfo.planInfo.id
  useInfo.value = useInfo.value ? useInfo.value : {}
  praiseInfo.value = props.options.ircMsg
  listenerSignalService()
  reportInterct()
})

onBeforeUnmount(() => {
  const IrcService = getIrcService()
  IrcService.off('onRecvRoomMessage', getThumbNumMsg)
})
defineExpose({
  receiveMessage
})
</script>
<style lang="scss" scoped>
@use './style.scss' as *;
</style>
