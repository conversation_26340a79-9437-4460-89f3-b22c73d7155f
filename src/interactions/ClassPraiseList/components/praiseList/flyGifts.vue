<template>
  <div class="gifts-container">
    <div v-for="item of showGiftList" :key="item.groupKey">
      <div class="gift-box"  v-for="(el, index) of item.onceShowGiftArr" :key="index" :class="`gift${el.aniIndex}`" :style="{ animationDelay: `${index * 200}ms` }">
        <img  class="gift-img" :src="`${el.src}`" :class="[{'setRadius': el.isUserAvatar}]"/>
      </div>
    </div>
  </div>
</template>
<script>
import { giftArr } from './utils'
export default {
  data() {
    return {
      showGiftList: [],
      randomIndex: 0
    }
  },
  methods: {
    popGiftsHandler(avatar, isUserAvatar) {
      // 每三个为一组展示
      let popGift1 = giftArr[Math.floor(Math.random() * 4)]
      let popGift2 = giftArr[Math.floor(Math.random() * 4)]
      this.randomIndex = Math.floor(Math.random() * 4)
      // 每次展示一个头像两个礼物，连点时第一次展示一个头像两个礼物，之后展示三个礼物
      const onceShowGiftArr = [{
        aniIndex: Math.floor(Math.random() * 6), // 采用哪种动效（共6种）
        src: avatar,
        isUserAvatar
      }, {
        aniIndex: Math.floor(Math.random() * 6),
        src: popGift1.src
      }, {
        aniIndex: Math.floor(Math.random() * 6),
        src: popGift2.src
      }]
      console.log('onceShowGiftArr', onceShowGiftArr[0].aniIndex, onceShowGiftArr[1].aniIndex, onceShowGiftArr[2].aniIndex)
      let groupKey = Math.random()
      this.showGiftList.push({
        groupKey,
        onceShowGiftArr
      })
      setTimeout(() => {
        this.showGiftList.shift() // 每1.5s移除一组元素
      }, 1500);
    }
  }
}
</script>
<style lang="less" scoped>
.gifts-container {
  width: 100%;
  height: 100%;
}
.gift-box {
  width: 30%;
  padding-bottom: 30%;
  position: absolute;
  left: 50%;
  bottom: 0;
  opacity: 0;
  z-index: 1;
}
.gift-img {
  position: absolute;
  width: 100%;
  height: 100%;
}
.setRadius {
  border-radius: 50%;
  border: 2px solid transparent;
  background: #fff;
}
.gift0 {
  transform: rotate(-15deg);
  animation: ee1L 1.5s ease-in-out forwards, eeT 1.5s cubic-bezier(0,0,1,.8) forwards, opac 1.5s linear forwards;
}
.gift1 {
  transform: rotate(-10deg);
  animation: ee2L 1.5s ease-in-out forwards, eeT 1.5s cubic-bezier(0,0,1,.8) forwards, opac 1.5s linear forwards;
}
.gift2 {
  transform: rotate(15deg);
  animation: ee1R 1.5s ease-in-out forwards, eeT 1.5s cubic-bezier(0,0,1,.8) forwards, opac 1.5s linear forwards;
}
.gift3 {
  transform: rotate(10deg);
  animation: ee2R 1.5s ease-in-out forwards, eeT 1.5s cubic-bezier(0,0,1,.8) forwards, opac 1.5s linear forwards;
}
.gift4 {
  transform: rotate(-5deg);
  animation: ee3L 1.5s ease-in-out forwards, eeT 1.5s cubic-bezier(0,0,1,.8) forwards, opac 1.5s linear forwards;
}
.gift5 {
  transform: rotate(5deg);
  animation: ee3R 1.5s ease-in-out forwards, eeT 1.5s cubic-bezier(0,0,1,.8) forwards, opac 1.5s linear forwards;
}
@keyframes ee1L {
  0%{
    left: 50%;
  }
  25%{
    left: 30%;
  }
  75%{
    left: 70%;
  }
  100%{
    left: 50%;
  }
}
@keyframes ee1R {
  0%{
    left: 50%;
  }
  25%{
    left: 70%;
  }
  75%{
    left: 20%;
  }
  100%{
    left: 50%;
  }
}
@keyframes ee2L {
  0%{
    left: 50%;
  }
  20%{
    left: 35%;
  }
  40%{
    left: 15%;
  }
  60%{
    left: 35%;
  }
  80%{
    left: 15%;
  }
  100%{
    left: 0%;
  }
}
@keyframes ee2R {
  0%{
    left: 50%;
  }
  25%{
    left: 65%;
  }
  50% {
    left: 70%;
  }
  75%{
    left: 55%;
  }
  100%{
    left: 70%;
  }
}
@keyframes ee3L {
  0%{
    left: 50%;
  }
  25%{
    left: 45%;
  }
  75%{
    left: 65%;
  }
  100%{
    left: 50%;
  }
}
@keyframes ee3R {
  0%{
    left: 50%;
  }
  25%{
    left: 65%;
  }
  75%{
    left: 45%;
  }
  100%{
    left: 50%;
  }
}
@keyframes eeT {
  to {
    bottom: 100%
  }
}
@keyframes opac {
  0%{
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
</style>

