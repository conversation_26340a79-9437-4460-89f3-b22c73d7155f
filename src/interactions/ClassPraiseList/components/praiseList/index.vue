<template>
  <div class="praise-list-container" ref="praise" :class="{ 'teacher-praise': role !== 'student' }">
    <div class="course-info" v-show="isCompositing">
      <span class="plan-num">{{ praiseInfo.lessonName }}</span>
      <span class="course-name">{{ praiseInfo.courseName }}</span>
      <span class="date">{{ praiseInfo.date }}</span>
    </div>
    <!-- 榜单title -->
    <div class="praise-list-info">
      <div class="header">
        <p class="title">{{ praiseInfo.firstTitle }}</p>
        <p class="sub-title" v-if="praiseInfo.secondTitle">{{ praiseInfo.secondTitle }}</p>
      </div>
      <!-- lottie部分 -->
      <img
        :src="lottieMap[praiseInfo.praiseType]?.image"
        class="lottie-player"
        v-show="isCompositing"
      />
      <lottie-player autoplay loop mode="normal" class="lottie-player" v-show="!isCompositing" />

      <!-- 学员列表 -->
      <template v-if="numType != 'empty'">
        <div :class="['list-container', 'list-container-' + numType]">
          <div
            v-for="(item, index) in studentList"
            :key="index"
            :class="[
              'num-card',
              'num-' + numType,
              { 'crad-item-myself': item.isMyself },
              { 'reverse-card': item.isReverseCard },
              cardLess3Style
            ]"
          >
            <div
              class="card-item"
              :class="{ 'card-cont-left': numType === 'less20' }"
              v-show="!item.isReverseCard"
            >
              <img :src="item.avatar" class="avatar" v-if="numType !== 'less30'" />
              <span>{{ item.nickname }}</span>
            </div>
            <!-- 徽章 -->
            <img
              src="./imgs/myself-logo.png"
              class="myself-logo"
              v-if="item.isMyself && studentList.length <= 10 && !item.isReverseCard"
            />
          </div>
        </div>
        <!-- 木桩 -->
        <img
          src="./imgs/board.png"
          :class="['board-' + numType]"
          v-if="numType === 'less3' || numType === 'less10'"
        />
        <img src="./imgs/board.png" class="board-less10-2" v-if="numType === 'less10'" />
      </template>
      <template v-else>
        <!-- 学生列表为空 -->
        <div class="list-container list-empty">
          <img src="./imgs/empty.png" />
          <div class="empty-text">
            <slot name="empty"></slot>
          </div>
        </div>
      </template>
      <!-- 底部logo -->
      <img src="./imgs/logo.png" class="logo" v-show="isCompositing" />
    </div>
    <!-- 底部按钮 -->
    <div class="footer-btn" v-if="role === 'student' && numType != 'empty'">
      <img
        src="./imgs/download-btn.png"
        :class="[showDownloadBtn ? 'show-btn' : 'hidden-btn']"
        id="download-btn"
        @click="downloadHandler('download-btn')"
      />
      <div class="thumb-container">
        <img src="./imgs/thumb-btn.png" id="thumb-btn" @click="clickMyThumbHandler('thumb-btn')" />
        <span class="thumb-num">{{ showThumbNum }}</span>
      </div>
      <div class="fly-container">
        <FlyGifts ref="flyGifts" />
      </div>
    </div>
    <div class="loading-mask" v-if="isCompositing && !previewImgVisible">
      <div class="download-tips">
        <img src="./imgs/saving-icon.png" />
        <span>{{ saveText }}</span>
      </div>
    </div>
    <div v-show="previewImgVisible" class="download-modal">
      <img :src="downloadImgUrl" class="prase-img" v-if="downloadImgUrl" />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { giftArr } from './utils'
import FlyGifts from './flyGifts.vue'
import domtoimage from './domToImage'
import "@lottiefiles/lottie-player";
import zuoyewanchengImg from './lottiefiles/zuoyewancheng/img.png'
import zuoyequanduiImg from './lottiefiles/zuoyequandui/img.png'
import zidingyiImg from './lottiefiles/zidingyi/img.png'
import { useBaseData } from '@/stores/baseData'

defineOptions({
  name: 'PraiseList'
})

const props = defineProps({
  // 榜单相关内容
  praiseInfo: {
    type: Object,
    default: () => ({})
  },
  // 角色 老师: 'teacher'，学生: 'student'
  role: {
    type: String,
    default: 'student'
  },
  // 收到其他学生的点赞消息
  getThumbInfo: {
    type: Object,
    default: () => ({})
  },
  // 课次id
  planId: {
    type: Number,
    default: 0
  },
  saveText: {
    type: String,
    default: 'Saving...'
  }
})

const emits = defineEmits(['sendThumbNumMsg', 'composeImgSuccess'])
const store = useBaseData()
const praise = ref(null)
const flyGifts = ref(null)
const studentList = ref(props.praiseInfo.studentList || [])
const hasMyself = ref(false) // 榜单上是否包含自己
const isCompositing = ref(false) // 是否正在展示下载的图片
const downloadImgUrl = ref('') // 下载的图片
const showStudentNum = ref(0) // 当前展示的学生列表（展示卡片正面的数量）
const totalThumbNum = ref(0) // 总点赞数
const otherStuThumbArr = ref([]) // 其他学生点赞队列
const onceClickNum = ref(0) // 一次连点点赞次数
const clickTimer = ref(null)
const useInfo = computed(() => store.userInfo)
const taskTimer = ref(null) // 轮询队列定时器
const pollTaskTime = 1000 // 轮询队列的时间间隔
const showOtherStuAvatarValidTime = 2000 // 展示其他学生头像的有效时间
const previewImgVisible = ref(false) // 下载的图片预览弹窗
const lottieMap = reactive({
  1: {
    lottie: './lottiefiles/zuoyewancheng/index.json',
    image: zuoyewanchengImg
  },
  2: {
    lottie: './lottiefiles/zuoyequandui/index.json',
    image: zuoyequanduiImg
  },
  3: {
    lottie: './lottiefiles/zidingyi/index.json',
    image: zidingyiImg
  }
})
const changeIndex = ref(props.praiseInfo.showStudentNum || 0)

// 判断布局类型
const numType = computed(() => {
  let type = ''
  if (studentList.value.length == 0) {
    type = 'empty'
  } else if (studentList.value.length <= 3) {
    type = 'less3'
  } else if (studentList.value.length <= 10) {
    type = 'less10'
  } else if (studentList.value.length <= 20) {
    type = 'less20'
  } else {
    type = 'less30'
  }
  return type
})

const cardLess3Style = computed(() => {
  if (studentList.value.length <= 3) return `stu-num-${studentList.value.length}`
  return ''
})

// 展示下载按钮：榜单中包含自己且(是系统榜单 或 自定义榜单的全部展示 或 自定义榜单一个个展示且卡片全部展示时)
const showDownloadBtn = computed(() => {
  return (
    hasMyself.value &&
    (props.praiseInfo.praiseType !== 3 ||
      props.praiseInfo.publishType === 1 ||
      changeIndex.value === studentList.value.length)
  )
})

const showThumbNum = computed(() => {
  return totalThumbNum.value > 999 ? '999+' : totalThumbNum.value
})

async function loadLottie() {
  const player = document.querySelector('lottie-player')
  const lottieUrl = lottieMap[props.praiseInfo.praiseType]?.lottie
  // 添加 @vite-ignore 注释来忽略动态导入警告
  const lottieJson = JSON.stringify((await import(/* @vite-ignore */ `${lottieUrl}`)).default)
  player.addEventListener('rendered', () => {
    player.load(lottieJson)
  })
}

// 设置列表中卡片状态
function setReverseCard() {
  // 系统榜单时，直接展示所有学员列表卡片 showStudentNum = studentList.length
  // 自定义榜单：（1）全部展示 showStudentNum = studentList.length
  // （2）一个个展示 showStudentNum < studentList.length
  if (showStudentNum.value <= studentList.value.length) {
    studentList.value = studentList.value.map((item, index) => {
      if (index < showStudentNum.value) {
        item.isReverseCard = false
      } else {
        item.isReverseCard = true
      }
      return item
    })
  }
}

// 检查是否有自己的名字，如果有则放到首位并特殊样式标记
function checkMyselfCard() {
  // 如果是自定义榜单则不进行排序
  const userId = useInfo.value.uid
  let mineIndex = -1
  let mineItem = {}
  studentList.value = studentList.value.map((item, index) => {
    if (item.studentId === userId) {
      mineIndex = index
      mineItem = item
      item.isMyself = true // 标记是自己
    }
    return item
  })
  // 将自己放到首位并特殊样式标记
  if (mineIndex > -1) {
    hasMyself.value = true
    // 如果是系统榜单，需要重新排序，将自己放在首位,如果就在第一位则不调换
    if (props.praiseInfo.praiseType !== 3 && mineIndex > 0) {
      studentList.value.splice(mineIndex, 1)
      studentList.value.unshift(mineItem)
    }
  }
}

// 翻转卡片
function reverseCardHandler(position) {
  // 翻转卡片
  let itemObj = {}
  if (position === 'forward') {
    setItemHandler(changeIndex.value, itemObj, false)
    changeIndex.value = changeIndex.value + 1
  } else {
    changeIndex.value = changeIndex.value - 1
    setItemHandler(changeIndex.value, itemObj, true)
  }
}

function setItemHandler(index, itemObj, flag) {
  // 为要翻转的卡片添加动效
  let dom = document.getElementsByClassName('num-card')[index]
  dom.classList?.add('reverse-ani')
  console.log(11111, index, itemObj, flag)
  setTimeout(() => {
    // 400ms后移除动效且更改isReverseCard
    dom.classList?.remove('reverse-ani')
    itemObj = studentList.value[index]
    itemObj.isReverseCard = flag
    // Vue3不需要$set，直接赋值即可
    studentList.value[index] = itemObj
  }, 400)
}

// 每1s轮询1次
function pollOtherStuThumbTask() {
  taskTimer.value = setInterval(() => {
    if (otherStuThumbArr.value.length === 0) return
    const curTime = new Date().getTime()
    console.log('【测试】轮询其他学员点赞队列', curTime)
    // 比当前时间早于2s的数据视为无效，舍弃并直接加上其点赞数
    while (
      otherStuThumbArr.value.length > 0 &&
      curTime - otherStuThumbArr.value[0].timeStamp > showOtherStuAvatarValidTime
    ) {
      const ele = otherStuThumbArr.value.shift() // 删除第一个元素
      // 增加点赞数
      totalThumbNum.value += ele.likeNum
      console.log('【测试】清除点赞队列中无效数据', ele.likeNum)
    }
    // 如果符合条件即入队列时间与当前时间间隔小于2s则pop进行点赞动效
    if (otherStuThumbArr.value.length > 0) {
      let popEle = otherStuThumbArr.value.shift() // 弹出第一个元素
      flyGifts.value?.popGiftsHandler(popEle.avatar, true) // 飘头像
      // 增加其点赞数
      totalThumbNum.value += popEle.likeNum
      console.log('【测试】队列出队列', popEle.likeNum)
    }
  }, pollTaskTime)
}

// 清空其他学员点赞队列
function clearOtherStuThumbTask() {
  clearInterval(taskTimer.value)
  // 清空队列并将队列中所有学生的点赞数加上
  otherStuThumbArr.value.forEach(item => {
    totalThumbNum.value += item.likeNum
  })
  otherStuThumbArr.value.length = 0
}

// 图片下载click
async function downloadHandler(id) {
  console.log('图片下载')
  setClickStyle(id)
  downloadPicture()
}

// 自己点赞click
function clickMyThumbHandler(id) {
  console.log('点赞')
  setClickStyle(id)
  totalThumbNum.value += 1
  quickClickThumb(sendThumbNumMsg)() // 处理连点效果
}

// 处理快速点击效果
function quickClickThumb(callback) {
  clearOtherStuThumbTask() // 自己点赞时先清空其他学生的点赞队列
  const delayTime = 300 // 小于300ms连续点击算连点

  const delayHandler = function (count) {
    clearTimeout(clickTimer.value)
    clickTimer.value = setTimeout(() => {
      callback(count)
      // 点赞结束后恢复轮询其他学员点赞队列
      pollOtherStuThumbTask()
      onceClickNum.value = 0
      clickTimer.value = null
    }, delayTime)
  }

  return function () {
    onceClickNum.value += 1
    let avatar = ''
    let isUserAvatar = false
    // 判断是连续点击过程中的第一次还是非第一次，如果是第一次点击，则显示1个头像两个礼物，非第一次则显示3个礼物
    // 传参区分起一个图片是头像还是礼物
    if (onceClickNum.value === 1) {
      avatar = useInfo.value.avatar
      isUserAvatar = true
    } else {
      avatar = giftArr[Math.floor(Math.random() * 4)].src
    }
    flyGifts.value?.popGiftsHandler(avatar, isUserAvatar)
    delayHandler(onceClickNum.value)
  }
}

// 自己点赞发送群聊消息
function sendThumbNumMsg(count) {
  console.log('发送群聊消息---点赞数', count)
  emits('sendThumbNumMsg', count)
}

// 设置点击动效
function setClickStyle(id) {
  if (!id) return
  document.getElementById(id)?.classList.add('click-style')
  setTimeout(() => {
    document.getElementById(id)?.classList.remove('click-style')
  }, 120)
}

// dom拼接并下载
async function downloadPicture() {
  // 截取课次信息部分
  downloadImgUrl.value = ''
  isCompositing.value = true
  try {
    // 获取课件信息dom以及榜单列表dom
    var node1 = document.getElementsByClassName('course-info')[0]
    var node2 = document.getElementsByClassName('praise-list-info')[0]
    const scale = 2
    // 将dom转换成图片
    console.time('图片下载时间')
    Promise.all([
      domtoimage.toPng(node1, {
        scale
      }),
      domtoimage.toPng(node2, {
        scale
      })
    ]).then(res => {
      console.timeEnd('图片下载时间')
      const courseInfoImgUrl = res[0]
      const praiseListImgUrl = res[1]
      const containerWidth = node2.clientWidth
      const containerHeight = node1.clientHeight + node2.clientHeight
      // 将两张图片进行拼接
      var canvas = document.createElement('canvas')
      canvas.width = containerWidth * scale
      canvas.height = containerHeight * scale
      var ctx = canvas.getContext('2d')
      var img1 = new Image()
      img1.src = courseInfoImgUrl
      img1.onload = () => {
        var img2 = new Image()
        img2.src = praiseListImgUrl
        img2.onload = async () => {
          ctx.scale(scale, scale)
          ctx.drawImage(img1, 0, 0, containerWidth, node1.clientHeight)
          ctx.drawImage(img2, 0, node1.clientHeight, containerWidth, node2.clientHeight)
          downloadImgUrl.value = canvas.toDataURL('image/png')
          isCompositing.value = false
          previewImgVisible.value = true
          emits('composeImgSuccess', downloadImgUrl.value)
          setTimeout(() => {
            previewImgVisible.value = false // 关闭预览弹窗
          }, 2000)
        }
      }
    })
  } catch (e) {
    // todo: 抛出异常   toast
    console.error('图片下载合成失败', e)
  }
}

// 监听群聊消息内容
watch(
  () => props.getThumbInfo,
  newValue => {
    if (newValue && Object.keys(newValue).length > 0) {
      // 收到其他学生点赞，存储到队列中
      otherStuThumbArr.value.push({
        avatar: newValue.avatar,
        likeNum: newValue.likeNum,
        timeStamp: new Date().getTime()
      })
    }
  },
  { deep: true, immediate: true }
)

onMounted(() => {
  console.log('【测试】初始化排行榜', props.praiseInfo, props.role)
  // praiseType = 3 自定义榜单榜单
  // publishType: 1 是全量展示,2是一个一个展示，一个个展示时需要动态更改卡片状态
  if (props.praiseInfo.praiseType === 3 && props.praiseInfo.publishType !== 1) {
    console.log('【测试】自定义榜单一个个展示')
    showStudentNum.value = props.praiseInfo.showStudentNum // 当前卡片是打开状态的学员个数
    setReverseCard() // 设置每张卡片状态（正面还是反面）
  }
  props.role === 'student' && checkMyselfCard() // 学生角色且非系统榜单检查是否有自己的名字
  loadLottie()
  // 轮询其他学生的点赞队列
  pollOtherStuThumbTask()
})
defineExpose({
  reverseCardHandler
})
</script>
<style lang="scss" scoped>
@use './style.scss' as *;
</style>
