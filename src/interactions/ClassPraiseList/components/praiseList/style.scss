.praise-list-container {
  width: 100%;
  height: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  z-index: 99;
  img {
    box-sizing: border-box;
  }
  .course-info {
    width: 100%;
    height: 6.9%;
    background: url('./imgs/course-info-bg.png');
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    display: flex;
    align-items: center;
    .plan-num {
      font-size: 18px;
      font-weight: bold;
      color: #FFFFFF;
      line-height: 20px;
      text-shadow: 0px 0px 7px rgba(183, 44, 0, 0.5);
      position: absolute;
      width: 16%;
      text-align: center;
    }
    .course-name, .date {
      font-size: 14px;
      font-weight: 600;
      color: #9E502E;
      line-height: 16px;
    }
    .course-name {
      margin-left: 18.3%;
      width: 100%;
    }
    .date {
      position: absolute;
      right: 2.6%;
      width: 100%;
      text-align: right;
    }
  }
  .praise-list-info {
    width: 100%;
    height: 100%;
    background: url('./imgs/praise-bg.png');
    background-size: 100% 100%;
  }
  .header {
    position: absolute;
    left: 11.2%;
    top: 11.1%;
    // width: 68.4%;
    width: 57.4%;
    height: 12%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .title {
      font-size: 26px;
      font-weight: bold;
      color: #FFFFFF;
      line-height: 31px;
    }
    .sub-title {
      margin-top: 1.4%;
      font-size: 14px;
      font-weight: 600;
      color: #FFFFFF;
      line-height: 16px;
    }
    p {
      width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin: 0;
    }
  }
  .lottie-player {
    position: absolute;
    width: 32.7%;
    height: auto;
    bottom: 76%;
    right: 4.7%;
  }
  
  .list-container {
    position: absolute;
    width: 100%;
    // padding: 0 6.8%;
    display: flex;
    z-index: 1;
    justify-content: space-around;
    flex-wrap: wrap;
    .num-card {
      position: relative;
      height: 0;
      background: #F3F3F3;
      border-radius: 8px;
      border: solid 4px #FFD7AD;
      box-sizing: content-box;
      backface-visibility:hidden;
      .card-item {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        text-align: center;
        .avatar {
          height: auto;
          display: block;
          margin: 0 auto;
          border-radius: 50%;
        }
        span {
          font-weight: 600;
          color: #343434;
          text-align: center;
          display: inline-block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .myself-logo {
        position: absolute;
        height: auto;
        top: 0;
      }
    }
    .reverse-ani {
      animation: reverse 400ms linear;
    }
    .crad-item-myself, .reverse-card {
      border: none;
    }
  }
  .list-empty {
    width: 65.5%;
    height: 42%;
    display: flex;
    flex-direction: column;
    background: #52210B;
    left: 0;
    right: 0;
    bottom: 20%;
    margin: auto;
    align-items: center;
    justify-content: center;
    box-shadow: 2px 2px 2px 0px rgba(138,85,61,0.6), inset 0px 1px 4px 0px #431A08;
border-radius: 8px;
    img {
      width: 36.1%;
      margin-bottom: 3.6%;
    }
    .empty-text {
      color: #FFFFFF;
    }
  }
  .board-less3 {
    position: absolute;
    padding: 0 5.2%;
    height: auto;
    bottom: 23.6%;
    width: 100%
  }
  .list-container-less3 {
    bottom: 27%;
    .num-less3 {
      width: calc(23.6% - 8px);
      padding-bottom: calc(23.6% - 8px);
      .card-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        padding-bottom: 38.8%;
        height: 0;
        .avatar {
          position: absolute;
          height: 100%;
          top: -30%;
          width: 38.8%;
          margin-bottom: 11.6%;
        }
        span {
          position: absolute;
          top: 90%;
          font-size: 21px;
          width: 88.8%;
          line-height: 25px;
        }
      }
      .myself-logo {
        width: 16.6%;
        left: 4.5%;
      }
    }
    .stu-num-2:first-child {
      margin-left: 15.7%;
    }
    .stu-num-2:last-child {
      margin-right: 15.7%;
    }
    .stu-num-3:first-child {
      margin-left: 10.7%;
    }
    .stu-num-3:last-child {
      margin-right: 10.7%;
    }
    .crad-item-myself {
      width: 23.6%;
      padding-bottom: 23.6%;
      background: url('./imgs/card-less3-myself.png');
      background-size: 100% 100%;
    }
    .reverse-card {
      width: 23.6%;
      padding-bottom: 23.6%;
      background: url('./imgs/reverse-card-less3.png');
      background-size: 100% 100%;
    }
  }
  .list-container-less10 {
    bottom: 20.4%;
    justify-content: flex-start;
    align-content: space-between;
    height: 44.7%;
    .num-less10 {
      width: calc(13.89% - 8px);
      padding-bottom: calc(13.89% - 8px);
      margin-right: 2.8%;
      .card-item {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: absolute;
        padding-bottom: 41.5%;
        height: 0;
        .avatar {
          position: absolute;
          height: 100%;
          top: -30%;
          width: 41.5%;
          margin-bottom: 7.5%;
        }
        span {
          font-size: 12px;
          width: 84.9%;
          line-height: 14px;
          position: absolute;
          top: 90%;
        }
      }
      .myself-logo {
        width: 24.5%;
        left: 7.5%;
      }
    }
    .num-less10:nth-child(5n+1) {
      margin-left: 9.4%;
    }
    .num-less10:nth-child(5n) {
      margin-right: 9.4%;
    }
    .crad-item-myself {
      width: 13.89%;
      padding-bottom: 13.89%;
      background: url('./imgs/card-less10-myself.png');
      background-size: 100% 100%;
    }
    .reverse-card {
      width: 13.89%;
      padding-bottom: 13.89%;
      background: url('./imgs/reverse-card-less10.png');
      background-size: 100% 100%;
    }
  }
  .list-container-less20 {
    top: 31.9%;
    justify-content: flex-start;
    align-content: space-between;
    .num-less20 {
      width: calc(19.65% - 8px);
      padding-bottom: calc(6.8% - 8px);
      margin-right: 1.3%;
      margin-bottom: 1.5%;
      .card-item {
        margin: 0 6.6%;
        padding-bottom: 21.3%;
        height: 0;
        .avatar {
          width: 21.3%;
          height: 100%;
          display: inline-block;
          position: absolute;
        }
        span {
          font-size: 12px;
          width: 61.3%;
          text-align: left;
          position: absolute;
          left: 26.6%;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .card-cont-left {
        text-align: left;
      }
    }
    .num-less20:nth-child(4n+1) {
      margin-left: 8.6%;
    }
    .num-less20:nth-child(4n) {
      margin-right: 8.6%;
    }
    .crad-item-myself {
      width: 19.65%;
      padding-bottom: 6.8%;
      background: url('./imgs/card-less20-myself.png');
      background-size: 100% 100%;
    }
    .reverse-card {
      width: 19.65%;
      padding-bottom: 6.8%;
      background: url('./imgs/reverse-card-less20.png');
      background-size: 100% 100%;
    }
  }
  .list-container-less30 {
    top: 31.6%;
    justify-content: flex-start;
    align-content: space-between;
    .num-less30 {
      width: calc(15.4% - 8px);
      padding-bottom: calc(5.7% - 8px); // 设置宽度
      margin-right: 1.3%;
      margin-bottom: 1.3%;
      .card-item {
        width: 89.8%;
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          font-size: 12px;
        }
      }
    }
    .num-less30:nth-child(5n+1) {
      margin-left: 8.6%;
    }
    .num-less30:nth-child(5n) {
      margin-right: 8.6%;
    }
    .crad-item-myself {
      width: 15.4%;
      padding-bottom: 5.7%;
      background: url('./imgs/card-less30-myself.png');
      background-size: 100% 100%;
    }
    .reverse-card {
      width: 15.4%;
      padding-bottom: 5.7%;
      background: url('./imgs/reverse-card-less30.png');
      background-size: 100% 100%;
    }
  }
  .board-less10 {
    position: absolute;
    bottom: 43.1%;
    padding: 0 5.2%;
    height: auto;
    width: 100%;
  }
  .board-less10-2 {
    position: absolute;
    bottom: 16.9%;
    padding: 0 5.2%;
    height: auto;
    width: 100%;
  }
  .logo {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20%;
    height: auto;
  }
  .footer-btn {
    position: absolute;
    bottom: 3.1%;
    width: 100%;
    padding-bottom: 9.1%;
    img {
      position: absolute;
      width: 9.1%;
      cursor: pointer;
      animation: scale 1.2s infinite linear;
    }
    #download-btn {
      right: 15.4%;
    }
    .show-btn {
      opacity: 1;
      z-index: 2;
    }
    .hidden-btn {
      opacity: 0;
      z-index: -999;
    }
    .click-style {
      animation: transStyle 120ms linear;
    }
    .thumb-container {
      width: 9.1%;
      padding-bottom: 9.1%;
      right: 2.3%;
      position: absolute;
      cursor: pointer;
      z-index: 2;
      img {
        width: 100%;
        height: auto;
        position: relative;
      }
      .thumb-num {
        padding: 2.8% 22.8%;
        display: inline-block;
        font-size: 14px;
        background: #FF7A00;
        border-radius: 12px;
        color: #FFFFFF;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        bottom: 10px;
      }
    }
    .fly-container {
      position: absolute;
      width: 15.7%;
      height: 257%;
      top: -250%;
      right: 0;
    }
  }
  .loading-mask {
    position: absolute;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    left: 0;
    top: 0;
    z-index: 9;
    display: flex;
    justify-content: center;
    align-items: center;
    .download-tips {
      width: 180px;
      height: 120px;
      background: rgba(0,0,0,0.7);
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      img {
        width: 38px;
        height: 38px;
      }
      span {
        display: inline-block;
        margin-top: 14px;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
      }
    }
  }
  .download-modal {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    left: 0;
    top: 0;
    z-index: 11;
    .prase-img {
      width: 80%;
      height: 85%;
      margin: 0 auto;
      top: 7%;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate3d(-50%, -50%, 0);
      border-radius: 8px;
      border: solid 4px #fff;
    }
  }
}
@keyframes scale {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
  100% {
    transform: scale(1);
  }
}
@keyframes transStyle {
  0% {
    transform: scale(1) rotate(0deg);
  }
  66.7% {
    transform: scale(1.1) rotate(-15deg);
  }
  100% {
    transform: scale(1) rotate(0deg);
  }
}
@keyframes reverse {
  to {
    transform: rotateY(-180deg);
  }
}
.teacher-praise {
  .header {
    .title {
      font-size: 19px;
      line-height: 19px;
    }
    .sub-title {
      font-size: 12px;
    }
  }
  .course-info {
    .plan-num {
      font-size: 16px;
    }
  }
}