<template>
  <div class="red-package-wrapper" data-log="均等红包wrapper">
    <audio :src="audioClick" class="hiden" ref="showClickMus"></audio>
    <transition name="slide-bg" appear>
      <div v-if="redPacketBgShow" class="red-packet-bg"></div>
    </transition>
    <transition name="prospect-bg" appear>
      <canvas v-if="prospectBgShow" class="prospect" id="prospect"></canvas>
    </transition>
    <transition name="light-bg" appear>
      <div v-if="lightBgShow" class="light-bg"></div>
    </transition>
    <div v-if="redPackageStep == 'open'" class="equal-red-open" data-log="红包open">
      <canvas v-if="redPackageStep == 'open'" id="chestGuide" class="chest-canvas"></canvas>
      <div @click="openRedPackage(1)" class="click-area"></div>
    </div>
    <div v-if="redPackageStep == 'opened'" class="get-red-result" data-log="红包opened">
      <canvas v-if="curAddCoin > 0" id="chestSuccess"></canvas>
      <canvas v-else id="chestFail"></canvas>
    </div>
  </div>
</template>
<script>
import { emitter } from '@/hooks/useEventBus'
import { studentGrabRedBag, submitRedPacketData } from '@/api/interaction/redPackage'
import logger from '@/utils/logger'
import { sensorEvent } from '@/utils/sensorEvent'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import _debounce from 'lodash/debounce'
import { usePlayPag } from '@/hooks/usePlayPag'

import audioClick from './audio/click.mp3'
export default {
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          ircMsg: {},
          roomMessage: {
            roomInfo: {
              commonOption: {},
            },
          },
        }
      },
    },
  },
  computed: {},
  data() {
    let stuInfoId = this.options.roomMessage.roomInfo.stuInfo.id,
      planId = this.options.roomMessage.roomInfo.commonOption.planId,
      classId = this.options.roomMessage.roomInfo.commonOption.classId,
      interactId = this.options.ircMsg.interactId || this.options.ircMsg.redPacket.interactId,
      oopsRedNotice = this.$t('classroom.interactions.redPacket.oopsRedNotice'),
      // oopsRedNotice = '你已经收到了这个红包',
      congratsRedResult = this.$t('classroom.interactions.redPacket.congrats')
      // congratsRedResult = '恭喜!'
    return {
      audioClick: audioClick,
      equalCoinShow: false,
      equalCoinX: 0,
      equalCoinY: 0,
      // 红包当前状态
      redPackageStep: '',
      currentStudentId: stuInfoId,
      // 0直播，1回放
      isPlayBack: this.options.roomMessage.roomInfo.isPlayBack,
      currentStuInfo: {},
      // 接口请求数据参数
      params: {
        classId, // 课堂ID
        planId: planId, // 讲次ID
        interactId: interactId, // 互动ID
      },
      // 打开红包的接口数据
      redResult: {
        redbagNum: 100,
        grabNum: 10,
        list: [
          {
            userId: 101,
            coin: 10,
          },
        ],
      },
      curAddCoin: 0,
      resultTips: {
        'congrats-red': congratsRedResult,
        'oops-red': 'Oops~',
      },
      tips: {
        // 抢到红包
        'congrats-red': '',
        // 回放过程中之前直播已经抢过红包
        'oops-red': `<p class="has-get-coin">${oopsRedNotice}</p>`,
      },
      timer: 0,
      timer1: 0,
      timer2: 0,
      isRedGrabing: false, // 老师发布结束信令自动领取金币
      disapperTime: 35000,
      containerDom: null,
      prospectPagDestroy: null,
      chestGuidePagDestroy: null,
      chestFailPagDestroy: null,
      chestSuccessPagDestroy: null,
      redPacketBgShow: false,
      lightBgShow: false,
      prospectBgShow: true,
      redPackageisClicked: false,
    }
  },
  beforeUnmount() {
    this.prospectPagDestroy && this.prospectPagDestroy()
    this.chestGuidePagDestroy && this.chestGuidePagDestroy()
    this.chestFailPagDestroy && this.chestFailPagDestroy()
    this.chestSuccessPagDestroy && this.chestSuccessPagDestroy()
  },
  unmounted() {
    this.userTrackLogger('普通红包结束')
    this.timer2 && this.flyCoin()
    this.closeRedPackage()
  },
  methods: {
    // 历史逻辑该如何处理，如果在教室中，且触发过一遍，则再进入不必展示红包了
    receiveMessage() {
      this.userTrackLogger('普通红包启动')
      this.initAqual()
    },
    // 普通红包的销毁逻辑无效，有自己的倒计时及消失处理的
    // 在closeRedPackage增加销毁组件操作
    // todo 分金币处理添加触发
    // todo 收到连续两条怎么半呢，待确认是否需要进行组件内数据清理
    destroyInteraction() {
      // const { ircMsg } = options
      this.sendLogger('结束互动自动领取金币')
      if (!this.isRedGrabing) {
        this.openRedPackage(1)
      }
    },
    setCoursewareAdaptation: _debounce(
      function () {
        const dom = document.getElementById('interactionController')
        this.containerDom = {
          offsetWidth: dom.offsetWidth,
          offsetHeight: dom.offsetHeight,
        }
      },
      300,
      {
        leading: true,
      },
    ),
    initAqual() {
      // 红包状态判断
      this.getstudentRedBagStatus()
      // 上报数据
      this.onOpenSendData()
    },
    /**
     * 上报数据
     */
    async onOpenSendData() {
      const { planId, classId } = this.options.roomMessage.roomInfo.commonOption
      const { interactId } = this.options.ircMsg
      const requestGetParams = {
        planId,
        classId,
        interactId,
      }
      const res = await submitRedPacketData(requestGetParams)
      if (res.code == 0) {
        console.log(res, '上报数据成功')
      }
      this.sendLogger('上报数据成功')
    },
    /*
     * 初始化红包出现判断红包状态
     */
    async getstudentRedBagStatus() {
      if (!this.isPlayBack) {
        this.openRedPackageOp()
        return false
      }
    },
    async openRedPackageOp() {
      this.sendLogger(`显示红包`)
      // 判断是否参与过打开红包
      this.redPackageStep = 'open'
      // 判断是否支持webgl
      let canvas = document.getElementById('prospect')
      let gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
      if (!gl) {
        this.sendLogger(`红包不支持webgl渲染`)
        console.error(`红包不支持webgl渲染`)
      }
      this.prospectPagDestroy = await usePlayPag().playMultiplePag(
        {
          path: 'prospect',
          domId: '#prospect',
        },
        null,
        () => {
          this.redPacketBgShow = true
          setTimeout(async () => {
            this.chestGuidePagDestroy = await usePlayPag().playMultiplePag({
              path: 'chest-guide',
              domId: '#chestGuide',
            })
            this.lightBgShow = true
          }, 800)
        },
      )
      // 5秒后自动领红包
      this.timer1 = setTimeout(() => {
        this.sendLogger('5秒后自动领红包')
        this.openRedPackage(1)
      }, 5000)
    },
    /*
     * 打开红包逻辑处理
     */
    async openRedPackage(clickButton) {
      if (this.redPackageisClicked) {
        this.sendLogger(`均等红包已经点击过`)
        return
      }
      this.redPackageisClicked = true
      // 宝箱点击音乐
      this.$nextTick(() => {
        this.$refs.showClickMus.play()
      })

      this.isRedGrabing = true
      this.timer1 && clearTimeout(this.timer1)
      this.curAddCoin = 0
      sensorEvent(
        'hw_classroom_interact_red_packet_click',
        this.options.roomMessage.roomInfo.commonOption,
        {
          interact_id: this.options.ircMsg.interactId,
          click_button: clickButton
        }
      )
      this.sendLogger(`点击${clickButton === 1 ? '大' : '小'}红包`)
      let res = await studentGrabRedBag(this.params)
      classLiveSensor.osta_ia_redpacket(this.options?.ircMsg?.interactId, res)
      this.sendLogger(`抢红包结果, res: ${JSON.stringify(res)}`)
      // 返回状态码不为0提示
      this.chestGuidePagDestroy && this.chestGuidePagDestroy()
      this.redPackageStep = 'opened'
      if (res.code != 0) {
        this.sendLogger(`抢红包结果异常`, 'submit', 'error')
        this.redPackageisClicked = false
        this.chestFailPagDestroy = await usePlayPag().playMultiplePag({
          path: 'chest-fail',
          domId: '#chestFail',
          replaceTextArr: [
            {
              index: 1,
              text: `Oops`,
            },
            {
              index: 0,
              text: res.msg,
            },
          ],
        })
      } else if (res.data.coin) {
        this.sendLogger(`抢红包成功`)
        this.curAddCoin = res.data.coin
        this.chestSuccessPagDestroy = await usePlayPag().playMultiplePag({
          path: 'chest-success',
          domId: '#chestSuccess',
          replaceTextArr: [
            {
              index: 0,
              text: `+${res.data.coin}`,
            },
          ],
        })
        // 定时器添加名称，手动关闭时销毁定时器，避免重复关闭
        this.timer2 && clearTimeout(this.timer2)
        this.timer2 = setTimeout(() => {
          this.flyCoin()
        }, 2000)
      }
      this.timer && clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.closeRedPackage()
      }, 4000)
    },
    // 飞金币
    flyCoin() {
      if (this.redPackageStep === 'opened' && this.curAddCoin) {
        this.equalCoinShow = true
        emitter.emit('addCoin', true, this.curAddCoin, true)
        // this.$bus.$emit('addCoin', true, this.curAddCoin, true)
      }
    },
    /*
     * 关闭红包
     */
    closeRedPackage() {
      this.sendLogger('关闭红包')
      this.timer && clearTimeout(this.timer)
      this.timer2 && clearTimeout(this.timer2)
      this.redPackageStep = ''
      this.redPackageisClicked = false
      // 增加销毁组件操作
      this.prospectBgShow = false
      this.redPacketBgShow = false
      this.lightBgShow = false
      this.$emit('close', 'redPacket')
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const interactionId = this.options?.ircMsg?.interactId || 'unknown'
      const interactionType = 'RedPacket'

      logger.send({
        tag: 'student.Interact',
        level,
        content: {
          msg: msg,
          interactType: interactionType,
          interactId: interactionId,
          interactStage: stage,
          params: '',
          response: '',
          timestamp: Date.now()
        }
      })

      console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
    },
    userTrackLogger(msg) {
      msg
      // logger.send({
      //   tag: 'userTrack',
      //   content: {
      //     msg,
      //     tag: 'student.Interact',
      //     interactType: 'Redpacket'
      //   }
      // })
    },
  },
}
</script>
<style lang="scss">
.hidden {
  display: none;
}
.red-packet-bg {
  position: absolute;
  width: 100%;
  top: 0;
  bottom: 0;
  left: 0;
  background: #000;
  z-index: 97;
}
.prospect {
  z-index: 100;
}
.prospect,
.light-bg,
.chest-canvas,
#chestSuccess,
#chestFail {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 50%;
  transform: translateX(-50%);
  pointer-events: none;
}
.light-bg {
  z-index: 98;
  background: url('./imgs/light-bg.png') center center no-repeat;
  background-size: cover;
}
.chest-canvas {
  z-index: 99;
}
#chestSuccess {
  z-index: 100;
}
#chestFail {
  z-index: 100;
}
.click-area {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 40vh;
  height: 40vh;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 101;
}
.redpacked-loading-tip {
  text-align: center;
  margin-top: 1.66vw;
  font-size: 1.33vw;
  font-weight: 500;
  color: #ffffff;
}

.slide-bg-enter-active {
  animation: 200ms opacShow linear forwards;
}

@keyframes opacShow {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 0.8;
  }
}

@keyframes opacShow1 {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.slide-bg-leave-active {
  animation: 200ms fadeHide linear forwards;
}
.light-bg-enter-active {
  animation: 200ms opacShow1 linear forwards;
}
@keyframes fadeHide {
  0% {
    opacity: 0.8;
  }

  100% {
    opacity: 0;
  }
}
.prospect-bg-leave-active {
  animation: 200ms fadeHide1 linear forwards;
}
@keyframes fadeHide1 {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
// 红包样式
.red-package-wrapper {
  width: 100%;
  height: 100%;

  .equal-red-open {
    height: 100%;
  }

  .get-red-result {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 100;
    pointer-events: auto;
  }

  ::-webkit-scrollbar {
    display: none;
  }
}
</style>
