@mixin makeBg($value, $width, $height) {
  background: url('@/interactions/TakePicture/img/#{$value}') no-repeat center center;
  background-size: 100% 100%;
  width: #{$width}px;
  height: #{$height}px;
  overflow: hidden;
}

.photoWallContainer {
  position: absolute;
  pointer-events: auto;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 97;
  overflow: hidden;
  font-family:
    -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    'Helvetica Neue', Helvetica, Arial, sans-serif;

  .toolbar,
  .small-class-toolbar {
    color: #fff;
    position: absolute;
    z-index: 12;
    left: 20px;
    top: 20px;

    .back {
      width: 30px;
      height: 30px;
      background: url('@/assets/images/live/icon-header-goback.png') no-repeat center center;
      background-size: 100%;
      cursor: pointer;
      display: block;
    }
  }

  .small-class-toolbar {
    left: 120px;
    top: 30px;
    position: fixed;
  }

  :deep(.photoShotContainer) {
    background: #323232;
    z-index: 10;
    position: fixed;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    display: flex;
  }

  :deep(.photoShotBtn) {
    @include makeBg('icon_camera_btn_bg.png', 58, 58);
    position: absolute;
    left: 50%;
    bottom: 20px;
    transform: translate(-50%, 0);
    cursor: pointer;

    &.disabled {
      cursor: not-allowed;
      pointer-events: none;

      i {
        background-image: url('@/interactions/TakePicture/img/icon_camera_disable.png');
      }
    }

    i {
      position: relative;
      transform: translate(-50%, -50%);
      left: 50%;
      top: 50%;
      display: inline-block;
      @include makeBg('icon_camera.png', 30, 30);
    }
  }

  & .hide {
    display: none;
  }
}

.photoWallContainer-mask {
  opacity: 0;
  background: rgba(0, 0, 0, 0.7);
  animation: show-mask 0.2s linear forwards;
}

@keyframes show-mask {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.countdownContainer,
.smallclass-countdownContainer {
  position: absolute;
  left: 50%;
  top: 14%;
  transform: translate(-50%, 15px);
  z-index: 20;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 17px;
  padding: 4px 12px 4px 4px;
  display: flex;
  justify-content: center;
  align-items: center;

  .countdown {
    font-size: 16px;
    font-family: 'SFProRounded-Medium,' SFProRounded;
    font-weight: 500;
    color: rgba(222, 226, 231, 1);
    line-height: 20px;
  }

  .countDown-icon {
    @include makeBg('icon_countdown.png', 26, 27);
    display: block;
    margin: 0 6px 0 0;
  }
}

.smallclass-countdownContainer {
  position: fixed;
  left: 50% !important;
  top: 25px;
}

.photoShotTip {
  @include makeBg('start_toast_bg.png', 379, 228);
  position: absolute;
  left: 50%;
  bottom: -10px;
  transform: translate(-50%, -50%);
  overflow: hidden;

  .close {
    display: block;
    @include makeBg('icon_close.png', 69, 23);
    position: absolute;
    left: 50%;
    bottom: 0;
    transform: translate3d(-50%, 0, 0);
    cursor: pointer;
  }

  li {
    font-size: 14px;
    font-weight: 400;
    color: #172b4d;
    line-height: 16px;
    height: calc(228px / 3 - 10px);
    padding: 0px 18px 0 55px;
    display: flex;
    align-items: center;

    &:nth-child(1) {
      background: url('@/interactions/TakePicture/img/step_01.png') no-repeat 8px center;
      background-size: 37px 29px;
    }

    &:nth-child(2) {
      background: url('@/interactions/TakePicture/img/step_02.png') no-repeat 8px center;
      background-size: 37px 29px;
    }

    &:nth-child(3) {
      background: url('@/interactions/TakePicture/img/step_03.png') no-repeat 8px center;
      background-size: 37px 29px;
    }
  }
}

.cameraAccessToast {
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.8);
  max-width: 380px;
  max-height: 55px;
  color: #fff;
  border-radius: 8px;
  position: absolute;
  left: 50%;
  bottom: 65px;
  transform: translate(-50%, -50%);
  overflow: hidden;
  padding: 8px 55px 10px 16px;

  p {
    line-height: 20px;
    font-size: 14px;
  }

  .showGraffiti {
    @include makeBg('icon_toast_right.png', 27, 26);
    font-size: 0;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translate(0%, -50%);
    cursor: pointer;
  }
}

:deep(.shortCutTip) {
  @include makeBg('shortCut_tip.png', 258, 60);
  padding: 18px 0 0 5px;
  box-sizing: border-box;
  overflow: hidden;
  position: absolute;
  margin-left: 5px;
  bottom: 35px;
  left: 66.5%;
  transform: translate(-50%, -50%);
  z-index: 12;

  p {
    font-family: 'Helvetica', 'PingFang SC', 'sans-serif', 'Arial', 'Verdana', 'Microsoft YaHei';
    line-height: 20px;
    font-size: 14px;
    color: #000;
  }
}

:deep(.wallConisTip) {
  position: absolute;
  // 动画中有缩放，会影响布局，需要动画中把transform: translate(-50%, -50%) scale(1.1) 加上
  left: 50%;
  top: 50%;
  box-sizing: border-box;
  width: 300px;
  background: linear-gradient(180deg, #fff0d6 0%, #ffffff 100%);
  box-shadow: inset -2px 2px 0px 0px #ffffff;
  border-radius: 16px;
  padding: 24px;
  margin: -150px 0 0 -150px;

  p {
    font-size: 18px;
    font-weight: 500;
    color: #172b4d;
    line-height: 20px;
    text-align: center;
    padding-top: 30px;
  }

  .title {
    position: relative;
  }

  .bg-img {
    @include makeBg('show_tip_bg.png', 160, 105);
    position: absolute;
    top: -50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .coins {
    margin-top: 16px;
    display: flex;
    justify-content: center;
    font-size: 24px;
    color: #ff8a3f;
    font-weight: 600;
    align-items: center;
    background: rgba(255, 185, 118, 0.16);
    border-radius: 8px;
    line-height: 100px;
  }

  i {
    display: block;
    @include makeBg('icon_add_coin.png', 68, 78);
    background-size: contain;
  }
}

:deep(.wallConisTipCorrectSmall) {
  margin: 0px 0 0 -216px;
}

:deep(.wallConisTipCorrectBig) {
  margin: -80px 0 0 -310px;
}

:deep(.notJoinTip) {
  @include makeBg('nojoin_toast_bg.png', 315, 155);
  box-sizing: border-box;
  position: fixed;
  top: 50%;
  left: 50%;
  z-index: 10;
  transform: translate(-50%, -50%);
  padding: 75px 50px 0 15px;

  &.notJoin {
    position: absolute;
  }

  p {
    color: #172b4d;
    font-size: 14px;
    line-height: 18px;
    font-weight: 500;
    text-align: center;
  }
}

:deep(.submitTipContainer) {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 16;
  background: rgba(0, 0, 0, 0.1);

  .tipContainer {
    box-sizing: border-box;
    z-index: 18;
    position: fixed;
    left: 50%;
    top: 30%;
    transform: translate(-50%, 0%);
    background: rgba(15, 25, 42, 0.95);
    border-radius: 10px;
    width: 200px;
    height: 85px;
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    p {
      display: block;
      color: #fff;
      text-align: center;
      width: 100%;
      padding-top: 10px;
      font-size: 16px;
      line-height: 20px;
    }
  }

  .error {
    background: url('@/interactions/TakePicture/img/icon_failed.png') no-repeat center center;
    background-size: 100%;
    width: 24px;
    height: 24px;
    display: block;
  }

  .loading {
    font-size: 24px;
    display: -webkit-inline-box;
    display: -webkit-inline-flex;
    display: inline-flex;
    position: relative;
    vertical-align: middle;
    color: #fff;
    animation: tipLoading 1s steps(60, end) infinite;

    &:before,
    &:after {
      content: '';
      display: block;
      width: 0.5em;
      height: 1em;
      box-sizing: border-box;
      border: 0.125em solid;
      border-color: currentColor;
    }

    &:before {
      border-right-width: 0;
      border-top-left-radius: 1em;
      border-bottom-left-radius: 1em;
      mask-image: linear-gradient(180deg, #000000 20%, rgba(0, 0, 0, 0.8) 95%);
    }

    &:after {
      border-left-width: 0;
      border-top-right-radius: 1em;
      border-bottom-right-radius: 1em;
      mask-image: linear-gradient(180deg, rgba(0, 0, 0, 0) 20%, rgba(0, 0, 0, 0.8) 95%);
    }
  }
}
