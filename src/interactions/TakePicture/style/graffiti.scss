@mixin makeBg($value, $width, $height) {
  background: url('@/interactions/TakePicture/img/#{$value}') no-repeat center center;
  background-size: 100% 100%;
  width: #{$width}px;
  height: #{$height}px;
  overflow: hidden;
}

$font-family:
  -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
  'Helvetica Neue', Helvetica, Arial, sans-serif !default;

.photoShotContainer {
  flex-direction: column;
  font-size: 14px;
  font-family: $font-family;
  font-weight: 400;
  color: rgba(222, 226, 231, 1);
  line-height: 16px;
}

.graffitiContainer {
  box-sizing: border-box;
  overflow: hidden;
  margin: 0 auto;
  border-radius: 10px;

  :deep(canvas) {
    width: 100% !important;
    height: 100% !important;
  }
}

.tools {
  width: 840px;
  margin: 35px auto 0 auto;
  height: 54px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .graffitiTools,
  .userTools {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .goCamera {
      display: flex;
      align-items: center;
      background: url('@/interactions/TakePicture/img/icon_rePhotoShot.png') no-repeat 4px center
        rgba(0, 0, 0, 0.5);
      background-size: 30px 30px;
      border-radius: 17px;
      height: 38px;
      width: 38px;
      overflow: hidden;
      transition: width 0.5s;
      white-space: nowrap;

      .cameraBtnName {
        padding-left: 40px;
      }

      cursor: pointer;
      &.disabled {
        cursor: not-allowed;
        pointer-events: none;
        // 加上滤镜
        filter: grayscale(100%);
      }
      &:hover {
        width: auto;
      }
    }
  }

  .graffitiTools {
    justify-content: flex-start;
  }
}

.icon {
  background-size: 100%;
  margin: 0px 10px 0 20px;
  cursor: pointer;
  font-size: 0;
  display: inline-block;
  width: 31px;
  height: 38px;
}

.camera {
  display: inline-block;
  @include makeBg('icon_rePhotoShot.png', 30, 30);
}

.eraser {
  @include makeBg('icon_eraser.png', 31, 38);

  &:hover,
  &.select {
    @include makeBg('icon_eraser_hover.png', 31, 38);
  }
}

.draw {
  @include makeBg('icon_draw.png', 31, 38);

  &:hover,
  &.select {
    @include makeBg('icon_draw_hover.png', 31, 38);
  }
}

.prev {
  @include makeBg('icon_prev.png', 31, 38);

  &:hover,
  &.select {
    @include makeBg('icon_prev_hover.png', 31, 38);
  }
}

.next {
  @include makeBg('icon_next.png', 31, 38);

  &:hover,
  &.select {
    @include makeBg('icon_next_hover.png', 31, 38);
  }
}

.submit-btn {
  padding: 5px 12px;
  margin: 0 0 0 32px;
  border: 0 none;
  background: linear-gradient(45deg, rgba(255, 213, 24, 1) 0%, rgba(255, 170, 10, 1) 100%);
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

:deep(.eraserCursor) {
  cursor: url('@/interactions/TakePicture/img/eraser_cursor.png'), pointer;
}

:deep(.penCursor) {
  cursor: url('@/interactions/TakePicture/img/pen_cursor.png'), pointer;
}

.anticon-caret-right {
  margin-left: 5px;
  margin-top: 2px;
}
