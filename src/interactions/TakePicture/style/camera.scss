@mixin makeBg($value, $width, $height) {
  background: url('@/interactions/TakePicture/img/#{$value}') no-repeat center center;
  background-size: 100% 100%;
  width: #{$width}px;
  height: #{$height}px;
  overflow: hidden;
}

$font-family:
  -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
  'Helvetica Neue', Helvetica, Arial, sans-serif !default;

.cameraContainer {
  box-sizing: border-box;
  height: calc(100% - 120px);
  overflow: hidden;
  margin: 25px auto 0 auto;
  display: flex;
  position: relative;

  .previewDom {
    position: absolute;
    z-index: 3;
  }

  // .cameraGrid {
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  //   left: 0;
  //   top: 0;
  //   z-index: 2;
  //   border: 0px solid rgba(255, 255, 255, 0.3);
  //   background: -webkit-linear-gradient(top, transparent 200px, rgba(255, 255, 255, 0.3) 201px),
  //     -webkit-linear-gradient(left, transparent 267px, rgba(255, 255, 255, 0.3) 268px);
  //   background-size: 268px 201px;
  // }
}

.photoShotControl {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 800px;
  padding: 0 100px 0 0;
  position: absolute;
  left: 50%;
  bottom: 15px;
  transform: translate(-50%, 0);
  z-index: 10;

  .changeCameraDropdown {
    display: inline-block;
    font-size: 14px;
    font-family: $font-family;
    font-weight: 400;
    color: rgba(222, 226, 231, 1);
    line-height: 20px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 100em;
    padding: 4px 15px 4px 4px;
    position: relative;

    i.anticon {
      margin-left: 5px;
    }

    i.changeCameraIcon {
      @include makeBg('icon_change_camera.png', 30, 30);
      display: block;
      margin-right: 10px;
    }
  }

  .photoShotBtn {
    position: static !important;
    transform: translate(0%, 0) !important;
    margin: 0 auto !important;
  }

  .graffitiBtn {
    font-family: $font-family;
    font-weight: 400;
    color: rgba(222, 226, 231, 1);
    line-height: 20px;
    font-size: 14px;
    display: flex;
    align-items: center;
    position: relative;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 100em;
    padding: 4px 4px 4px 20px;

    i {
      @include makeBg('icon_graffiti.png', 30, 30);
      display: block;
      margin-left: 10px;
      cursor: pointer;
    }

    span {
      display: block;
      color: rgba(162, 170, 184, 0.6);
      font-size: 12px;
      line-height: 14px;
      position: absolute;
      right: -238px;
      width: 130%;
    }
  }
}

.submitControl {
  z-index: 4;
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 250px;
  position: absolute;
  left: 50%;
  bottom: 15px;
  transform: translate(-50%, 0);
  text-align: center;
  font-size: 14px;
  font-family: $font-family;
  font-weight: 400;
  color: rgba(222, 226, 231, 1);
  line-height: 16px;
  cursor: pointer;

  i {
    background-size: 100%;
    width: 44px;
    height: 44px;
    display: block;
    margin-bottom: 4px;
  }
  .retryPhotoShot,
  .submit {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .retryPhotoShot {
    i {
      @include makeBg('icon_rePhotoShot.png', 44, 44);
    }
  }

  .submit {
    i {
      @include makeBg('icon_submit.png', 44, 44);
    }
  }
}

.ant-dropdown {
  top: auto;
  left: auto;
  right: 0;
  bottom: 50px;
  position: absolute;
}

.ant-dropdown-menu {
  background-color: transparent;
  box-shadow: none;
}

.ant-dropdown-link {
  display: flex;
  justify-content: left;
  align-items: center;
  color: rgba(255, 170, 10, 1);

  &:hover {
    color: rgba(255, 170, 10, 1) !important;
  }
}

.ant-dropdown-menu-item,
.ant-dropdown-menu-submenu-title {
  background: rgba(0, 0, 0, 0.5) !important;
  border-radius: 100em !important;
  color: #fff;
  line-height: 20px;
  font-family: $font-family;
  padding: 0px 35px 0 14px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  text-align: left;
}

.ant-dropdown-menu-item > a,
.ant-dropdown-menu-submenu-title > a {
  background: url('@/interactions/TakePicture/img/icon_change.png') no-repeat 0px center;
  background-size: 16px 16px;
  color: rgba(222, 226, 231, 1);
  text-indent: 25px;
  padding: 0;
  margin: 0;
  line-height: 50px;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    color: #fff;
  }
}
