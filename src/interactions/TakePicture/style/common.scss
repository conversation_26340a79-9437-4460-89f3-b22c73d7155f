.ignore-photoShot-modal {

  h3 {
    font-size: 18px;
    font-weight: 500;
    color: rgba(23, 43, 77, 1);
    line-height: 20px;
    padding: 12px 0;
  }

  p {
    font-size: 14px;
    font-weight: 500;
    color: rgba(162, 170, 184, 1);
    line-height: 16px;
  }

  .ant-modal-content {
    border-radius: 16px;
    font-size: 18px;
    font-weight: 600;
    color: rgba(23, 43, 77, 1);
    line-height: 20px;
    text-align: center;
  }

  .ant-modal-footer {
    border-top: 0 none;
    display: flex;
    padding: 0 16px 16px 16px;
    justify-content: center;
  }

  .ant-btn {
    height: 44px;
    border: none;
    color: #ffaa0a;
    background-color: #fff3dc;
    box-shadow: none;
    width: 50%;
    border-radius: 100em;
    font-size: 16px;
  }

  .ant-btn-primary {
    color: #fff
  }
}

.disabled,
.ant-btn[disabled] {
  cursor: not-allowed;
  pointer-events: none;
  opacity: 0.6;
}

.animateZoomIn {
  animation-duration: .3s;
  animation-name: zoomIn;
}

.animateSlideInDown {
  animation-duration: .3s;
  animation-name: slideInDown;
}

.animateTada-new {
  animation: bounce linear 3.4s forwards;
}

.animateTada {
  animation-duration: 1s;
  animation-name: tada;
}

.animateFadeIn {
  animation-duration: .5s;
  animation-name: fadeIn;
}

@keyframes bounce {

  0%,
  100% {
    opacity: 0;
    transform: scale(1);
  }

  6% {
    opacity: 1;
    transform: scale(1.1);
  }

  10% {
    opacity: 1;
    transform: scale(1);
  }

  94% {
    opacity: 1;
  }
}

@keyframes bounceIn-start1 {
  0% {
    opacity: 0;
    transform: scale(0);
  }

  100% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes bounceIn-start2 {
  0% {
    opacity: 1;
    transform: scale(1.1);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn-end {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale3d(0.3, 0.3, 0.3);
  }

  50% {
    opacity: 1;
  }
}

@keyframes tada {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale3d(1, 1, 1);
  }

  10%,
  20% {
    transform: translate(-50%, -50%) scale3d(0.9, 0.9, 0.9) rotate3d(0, 0, 1, -3deg);
  }

  30%,
  50%,
  70%,
  90% {
    transform: translate(-50%, -50%) scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, 3deg);
  }

  40%,
  60%,
  80% {
    transform: translate(-50%, -50%) scale3d(1.1, 1.1, 1.1) rotate3d(0, 0, 1, -3deg);
  }

  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale3d(1, 1, 1);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translate3d(-50%, -80%, 0);
    visibility: visible;
  }

  to {
    opacity: 1;
    transform: translate3d(-50%, -50%, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes tipLoading {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
  }

  100% {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}