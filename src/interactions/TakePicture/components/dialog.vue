<template>
  <Modal
    v-model:open="modalVisible"
    :closable="false"
    :maskClosable="false"
    class="ignore-photoShot-modal"
    :width="width"
    :destroyOnClose="true"
    centered
    :zIndex="2021"
    @cancel="handleCancel"
    @ok="handleConfirm"
  >
    <template #title>
      <h2 v-if="title">{{ title }}</h2>
    </template>
    <h3 v-if="content" v-html="content"></h3>
    <p v-if="description" v-html="description"></p>
    <template #footer>
      <AButton @click="handleCancel">
        {{ cancelText || t('common.cancel') }}
      </AButton>
      <AButton type="primary" :loading="loading" @click="handleConfirm">
        {{ confirmText || t('common.confirm') }}
      </AButton>
    </template>
  </Modal>
</template>

<script setup>
import { ref, computed } from 'vue'
import { Modal, Button as AButton } from 'ant-design-vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

defineOptions({
  name: 'PhotoWallDialog'
})

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: true
  },
  width: {
    type: Number,
    default: 380
  },
  title: {
    type: String,
    default: ''
  },
  content: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  cancelText: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: ''
  },
  loading: {
    type: Boolean,
    default: false
  },
  getContainer: {
    type: String,
    default: 'photoWallContainer'
  }
})

const emit = defineEmits(['update:modelValue', 'cancel', 'confirm'])
const disabled = ref(false)

const modalVisible = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

const handleCancel = () => {
  emit('update:modelValue', false)
  emit('cancel')
}

const handleConfirm = () => {
  disabled.value = true
  emit('confirm')
}
</script>

<style lang="less" scoped>
.ignore-photoShot-modal {
  :deep(.ant-modal-header) {
    padding: 16px 24px;
  }
  :deep(.ant-modal-body) {
    padding: 24px;
  }
  :deep(.ant-modal-footer) {
    padding: 10px 16px;
  }
}
</style>
