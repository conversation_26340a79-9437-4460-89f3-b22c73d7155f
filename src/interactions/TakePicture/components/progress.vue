<template>
  <div class="progressContainer" v-if="visible">
    <div class="progress">
      <div class="progress-text">{{ t('common.uploading') }}… {{ percent }}%</div>
      <AProgress
        :percent="percent"
        :stroke-color="{
          '0%': 'rgba(255,213,24,1)',
          '100%': 'rgba(255,170,10,1)'
        }"
        :strokeWidth="4"
        :showInfo="false"
      />
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { Progress as AProgress } from 'ant-design-vue'
defineOptions({
  name: 'takeProgress'
})
// 使用 i18n
const { t } = useI18n()

// 定义 props
defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  percent: {
    type: Number,
    default: 0
  }
})
</script>

<style lang="scss" scoped>
.progressContainer {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 16;
  background: rgba(0, 0, 0, 0.1);
}
.progress {
  box-sizing: border-box;
  z-index: 18;
  position: fixed;
  left: 50%;
  top: 30%;
  transform: translate(-50%, 0%);
  background: rgba(15, 25, 42, 0.95);
  border-radius: 10px;
  width: 200px;
  height: 62px;
}

.ant-progress-line {
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  width: 160px;
  transform: translate(-50%, 0%);
}
.progress-text {
  display: block;
  color: #fff;
  font-size: 14px;
  text-align: center;
  width: 100%;
  padding-top: 10px;
}
</style>
