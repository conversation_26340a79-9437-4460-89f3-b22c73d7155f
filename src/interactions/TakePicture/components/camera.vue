<template>
  <!-- 相机操作区域 -->
  <div class="photoShotContainer">
    <!-- 相机视频区 -->
    <div id="cameraContainer" class="cameraContainer" data-log="相机视频区">
      <img id="previewDom" class="previewDom" ref="previewDom" />
      <!-- <div class="cameraGrid"></div> -->
      <div id="photoShotVideo" ref="photoShotVideo" :style="{ width: curWidth + 'px' }"></div>
    </div>

    <!-- 拍照控制区 -->
    <div
      class="photoShotControl"
      v-show="showPhotoShotControl && !isPhotoShot"
      id="photoShotControl"
      data-log="拍照控制区"
      style="pointer-events: auto"
    >
      <div style="width: 40%; text-align: right">
        <!-- 摄像头列表 -->
        <div data-log="摄像头列表" class="changeCameraDropdown" v-if="deviceLists.length">
          <a href="javascript:;" class="ant-dropdown-link" @click="toggleLists" ref="dropdownRef">
            <i class="changeCameraIcon"></i>
            {{ deviceName }}
            <CaretUpFilled />
          </a>
          <div class="ant-dropdown" v-if="showLists">
            <ul class="ant-dropdown-menu">
              <div
                class="ant-dropdown-menu-item"
                v-for="item in deviceLists"
                :key="item.deviceId"
                @click="changeCameraId(item.deviceId, item.label)"
              >
                <a href="javascript:;">{{ item.label }}</a>
              </div>
            </ul>
          </div>
        </div>
      </div>
      <div style="width: 33.33%" ref="takePhotoControl">
        <!-- 拍照按钮 -->
        <div class="photoShotBtn" @click="photoShot" data-log="拍照按钮"><i></i></div>
      </div>
      <div class="graffitiBtn" data-log="拍照上墙中的涂鸦画板">
        {{ t('classroom.interactions.photoWall.graffitiBtnName') }}
        <i @click="goGraffiti"></i>
        <span>
          {{ t('classroom.interactions.photoWall.graffitiDesc1') }}<br />
          {{ t('classroom.interactions.photoWall.graffitiDesc2') }}
        </span>
      </div>
    </div>

    <!-- 提交控制区 -->
    <div class="submitControl" v-show="isPhotoShot" style="pointer-events: auto">
      <div class="retryPhotoShot" @click="retryPhotoShot"><i></i>{{ t('common.retry') }}</div>
      <div class="submit" @click="submit"><i></i>{{ t('common.submit') }}</div>
    </div>

    <audio
      :src="getAssetPath('/mp3/takePicture/photowall-button.mp3')"
      class="hide"
      ref="photoWallBtnSound"
    ></audio>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, nextTick, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { onClickOutside } from '@vueuse/core'
import { sensorEvent } from '@/utils/sensorEvent'
import { getValueByCloudConfigKey } from '@/utils/initConfig'
import { compress } from '@/utils/util'
import _debounce from 'lodash/debounce'
import { emitter } from '@/hooks/useEventBus'

import { useClassData } from '@/stores/classroom'
import { CaretUpFilled } from '@ant-design/icons-vue'
import { getAssetPath } from '@/utils/assetPath'
import { pictureWallLog } from '@/utils/web-log/HWLogDefine.js'
import { useRtcService } from '@/hooks/useRtcService'
defineOptions({
  name: 'takeCamera'
})
const { getRtcService } = useRtcService()
const store = useClassData()
// 使用 i18n
const { t } = useI18n()

// 定义 props
const props = defineProps({
  delay: {
    type: Number,
    default: 0
  },
  options: {
    type: Object,
    default: () => ({})
  }
})

// 定义 emits
const emit = defineEmits(['photoData', 'changeTakePhotoWay', 'submit'])
const desc1 = computed(() => t('classroom.interactions.photoWall.graffitiDesc')[0])
// 响应式数据
const deviceLists = ref([])
const deviceName = ref(null)
const isPhotoShot = ref(false) // 是否已拍照
const showPhotoShotControl = ref(false) // 是否显示拍照控制条
const showLists = ref(false)
const localDisplayVideoStatus = computed(() => !store.selfInfo.videoMute)
const curWidth = ref(960)

// DOM 引用
const previewDom = ref(null)
const photoShotVideo = ref(null)
const takePhotoControl = ref(null)
const photoWallBtnSound = ref(null)
const dropdownRef = ref(null) // 下拉菜单引用

// 使用 onClickOutside 替代 v-clickoutside 指令
onClickOutside(dropdownRef, () => {
  showLists.value = false
})

// 编码器配置
const highEncoderConfig = {
  // 高分辨率
  width: 960,
  height: 720,
  bitrate: 910,
  frameRate: 15
}

const lowEncoderConfig = {
  // 低分辨率
  bitrate: 80,
  frameRate: 10,
  width: 160,
  height: 120
}

// 方法
const resetWidth = _debounce(
  () => {
    nextTick(() => {
      const photoShotDom = document.querySelector('#photoShotVideo')
      if (photoShotDom.clientHeight) {
        curWidth.value = (photoShotDom.clientHeight * 4) / 3
      }
    })
  },
  300,
  { leading: true }
)

const loadDeviceList = async () => {
  const RtcService = getRtcService()
  const list = (await RtcService.getVideoDevices()) || []
  let cameraList = []
  list.forEach(item => {
    cameraList.push({
      label: item.label,
      deviceId: item.deviceId
    })
  })
  return cameraList
}

/**
 * 封装埋点事件
 */
const reSensorEvent = (eventName, params = {}) => {
  sensorEvent(eventName, props.options.roomMessage.roomInfo.commonOption, {
    interact_id: props.options.ircMsg.interactId,
    ...params
  })
}

const toggleLists = () => {
  showLists.value = true
  reSensorEvent('hw_classroom_interact_photograph_control', {
    control_type: '切换摄像头'
  })
  pictureWallLog.action('点击打开摄像头列表')
}

// 将base64转为Blob
const base64ToBlob = (base64, mimeType) => {
  // @log-ignore
  const bytes = window.atob(base64.split(',')[1])
  const ab = new ArrayBuffer(bytes.length)
  const ia = new Uint8Array(ab)
  for (let i = 0; i < bytes.length; i++) {
    ia[i] = bytes.charCodeAt(i)
  }
  return new Blob([ab], { type: mimeType })
}

/**
 * 拍照
 */
const photoShot = _debounce(() => {
  photoWallBtnSound.value.play() // 点击按钮打开音效
  reSensorEvent('hw_classroom_interact_photograph_control', {
    control_type: '拍照'
  })
  try {
    const videoContainer = photoShotVideo.value
    const video = videoContainer.querySelector('video')
    const canvas = document.createElement('canvas')
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight
    const context = canvas.getContext('2d')
    context.drawImage(video, 0, 0, canvas.width, canvas.height)
    let base64 = canvas.toDataURL('image/jpeg', 1)
    previewDom.value.src = base64
    previewDom.value.style.width = '100%'
    previewDom.value.style.height = '100%'
    // 输出图片需要变成制定小尺寸，最大宽1440，宽高等比，若宽<高，则高为1440
    const imageCompressionFactor =
      Number(getValueByCloudConfigKey('imageCompressionFactor', 'configValue')) || 0.75
    compress(base64, 1440, imageCompressionFactor).then(adjustBase64 => {
      // @log-ignore
      let blob = base64ToBlob(adjustBase64, 'image/jpeg')
      emit('photoData', { base64, blob })
      isPhotoShot.value = true
      pictureWallLog.action('点击拍照')
    })
  } catch (err) {
    pictureWallLog.error('点击拍照报错', err)
  }
}, 300)

/**
 * 重拍
 */
const retryPhotoShot = () => {
  pictureWallLog.action('点击重拍按钮')

  photoWallBtnSound.value.play() // 点击按钮打开音效
  previewDom.value.src = ''
  previewDom.value.style.width = ''
  previewDom.value.style.height = ''
  isPhotoShot.value = false
}

/**
 * 更改摄像头
 */
const changeCameraId = (deviceId, name) => {
  if (!deviceId || !name) return
  deviceName.value = name
  // 通知rtc切换摄像头
  emitter.emit('setDefaultVideoDevice', deviceId)
  const RtcService = getRtcService()
  RtcService.switchVideoDevice(deviceId)
  pictureWallLog.action('点击切换摄像头')
}

/**
 * 跳转到涂鸦
 */
const goGraffiti = () => {
  photoShotVideo.value.innerHTML = ''
  emit('changeTakePhotoWay', 'graffiti')
  reSensorEvent('hw_classroom_interact_photograph_control', {
    control_type: '涂鸦板'
  })
  pictureWallLog.action('点击切换到涂鸦画板')
}

/**
 * 提交数据
 */
const submit = () => {
  photoWallBtnSound.value.play() // 点击按钮打开音效
  emit('submit')
  reSensorEvent('hw_classroom_interact_photograph_submit', {
    submit_type: 1
  })
}

// 生命周期钩子
onMounted(() => {
  // 关闭特效
  window.addEventListener('resize', resetWidth)
  if (localDisplayVideoStatus.value) {
    const RtcService = getRtcService()
    RtcService.muteLocalVideo(true)
  }

  nextTick(async () => {
    deviceLists.value = await loadDeviceList() // 获取摄像头列表
    const RtcService = getRtcService()
    const currentDeviceId = RtcService.getCurrentVideoDevice()
    // 设置拍照上墙默认摄像头名称
    const currentDevice = deviceLists.value.filter(item => item.deviceId === currentDeviceId)
    if (currentDevice[0]?.label) {
      deviceName.value = currentDevice[0].label
    } else {
      deviceName.value = deviceLists.value.length && deviceLists.value[0]['label'].substring(0, 16)
    }
    resetWidth()
    showPhotoShotControl.value = true
    RtcService.setVideoEncoderConfiguration(highEncoderConfig)
    RtcService.createLocalVideo(document.getElementById('photoShotVideo'), false)
  })
})

onBeforeUnmount(() => {
  const RtcService = getRtcService()
  RtcService.setVideoEncoderConfiguration(lowEncoderConfig)
  if (localDisplayVideoStatus.value) {
    RtcService.muteLocalVideo(false)
    RtcService.createLocalVideo(document.getElementById('video-group-local'))
  }
  window.removeEventListener('resize', resetWidth)
  photoShotVideo.value.innerHTML = ''
})

// 暴露给父组件的方法
defineExpose({
  photoShot,
  retryPhotoShot,
  changeCameraId,
  goGraffiti,
  submit
})
</script>

<style lang="scss" scoped>
@use '@/interactions/TakePicture/style/camera.scss';

#photoShotVideo {
  :deep(canvas) {
    transform: none !important;
  }
}
</style>
