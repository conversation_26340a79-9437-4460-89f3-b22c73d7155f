<template>
  <div class="photoShotContainer" style="pointer-events: auto">
    <!-- 涂鸦工具栏 -->
    <div class="tools">
      <div class="graffitiTools">
        <i class="icon draw select" @click="graffitiDraw">画笔</i>
        <i class="icon eraser" @click="graffitiEraser">橡皮擦</i>
        <i class="icon prev" @click="graffitiPrev">上一步</i>
        <i class="icon next" @click="graffitiNext">下一步</i>
      </div>
      <div class="userTools">
        <div class="goCamera" @click="goCamera" :class="disableCamera">
          <span class="cameraBtnName">{{
            $t('classroom.interactions.photoWall.cameraBtnName')
          }}</span>
          <CaretRightOutlined />
        </div>
        <a-button shape="round" class="submit-btn" @click="confirmSubmit">{{
          $t('common.submit')
        }}</a-button>
      </div>
    </div>
    <div id="graffitiContainer" class="graffitiContainer"></div>
    <photoWallDialog
      @cancel="cancel"
      @confirm="confirm"
      :cancelText="$t('common.cancel')"
      :confirmText="$t('common.submit')"
      v-model="dialogVisible"
      :content="dialogContent"
    />
  </div>
</template>

<script>
import Graffiti from '@neosjs/h5-graffiti'
import { addClass, removeClass } from '@/utils/util'
import photoWallDialog from './dialog.vue'
import { sensorEvent } from '@/utils/sensorEvent'
import logger from '@/utils/logger'
import _debounce from 'lodash/debounce'
import { CaretRightOutlined } from '@ant-design/icons-vue'
export default {
  name: 'photoShotGraffiti',
  props: {
    cameraAccess: {
      style: Boolean,
      default: false
    },
    closeInteractive: {
      style: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      graffiti: null,
      dialogVisible: false,
      dialogContent: null,
      dialogDescription: null,
      graffitiInit: {
        dom: 'graffitiContainer',
        quality: 0.8,
        lineColor: '#FFCF1B',
        background: '#111111',
        lineSize: 2
      }
    }
  },
  components: {
    photoWallDialog,
    CaretRightOutlined
  },
  mounted() {
    this.$nextTick(async () => {
      // 初始化涂鸦
      this.graffiti = await Graffiti.init(this.graffitiInit)
      this.graffiti.setAttribute('class', 'penCursor')
      Graffiti.draw()
    })
  },
  computed: {
    disableCamera() {
      return !this.cameraAccess && 'disabled'
    }
  },
  methods: {
    setClass(target) {
      target.parentNode.children.forEach(item => {
        removeClass(item, 'select')
      })
      addClass(target, 'select')
    },
    /**
     * 返回到相机
     */
    goCamera() {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `点击切换到相机`,
          disableCamera: this.disableCamera
        }
      })
      Graffiti.destroy()
      this.graffiti = null
      // this.$bus.$emit('changeTakePhotoWay', 'camera')
      this.$emit('changeTakePhotoWay', 'camera')
      // this.$parent.changeTakePhotoWay('camera')
    },
    // 涂鸦画笔
    graffitiDraw(e) {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `选择画笔`
        }
      })
      this.setClass(e.target)
      this.graffiti.setAttribute('class', 'penCursor')
      Graffiti.draw()
    },
    // 涂鸦橡皮擦
    graffitiEraser(e) {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `选择涂鸦橡皮擦`
        }
      })
      this.setClass(e.target)
      this.graffiti.setAttribute('class', 'eraserCursor')
      Graffiti.eraser(20)
    },
    // 涂鸦上一步
    graffitiPrev() {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `选择涂鸦上一步`
        }
      })
      Graffiti.prev()
    },
    // 涂鸦下一步
    graffitiNext() {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `选择涂鸦下一步`
        }
      })
      Graffiti.next()
    },

    /**
     * 生成涂鸦图片
     */
    async createImage() {
      const res = await Graffiti.createImage()
      if (res.errCode === 0) {
        this.$emit('photoData', res.data)
      }
    },
    /**
     * 提交二次确认
     */
    confirmSubmit() {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `点击提交涂鸦按钮`
        }
      })
      this.dialogContent = `
        ${this.$t('classroom.interactions.photoWall.confirmNotice')[0]}<br>
        ${this.$t('classroom.interactions.photoWall.confirmNotice')[1]}
      `
      this.dialogVisible = true
    },
    // 二次确认取消按钮
    cancel() {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `点击弹窗取消提交`
        }
      })
      this.dialogVisible = false
    },
    // 二次确认确定按钮
    confirm: _debounce(async function () {
      logger.send({
        tag: 'student.Interact',
        content: {
          interactType: 'Wall',
          msg: `点击弹窗确认提交`
        }
      })
      this.dialogVisible = false
      await this.createImage()
      sensorEvent(
        'hw_classroom_interact_photograph_submit',
        this.options.roomMessage.roomInfo.commonOption,
        {
          interact_id: this.options.ircMsg.interactId,
          submit_type: 2
        }
      )
      this.$emit('submit')
    }, 500)
  },
  beforeUnmount() {
    Graffiti.destroy()
    // console.log('Graffiti destroy')
  },
  watch: {
    closeInteractive(val) {
      if (val) {
        logger.send({
          tag: 'student.Interact',
          content: {
            interactType: 'Wall',
            msg: `监听关闭互动，创建涂鸦图片`
          }
        })
        this.createImage()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@use '../style/graffiti.scss' as *;
</style>
