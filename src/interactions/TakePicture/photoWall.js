// 业务逻辑
import { submitUserPhoto, interactOpen, getWallStatus } from '@/api/interaction/photoWall'
export default class Main {
  constructor(opts = {}) {
    this.options = opts
    this.tipTimer = null
  }

  clearTipTimer() {
    if (this.tipTimer) {
      clearTimeout(this.tipTimer)
      this.tipTimer = null
    }
  }

  /**
   * 关闭提示
   * @param {*} dom
   * @param {*} duration
   */
  closeTip(dom, duration, callback = null, parentClassName = null) {
    if (!dom) return
    this.clearTipTimer()
    // 定时关闭提示
    if (duration > 0) {
      let tipTimer = setTimeout(() => {
        if (dom?.parentNode && parentClassName) {
          dom?.parentNode?.classList.remove(parentClassName)
        }
        dom?.parentNode?.removeChild(dom)
        clearTimeout(tipTimer)
        tipTimer = null
        callback && callback()
      }, duration)
    }
  }

  /**
   * 显示未参与提示
   * @param {*} dom
   * @param {*} duration
   */
  showNotjoinTip(options = {}) {
    const {
      tips,
      callback,
      className = null,
      duration = 3000,
      dom = 'photoWallContainer'
    } = options
    const _tipDom = document.createElement('div')
    _tipDom.setAttribute('class', `notJoinTip ${className ? className : ''}`)
    _tipDom.innerHTML = `
      <p>${tips}</p>
    `
    document.getElementById(dom)?.appendChild(_tipDom)
    this.closeTip(_tipDom, duration, callback)
  }

  /**
   * 提交成功，显示金币
   * @param {*} coins
   */
  showCoinsTip(options = {}) {
    const {
      duration = 3200,
      callback = null,
      dom = 'photoWallContainer',
      maskClass = 'photoWallContainer-mask'
    } = options
    // 展示金币时，需要关闭所有无关的弹层
    const submitTipContainer = document.getElementById('submitTipContainer')
    if (submitTipContainer) {
      submitTipContainer.parentNode.removeChild(submitTipContainer)
    }
    const _tipDom = document.createElement('div')

    if (maskClass && document.getElementById(dom)) {
      document.getElementById(dom)?.classList?.add(maskClass)
    }
    document.getElementById(dom)?.appendChild(_tipDom)
    this.closeTip(_tipDom, duration, callback, maskClass)
  }

  /**
   * 创建错误提示
   * @param {*} options
   */
  showSubmitTip(options = {}) {
    const { duration = 3000, callback = null, text, icon, dom = 'photoWallContainer' } = options
    const _duration = icon === 'loading' ? 0 : duration
    const submitTipContainer = document.getElementById('submitTipContainer')
    if (submitTipContainer) {
      submitTipContainer.parentNode.removeChild(submitTipContainer)
    }
    const _tipDom = document.createElement('div')
    _tipDom.setAttribute('id', 'submitTipContainer')
    _tipDom.setAttribute('class', 'submitTipContainer')
    _tipDom.innerHTML = `
      <div class="tipContainer">
        ${icon ? '<i class="' + icon + '"></i>' : ''}
        <p>${text}</p>
      </div>
    `
    document.getElementById(dom)?.appendChild(_tipDom)
    this.closeTip(_tipDom, _duration, callback)
  }

  /**
   * 初始化倒计时
   */
  initCountdownTime() {
    const localTime = +new Date() // 本机时间
    const timeOffset = this.options.roomMessage.roomInfo.commonOption.timeOffset // 本机时间和服务器时间的差值
    const times =
      this.options.ircMsg.totalTime * 1000 -
      (localTime + timeOffset - this.options.ircMsg.beginTime)
    return times
  }

  /**
   * 上报开启互动
   */
  async interactOpen() {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次ID
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId * 1, // classId
      interactId: this.options.ircMsg.interactId // 互动ID
    }
    await interactOpen(params)
  }

  /**
   * 提交照片
   */
  async submitPhoto(key) {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次ID
      classId: this.options.roomMessage.roomInfo.stuLiveInfo.classId,
      tutorId: this.options.roomMessage.roomInfo.stuLiveInfo.tutorId,
      photoPath: key,
      interactId: this.options.ircMsg.interactId, // 互动ID
      teacherId: this.options.roomMessage.roomInfo.teacherInfo.id, // 主讲老师ID（只有拍照上墙传，0722版本新增）
      version: 0 // 小黑板提交传 722、拍照上墙默认0（722代表当前需求版本722，如果为0保持老逻辑加积分，如果为722提交不加金币）
    }
    const res = await submitUserPhoto(params)
    return res
  }

  async getWallStatus() {
    const params = {
      planId: this.options.roomMessage.roomInfo.stuLiveInfo.planId * 1, // 讲次ID
      interactId: this.options.ircMsg.interactId // 互动ID
    }
    const res = await getWallStatus(params)
    return res
  }

  /**
   * 获取摄像头权限
   * @returns {Promise<boolean>} 是否获取到权限
   */
  async getCameraPermission() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true })
      // 获取到权限后，停止所有轨道
      stream.getTracks().forEach(track => track.stop())
      return true
    } catch (error) {
      console.error('获取摄像头权限失败:', error)
      return false
    }
  }
}
