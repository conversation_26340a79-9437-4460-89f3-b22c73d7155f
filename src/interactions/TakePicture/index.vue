<template>
  <div id="photoWallContainer" class="photoWallContainer">
    <!-- 倒计时 -->
    <div
      class="countdownContainer"
      id="countdownContainer"
      ref="countdownContainer"
      :style="countdownStyle"
    >
      <i class="countDown-icon"></i>
      <NeCountdown
        :time="countDownTime"
        ref="countDown"
        class="countdown"
        format="mm:ss"
        @complete="countdownComplete"
      >
      </NeCountdown>
    </div>

    <div
      class="toolbar"
      id="toolbar"
      v-if="showCamera || showGraffiti"
      style="pointer-events: auto"
    >
      <i class="back" @click="goClassRoom"></i>
    </div>

    <!-- 调起相机按钮 -->
    <div
      class="photoShotBtn"
      id="photoShotBtnId"
      ref="photoShotBtn"
      @click="openClassCamera"
      :class="disableCamera"
      v-if="!isSubmit && isCanOpenCamera"
    >
      <i></i>
    </div>

    <!-- 相机拍照功能-->
    <camera
      v-if="showCamera && !showGraffiti"
      :delay="2000"
      :options="options"
      @changeTakePhotoWay="changeTakePhotoWay"
      @submit="submitPhoto"
      @photoData="getPhotoData"
    />
    <!-- 涂鸦功能 -->
    <graffiti
      v-if="showGraffiti && !showCamera"
      :options="options"
      @submit="submitPhoto"
      @photoData="getPhotoData"
      :cameraAccess="cameraAccess"
      @changeTakePhotoWay="changeTakePhotoWay"
      :closeInteractive="closeInteractive"
    />

    <!-- 有相机权限提示 -->
    <div
      class="photoShotTip animateSlideInDown"
      ref="photoShotTip"
      style="pointer-events: auto"
      v-if="showPhotoShotTip && !isSubmit && !userClosePhotoShotTip"
    >
      <div class="close" @click="closePhotoShotTip"></div>
      <ul>
        <li>{{ $t('classroom.interactions.photoWall.hasAccessNotice')[0] }}</li>
        <li>{{ $t('classroom.interactions.photoWall.hasAccessNotice')[1] }}</li>
        <li>{{ $t('classroom.interactions.photoWall.hasAccessNotice')[2] }}</li>
      </ul>
    </div>
    <!-- 无相机权限提示 -->
    <div class="cameraAccessToast animateFadeIn" v-if="!cameraAccess" style="pointer-events: auto">
      <p>
        {{ $t('classroom.interactions.photoWall.notAccessNotice')[0] }}<br />
        {{ $t('classroom.interactions.photoWall.notAccessNotice')[1] }}
      </p>
      <div class="showGraffiti" @click="openClassGraffiti">去涂鸦</div>
    </div>

    <!--结束互动提交照片提示：拍照、涂鸦 共用-->
    <photoWallDialog
      @cancel="dialogCancel"
      @confirm="dialogConfirm"
      :cancelText="$t('common.no')"
      :confirmText="confirmText"
      v-model="dialogVisible"
      :content="dialogContent"
      :description="dialogDescription"
    />
    <!--上传进度条：拍照、涂鸦 共用-->
    <uploadProgress :percent="percent" :visible="showUploadProgress" />

    <audio
      :src="getAssetPath('/mp3/takePicture/photowall-begin.mp3')"
      class="hide"
      ref="photoWallBeginSound"
    ></audio>
    <audio
      :src="getAssetPath('/mp3/takePicture/photowall-button.mp3')"
      class="hide"
      ref="photoWallBtnSound"
    ></audio>
    <audio
      :src="getAssetPath('/mp3/takePicture/photowall-submit-success.mp3')"
      class="hide"
      ref="photoWallSuccessSound"
    ></audio>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import NeCountdown from '@/components/NeCountdown/index.vue'
import PhotoWall from './photoWall.js'
import camera from './components/camera.vue'
import graffiti from './components/graffiti.vue'
import photoWallDialog from './components/dialog.vue'
import uploadProgress from './components/progress.vue'
import { delay, addClass, removeClass } from '@/utils/util'
import Upload from '@/utils/upload'
import { emitter } from '@/hooks/useEventBus'
import logger from '@/utils/logger'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import { sensorEvent } from '@/utils/sensorEvent'
import { useI18n } from 'vue-i18n'
import { useMediaAccess } from '@/hooks/useMediaAccess.js'
import { message } from 'ant-design-vue'
import { getAssetPath } from '@/utils/assetPath'
import { pictureWallLog } from '@/utils/web-log/HWLogDefine.js'

// 定义组件名称
defineOptions({
  name: 'TakePictureView'
})

const { cameraAccess, getCameraAccess } = useMediaAccess()

// 定义props
const props = defineProps({
  options: {
    type: Object,
    default: () => ({})
  }
})

const { t, tm } = useI18n()
// 定义emit
const emit = defineEmits(['close'])

// 响应式状态
const showPhotoShotTip = ref(false) // 有摄像头提示
const userClosePhotoShotTip = ref(false) // 是否用户手动关闭提示
const dialogVisible = ref(false)
const isCountdownComplete = ref(false) // 倒计时是否已经结束
const countDownTime = ref(0) // 倒计时时长
const showCamera = ref(false) // 是否显示相机
const showGraffiti = ref(false) // 是否显示涂鸦
const isSubmit = ref(false) // 是否已提交
const submiting = ref(false) // 提交中
const submitTimer = ref(null) // 提交定时器
const submitCountdown = ref(5) // 提交倒计时
const confirmText = ref(`${t('common.yes')} (5)`)
const percent = ref(0) // 上传进度
const showUploadProgress = ref(false)
const photoData = ref(false) // 图片数据
const closeInteractive = ref(false) // 互动是否结束，主要是给涂鸦用
const isInteraction = ref(false) // 是否进入了互动操作
const isCanOpenCamera = ref(true) // 拍照按钮
const rainCoin = ref(0)
const dialogContent = ref('')
const dialogDescription = ref('')

// DOM引用
const countdownContainer = ref(null)
const photoShotTip = ref(null)
const photoShotBtn = ref(null)
const countDown = ref(null)
const photoWallBeginSound = ref(null)
const photoWallBtnSound = ref(null)
const photoWallSuccessSound = ref(null)

// 计算属性
const countdownStyle = computed(() => {
  return showCamera.value || showGraffiti.value ? `left:67.5%` : ''
})

const disableCamera = computed(() => {
  return !cameraAccess.value && 'disabled'
})

// 初始化photoWall实例
const photoWall = new PhotoWall({
  ...props.options
})

// 生命周期钩子
onMounted(async () => {
  pictureWallLog.info('拍照上墙启动')
  countDownTime.value = photoWall.initCountdownTime() || 0
  await getWallStatus() // 获取异常恢复数据
  start()
})

// 方法
async function start() {
  emitter.emit('photoWallInteractId', props.options.ircMsg.interactId) // 广播互动ID
  photoWall.interactOpen() // 上报互动打开
  // 获取摄像头权限状态
  await getCameraAccess()
  if (!cameraAccess.value) {
    pictureWallLog.warning(`拍照上墙初始化摄像头无权限`, 'init')
    emitter.emit('showMediaSecurityAccess', 'camera')
    return
  }
  showPhotoShotTip.value = true
  // 将互动数据存入在本地，为订正提供互动数据支持
  localStorage.setItem('photoWallOptions', JSON.stringify(props.options))
}

function changeTakePhotoWay(way) {
  if (way === 'graffiti') {
    showCamera.value = false
    showGraffiti.value = true
    pictureWallLog.info('切换到涂鸦板成功')
  } else {
    showCamera.value = true
    showGraffiti.value = false
    pictureWallLog.info('切换到拍照成功')
  }
}

function closePhotoShotTip() {
  pictureWallLog.action('点击关闭有权限提示层')
  showPhotoShotTip.value = false
  userClosePhotoShotTip.value = true // 收到关闭提示层
}

function openClassCamera() {
  nextTick(() => {
    photoWallBtnSound.value?.play() // 点击按钮打开音效
  })
  showCamera.value = !showCamera.value
  isInteraction.value = true
  pictureWallLog.info('打开拍照弹层成功')
  addSmallClassStyle()
  sensorEvent(
    'hw_classroom_interact_photograph_click',
    props.options.roomMessage.roomInfo.commonOption,
    {
      interact_id: props.options.ircMsg.interactId
    }
  )
}

function sendPeerMessageToTutor(isBusy) {
  const content = {
    type: 170,
    isFunction: 1,
    msg: '拍照上墙',
    parameter: {
      cameraBusy: isBusy
    }
  }
  window.ChatClient.PeerChatManager.sendPeerMessage(
    [{ nickname: props.options.roomMessage.roomInfo.configs.tutorIrcId }],
    JSON.stringify(content),
    99
  )
}

function openClassGraffiti() {
  showGraffiti.value = !showGraffiti.value
  isInteraction.value = true
  pictureWallLog.info('打开涂鸦板成功')
  addSmallClassStyle()
}

/**
 * 返回课堂
 */
function goClassRoom() {
  pictureWallLog.info('点击返回课堂')
  showPhotoShotTip.value = true
  showCamera.value = showGraffiti.value = isInteraction.value = false
  removeSmallClassStyle()
}

/**
 * 获取各组件发来的图片数据
 */
function getPhotoData(res) {
  pictureWallLog.info('[拍照上墙]获取图片数据')
  photoData.value = res
}

/**
 * 异常恢复
 */
async function getWallStatus() {
  try {
    const { code, data } = await photoWall.getWallStatus()
    pictureWallLog.info('拍照上墙异常恢复')

    if (code === 0) {
      isSubmit.value = data.isSubmit
      if (!isSubmit.value) {
        pictureWallLog.info('未提交答案,需要恢复')
        photoWallBeginSound.value?.play() // 拍照上墙互动开始音效
      } else {
        pictureWallLog.info(`已提交答案,不需要恢复`)
      }
    }
  } catch (error) {
    pictureWallLog.error(`拍照上墙异常恢复错误`, error)
  }
}

/**
 * 结束互动
 */
async function interactiveOver() {
  showPhotoShotTip.value = false
  await delay(800) // 这里主要为了等待涂鸦的图片
  // 没有图片数据、没有提交过、也不是处于提交中，表示没参与互动！
  if (!photoData.value && !isSubmit.value && !submiting.value) {
    pictureWallLog.warning('没参与拍照上墙结束')
    destroy()
    return
  }
  // 有图片数据并且没有提交过，则需要进行弹层提示提交作业！
  if (photoData.value && !isSubmit.value && !submiting.value) {
    pictureWallLog.info('有图片数据，弹框提醒提交')
    clearSubmitTimer()
    dialogContent.value = tm('classroom.interactions.photoWall.endNotice')[0]
    dialogDescription.value = tm('classroom.interactions.photoWall.endNotice')[1]
    dialogVisible.value = true

    submitTimer.value = setInterval(() => {
      submitCountdown.value--
      confirmText.value = `${t('common.yes')} (${submitCountdown.value})`
      if (submitCountdown.value === 0) {
        clearInterval(submitTimer.value)
        submitTimer.value = null
        destroy()
      }
    }, 1000)
    return
  }
  // 已经提交过，则说明已经参与过互动了
  destroy()
}

/**
 * 提交数据
 */
async function submitPhoto() {
  pictureWallLog.info('[拍照上墙]提交数据')
  // 如果提交中或者老师已经关闭互动，禁止提交
  if (submiting.value && closeInteractive.value) {
    pictureWallLog.info('提交中或者老师已经关闭互动')
    return
  }
  dialogVisible.value = showPhotoShotTip.value = false
  submiting.value = showUploadProgress.value = true
  isCanOpenCamera.value = false
  const upload = new Upload({
    scene: 'pictureWall'
  })
  if (
    photoData.value.base64 &&
    photoData.value.base64.substring(photoData.value.base64.indexOf(',') + 1).length > 0
  ) {
    pictureWallLog.info('base64有内容开始拍照上墙文件上传')
  } else {
    pictureWallLog.error('拍照文件为空，无法上传')
  }

  try {
    const { key: uploadKey } = await upload.putFile({
      filePath: 'pictureWall.jpg',
      file: photoData.value.blob,
      progress: p => {
        percent.value = p
      },
      success: res => {
        pictureWallLog.success(`上传照片成功, url: ${res.url}`)
        classLiveSensor.osta_ia_photowall_submit(props.options?.ircMsg?.interactId, res)
      },
      fail: err => {
        showUploadProgress.value = false
        submiting.value = false // 恢复提交状态
        percent.value = 0 // 上传进度清0
        photoWall.showSubmitTip({
          text: t('common.uploadFailed'),
          icon: 'error',
          callback: () => {
            isCountdownComplete.value && destroy() // 倒计时结束，销毁互动
          }
        })
        pictureWallLog.error('上传照片失败', err)
      }
    })
    // 提交数据到后台
    if (uploadKey) {
      showUploadProgress.value = false
      // 订正拍照上墙不弹金币奖励
      photoWall.showSubmitTip({
        text: `${t('common.submiting')}...`,
        icon: 'loading',
        duration: 0
      })
      const submitRes = await photoWall.submitPhoto(uploadKey)
      pictureWallLog.info('拍照上墙提交地址完毕')

      if (submitRes.code === 0 && submitRes.data) {
        const { rightCoin } = submitRes.data
        showGraffiti.value = showCamera.value = false
        isSubmit.value = true
        removeSmallClassStyle()
        nextTick(() => {
          photoWallSuccessSound.value?.play() // 提交成功音效
        })

        // rewardType == 2 或者 为0 或者空时才会有提交奖励金币, 答对场景奖励金币枚举值1
        emitter.emit('uploadSuccess', {
          tips: t('common.sumbittedSuccessfully'),
          coins: props.options.ircMsg.rewardType !== 1 ? rightCoin : 0
        })
        photoWall.showCoinsTip({
          callback: () => {
            if (closeInteractive.value) {
              return destroy()
            }
            rainCoin.value = rightCoin
            // 金币是0不能展示飞金币
            if (rightCoin) {
              emitter.emit('addCoin', true, rightCoin)
            }
            isInteraction.value = false
            // 按照需求，提交成功后，不能销毁互动，倒计时还要继续走到。所以，这个只隐藏拍照按钮
            if (photoShotBtn.value) {
              photoShotBtn.value.style.display = 'none'
            }
          }
        })
        pictureWallLog.success(`获得金币成功, ${JSON.stringify(submitRes)}`)
      } else {
        submiting.value = false // 恢复提交状态
        percent.value = 0 // 上传进度清0
        photoWall.showSubmitTip({
          text: t('common.submissionFailed'),
          icon: 'error',
          callback: () => {
            isCountdownComplete.value && destroy() // 倒计时结束，销毁互动
          }
        })
        pictureWallLog.error(`获得金币失败, ${JSON.stringify(submitRes)}`)
      }
    }
  } catch (e) {
    e
    message.error({
      content: t('setting.reportLogs.uploadfailed'),
      duration: 2
    })
    showUploadProgress.value = false
    isCanOpenCamera.value = true // 返回后可以重新点击拍照按钮
  }
}

// 弹层取消按钮
function dialogCancel() {
  pictureWallLog.action('弹框提醒取消按钮点击')
  clearSubmitTimer()
  destroy()
}

// 弹层确定按钮
function dialogConfirm() {
  pictureWallLog.action('弹框提醒提交按钮点击')
  clearSubmitTimer()
  confirmText.value = `${t('common.submiting')}...`
  submitPhoto()
}

/**
 * 清除按钮上的定时器
 */
function clearSubmitTimer() {
  if (submitTimer.value) {
    clearInterval(submitTimer.value)
    submitTimer.value = null
  }
}

/**
 * 倒计时结束
 */
function countdownComplete() {
  // 倒计时结束了，产品经理说，不需要做啥。等待老师主动关闭互动
}

/**
 * 关闭互动
 */
async function destroyInteraction() {
  if (!document.getElementById('correctPhotoWallContainer')) {
    sendPeerMessageToTutor(2)
  }
  closeInteractive.value = true
  isCanOpenCamera.value = false
  emitter.emit('photoWallInteractId', null)
  await interactiveOver()
}

/**
 * 增加小班样式
 */
function addSmallClassStyle() {
  // 小班样式适配
  nextTick(() => {
    addClass(document.getElementById('countdownContainer'), 'smallclass-countdownContainer')
    addClass(document.getElementById('toolbar'), 'small-class-toolbar')
  })
}

/**
 * 移除小班样式
 */
function removeSmallClassStyle() {
  removeClass(document.getElementById('countdownContainer'), 'smallclass-countdownContainer')
  removeClass(document.getElementById('toolbar'), 'small-class-toolbar')
}

// /**
//  * 日志上报
//  */
// function sendLogger(msg, stage = '', level = 'info') {
//   const interactionId = props.options?.ircMsg?.interactId || 'unknown'
//   const interactionType = 'TakePicture'

//   logger.send({
//     tag: 'student.Interact',
//     level,
//     content: {
//       msg: msg,
//       interactType: interactionType,
//       interactId: interactionId,
//       interactStage: stage,
//       params: '',
//       response: '',
//       timestamp: Date.now()
//     }
//   })

//   console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
// }

/**
 * 销毁互动
 */
function destroy() {
  clearSubmitTimer()
  emitter.emit('updateAchievement', 'update') // 更新金币数
  emitter.off('changeTakePhotoWay')
  emitter.emit('photoWallInteractId', null)
  pictureWallLog.info('拍照上墙结束')
  emit('close', 'take_picture')
}

// 监听器
watch(
  showCamera,
  val => {
    const isBusy = val ? 1 : 2
    sendPeerMessageToTutor(isBusy) //开启拍照上墙，告诉辅导摄像头被占用
  },
  { deep: true, immediate: true }
)

watch(isInteraction, val => {
  if (val) {
    addClass(document.getElementById('interactionController'), 'index-1000')
  } else {
    removeClass(document.getElementById('interactionController'), 'index-1000')
  }
})

// 暴露方法给父组件
defineExpose({
  destroyInteraction
})
</script>

<style lang="scss" scoped>
@use '@/interactions/TakePicture/style/common.scss';
@use '@/interactions/TakePicture/style/app.scss';
</style>
