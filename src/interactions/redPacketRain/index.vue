<template>
  <div class="class-content">
    <div class="red-rain-wrapper" :style="{ width: rainWidth }" data-log="红包雨">
      <audio :src="audioSrc" class="hidden" ref="rainPot"></audio>
      <audio :src="clickAudioSrc" class="hidden" ref="redRainClick"></audio>
      <iframe
        @load="handleIframeLoad"
        :src="rainSrc"
        ref="rainRed"
        id="rainRed"
        :width="rainWidth"
        :height="rainHeight"
        frameborder="0"
        v-if="rainGameShow && rainSrc"
        allow="autoplay"
      ></iframe>
      <div class="redpacket-loading" v-if="rainLoading">
        <div
          :style="{
            position: 'absolute',
            width: `${getLoadingWidth}px`,
            height: `${getLoadingHeight}px`,
            ...getLoadingMargin
          }"
        >
          <lottie-player
            mode="normal"
            loop
            autoplay
            :src="getAssetPath('/lottiefiles/redRain/loading/index.json')"
          ></lottie-player>
          <div class="redpacked-loading-tip">
            {{ $t('classroom.interactions.redRain.loading') }}
          </div>
        </div>
      </div>
      <div class="mask" v-show="rainPotShow || rainCoinShow"></div>
      <div
        @animationend="potWarpEnd"
        :class="['red-rain-pot', { 'pot-move': !potRemove }, { 'pot-remove': potRemove }]"
        ref="potWrap"
        v-show="rainPotShow"
        :style="{ width: `${getPotWidth}px`, height: `${getPotHeight}px`, ...getPotMargin }"
      >
        <lottie-player
          id="pot"
          loop
          mode="normal"
          :src="getAssetPath('/lottiefiles/redRain/pot/index.json')"
        >
        </lottie-player>
        <div :style="getRainCoinStyle" class="rain-coin">+{{ rainCoin }}</div>
        <div
          ref="rainPotBtn"
          @pointerdown="rainPotBtnDown"
          @pointerup="rainPotBtnUp"
          :style="getRainBtnStyle"
          class="rain-button"
        >
          <span :style="getRainBtnText">
            {{ $t('classroom.interactions.redRain.collect') }}
            ({{ rainTime }}s)</span
          >
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { emitter } from '@/hooks/useEventBus'
import {
  submitRedPacketData,
  submitRedPacketRainData,
  getRedPacketRainStatus,
  getRedPacketRain
} from '@/api/interaction/redPackage'
import '@lottiefiles/lottie-player'
import { message } from 'ant-design-vue'
import * as classLiveSensor from '@/utils/sensorTrack/classLive'
import _debounce from 'lodash/debounce'
let resizeContentOb = null
import audioSrc from './audio/rain-pot.mp3'
import clickAudioSrc from './audio/red-rain-click.mp3'
import { useRequestInfo } from '@/hooks/useRequestInfo'
import { useClassData } from '@/stores/classroom'
const store = useClassData()
import { getAssetPath } from '@/utils/assetPath'
import { redRainLog } from '@/utils/web-log/HWLogDefine.js'
export default {
  name: 'RedPacketRain',
  props: {
    options: {
      type: Object,
      default: () => {
        return {
          ircMsg: {},
          roomMessage: {
            roomInfo: {
              commonOption: {}
            }
          }
        }
      }
    }
  },
  // i18n,
  computed: {
    rainSrc() {
      if (this.rainHost) {
        const { redbagDuration, redbagQueue, bombQueue, action, parameter } = this.options.ircMsg
        // const url = `https://pa-oss-cn-prod.thethinkacademy.com/game_test/game-online/3.1.10-beta/index.html`
        let url = `https://pa-oss-cn-prod.thethinkacademy.com/game_test/game-online/3.1.10-beta-1/index.html`
        // redbagDuration=10&redbagQueue=%5B1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1%5D&bombQueue=%5B1,2,2,2,1%5D&action=start&level=3&schoolCode=8601`
        // const url = this.rainHost
        if (store?.redBagInfo?.onlinePath) {
          url = store.redBagInfo.onlinePath
        }
        const src = `${
          url
        }?redbagDuration=${redbagDuration}&redbagQueue=[${redbagQueue}]&bombQueue=[${bombQueue}]&action=${action}${
          parameter ? '&' + parameter : ''
        }&schoolCode=${this.schoolCode}`
        this.sendLogger(`加载红包雨Src：${src}`)
        // window.thinkApi.ipc.send('test:url', src)
        return encodeURI(src)
      }
      return ''
    },
    containerSize() {
      return {
        width: (((this.containerDom?.offsetWidth / 16) * 9) / 3) * 4,
        height: this.containerDom?.offsetHeight
      }
    },
    rainWidth() {
      return `${this.containerSize.width}px`
    },
    rainHeight() {
      return `${this.containerSize.height}px`
    },
    getPotWidth() {
      const ratio = this.containerSize.width / this.containerSize.height

      if (ratio > 1.167) {
        const h = this.containerSize.height - 64
        return h * 1.167
      } else {
        const w = this.containerSize.width - 168
        return w / 1.167
      }
    },
    getPotHeight() {
      return this.getPotWidth / 1.167
    },
    getPotMargin() {
      return {
        top: `${(this.containerSize.height - this.getPotHeight) / 2}px`,
        left: `${(this.containerSize.width - this.getPotHeight) / 2}px`
      }
    },
    getLoadingWidth() {
      const ratio = this.containerSize.width / this.containerSize.height

      if (ratio > 1.108) {
        const h = this.containerSize.height - 235
        return h * 1.108
      } else {
        const w = this.containerSize.width - 418
        return w / 1.108
      }
    },
    getLoadingHeight() {
      return this.getLoadingWidth / 1.108
    },
    getLoadingMargin() {
      return {
        top: `${(this.containerSize.height - this.getLoadingHeight) * 0.38}px`,
        left: `${(this.containerSize.width - this.getLoadingWidth) / 2}px`
      }
    },
    getRainCoinStyle() {
      return {
        left: `${(273 / 594) * this.getPotWidth}px`,
        top: `${(213 / 509) * this.getPotHeight}px`,
        fontSize: `${(44 / 509) * this.getPotHeight}px`,
        lineHeight: `${(44 / 509) * this.getPotHeight}px`
      }
    },
    getRainBtnStyle() {
      return {
        width: `${(164 / 594) * this.getPotWidth}px`,
        height: `${(61 / 509) * this.getPotHeight}px`,
        top: `${(301 / 509) * this.getPotHeight}px`,
        left: `${(215 / 594) * this.getPotWidth}px`
      }
    },
    getRainBtnText() {
      return {
        marginTop: `-${(8 / 509) * this.getPotHeight}px`,
        fontSize: `${(18 / 509) * this.getPotHeight}px`
      }
    },
    getCoinWidth() {
      const ratio = this.containerSize.width / this.containerSize.height

      if (ratio > 1.046) {
        const h = (229 / 572) * this.containerSize.height
        return h * 1.046
      } else {
        const w = (240 / 763) * this.containerSize.width
        return w / 1.046
      }
    },
    getCoinHeight() {
      return this.getCoinWidth / 1.046
    },
    getCoinMargin() {
      return {
        top: `${(this.containerSize.height - this.getCoinHeight) / 2}px`,
        left: `${(this.containerSize.width - this.getCoinWidth) / 2}px`
      }
    }
  },
  data() {
    let stuInfoId = this.options.roomMessage.roomInfo.stuInfo.id,
      planId = this.options.roomMessage.roomInfo.commonOption.planId,
      classId = this.options.roomMessage.roomInfo.commonOption.classId,
      interactId = this.options.ircMsg.interactId || this.options.ircMsg.redPacket.interactId
    return {
      // 红包雨状态
      audioSrc: audioSrc,
      clickAudioSrc: clickAudioSrc,
      schoolCode: '',
      rainLoading: true,
      rainStatus: 'start',
      rainCoin: 0,
      rainTime: 3,
      rainTimer: null,
      rainPotShow: false,
      rainCoinShow: false,
      // rainGameShow: true,
      rainGameShow: false,
      rainHost: '',
      rainDestory: null,
      rainStartTime: Date.now(),
      isAddingCoin: false,
      potRemove: false,
      needPlay: false,
      currentStudentId: stuInfoId,
      // 接口请求数据参数
      params: {
        classId, // 课堂ID
        planId: planId, // 讲次ID
        interactId: interactId // 互动ID
      },
      containerDom: document.getElementsByClassName('class-interaction-layer')[0]
    }
  },
  mounted() {
    this.$nextTick(() => {
      // todo 还是不能自适应宽度, 线上就是这样，不能中间更改尺寸的，问过毕尧如果非得改，那么性能损耗过大，会卡的
      window.addEventListener('resize', this.setCoursewareAdaptation)
    })
    if (!document.hidden) {
      this.rainGameShow = true
    }
    window.addEventListener('visibilitychange', this.visibilitychange.bind(this))
  },
  unmounted() {
    this.userTrackLogger('红包雨结束')
    resizeContentOb?.disconnect()
    resizeContentOb = null
    window.removeEventListener('message', this.rainLoad, false)
    if (location.pathname.includes('largeClass.html')) {
      window.removeEventListener('resize', this.setCoursewareAdaptation)
    }
    window.removeEventListener('visibilitychange', this.visibilitychange.bind(this))
  },
  methods: {
    getAssetPath,
    visibilitychange() {
      if (document.visibilityState === 'hidden') {
        if (this.rainLoading) {
          this.rainGameShow = false
        }
      } else if (document.visibilityState === 'visible') {
        if (!this.rainGameShow && this.rainStatus !== 'end') {
          this.rainGameShow = true
        }
      }
    },
    async receiveMessage(noticeContent) {
      const { action } = noticeContent
      this.$nextTick(async () => {
        this.userTrackLogger('红包雨启动')
        // 通过这个判断组件是否之前存在了
        const isExistInstance = this.schoolCode !== ''
        if (isExistInstance && action === 'start') {
          this.sendLogger(`红包雨--已存在`, 'start', 'error')
          return
        }
        if (action === 'play') {
          const interactId = localStorage.getItem('redPacketRainKey')
          const status = localStorage.getItem('redPacketRainStatus')
          // 时间超过M（10）秒不恢复 https://wiki.zhiyinlou.com/pages/viewpage.action?pageId=179547448
          if (isExistInstance && interactId === noticeContent.interactId && status === 'end') {
            this.sendLogger(`红包雨--互动重复`, 'play', 'error')
            return
          }
          if (isExistInstance && (status === '' || status === 'start')) {
            this.play()
            this.sendLogger(`红包雨--初始化完成开始玩`)
            return
          }
          const sendTime = this.options.sendTime
          // const nowTime =
          //   this.$store.state.smallClass?.baseData?.commonOption?.timeOffset + +new Date()
          const nowTime = this.options.roomMessage.roomInfo.commonOption?.timeOffset + +new Date()
          console.log('redpacketTime', nowTime)
          if (nowTime > sendTime + 10000) {
            this.$emit('close', 'red_packet_rain')
            this.sendLogger(`红包雨--超时`, 'timeout', 'error')
            return
          }
          // const { stuInfo, planInfo } = this.$store.state.smallClass.baseData
          const { stuInfo, planInfo } = this.options.roomMessage.roomInfo
          const hasReport = await getRedPacketRainStatus({
            interactId: noticeContent.interactId,
            planId: planInfo.id,
            userId: stuInfo.id.toString()
          })
          if (hasReport) {
            this.$emit('close', 'red_packet_rain')
            this.sendLogger(`红包雨--参与过`, 'check', 'error')
            return
          }
        }
        this.initRandom()
      })
    },
    async destroy() {
      this.sendLogger(
        `老师结束了互动, 状态：${this.rainStatus}, 是红包雨, 是否添加金币: ${this.isAddingCoin}`
      )
      const needToast = this.rainStatus === 'start' || this.rainStatus === 'play'
      if (needToast) {
        const status = this.rainStatus
        const timer = setTimeout(() => {
          if (status === 'start') {
            this.sendLogger(`倒计时销毁红包雨`)
            this.$emit('close', 'red_packet_rain')
          }
          clearTimeout(timer)
        }, 2000)
        // msg: this.$t('classroom.interactions.redRain.toastTip'),
        message.info(this.$t('classroom.interactions.redRain.toastTip'))
      }
      if (this.rainStatus === 'play') {
        await this.getPushData()
      }
      if (this.isAddingCoin) {
        await this.getCoinStatus()
      }
      if (this.rainStatus && this.rainStatus !== 'start') {
        this.sendLogger(`直接销毁红包雨`)
        this.$emit('close', 'red_packet_rain')
      }
    },
    destroyInteraction() {
      this.destroy()
    },
    checkRedPacketInfo() {
      getRedPacketRain()
        .then(res => {
          if (+res.code !== 0) return
          const redPacketRain = res.data && res.data.redbagrainPackage

          this.rainHost = redPacketRain && redPacketRain.onlinePath
          this.sendLogger(`红包雨游戏地址:${this.rainHost}`)
          // else {
          //   // 二次确认提示, 点确认再请求接口重试
          //   this.$Modal.confirm({
          //     class: 'modal-simple apu-header center-modal',
          //     title: `${this.$t('common.networkErr')}(${this.$t(
          //       'classroom.interactions.redRain.title'
          //     )})`,
          //     content: this.$t('classroom.interactions.redRain.NetworkFailTip'),
          //     okText: this.$t('classroom.interactions.redRain.RetryBtn'),
          //     cancelText: this.$t('common.cancel'),
          //     width: px2vh(416),
          //     centered: true,
          //     zIndex: 3000,
          //     onOk: () => {
          //       this.checkRedPacketInfo()
          //     }
          //   })
          // }
        })
        .catch(() => {})
    },
    setCoursewareAdaptation: _debounce(
      function () {
        const dom = document.getElementsByClassName('class-interaction-layer')[0]
        this.containerDom = {
          offsetWidth: dom?.offsetWidth,
          offsetHeight: dom?.offsetHeight
        }
      },
      300,
      {
        leading: true
      }
    ),
    initRandom() {
      this.$sensors.track('hw_red_package_rain_load', {
        result: 'start',
        url: this.rainSrc,
        is_local: this.rainHost.includes('localhost')
      })
      window.addEventListener('message', this.rainLoad, false)
      localStorage.setItem('redPacketRainStatus', '')
      this.onOpenSendData()
      this.checkRedPacketInfo()
      const { getSchoolCode } = useRequestInfo()
      this.schoolCode = getSchoolCode()
    },
    playCoinLottie() {
      this.potRemove = true
      this.rainTime = 3
      this.rainTimer = null
      if (+this.rainCoin === 0) {
        this.isAddingCoin = false
        this.rainGameShow = false
        if (this.rainDestory) this.rainDestory()
        this.$emit('close', 'red_packet_rain')
        return
      }
      this.rainCoinShow = true
      console.log('看下执行定向金币 -1', this.rainCoin)
      emitter.emit('addCoin', true, this.rainCoin, true)
      // todo 暂不需要金币互动
      // this.$bus.$emit('addCoin', true, this.rainCoin, true)
      this.$emit('close', 'red_packet_rain')
    },
    getCoinStatus() {
      return new Promise(resolve => {
        this.rainDestory = resolve
      })
    },
    getPushData() {
      return new Promise(resolve => {
        try {
          this.$refs.rainRed.contentWindow.postMessage(
            {
              type: 'getData'
            },
            '*'
          )
        } catch (err) {
          this.sendLogger(`红包雨message信息`, 'error', {
            err: 'dom加载失败',
            meg: err,
            dom: `rainRed:${this.rainSrc}`
          })
        }
        this.rainDestory = resolve
      })
    },
    play() {
      if (this.$refs.rainRed && !this.rainLoading) {
        this.rainStatus = 'play'
        try {
          this.$refs.rainRed.contentWindow.postMessage(
            {
              type: 'play'
            },
            '*'
          )
          this.sendLogger(`发送play红包雨`)
        } catch (err) {
          console.error(err)
          this.sendLogger(`红包雨message信息`, 'error', {
            err: 'dom加载失败',
            meg: err,
            dom: `rainRed:${this.rainSrc}`
          })
        }
      } else {
        this.needPlay = true
      }
    },
    async rainLoad(event) {
      const type = event.data.type
      const typeList = ['init', 'push', 'error']
      if (!typeList.includes(type)) {
        // 不是红包雨信息
        return
      }

      this.sendLogger(`红包雨message信息`, type == 'error' ? 'error' : 'info', {
        data: event.data
      })
      if (type === 'init') {
        this.rainLoading = false
        console.log('发送红包雨消息，play', this.needPlay)

        if (this.needPlay) {
          this.needPlay = false
          this.play()
        }
        this.sendLogger(`红包雨init成功`)
        this.$sensors.track('hw_red_package_rain_load', {
          result: 'success',
          url: this.rainSrc,
          is_local: this.rainHost.includes('localhost'),
          load_duration: Date.now() - this.rainStartTime
        })
      } else if (type === 'push') {
        clearTimeout(this.rainTimer)
        const { planId, classId } = this.options.roomMessage.roomInfo.commonOption
        const { interactId } = this.options.ircMsg
        const requestGetParams = {
          planId,
          classId,
          interactId,
          userId: this.currentStudentId,
          coin: event.data.coin,
          grabRedbagQueue: event.data.grabRedbagQueue,
          grabBombQueue: event.data.grabBombQueue
        }
        this.rainCoin = event.data.coin
        this.rainPotShow = true
        this.rainStatus = 'end'
        this.isAddingCoin = true
        // this.$refs.rainPot?.play()
        this.sendLogger(`红包雨游戏push完成${JSON.stringify(requestGetParams)}`)
        this.countDown()
        const res = await submitRedPacketRainData(requestGetParams)
        if (+res.code !== 0 && +res.code !== 500) {
          // 500 已经上报过
          this.$Message.error(res.msg)
          localStorage.setItem('redPacketRainReportError', JSON.stringify(requestGetParams))
        }
        classLiveSensor.osta_ia_coinshower(interactId, this.rainCoin)
      } else if (type === 'error') {
        this.$sensors.track('hw_red_package_rain_load', {
          result: 'fail',
          url: this.rainSrc,
          is_local: this.rainHost.includes('localhost')
        })
      }
      localStorage.setItem('redPacketRainKey', this.params.interactId)
      localStorage.setItem('redPacketRainStatus', this.rainStatus)
    },
    countDown() {
      if (this.rainTime === 0) {
        this.sendLogger('游戏结束倒计时开始收金币动效')
        this.playCoinLottie()
      } else {
        this.rainTimer = setTimeout(() => {
          this.rainTime--
          this.countDown()
        }, 1000)
      }
    },
    potWarpEnd() {
      this.sendLogger('展示获得金币数量弹窗')
      if (this.potRemove) {
        // this.rainPotShow = false
        // this.potRemove = false
      }
      document.getElementById('pot').play()
    },
    rainPotBtnDown() {
      this.$refs.rainPotBtn.style.transform = 'scale(0.9)'
    },
    rainPotBtnUp() {
      this.$refs.rainPotBtn.style.transform = 'scale(1)'
      this.rainTimer && setTimeout(this.rainPotClick, 200)
    },
    rainPotClick() {
      if (this.rainTimer) {
        clearTimeout(this.rainTimer)
      }
      this.sendLogger('游戏结束点击collect按钮开始收金币动效')
      this.rainTimer && this.$refs.redRainClick.play()
      this.playCoinLottie()
    },
    /**
     * 上报数据
     */
    async onOpenSendData() {
      const { planId, classId } = this.options.roomMessage.roomInfo.commonOption
      const { interactId } = this.options.ircMsg
      const requestGetParams = {
        planId,
        classId,
        interactId
      }
      const res = await submitRedPacketData(requestGetParams)
      if (res.code == 0) {
        console.log(res, '上报数据成功')
      }
      this.sendLogger('上报数据成功')
    },
    handleIframeLoad() {
      this.sendLogger('加载H5成功-红包雨', 'info', {
        rainSrc: this.rainSrc
      })
      // setTimeout(() => {
      //   this.isIframeLoaded()
      // }, 1000)
    },
    // 判断iframe是否加载完成
    // 这里会造成跨域报错 SecurityError Error: Blocked a frame with origin "(file://" from accessing a cross-origin frame.)

    isIframeLoaded() {
      // 获取 iframe 元素
      var iframe = document.getElementById('rainRed')

      // 获取 iframe 中的 DOM
      var iframeDocument = iframe.contentDocument || iframe.contentWindow.document

      // 获取 iframe 中的某个元素
      var elementInIframe = iframeDocument.getElementsByClassName('pixi-canvas')
      console.log('elementInIframe', elementInIframe)
      if (elementInIframe) {
        // iframe 中的元素已经加载完成
        this.sendLogger('iframe加载完成,获取到了红包雨dom')
      } else {
        // 红包雨没有加载完成
        this.sendLogger('iframe加载未完成,没有获取到红包雨dom')
      }
    },
    /**
     * 日志上报
     */
    sendLogger(msg, stage = '', level = 'info') {
      const content = {
        msg: msg,
        interactId: this.options?.ircMsg?.interactId,
        interactStage: stage
      }
      redRainLog[level === 'error' ? 'error' : 'info'](msg, content)
      // logger.send({
      //   tag: 'student.Interact',
      //   level,
      //   content: {
      //     msg: msg,
      //     interactType: 'RedPacketRain',
      //     interactId: this.options.ircMsg.interactId,
      //     interactStage: stage,
      //     params: '',
      //     response: '',
      //     timestamp: Date.now()
      //   }
      // })

      // console.log(`[RedPacketRain][${this.options.ircMsg.interactId}] ${msg}`, { stage, level })
    },
    userTrackLogger(msg) {
      const content = {
        msg: msg,
        interactId: this.options?.ircMsg?.interactId
      }
      redRainLog.action(msg, content)

      // msg
      // logger.send({
      //   tag: 'userTrack',
      //   content: {
      //     msg,
      //     tag: 'student.Interact',
      //     interactType: 'red_packet_rain'
      //   }
      // })
    }
  }
}
</script>
<style lang="scss">
.class-content {
  height: 100%;
  width: 100%;
  background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAP4AAADwBAMAAADSssQSAAAAAXNSR0IArs4c6QAAABtQTFRFMjIyMzMzNDQ0NTU1NjY2Nzc3ODg4OTk5Ojo6QUDc4AAAA9ZJREFUeNrtnSt320AQhUeSHdOwGAYaFhoaGgYWGgo6zDROU9+fXaC2lmOvtI/Z2SfbE7Dn3M3cydWnWYWAHRER0U8Av4blAgDehvUWwO9h2QHAYVhvAFyGZQMAx2H9AgDPw/oE4GNYrgDgdVj3AD6H5RKgR7usAVymdzldd3kCgB+TIvZKEWS4y9f9LtvrUbTfRfzVphZBDruMTug8+vFIxPWc//+av4mgmV362V0UxaISAQDv12IhjZI7GpXcea7kbkSQfuGalZymb8iq5BRHMfJNoymCbAv33zkrjmJp5JubXTRL7qBdcvO+sShc+5J7IMJwF82S0/eNfcNfG/lm+bhYdAv37NqqFSLuCnfnr+E/EqFRchoN39o3PA2/s/aNY8P/cPWN74xh4j6pkrtzn3avfDLOGDMivGeMGd+0EyXH3vDfFO5T7MKSMaZ94z9jTIuQyhit4gFDs3CdM4aO+8wKl8U3YhlD4Ruehn+y9Y1jw3f2TYiGP/m3z1PGUIkgoYyhEkFCGUPlG/LxUGngG3q4S2ve8C2zOclkDKVvyLbhM2VzEskYat9QEfxvG4r/zfqGl/9NBaWI+N9IRDH871j5XxH8z0pEuvzvpfK/dPhfV/mfP/63DcX/XH2TJ//Tf6bNh/9ZBaVS+F9TKv87VP4XF/+jnPlfV/lfQvyvT4P/+cvmZfC/lo//MWfzzOf/SuF/Zr5Jnv8to+d/FJj/LXzyvy4H/teF4n/vlf9V/nfjPsmB79D87yu7+T9XGOM9YzDxP+uMUfmfH/5HgfnfovI/96AkljEq/yuS/104+V9bOP/bx8v/LH2TNP9j8E3m/G9WRF7zf02B/M8tKJFExqj8L0b+t2Hjf86+KWr+T5r/9Wz8z1c2Z8wYVhCzFP638sL/zHzjjf9Jzf81BfI/rzDGkf+5+qbyP/kXPhb8z1c2z3P+T1+Enxc+WfM/zhel2fG/VQT8T3+IMZ/5P0sRkvzvmZP/MV2UIPEPPlT+NxZB0h98KH3+T5T/zYsg6Rc+ifA/2WGsxPgfe1Aqiv/tBPifmQhR/vdpxv8ELmMWw//2kvzP7vZ9gMuY+c3/5cP/BGBM5X9R8j+py5h58j9tEWID32uzr29uhWAMmRVuqfzP2zAWCWUMM/7XVP7nbeA7T/5nO4xVBP+jOv9X+V8c/K9n43+cvpHlf6858D/WixKCH3yo/E+f/wnBmFT4n6+LElnwP4qf/23q/F8R/K9n438S2TzEBx9C8T8D95lljI21iHT5n9f/vhMp/2MPSrHxP+nLmEnxP4qf/23Y+J8MjAnN//4Ax3H9/N6BeDoAAAAASUVORK5CYII=);
  background-repeat: repeat;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  pointer-events: auto;
}
.hidden {
  display: none;
}
.redpacket-loading {
  width: calc(100% / 16 * 9 / 3 * 4);
  margin-left: calc((100% - 100% / 16 * 9 / 3 * 4) / 2);
  position: absolute;
  background-image: url('./imgs/redpacket-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}
.redpacket-loading::before {
  position: absolute;
  content: '';
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
}
.redpacked-loading-tip {
  text-align: center;
  margin-top: 1.66vw;
  font-size: 1.33vw;
  font-weight: 500;
  color: #ffffff;
}
.red-rain-wrapper {
  z-index: 1000;
  height: 100%;
}

.mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
}
.red-rain-pot {
  position: absolute;
  height: 100%;
  width: calc(100% / 16 * 9 / 3 * 4);
  margin-left: calc((100% - 100% / 16 * 9 / 3 * 4) / 2);
}

.rain-coin {
  position: absolute;
  font-weight: bold;
  color: #ff8c33;
}

.rain-button {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  font-weight: 600;
  color: #fff;
  background: url('./imgs/rain-button.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  transform-origin: center;
  transition: all 0.2s linear;
  cursor: pointer;
}

.pot-move {
  animation: potSize 0.32s linear;
}

.pot-remove {
  animation: potRemove 0.12s linear forwards;
}

@keyframes potSize {
  0% {
    transform: scale(0.6);
    filter: opacity(0%);
  }
  12.5% {
    filter: opacity(100%);
  }
  50% {
    transform: scale(1.1);
  }
  75% {
    transform: scale(0.94);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes potRemove {
  0% {
    transform: scale(1);
    filter: opacity(100%);
  }
  100% {
    transform: scale(0.6);
    filter: opacity(0%);
  }
}
</style>
