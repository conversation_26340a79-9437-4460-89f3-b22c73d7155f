<template>
  <div
    id="randomCallContainer"
    :class="['randomCallContainer', msgType == 'small_random_call' ? 'smallRandomStyle' : '']"
  >
    <audio :src="getAssetPath('/mp3/randomCall/xuanren.mp3')" class="hidden" ref="xuanren"></audio>
    <audio
      :src="getAssetPath('/mp3/randomCall/wheel_selected.mp3')"
      class="hidden"
      ref="wheelSelectedAudio"
    ></audio>
    <!-- 不能单纯的 'if else' 去判断显示机器还是winner。因为，可能存在异常情况-->
    <div class="machineContainer" v-if="showMachine && !showWinner">
      <h3>{{ $t('classroom.interactions.randomCall.title') }}</h3>
      <div class="rocker" ref="rocker" id="rocker">摇杆</div>
      <div class="studentsRollContainer" NeosScroll></div>
    </div>
    <div class="winerContainer" v-if="showWinner && !showMachine && selected">
      <h3>
        <p>{{ winnerMsg }}</p>
      </h3>
      <lottie-player
        autoplay
        loop
        mode="normal"
        :src="getAssetPath('/lottiefiles/randomCall/data.json')"
        class="lottie-player"
      />
      <div class="winerInfo">
        <span class="word-ellipsis">{{ selected.name }}</span>
      </div>
      <img :src="selected.avatar" />

      <i class="close" @click="destroyInteraction('close')" />
    </div>
  </div>
</template>

<script setup>
import '@lottiefiles/lottie-player'
import { useI18n } from 'vue-i18n'
import { addClass, removeClass } from '@/utils/util'
import h5Scroller from '@thinkacademy/new-scroll'
import '@lottiefiles/lottie-player'
import logger from '@/utils/logger'
// import { getCameraStatus, getMicrophoneStatus } from 'utils/mediaAccess'
// import logger from 'utils/logger'
import { getAssetPath } from '@/utils/assetPath'
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'

const { t } = useI18n()

// Props 定义
const props = defineProps({
  options: {
    type: Object,
    default: () => ({})
  },
  msgType: {
    type: String,
    default: 'random_call'
  },
  studentList: {
    type: Array,
    default: () => []
  }
})

// Emits 定义
const emit = defineEmits(['close'])

// 响应式状态
const showMachine = ref(true)
const showWinner = ref(false)
const selected = ref(null)
const winnerMsg = ref('')

const students = ref([])
const myself = props.options.roomMessage.roomInfo.stuInfo.id

// 模板引用
const xuanren = ref(null)
const wheelSelectedAudio = ref(null)
const rocker = ref(null)

// 存储事件监听器引用
let animationEndHandler = null

// 方法
const scrollItems = () => {
  if (!students.value) return
  const items = []
  students.value.forEach(item => {
    items.push(
      `<div class="rollBg">
        <div class="avatar">
          <img src="${item.avatar}" />
        </div>
        <p class="name">${item.name}</p>
      </div>`
    )
  })
  return items
}

const destroyInteraction = () => {
  sendLogger('用户主动关闭随机点名', 'close')
  emit('close')
}

const sendLogger = (msg, stage = '', level = 'info') => {
  const interactionId = props.options?.ircMsg?.interactId || 'unknown'
  const interactionType = 'RandomCall'

  logger.send({
    tag: 'student.Interact',
    level,
    content: {
      msg: msg,
      interactType: interactionType,
      interactId: interactionId,
      interactStage: stage,
      params: '',
      response: '',
      timestamp: Date.now()
    }
  })

  console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
}

// 生命周期钩子 - created
const ircStudent = [...props.options.ircMsg.students, props.options.ircMsg.selected]
students.value = ircStudent.map(item => {
  item.name = item.nickName
  return item
})
sendLogger(`随机点名学生列表初始化 - 学生数量:${students.value.length}`, 'init')

// 生命周期钩子 - mounted
onMounted(() => {
  sendLogger('随机点名转盘组件初始化', 'init')
  const scroller = new h5Scroller()
  sendLogger('h5Scroller实例创建完成', 'init')

  scroller.on('finished', item => {
    selected.value = students.value[item]
    sendLogger(`转盘选中结果 - 学生:${selected.value.name} 索引:${item}`, 'result')

    if (selected.value.userId === myself) {
      winnerMsg.value = t('classroom.interactions.randomCall.finishedNotice.self')
      sendLogger('选中自己', 'result')
    } else {
      winnerMsg.value = t('classroom.interactions.randomCall.finishedNotice.other', {
        name: selected.value.name
      })
      sendLogger(`选中其他学生 - 学生:${selected.value.name}`, 'result')
    }
    wheelSelectedAudio.value.play()
    sendLogger('播放选中音效', 'audio')
    showMachine.value = false
    showWinner.value = true
    sendLogger('切换到获胜者展示界面', 'result')
  })

  nextTick(() => {
    addClass(rocker.value, 'active')
    sendLogger('摇杆动画开始', 'animation')

    scroller.init({
      selected: students.value.length - 1, // 选中项
      count: 1, // 滚动次数
      duration: 3.5, // 持续时间
      direction: 'up', // 方向
      items: scrollItems() // 需要滚动的数据(这里滚动的是整个dom)
    })
    sendLogger(`转盘初始化完成 - 选中索引:${students.value.length - 1} 持续时间:3.5s`, 'init')

    animationEndHandler = () => {
      removeClass(rocker.value, 'active')
      sendLogger('摇杆动画结束，开始转盘滚动', 'animation')
      xuanren.value.play()
      sendLogger('播放选人音效', 'audio')
      scroller.start()
      sendLogger('转盘滚动开始', 'animation')
    }

    rocker.value.addEventListener('animationend', animationEndHandler)
  })

  sendLogger('开启随机点名转盘互动', 'start')
})

onBeforeUnmount(() => {
  if (rocker.value && animationEndHandler) {
    rocker.value.removeEventListener('animationend', animationEndHandler)
  }
})
</script>

<style lang="scss" scoped>
$namespace: 'randomCall';
.smallRandomStyle {
  z-index: 1010;
}
.#{$namespace}Container {
  background: rgba(0, 0, 0, 0.8);
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    'Helvetica Neue', Helvetica, Arial, sans-serif;
  .hidden {
    display: none;
  }
  .machineContainer {
    background: url(../imgs/machine_bg.png) no-repeat center center;
    background-size: 100%;
    width: 466px;
    height: 375px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    h3 {
      padding: 0;
      margin: 0;
      position: absolute;
      top: 30px;
      left: 0;
      right: 0;
      font-size: 26px;
      font-weight: 500;
      color: rgba(255, 223, 95, 1);
      text-align: center;
      line-height: 1;
      font-family:
        'SFProRounded-Black', SFProRounded, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
        'Helvetica Neue', Helvetica, Arial, sans-serif;
    }
    .rocker {
      background: url('../imgs/rocker.png') no-repeat center center;
      background-size: 100%;
      width: 40px;
      height: 192px;
      font-size: 0px;
      text-decoration: -999em;
      position: absolute;
      right: 0;
      top: 100px;
      overflow: hidden;
      &.active {
        animation: rocker-push ease-in 0.2s 2 alternate forwards;
      }
    }
  }
  :deep(.studentsRollContainer) {
    width: 260px;
    height: 168px;
    overflow: hidden;
    position: relative;
    top: 130px;
    left: 103px;
  }
  :deep(.studentsRollContainer .name) {
    color: #b05a2a;
    font-size: 20px;
    line-height: 1.25;
    font-weight: 500;
    padding-top: 5px;
    padding-right: 5px;
    font-family:
      'SFProRounded-Black', SFProRounded, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
      'Helvetica Neue', Helvetica, Arial, sans-serif;
    overflow: hidden;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
  :deep(.studentsRollContainer .avatar) {
    width: 88px;
    height: 88px;
    border-radius: 100em;
    border: 5px solid #fff;
    img {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: 100em;
    }
  }
  :deep(.studentsRollContainer .avatar img) {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 100em;
  }
  :deep(.studentsRollContainer .rollBg) {
    background: url(../imgs/roll_bg.png) no-repeat center center;
    background-size: 100%;
    width: 260px;
    height: 158px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    margin: 0 0 10px 0;
  }

  .winerContainer {
    width: 440px;
    height: 320px;
    position: relative;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    flex-direction: column;
    font-family:
      'SFProRounded-Black', SFProRounded, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
      'Helvetica Neue', Helvetica, Arial, sans-serif;
    animation: scale-in 0.5s;
    .close {
      pointer-events: auto;
      position: absolute;
      left: 50%;
      bottom: -90px;
      transform: translate(-50%, 0%);
      cursor: pointer;
      display: block;
      background: url(../imgs/close.png) no-repeat center center;
      background-size: 100%;
      width: 32px;
      height: 32px;
    }
    .lottie-player {
      width: 320px;
      height: 320px;
      margin: 0 auto;
      position: relative;
      z-index: 1;
    }
    h3 {
      color: #ffe132;
      font-weight: 500;
      font-size: 24px;
      line-height: 30px;
      text-align: center;
      white-space: nowrap;
      text-overflow: ellipsis;
      height: 30px;
      p {
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .winerInfo {
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      z-index: 10;
      color: #fff;
      font-size: 24px;
      font-family:
        'SFProRounded-Black', SFProRounded, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
        'Helvetica Neue', Helvetica, Arial, sans-serif;
      i.level {
        display: block;
        width: 40px;
        height: 40px;
        margin-right: 10px;
        font-size: 0;
        &.level_0 {
          width: 0;
          height: 0;
        }
      }
      @for $i from 1 to 8 {
        .level_#{$i} {
          background: url(../imgs/level_#{$i}.png) no-repeat center center;
          background-size: 100%;
        }
      }
      .word-ellipsis {
        overflow: hidden;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        max-width: 80%;
        text-align: center;
      }
    }
    img {
      position: absolute;
      width: 130px;
      height: 130px;
      border-radius: 100em;
      display: block;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -52.5%);
      z-index: 2;
    }
  }
}

@keyframes rocker-push {
  0% {
    transform: scaleY(1);
  }
  100% {
    transform: scaleY(0.4);
  }
}

@keyframes scale-in {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.6);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
</style>
