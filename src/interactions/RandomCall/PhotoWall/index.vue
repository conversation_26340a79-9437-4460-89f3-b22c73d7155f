<template>
  <div id="wallRandomCall">
    <audio
      :src="getAssetPath('/mp3/randomCall/wall_background.mp3')"
      class="hidden"
      ref="wallBg"
    ></audio>
    <audio
      :src="getAssetPath('/mp3/randomCall/wall_spinner1.mp3')"
      class="hidden"
      ref="wallSpinner1"
    ></audio>
    <audio
      :src="getAssetPath('/mp3/randomCall/wall_spinner2.mp3')"
      class="hidden"
      ref="wallSpinner2"
    ></audio>
    <audio
      :src="getAssetPath('/mp3/randomCall/wall_selected.mp3')"
      class="hidden"
      ref="wallSelected"
    ></audio>

    <div class="wallBackground" v-if="animationStatus">
      <div :class="['wallTitle', titleClassName]"></div>
      <div class="wallContent">
        <div
          :class="['wallVideoOutBorder', selectStutId == item.userId ? 'selectBorder' : '']"
          :key="item.userId"
          v-for="item in studentsInfo"
        >
          <div class="wallVideoBox">
            <div v-show="showVideo(item)" class="video-wrapper" :id="'wall-video-' + item.userId" />
            <div class="avatar-wrapper">
              <img :src="item.avatar ? item.avatar : defaultAvatar" />
              <span class="student-name">{{ item.stuName }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="selectBackground" v-else>
      <div class="close" @click="clickClose"></div>
      <div :class="['wallTitle', titleClassName]"></div>
      <div class="selectVideoBox">
        <div
          v-show="showVideo(selectInfo)"
          class="video-wrapper"
          :id="'select-video-' + selectInfo.userId"
        />
        <div class="avatar-wrapper">
          <img :src="selectInfo.avatar ? selectInfo.avatar : defaultAvatar" />
        </div>
        <div class="stuInfoBox">
          <div class="stuName">
            <span class="leftBg"></span
            ><span class="nameBg word-ellipsis">{{ selectInfo.stuName }}</span
            ><span class="rightBg"></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'
import { useClassData } from '@/stores/classroom'
import { useBaseData } from '@/stores/baseData'
import logger from '@/utils/logger'
import defaultAvatar from '../imgs/avatar_default.png'
import { useRtcService } from '@/hooks/useRtcService'
import { getAssetPath } from '@/utils/assetPath'

const { getRtcService } = useRtcService()

const props = defineProps({
  options: {
    type: Object,
    default: () => {}
  }
})

// Emits 定义
const emit = defineEmits(['close'])

// Store
const store = useClassData()
const baseStore = useBaseData()
const studentList = computed(() => store.studentList)

// 响应式状态
const locale = computed(() => baseStore.locale)
const stuId = ref(props.options.roomMessage.roomInfo.stuInfo.id)
const studentsInfo = ref([]) // 构建完毕的所有学生列表信息
const randomStuIdList = ref([]) // 随机生成的学生id列表，提供随机闪烁路径
const selectStutId = ref(0) // 每次闪烁选中的学生id
const randomCount = 16 // 随机次数16次
const currentRandomCount = ref(0) // 当前随机步数
const animationStatus = ref(true) // true 随机动画阶段, false 动画结束显示选中的人阶段
const animationTimer = ref(null) // 动画定时器

// 获取DOM元素的引用
const wallBg = ref(null)
const wallSpinner1 = ref(null)
const wallSpinner2 = ref(null)
const wallSelected = ref(null)

// 计算属性
const titleClassName = computed(() => {
  return locale.value == 'hk' ? 'title_zh' : 'title_en'
})

const ircStudentList = computed(() => {
  return props.options.ircMsg.students
})

// 选中的学生
const selectedStuId = computed(() => {
  return props.options.ircMsg.selected.userId
})

const selectInfo = computed(() => {
  return studentList.value.find(item => item.userId == props.options.ircMsg.selected.userId)
})

const cameraStatus = computed(() => {
  return !store.selfInfo.videoMute
})

// 方法
const creatStudentVideo = ({ uid, mute }) => {
  sendLogger(`学生视频状态变化 - 学生ID:${uid} 静音:${mute}`, 'video')
  if (mute) {
    sendLogger(`学生视频已静音，跳过创建 - 学生ID:${uid}`, 'video')
    return
  }
  const stuInfo = studentsInfo.value.find(item => item.userId == uid)
  let remoteDomId = ''
  // 进入的学生在照片墙随机列表中
  if (stuInfo) {
    // 照片墙闪烁状态
    if (animationStatus.value) {
      remoteDomId = `wall-video-${stuInfo.userId}`
      sendLogger(`照片墙闪烁状态，创建视频 - 学生:${stuInfo.stuName} DOM:${remoteDomId}`, 'video')
    } else {
      // 选中状态
      if (stuInfo.userId == selectedStuId.value) {
        remoteDomId = `select-video-${stuInfo.userId}`
        sendLogger(`选中状态，创建选中学生视频 - 学生:${stuInfo.stuName}`, 'video')
      } else {
        sendLogger('已到达选中状态，且不是选中的学生，不需要恢复视频流', 'video')
      }
    }
  }
  // 存在dom元素，进行视频创建
  if (document.getElementById(remoteDomId)) {
    nextTick(() => {
      const RtcService = getRtcService()
      RtcService.createRemoteVideo(uid, remoteDomId)
      sendLogger(`视频创建成功 - DOM:${remoteDomId}`, 'video')
    })
  } else {
    sendLogger(`DOM元素不存在，无法创建视频 - DOM:${remoteDomId}`, 'video', 'warn')
  }
}

const showVideo = item => {
  if (item.isSelf) {
    return cameraStatus.value && !item.multVideoLinkStatus
  } else {
    return !item.videoMute && !item.multVideoLinkStatus && item.inClass
  }
}

// 初始化学生列表
const initVideo = () => {
  sendLogger('开始初始化照片墙学生列表', 'init')
  // 主讲传过来的展示在照片墙学生列表id
  const ircStudentIds = new Set(ircStudentList.value.map(item => item.userId))
  sendLogger(`IRC学生列表 - 学生数量:${ircStudentIds.size}`, 'init')
  console.log('ircStudentIds', ircStudentIds)
  // 筛选本地存储学生信息列表
  studentsInfo.value = studentList.value.filter(item => ircStudentIds.has(Number(item.userId)))
  sendLogger(`筛选后学生列表 - 学生数量:${studentsInfo.value.length}`, 'init')
  // 初始化random列表
  initRandomList()
  nextTick(() => {
    studentsInfo.value.forEach(item => {
      const videoId = `wall-video-${item.userId}`
      createVideo(item, videoId)
    })
    sendLogger('所有学生视频初始化完成', 'init')
  })
}

const initRandomList = () => {
  sendLogger(`开始初始化随机列表 - 随机次数:${randomCount}`, 'random')
  for (let i = 0; i < randomCount; i++) {
    const randomIndex = getRandomIndex()
    let currentRandomId = studentsInfo.value[randomIndex].userId
    if (i > 0) {
      let lastRandomId = randomStuIdList.value[i - 1]
      // 如果上一个随机数等于当前随机数，则重新随机
      reRandom(lastRandomId, currentRandomId)
    } else {
      // 将选中的人当作第一位
      randomStuIdList.value.push(selectedStuId.value)
      sendLogger(`设置选中学生为第一位 - 学生ID:${selectedStuId.value}`, 'random')
    }
  }
  // 进行翻转，将选中的人置为最后一位
  randomStuIdList.value.reverse()
  sendLogger(`随机列表构建完成 - 列表长度:${randomStuIdList.value.length}`, 'random')
  // 构建好随机列表后运行动画
  beginAnimation()
}

// 递归重随逻辑
const reRandom = (lastRandomId, currentRandomId) => {
  if (lastRandomId == currentRandomId) {
    sendLogger(`检测到重复随机，重新生成 - 上次:${lastRandomId} 当前:${currentRandomId}`, 'random')
    const randomIndex = getRandomIndex()
    let newCurrentRandomId = studentsInfo.value[randomIndex].userId
    reRandom(lastRandomId, newCurrentRandomId)
  } else {
    randomStuIdList.value.push(currentRandomId)
    sendLogger(`添加随机学生 - 学生ID:${currentRandomId}`, 'random')
  }
}

const getRandomIndex = () => {
  return Math.ceil(Math.random() * studentsInfo.value.length) - 1
}

// 开始动画
const beginAnimation = () => {
  selectStutId.value = randomStuIdList.value[0]
  sendLogger(`开始照片墙动画 - 首个选中学生:${selectStutId.value}`, 'animation')
  wallSpinner1.value.play()
  animations()
}

const animations = () => {
  currentRandomCount.value += 1
  let time = (Math.pow(currentRandomCount.value - 1, 3) / 6 + 100).toFixed(2)
  sendLogger(`动画步骤 - 第${currentRandomCount.value}步 延时:${time}ms`, 'animation')

  // 最后一次，停留计算好的时间后，显示选中的人。
  if (currentRandomCount.value == randomCount) {
    sendLogger('到达最后一次动画，准备显示选中结果', 'animation')
    animationTimer.value = setTimeout(() => {
      destroyedTimer()
      // 弹出选中
      animationStatus.value = false
      sendLogger(`照片墙动画结束，显示选中学生 - 学生:${selectInfo.value?.stuName}`, 'result')
      wallSelected.value.play()
      nextTick(() => {
        const videoId = `select-video-${selectInfo.value.userId}`
        createVideo(selectInfo.value, videoId)
        sendLogger('选中学生视频创建完成', 'result')
      })
    }, time)
  } else {
    // 进行选人变化
    animationTimer.value = setTimeout(() => {
      destroyedTimer()
      selectStutId.value = randomStuIdList.value[currentRandomCount.value]
      sendLogger(`切换选中学生 - 学生ID:${selectStutId.value}`, 'animation')
      // 音频交互播放
      let refValue = currentRandomCount.value % 2 == 0 ? wallSpinner1 : wallSpinner2
      refValue.value.play()
      if (currentRandomCount.value < randomCount) {
        animations()
      }
    }, time)
  }
}

// 摧毁定时器
const destroyedTimer = () => {
  if (animationTimer.value) {
    clearTimeout(animationTimer.value)
    animationTimer.value = null
    sendLogger('清除动画定时器', 'cleanup')
  }
}

const createVideo = (stuInfo, videoId) => {
  const RtcService = getRtcService()
  sendLogger(`创建视频 - 学生:${stuInfo.stuName} 视频ID:${videoId}`, 'video')
  if (stuInfo.userId == stuId.value) {
    RtcService.createLocalVideo(videoId)
    sendLogger('创建本地视频', 'video')
  } else {
    RtcService.createRemoteVideo(stuInfo.userId, videoId)
    sendLogger(`创建远程视频 - 学生ID:${stuInfo.userId}`, 'video')
  }
}

// 销毁照片墙互动
const clickClose = () => {
  sendLogger('用户点击关闭照片墙', 'close')
  store.updateWallRandomCallStatus(false)
  destroyedTimer()
  emit('close')
  sendLogger('照片墙互动已关闭', 'close')
}

/**
 * 日志上报
 */
const sendLogger = (msg, stage = '', level = 'info') => {
  const interactionId = props.options?.ircMsg?.interactId || 'unknown'
  const interactionType = 'WallRandomCall'

  logger.send({
    tag: 'student.Interact',
    level,
    content: {
      msg: msg,
      interactType: interactionType,
      interactId: interactionId,
      interactStage: stage,
      params: '',
      response: '',
      timestamp: Date.now()
    }
  })

  console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
}

// 生命周期钩子
onMounted(() => {
  sendLogger('照片墙随机点名组件初始化', 'init')
  // 监听远端学生加入
  const RtcService = getRtcService()
  RtcService.on('studentVideoMute', creatStudentVideo)
  store.updateWallRandomCallStatus(true)
  sendLogger('开启照片墙随机点名互动', 'start')
  // 初始化样式
  wallBg.value.play()
  sendLogger('背景音乐开始播放', 'audio')
  // 初始化视频
  initVideo()
})

onUnmounted(() => {
  sendLogger('照片墙组件开始销毁', 'destroy')
  destroyedTimer()
  const RtcService = getRtcService()
  RtcService.off('studentVideoMute', creatStudentVideo)
  sendLogger('照片墙组件销毁完成', 'destroy')
})
</script>

<style lang="scss">
#wallRandomCall {
  background: rgba(0, 0, 0, 0.8);
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  left: 0;
  top: 0;
  .hidden {
    display: none;
  }
  .wallTitle {
    background-repeat: no-repeat;
    background-size: 100%;
    width: 268px;
    height: 83px;
  }
  .title_zh {
    background-image: url('../imgs/wall_title_zh.png');
  }
  .title_en {
    background-image: url('../imgs/wall_title_en.png');
  }
  .video-wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
    z-index: 1;
    border-radius: 6px;
  }
  .wallBackground {
    background: url('../imgs/wall_bg.png') no-repeat;
    background-size: 100%;
    width: 680px;
    height: 615px;
    position: relative;
    top: 50%;
    transform: translateY(-50%);

    .wallTitle {
      margin: -11px auto 0;
    }

    .wallContent {
      width: 640px;
      height: 484px;
      display: flex;
      justify-content: center;
      align-items: center;
      align-content: center;
      flex-wrap: wrap;
      margin: -8px 0 0 18px;

      .wallVideoOutBorder {
        margin: 4px;
        padding: 4px;
        border-radius: 8px;
      }

      .selectBorder {
        background: #1f6cff;
      }

      .wallVideoBox {
        width: 140px;
        height: 105px;
        border-radius: 6px;
        background: #ffae74;
        overflow: hidden;
        position: relative;
        .avatar-wrapper {
          position: absolute;
          top: 0;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          height: 100%;
          flex-direction: column;
          img {
            width: 64px;
            height: 64px;
            border-radius: 40px;
            margin-bottom: 4px;
          }
          .student-name {
            font-size: 14px;
            font-weight: 600;
            color: #ffffff;
            line-height: 16px;
            overflow: hidden;
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }
        }
      }
    }
  }
  .selectBackground {
    background: url('../imgs/wall_select_bg.png') no-repeat;
    background-size: 100%;
    width: 718px;
    height: 525px;
    position: relative;
    top: 50%;
    transform: translateY(-50%);

    .close {
      position: absolute;
      top: 48px;
      right: 105px;
      width: 48px;
      height: 45px;
      cursor: pointer;
      background: url('../imgs/wall_close.png') no-repeat;
      background-size: 100%;
      pointer-events: auto;
    }

    .wallTitle {
      margin: 0 auto;
    }

    .selectVideoBox {
      margin: -3px auto 0;
      width: 376px;
      height: 282px;
      border-radius: 8px;
      background: #ffae74;
      border: 3px solid #ffc299;
      position: relative;
      .avatar-wrapper {
        display: flex;
        justify-content: center;
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        img {
          margin-top: 62px;
          width: 110px;
          height: 110px;
          border-radius: 60px;
        }
      }
      .stuInfoBox {
        position: absolute;
        bottom: -29px;
        left: 0;
        right: 0;
        margin: auto;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        z-index: 2;

        .stuLevel {
          width: 176px;
          height: 29px;
          background: url('../imgs/wall_level.png') no-repeat;
          background-size: 100%;
          position: relative;
          span.levelBox {
            display: flex;
            height: 24px;
            align-items: center;
            justify-content: center;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            .levelNum {
              height: 16px;
              background: #fe6782;
              box-shadow: 0px -8px 10px -6px rgba(0, 0, 0, 0.24);
              border-radius: 0px 3px 3px 0px;
              font-size: 14px;
              font-weight: 500;
              color: #ffffff;
              line-height: 16px;
              margin-left: -9px;
              padding: 0 3px 0 11px;
            }
            i.levelIcon {
              z-index: 1;
              display: block;
              width: 50px;
              height: 24px;
            }
            @for $i from 1 to 8 {
              .level_#{$i} {
                background: url(../imgs/level-#{$i}.png) no-repeat center center;
                background-size: 100%;
              }
            }
          }
        }
        .stuName {
          display: flex;
          height: 48px;
          text-align: center;
          font-weight: 500;
          color: #d23c3c;

          .leftBg {
            width: 25px;
            background: url('../imgs/leftbg.png') no-repeat;
            background-size: 25px 48px;
          }

          .nameBg {
            min-width: 130px;
            width: 80%;
            background: url('../imgs/namebg.png');
            background-size: 100% 100%;
            line-height: 48px;
            font-size: 22px;
          }
          .word-ellipsis {
            overflow: hidden;
            word-break: break-all;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
          }
          .rightBg {
            width: 25px;
            background: url('../imgs/rightbg.png') no-repeat;
            background-size: 25px 48px;
          }
        }
      }
    }
  }
}
</style>
