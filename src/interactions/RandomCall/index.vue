<template>
  <div>
    <Wheel v-if="randomType == 'wheel'" :options="options" @close="closeInteract"></Wheel>
    <PhotoWall
      v-else-if="randomType == 'wall'"
      :options="options"
      @close="closeInteract"
    ></PhotoWall>
  </div>
</template>
<script setup>
import Wheel from './Wheel/index.vue'
import PhotoWall from './PhotoWall/index.vue'
import { ref } from 'vue'
import { useClassData } from '@/stores/classroom'
import logger from '@/utils/logger'

// 定义组件名称
defineOptions({
  name: 'RandomCallView'
})

// 定义props
const props = defineProps({
  options: {
    type: Object,
    default: () => ({})
  }
})

// 定义emit
const emit = defineEmits(['close'])

// 获取store
const store = useClassData()

// 响应式状态
const randomType = ref('')

// 统一的日志记录方法
const sendLogger = (msg, stage = '', level = 'info') => {
  const interactionId = props.options?.ircMsg?.interactId || 'unknown'
  const interactionType = 'RandomCall'

  logger.send({
    tag: 'student.Interact',
    level,
    content: {
      msg: msg,
      interactType: interactionType,
      interactId: interactionId,
      interactStage: stage,
      params: '',
      response: '',
      timestamp: Date.now()
    }
  })

  console.log(`[${interactionType}][${interactionId}] ${msg}`, { stage, level })
}

// 方法
const receiveMessage = () => {
  sendLogger('收到随机点名消息', 'start')
  store.changeStudentInfo(String(props.options.ircMsg.selected.userId), {
    ...props.options.ircMsg.selected,
    stuName: props.options.ircMsg.selected.nickName
  })
  randomType.value = props.options.ircMsg.type
  sendLogger(`随机点名类型: ${randomType.value}`, 'type_set')
}

const closeInteract = () => {
  sendLogger('关闭随机点名互动', 'end')
  emit('close', 'small_random_call')
}

const destroyInteraction = msg => {
  sendLogger('销毁随机点名互动', 'destroy')
  if (msg.type === 'wall' && msg.recoverVideo) {
    store.updateWallRandomCallStatus(false)
  }
  closeInteract()
}
defineExpose({
  receiveMessage,
  destroyInteraction
})
</script>
