<template>
  <div class="speedy-hand">
    <div class="speedy-hand-box">
      <lottie-player
        v-show="isShowReadyLottie"
        id="ready"
        autoplay
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/01ready.json')"
        class="lottie-player"
        @complete="onLottieReadyComplete"
      />
      <audio
        :src="getAssetPath('/mp3/speedyHand/readygo1.mp3')"
        class="hide"
        autoplay
        ref="readygoAudio"
      ></audio>

      <audio
        :src="getAssetPath('/mp3/speedyHand/backgroundAudio.mp3')"
        class="hide"
        autoplay
        ref="backgroundAudio"
      ></audio>
      <!-- go-->
      <lottie-player
        v-show="isShowGoLottie"
        id="go"
        autoplay
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/01go.json')"
        class="lottie-player"
        @complete="onLottieGoComplete"
      />
      <lottie-player
        v-show="isShowWaitLottie"
        id="waitClickLottie"
        loop
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/02wait.json')"
        class="lottie-player"
      />
      <div v-show="isShowBtn" class="mouseAction" @click="onSpeedyHand"></div>
      <!-- 按下-->
      <lottie-player
        id="press"
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/03press.json')"
        class="lottie-player"
      />
      <audio :src="getAssetPath('/mp3/speedyHand/press.mp3')" class="hide" ref="pressAudio"></audio>
      <!-- 按下后循环-->
      <lottie-player
        v-show="isShowLoadingLottie"
        id="loading"
        loop
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/04loading.json')"
        class="lottie-player"
      />
      <!-- 选中 -->
      <lottie-player
        id="result"
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/05result.json')"
        class="lottie-player"
        @complete="onResultLottieComplete"
      />
      <audio
        :src="getAssetPath('/mp3/speedyHand/getResult.mp3')"
        class="hide"
        ref="resultAudio"
      ></audio>
      <lottie-player
        v-show="isShowResult"
        id="onStage"
        loop
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/06onStage.json')"
        class="lottie-player"
      />
      <div v-show="resultData.studentId > 0" class="result-student-img">
        <img :src="resultData.avatar" width="140" height="140" />
      </div>
      <div v-show="resultData.studentId > 0" class="result-student-name">
        {{ resultData.nickName }}
      </div>
      <!-- 未选中 -->
      <div v-show="isNoStudent" class="result-nostudent-tips">{{ $t('common.speedyHand') }}</div>
      <lottie-player
        v-show="true"
        id="errorLottie"
        mode="normal"
        :src="getAssetPath('/lottiefiles/speedyHand/07error.json')"
        class="lottie-player pos"
      />
      <audio
        :src="getAssetPath('/mp3/speedyHand/error.mp3')"
        class="hide"
        ref="errorLottieAudio"
      ></audio>
    </div>
  </div>
</template>
<script>
import normalAvatar from './imgs/normal.png'
import { useClassData } from '@/stores/classroom'
import '@lottiefiles/lottie-player'
import { answerRob } from '@/api/classroom'
import { speedyHandLog } from '@/utils/web-log/HWLogDefine.js'
import { getAssetPath } from '@/utils/assetPath'
import { message } from 'ant-design-vue'

export default {
  name: 'SpeedyHandInteraction',
  data() {
    return {
      isShowBtn: false, // 是否展示点击区域
      pressTimer: null, // 按下timer
      destroyInteractionTimer: null, // 摧毁互动timer
      isShowReadyLottie: true, // 是否展示GO过渡动画
      isShowGoLottie: false, // 是否展示GO过渡动画
      isShowWaitLottie: false, // 是否展示等待动画
      isShowLoadingLottie: false, // 是否展示加载动画
      isShowResult: false, // 抢到后循环
      isNoStudent: false,
      resultData: {
        studentId: '',
        nickName: '',
        avatar: normalAvatar
      }, // 结果
      isPressSpeedyHand: false, // 是否点击抢答
      halfWay: 'halfway', // 中途结束；
      resultNoBody: 'nobody', // 无人抢答
      resultHasBody: 'normal' // 有人抢答正常结束;
    }
  },
  props: {
    options: {
      type: Object,
      default: () => {}
    }
  },
  created() {
    speedyHandLog.info('抢答组件初始化', 'init')
  },
  methods: {
    getAssetPath,
    /**
     * ready 播放完
     */
    onLottieReadyComplete() {
      speedyHandLog.info('倒计时动画播放完成，开始抢答', 'ready')
      this.isShowReadyLottie = false
      this.isShowGoLottie = true
      document.getElementById('ready')?.destroy()
      document.getElementById('go')?.play()
      this.isShowBtn = true
    },
    /**
     * go 播放完
     */
    onLottieGoComplete() {
      speedyHandLog.info('GO动画播放完成，等待用户点击', 'go')
      this.isShowGoLottie = false
      document.getElementById('go')?.destroy()
      this.isShowWaitLottie = true
      document.getElementById('waitClickLottie')?.play()
    },
    /**
     * 有人抢答 播放完
     */
    onResultLottieComplete() {
      speedyHandLog.info('抢答）有人抢答 播放完 onResultLottieComplete')
      this.isShowWaitLottie = false
      this.isShowResult = true
      document.getElementById('result')?.destroy()
      document.getElementById('onStage')?.play()
      const store = useClassData()
      const videoMicLinkUsers = store.videoMicLinkUsers
      const student = videoMicLinkUsers.filter(item => item == this.resultData.studentId)
      speedyHandLog.info(
        `检查连麦状态 - 连麦用户数:${videoMicLinkUsers.length} 获奖者是否连麦:${student.length > 0}`,
        'check'
      )
      const timer = setTimeout(() => {
        document.getElementById('onStage')?.destroy()
        this.close(this.resultHasBody)
      }, 5000)
      if (student.length) {
        document.getElementById('onStage')?.destroy()
        this.close(this.resultHasBody)
        clearTimeout(timer)
      }
    },
    /**
     * 点击抢答
     */
    async onSpeedyHand() {
      this.isShowWaitLottie = false
      this.isShowGoLottie = false
      if (this.isPressSpeedyHand) {
        speedyHandLog.action('重复点击抢答，忽略', 'click', 'warn')
        return
      }
      this.isPressSpeedyHand = true
      speedyHandLog.info('用户点击抢答按钮', 'click')
      this.$refs.pressAudio.play()
      document.getElementById('go')?.destroy()
      document.getElementById('waitClickLottie')?.destroy()
      document.getElementById('press')?.play()
      const params = {
        planId: this.options.roomMessage.roomInfo.planInfo.id,
        classId: this.options.roomMessage.roomInfo.commonOption.classId,
        interactId: this.options.ircMsg.interactId
      }
      speedyHandLog.info(`发送抢答请求 - 互动ID:${params.interactId}`, 'request')
      const res = await answerRob(params).catch(err => {
        speedyHandLog.error(`抢答接口调用失败: ${err.message || err}`, 'request')
        return err
      })
      console.log('0704 res', res)

      const { code, data } = res
      if (code === 0 && data?.studentInfo?.userId > 0) {
        // 有人抢答
        const winner = data.studentInfo
        speedyHandLog.info(`抢答成功 - 获胜者:${winner.nickName} ID:${winner.userId}`, 'success')
        this.isShowBtn = false
        this.resultStudent(data.studentInfo)
      } else if (code === 0) {
        speedyHandLog.info('抢答请求成功但无获胜者', 'success')
      } else {
        speedyHandLog.error(`抢答请求失败 - 错误码:${code}`, 'request', 'error')
      }

      this.pressTimer = setTimeout(() => {
        clearTimeout(this.pressTimer)
        document.getElementById('press')?.destroy()
        this.isShowLoadingLottie = true
        document.getElementById('loading')?.play()
      }, 800)
    },
    destroyInteraction(noticeContent) {
      this.isShowWaitLottie = false
      this.isShowGoLottie = false

      speedyHandLog.info('收到抢答结束通知', 'destroy')
      const { data, status } = noticeContent
      if (status === 3) {
        speedyHandLog.info('抢答中途结束', 'destroy', 'warn')
        this.close(this.halfWay)
        return
      }

      if (status === 2 && !data.studentId) {
        // 无人抢答data为{}
        speedyHandLog.info('抢答结束 - 无人抢答', 'destroy')
        // 无人抢答
        this.isShowBtn = false
        this.isNoStudent = true
        document.getElementById('errorLottie')?.play()
        this.$refs.errorLottieAudio?.play()
        this.$refs.backgroundAudio?.pause()
        // 摧毁lottie
        this.destoryLottie()
        this.close(this.resultNoBody)
      } else if (status === 1 && data?.studentId > 0) {
        // 有人抢答data.studentId存在且大于0
        speedyHandLog.info(`抢答结束 - 获胜者:${data.nickName} ID:${data.studentId}`, 'destroy')
        // 有人抢答
        this.isShowBtn = false
        this.destoryLottie()
        this.resultStudent(data)
        this.close(this.resultHasBody)
      }
    },
    /**
     * 摧毁互动
     */
    close(type) {
      this.isShowWaitLottie = false
      this.isShowGoLottie = false
      const typeMap = {
        [this.halfWay]: '中途结束',
        [this.resultNoBody]: '无人抢答结束',
        [this.resultHasBody]: '有人抢答结束'
      }
      speedyHandLog.info(`抢答互动关闭 - ${typeMap[type] || type}`, 'close')
      this.isPressSpeedyHand = false
      clearTimeout(this.pressTimer)
      clearTimeout(this.destroyInteractionTimer)
      if (type === this.halfWay) {
        message.info(this.$t('classroom.interactions.redRain.toastTip'))
        this.$emit('close', 'speedyHand')
      } else if (type === this.resultNoBody) {
        this.destroyInteractionTimer = setTimeout(() => {
          clearTimeout(this.destroyInteractionTimer)
          this.$emit('close', 'speedyHand')
        }, 3.5 * 1000)
      } else if (type === this.resultHasBody) {
        this.destroyInteractionTimer = setTimeout(() => {
          clearTimeout(this.destroyInteractionTimer)
          this.$emit('close', 'speedyHand')
        }, 1.5 * 1000)
      }
    },
    /**
     * 有人抢答
     */
    resultStudent(data) {
      speedyHandLog.info('0704 有人抢答', data, this.resultData.studentId)
      // 已经获取到结果不再重复赋值
      if (this.resultData.studentId) {
        speedyHandLog.info('抢答结果已存在，跳过重复设置', 'result', 'warn')
        return
      }
      speedyHandLog.info('0704 有人抢答 1', this.resultData.studentId)
      const stuId = this.options.roomMessage.roomInfo.stuInfo.id
      const studentId = data?.studentId || data?.userId
      let nickName = ''
      if (stuId === studentId) {
        nickName = `Me(${data?.nickName})`
        speedyHandLog.info(`抢答获胜 - 我抢到了！`, 'winner')
      } else {
        nickName = data?.nickName
        speedyHandLog.info(`抢答结果 - 获胜者:${nickName} ID:${studentId}`, 'winner')
      }
      this.resultData.studentId = studentId
      this.resultData.nickName = nickName
      this.resultData.avatar = data?.avatar || normalAvatar
      speedyHandLog.info('0704 有人抢答 2', this.resultData)
      this.$refs.resultAudio?.play()
      document.getElementById('result')?.play()
      // 摧毁lottie
      this.destoryLottie()
    },
    /**
     * 摧毁lottie
     */
    destoryLottie() {
      speedyHandLog.info('清理动画资源', 'cleanup')
      document.getElementById('waitClickLottie')?.destroy()
      document.getElementById('press')?.destroy()
      document.getElementById('loading')?.destroy()
    }
  }
}
</script>
<style lang="scss">
.speedy-hand {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  z-index: 999; // 目前调整上台最高层级 上台视频框的z-index是101
  padding-bottom: 20px;
  height: 100%;
  text-align: center;
  animation: bg1 0.2s 1 forwards;

  @keyframes bg1 {
    0% {
      background: none;
    }

    100% {
      background: rgba(0, 0, 0, 0.7);
    }
  }

  &-box {
    width: 450px;
    height: 423px;
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .mouseAction {
      width: 240px;
      height: 184px;
      position: absolute;
      top: 143px;
      left: 105px;
      z-index: 103;
      cursor: url(./imgs/hand1.png), auto;
    }
  }

  .result {
    &-student {
      &-img {
        width: 140px;
        height: 140px;
        border-radius: 50%;
        background-color: #fff;
        border: 5px solid #fff;
        position: absolute;
        left: 50%;
        overflow: hidden;
        top: 118px;
        transform: translateX(-50%);

        img {
          display: block;
          width: 100%;
          height: 100%;
        }
      }

      &-name {
        font-size: 19px;
        font-weight: 600;
        color: #ffffff;
        position: absolute;
        top: 300px;
        left: 50%;
        transform: translateX(-50%);
        overflow: hidden;
        word-break: break-all;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    &-nostudent {
      &-tips {
        width: 100%;
        color: #fff;
        font-size: 20px;
        font-weight: 600;
        line-height: 28px;
        text-align: center;
        position: absolute;
        top: 86px;
      }
    }
  }

  .pos {
    position: absolute;
    left: 0;
    top: 0;
  }
}
</style>
