// 入口文件
import InteractionBase from '../interaction-base'
import app from './app'

/**
 * @description
 */
export default class SpeedyHand extends InteractionBase {
  constructor(opts = {}, store = {}) {
    super(app)
    this.options = opts
    this.store = store
    this.dom = opts.roomMessage.roomInfo.interactionController
    this.keepOtherDom = opts.keepOtherDom // 是否保存其他互动状态
    this.initVmApp('抢答')
  }

  /**
   * @description 定义一个数据结构，控制试图改变
   */
  createPropsData() {
    const opts = {}
    return {
      options: this.options,
      ...opts
    }
  }
}
