@font-face {
  font-family: "iconfont"; /* Project id 4919779 */
  src: url('iconfont.woff2?t=1747215804135') format('woff2'),
       url('iconfont.woff?t=1747215804135') format('woff'),
       url('iconfont.ttf?t=1747215804135') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-bofang:before {
  content: "\e61f";
}

.icon-wushipin:before {
  content: "\e61e";
}

.icon-weikaifang:before {
  content: "\e61d";
}

.icon-gengduo:before {
  content: "\e61c";
}

.icon-xiala:before {
  content: "\e61a";
}

.icon-fanhui:before {
  content: "\e619";
}

.icon-guanbi:before {
  content: "\e618";
}

.icon-didian:before {
  content: "\e617";
}

.icon-riqi:before {
  content: "\e616";
}

.icon-shijian:before {
  content: "\e615";
}

.icon-a-logout:before {
  content: "\e614";
}

.icon-a-CorrectionBook:before {
  content: "\e612";
}

.icon-assignment:before {
  content: "\e611";
}

.icon-a-LearnPlan:before {
  content: "\e613";
}

.icon-home:before {
  content: "\e610";
}

