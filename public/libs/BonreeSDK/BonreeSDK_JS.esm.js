var version="2.5.1";function N$6(){return"labels"in HTMLInputElement.prototype}function a$6(ee){if(!ee.isContentEditable){if("innerText"in ee){var Gn=ee.innerText;if(!D$8())for(var qn=ee.querySelectorAll("script, style"),Vn=0;Vn<qn.length;Vn++){var Wn=qn[Vn];"innerText"in Wn&&(Wn=Wn.innerText)&&0<Wn.trim().length&&(Gn=Gn.replace(Wn,""))}return Gn}return ee.textContent}}function y$6(ee,Gn){for(var qn=0;qn<ee.length;qn++){var Vn=ee[qn];if(Gn(Vn))return Vn}}function E$7(ee,Gn,qn){qn=qn===void 0?"":qn;var Vn=ee.charCodeAt(Gn-1),Vn=55296<=Vn&&Vn<=56319?Gn+1:Gn;return ee.length<=Vn?ee:ee.slice(0,Vn)+qn}function d$7(ee){return 100<ee.length?E$7(ee,100)+" [...]":ee}function A$6(ee){return ee.replace(/\s+/g," ")}function S$6(ee,Gn){return ee.ownerDocument?ee.ownerDocument.getElementById(Gn):null}var g$6=[function(ee){var Gn;if(N$6()){if("labels"in ee&&ee.labels&&0<ee.labels.length)return a$6(ee.labels[0])}else if(ee.id)return(Gn=y$6(ee.ownerDocument.querySelectorAll("label"),function(qn){return qn.htmlFor===ee.id}))&&a$6(Gn)},function(ee){if(ee.nodeName==="INPUT"){var Gn=ee.getAttribute("type"),qn=ee.getAttribute("bonree-value");if(qn==="part"||qn==="all"||Gn==="button"||Gn==="submit"||Gn==="reset")return ee.value}},function(ee){if(ee.nodeName==="BUTTON"||ee.nodeName==="LABEL"||ee.getAttribute("role")==="button")return a$6(ee)},function(ee){return ee.getAttribute("aria-label")},function(ee){var Gn=ee.getAttribute("aria-labelledby");if(Gn){for(var qn=Gn.split(/\s+/),Vn=[],Wn=0;Wn<qn.length;Wn++){var jn=S$6(ee,qn[Wn]);jn&&Vn.push(a$6(jn))}return Vn.join(" ")}},function(ee){return ee.getAttribute("alt")},function(ee){return ee.getAttribute("name")},function(ee){return ee.getAttribute("title")},function(ee){return ee.getAttribute("placeholder")},function(ee){if("options"in ee&&0<ee.options.length)return a$6(ee.options[0])}];function D$8(){return!I$b()}function I$b(){return!!document.documentMode}var L$6=[function(ee){return a$6(ee)}],x$8=10;function l$7(ee,Gn){for(var qn=ee,Vn=0;Vn<=x$8&&qn&&qn.nodeName!=="BODY"&&qn.nodeName!=="HTML"&&qn.nodeName!=="HEAD";){for(var Wn=0;Wn<Gn.length;Wn++){var jn=(0,Gn[Wn])(qn);if(typeof jn=="string"&&(jn=jn.trim(),jn))return d$7(A$6(jn))}if(qn.nodeName==="FORM")break;qn=qn.parentElement,Vn++}}function B$4(ee){return ee.textContent.replace(/\s+/g,"")}function p$b(ee,Gn){return(Gn=Gn===void 0?10:Gn)!==0&&ee&&!ee.hasAttribute("bonree-title")&&ee.nodeName!=="BODY"&&ee.nodeName!=="HTML"&&ee.nodeName!=="HEAD"?p$b(ee.parentElement,Gn-1):ee}function w$c(ee,Gn,qn){if(qn=qn===void 0?"##":qn,Gn=(ee=p$b(ee,Gn)).getAttribute("bonree-content"),ee.hasAttribute("bonree-title")){var Vn=ee.getAttribute("bonree-title");if(isDefined(Gn)&&!isEmpty(Gn))try{new RegExp(Gn).test(Vn)}catch(Wn){}return Vn}return T$6(ee,Gn,qn).slice(0,-qn.length)}function T$6(ee,Gn,qn){for(var Vn="",Wn=0;Wn<ee.childNodes.length;Wn++){var jn=ee.childNodes[Wn];if(jn.nodeType===3){var Qn=B$4(jn);if(Qn)if(Gn)try{new RegExp(Gn).test(Qn)&&(Vn+=Qn+qn)}catch(Xn){}else Vn+=Qn+qn}else if(jn.nodeType===1&&jn.nodeName==="INPUT")for(var Jn=0;Jn<g$6.length;Jn++){var eo=g$6[Jn](jn);typeof eo=="string"&&(eo=eo.trim())&&(Vn+=d$7(A$6(eo))+qn)}else jn.nodeType===1&&jn.nodeName!=="SCRIPT"&&(Vn+=T$6(jn,Gn,qn))}return Vn}function getActionText(ee,Gn,qn){return isDefined(Gn)&&0<Gn?w$c(ee,Gn,qn):l$7(ee,g$6)||l$7(ee,L$6)||""}function n$3(ee){var Gn=Math.floor(100*Math.random());return!(0<=ee&&ee<=100)||Gn<ee}var d$6=8;function hex_md5(ee){return p$a(w$b(k$5(ee),ee.length*d$6))}function w$b(ee,Gn){ee[Gn>>5]|=128<<Gn%32,ee[14+(Gn+64>>>9<<4)]=Gn;for(var qn=1732584193,Vn=-271733879,Wn=-1732584194,jn=271733878,Qn=0;Qn<ee.length;Qn+=16){var Jn=qn,eo=Vn,Xn=Wn,to=jn,qn=v$6(qn,Vn,Wn,jn,ee[Qn+0],7,-680876936),jn=v$6(jn,qn,Vn,Wn,ee[Qn+1],12,-389564586),Wn=v$6(Wn,jn,qn,Vn,ee[Qn+2],17,606105819),Vn=v$6(Vn,Wn,jn,qn,ee[Qn+3],22,-1044525330);qn=v$6(qn,Vn,Wn,jn,ee[Qn+4],7,-176418897),jn=v$6(jn,qn,Vn,Wn,ee[Qn+5],12,1200080426),Wn=v$6(Wn,jn,qn,Vn,ee[Qn+6],17,-1473231341),Vn=v$6(Vn,Wn,jn,qn,ee[Qn+7],22,-45705983),qn=v$6(qn,Vn,Wn,jn,ee[Qn+8],7,1770035416),jn=v$6(jn,qn,Vn,Wn,ee[Qn+9],12,-1958414417),Wn=v$6(Wn,jn,qn,Vn,ee[Qn+10],17,-42063),Vn=v$6(Vn,Wn,jn,qn,ee[Qn+11],22,-1990404162),qn=v$6(qn,Vn,Wn,jn,ee[Qn+12],7,1804603682),jn=v$6(jn,qn,Vn,Wn,ee[Qn+13],12,-40341101),Wn=v$6(Wn,jn,qn,Vn,ee[Qn+14],17,-1502002290),qn=f$7(qn,Vn=v$6(Vn,Wn,jn,qn,ee[Qn+15],22,1236535329),Wn,jn,ee[Qn+1],5,-165796510),jn=f$7(jn,qn,Vn,Wn,ee[Qn+6],9,-1069501632),Wn=f$7(Wn,jn,qn,Vn,ee[Qn+11],14,643717713),Vn=f$7(Vn,Wn,jn,qn,ee[Qn+0],20,-373897302),qn=f$7(qn,Vn,Wn,jn,ee[Qn+5],5,-701558691),jn=f$7(jn,qn,Vn,Wn,ee[Qn+10],9,38016083),Wn=f$7(Wn,jn,qn,Vn,ee[Qn+15],14,-660478335),Vn=f$7(Vn,Wn,jn,qn,ee[Qn+4],20,-405537848),qn=f$7(qn,Vn,Wn,jn,ee[Qn+9],5,568446438),jn=f$7(jn,qn,Vn,Wn,ee[Qn+14],9,-1019803690),Wn=f$7(Wn,jn,qn,Vn,ee[Qn+3],14,-187363961),Vn=f$7(Vn,Wn,jn,qn,ee[Qn+8],20,1163531501),qn=f$7(qn,Vn,Wn,jn,ee[Qn+13],5,-1444681467),jn=f$7(jn,qn,Vn,Wn,ee[Qn+2],9,-51403784),Wn=f$7(Wn,jn,qn,Vn,ee[Qn+7],14,1735328473),qn=o$3(qn,Vn=f$7(Vn,Wn,jn,qn,ee[Qn+12],20,-1926607734),Wn,jn,ee[Qn+5],4,-378558),jn=o$3(jn,qn,Vn,Wn,ee[Qn+8],11,-2022574463),Wn=o$3(Wn,jn,qn,Vn,ee[Qn+11],16,1839030562),Vn=o$3(Vn,Wn,jn,qn,ee[Qn+14],23,-35309556),qn=o$3(qn,Vn,Wn,jn,ee[Qn+1],4,-1530992060),jn=o$3(jn,qn,Vn,Wn,ee[Qn+4],11,1272893353),Wn=o$3(Wn,jn,qn,Vn,ee[Qn+7],16,-155497632),Vn=o$3(Vn,Wn,jn,qn,ee[Qn+10],23,-1094730640),qn=o$3(qn,Vn,Wn,jn,ee[Qn+13],4,681279174),jn=o$3(jn,qn,Vn,Wn,ee[Qn+0],11,-358537222),Wn=o$3(Wn,jn,qn,Vn,ee[Qn+3],16,-722521979),Vn=o$3(Vn,Wn,jn,qn,ee[Qn+6],23,76029189),qn=o$3(qn,Vn,Wn,jn,ee[Qn+9],4,-640364487),jn=o$3(jn,qn,Vn,Wn,ee[Qn+12],11,-421815835),Wn=o$3(Wn,jn,qn,Vn,ee[Qn+15],16,530742520),qn=F$5(qn,Vn=o$3(Vn,Wn,jn,qn,ee[Qn+2],23,-995338651),Wn,jn,ee[Qn+0],6,-198630844),jn=F$5(jn,qn,Vn,Wn,ee[Qn+7],10,1126891415),Wn=F$5(Wn,jn,qn,Vn,ee[Qn+14],15,-1416354905),Vn=F$5(Vn,Wn,jn,qn,ee[Qn+5],21,-57434055),qn=F$5(qn,Vn,Wn,jn,ee[Qn+12],6,1700485571),jn=F$5(jn,qn,Vn,Wn,ee[Qn+3],10,-1894986606),Wn=F$5(Wn,jn,qn,Vn,ee[Qn+10],15,-1051523),Vn=F$5(Vn,Wn,jn,qn,ee[Qn+1],21,-2054922799),qn=F$5(qn,Vn,Wn,jn,ee[Qn+8],6,1873313359),jn=F$5(jn,qn,Vn,Wn,ee[Qn+15],10,-30611744),Wn=F$5(Wn,jn,qn,Vn,ee[Qn+6],15,-1560198380),Vn=F$5(Vn,Wn,jn,qn,ee[Qn+13],21,1309151649),qn=F$5(qn,Vn,Wn,jn,ee[Qn+4],6,-145523070),jn=F$5(jn,qn,Vn,Wn,ee[Qn+11],10,-1120210379),Wn=F$5(Wn,jn,qn,Vn,ee[Qn+2],15,718787259),Vn=F$5(Vn,Wn,jn,qn,ee[Qn+9],21,-343485551),qn=h$9(qn,Jn),Vn=h$9(Vn,eo),Wn=h$9(Wn,Xn),jn=h$9(jn,to)}return Array(qn,Vn,Wn,jn)}function _$5(ee,Gn,qn,Vn,Wn,jn){return h$9(C$9(h$9(h$9(Gn,ee),h$9(Vn,jn)),Wn),qn)}function v$6(ee,Gn,qn,Vn,Wn,jn,Qn){return _$5(Gn&qn|~Gn&Vn,ee,Gn,Wn,jn,Qn)}function f$7(ee,Gn,qn,Vn,Wn,jn,Qn){return _$5(Gn&Vn|qn&~Vn,ee,Gn,Wn,jn,Qn)}function o$3(ee,Gn,qn,Vn,Wn,jn,Qn){return _$5(Gn^qn^Vn,ee,Gn,Wn,jn,Qn)}function F$5(ee,Gn,qn,Vn,Wn,jn,Qn){return _$5(qn^(Gn|~Vn),ee,Gn,Wn,jn,Qn)}function h$9(ee,Gn){var qn=(65535&ee)+(65535&Gn);return(ee>>16)+(Gn>>16)+(qn>>16)<<16|65535&qn}function C$9(ee,Gn){return ee<<Gn|ee>>>32-Gn}function k$5(ee){for(var Gn=Array(),qn=(1<<d$6)-1,Vn=0;Vn<ee.length*d$6;Vn+=d$6)Gn[Vn>>5]|=(ee.charCodeAt(Vn/d$6)&qn)<<Vn%32;return Gn}function p$a(ee){for(var Gn="0123456789abcdef",qn="",Vn=0;Vn<4*ee.length;Vn++)qn+=Gn.charAt(ee[Vn>>2]>>Vn%4*8+4&15)+Gn.charAt(ee[Vn>>2]>>Vn%4*8&15);return qn}var e$4={"3dm":"x-world/x-3dmf","3dmf":"x-world/x-3dmf",a:"application/octet-stream",aab:"application/x-authorware-bin",aam:"application/x-authorware-map",aas:"application/x-authorware-seg",abc:"text/vndabc",acgi:"text/html",afl:"video/animaflex",ai:"application/postscript",aif:"audio/x-aiff",aifc:"audio/x-aiff",aiff:"audio/x-aiff",aim:"application/x-aim",aip:"text/x-audiosoft-intra",ani:"application/x-navi-animation",aos:"application/x-nokia-9000-communicator-add-on-software",aps:"application/mime",arc:"application/octet-stream",arj:"application/octet-stream",art:"image/x-jg",asf:"video/x-ms-asf",asm:"text/x-asm",asp:"text/asp",asx:"application/x-mplayer2",au:"audio/x-au",avi:"video/avi",bcpio:"application/x-bcpio",bin:"application/x-macbinary",bm:"image/bmp",bmp:"image/x-windows-bmp",boo:"application/book",book:"application/book",boz:"application/x-bzip2",bsh:"application/x-bsh",bz:"application/x-bzip",bz2:"application/x-bzip2",c:"text/plain","c++":"text/plain",cat:"application/vndms-pki.seccat",cc:"text/plain",ccad:"application/clariscad",cco:"application/x-cocoa",cdf:"application/x-cdf",cer:"application/pkix-cert",cha:"application/x-chat",chat:"application/x-chat",class:"application/x-java-class",com:"application/octet-stream",conf:"text/plain",cpio:"application/x-cpio",cpp:"text/x-c",cpt:"application/x-cpt",crl:"application/pkcs-crl",crt:"application/x-x509-ca-cert",csh:"application/x-csh",css:"application/x-pointplus",cxx:"text/plain",dcr:"application/x-director",deepv:"application/x-deepv",der:"application/x-x509-ca-cert",dif:"video/x-dv",dir:"application/x-director",dl:"video/dl",doc:"application/msword",dot:"application/msword",dp:"application/commonground",drw:"application/drafting",dump:"application/octet-stream",dv:"video/x-dv",dvi:"application/x-dvi",dwf:"model/vnd.dwf",dwg:"application/acad",dxf:"application/dxf",dxr:"application/x-director",el:"text/x-script.elisp",elc:"application/x-bytecode.elisp compiled elisp)",env:"application/x-envoy",eps:"application/postscript",es:"application/x-esrehber",etx:"text/x-setext",evy:"application/x-envoy",exe:"application/octet-stream",f:"text/x-fortran",f77:"text/x-fortran",f90:"text/x-fortran",fdf:"application/vnd.fdf",fif:"application/fractals",fli:"video/x-fli",flo:"image/florian",flx:"text/vnd.fmi.flexstor",fmf:"video/x-atomic3d-feature",for:"text/x-fortran",fpx:"image/vnd.net-fpx",frl:"application/freeloader",funk:"audio/make",g:"text/plain",g3:"image/g3fax",gif:"image/gif",gl:"video/x-gl",gsd:"audio/x-gsm",gsm:"audio/x-gsm",gsp:"application/x-gsp",gss:"application/x-gss",gtar:"application/x-gtar",gz:"application/x-compressed",gzip:"application/x-gzip",h:"text/x-h",hdf:"application/x-hdf",help:"application/x-helpfile",hgl:"application/vnd.hp-hpgl",hh:"text/x-h",hlb:"text/x-script",hlp:"application/x-winhelp",hpg:"application/vnd.hp-hpgl",hpgl:"application/vnd.hp-hpgl",hqx:"application/binhex",hta:"application/hta",htc:"text/x-component",htm:"text/html",html:"text/html",htmls:"text/html",htt:"text/webviewhtml",htx:"text/html",ice:"x-conference/x-cooltalk",ico:"image/x-icon",idc:"text/plain",ief:"image/ief",iefs:"image/ief",iges:"application/iges",igs:"model/iges",ima:"application/x-ima",imap:"application/x-httpd-imap",inf:"application/inf",ins:"application/x-internett-signup",ip:"application/x-ip2",isu:"video/x-isvideo",it:"audio/it",iv:"application/x-inventor",ivr:"i-world/i-vrml",ivy:"application/x-livescreen",jam:"audio/x-jam",jav:"text/x-java-source",java:"text/x-java-source",jcm:"application/x-java-commerce",jfif:"image/pjpeg","jfif-tbnl":"image/jpeg",jpe:"image/pjpeg",jpeg:"image/pjpeg",jpg:"image/pjpeg",jps:"image/x-jps",js:"application/x-javascript",jut:"image/jutvision",kar:"audio/midi",ksh:"application/x-ksh",la:"audio/x-nspaudio",lam:"audio/x-liveaudio",latex:"application/x-latex",lha:"application/octet-stream",lhx:"application/octet-stream",list:"text/plain",lma:"audio/x-nspaudio",log:"text/plain",lsp:"application/x-lisp",lst:"text/plain",lsx:"text/x-la-asf",ltx:"application/x-latex",lzh:"application/octet-stream",lzx:"application/octet-stream",m:"text/x-m",m1v:"video/mpeg",m2a:"audio/mpeg",m2v:"video/mpeg",m3u:"audio/x-mpequrl",man:"application/x-troff-man",map:"application/x-navimap",mar:"text/plain",mbd:"application/mbedlet",mc$:"application/x-magic-cap-package-1.0",mcd:"application/x-mathcad",mcf:"text/mcf",mcp:"application/netmc",me:"application/x-troff-me",mht:"message/rfc822",mhtml:"message/rfc822",mid:"application/x-midi",midi:"application/x-midi",mif:"application/x-mif",mime:"www/mime",mjf:"audio/x-vnd.audioexplosion.mjuicemediafile",mjpg:"video/x-motion-jpeg",mm:"application/x-meme",mod:"audio/x-mod",moov:"video/quicktime",mov:"video/quicktime",movie:"video/x-sgi-movie",mp2:"audio/x-mpeg",mp3:"audio/x-mpeg-3",mpa:"audio/mpeg",mpc:"application/x-project",mpe:"video/mpeg",mpeg:"video/mpeg",mpg:"video/mpeg",mpga:"audio/mpeg",mpp:"application/vnd.ms-project",mpt:"application/x-project",mpv:"application/x-project",mpx:"application/x-project",mrc:"application/marc",ms:"application/x-troff-ms",mv:"video/x-sgi-movie",my:"audio/make",mzz:"application/x-vnd.audioexplosion.mzz",nap:"image/naplps",naplps:"image/naplps",nc:"application/x-netcdf",ncm:"application/vnd.nokia.configuration-message",nif:"image/x-niff",niff:"image/x-niff",nix:"application/x-mix-transfer",nsc:"application/x-conference",nvd:"application/x-navidoc",o:"application/octet-stream",oda:"application/oda",omc:"application/x-omc",omcd:"application/x-omcdatamaker",omcr:"application/x-omcregerator",p:"text/x-pascal",p10:"application/x-pkcs10",p12:"application/x-pkcs12",p7a:"application/x-pkcs7-signature",p7c:"application/x-pkcs7-mime",p7m:"application/x-pkcs7-mime",p7r:"application/x-pkcs7-certreqresp",p7s:"application/pkcs7-signature",part:"application/pro_eng",pas:"text/pascal",pbm:"image/x-portable-bitmap",pcl:"application/x-pcl",pcx:"image/x-pcx",pdb:"chemical/x-pdb",pdf:"application/pdf",pfunk:"audio/make.my.funk",pgm:"image/x-portable-greymap",pic:"image/pict",pict:"image/pict",pkg:"application/x-newton-compatible-pkg",pko:"application/vnd.ms-pki.pko",pl:"text/x-script.perl",plx:"application/x-pixclscript",pm:"text/x-script.perl-module",pm4:"application/x-pagemaker",pm5:"application/x-pagemaker",png:"image/png",pnm:"application/x-portable-anymap",pot:"application/mspowerpoint",pov:"model/x-pov",ppa:"application/vnd.ms-powerpoint",ppm:"image/x-portable-pixmap",pps:"application/vnd.ms-powerpoint",ppt:"application/powerpoint",ppz:"application/mspowerpoint",pre:"application/x-freelance",prt:"application/pro_eng",ps:"application/postscript",psd:"application/octet-stream",pvu:"paleovu/x-pv",pwz:"application/vnd.ms-powerpoint",py:"text/x-script.phyton",pyc:"application/x-bytecode.python",qcp:"audio/vnd.qcelp",qd3:"x-world/x-3dmf",qd3d:"x-world/x-3dmf",qif:"image/x-quicktime",qt:"video/quicktime",qtc:"video/x-qtc",qti:"image/x-quicktime",qtif:"image/x-quicktime",ra:"audio/x-realaudio",ram:"audio/x-pn-realaudio",ras:"application/x-cmu-raster",rast:"image/cmu-raster",rexx:"text/x-script.rexx",rf:"image/vnd.rn-realflash",rgb:"image/x-rgb",rm:"application/vnd.rn-realmedia",rmm:"audio/x-pn-realaudio",rmp:"audio/x-pn-realaudio-plugin",rng:"application/vnd.nokia.ringing-tone",rnx:"application/vnd.rn-realplayer",roff:"application/x-troff",rp:"image/vnd.rn-realpix",rpm:"audio/x-pn-realaudio-plugin",rt:"text/vnd.rn-realtext",rtf:"application/x-rtf",rtx:"application/rtf",rv:"video/vnd.rn-realvideo",s:"text/x-asm",s3m:"audio/s3m",saveme:"application/octet-stream",sbk:"application/x-tbook",scm:"application/x-lotusscreencam",sdml:"text/plain",sdp:"application/x-sdp",sdr:"application/sounder",sea:"application/x-sea",set:"application/set",sgm:"text/x-sgml",sgml:"text/x-sgml",sh:"application/x-sh",shar:"application/x-shar",shtml:"text/x-server-parsed-html",sid:"audio/x-psid",sit:"application/x-sit",skd:"application/x-koan",skm:"application/x-koan",skp:"application/x-koan",skt:"application/x-koan",sl:"application/x-seelogo",smi:"application/smil",smil:"application/smil",snd:"audio/x-adpcm",sol:"application/solids",spc:"application/x-pkcs7-certificates",spl:"application/futuresplash",spr:"application/x-sprite",sprite:"application/x-sprite",src:"application/x-wais-source",ssi:"text/x-server-parsed-html",ssm:"application/streamingmedia",sst:"application/vnd.ms-pki.certstore",step:"application/step",stl:"application/vnd.ms-pki.stl",stp:"application/step",sv4cpio:"application/x-sv4cpio",sv4crc:"application/x-sv4crc",svf:"image/x-dwg",svr:"application/x-world",swf:"application/x-shockwave-flash",t:"application/x-troff",talk:"text/x-speech",tar:"application/x-tar",tbk:"application/toolbook",tcl:"application/x-tcl",tcsh:"text/x-script.tcsh",tex:"application/x-tex",texi:"application/x-texinfo",texinfo:"application/x-texinfo",text:"application/plain",tgz:"application/gnutar",tif:"image/x-tiff",tiff:"image/x-tiff",tr:"application/x-troff",tsi:"audio/tsp-audio",tsp:"application/dsptype",tsv:"text/tab-separated-values",turbot:"image/florian",txt:"text/plain",uil:"text/x-uil",uni:"text/uri-list",unis:"text/uri-list",unv:"application/i-deas",uri:"text/uri-list",uris:"text/uri-list",ustar:"application/x-ustar",uu:"application/octet-stream",uue:"text/x-uuencode",vcd:"application/x-cdlink",vcs:"text/x-vcalendar",vda:"application/vda",vdo:"video/vdo",vew:"application/groupwise",viv:"video/vivo",vivo:"video/vivo",vmd:"application/vocaltec-media-desc",vmf:"application/vocaltec-media-file",voc:"audio/x-voc",vos:"video/vosaic",vox:"audio/voxware",vqe:"audio/x-twinvq-plugin",vqf:"audio/x-twinvq",vql:"audio/x-twinvq-plugin",vrml:"application/x-vrml",vrt:"x-world/x-vrt",vsd:"application/x-visio",vst:"application/x-visio",vsw:"application/x-visio",w60:"application/wordperfect6.0",w61:"application/wordperfect6.1",w6w:"application/msword",wav:"audio/x-wav",wb1:"application/x-qpro",wbmp:"image/vnd.wap.wbmp",web:"application/vnd.xara",wiz:"application/msword",wk1:"application/x-123",wmf:"windows/metafile",wml:"text/vnd.wap.wml",wmlc:"application/vnd.wap.wmlc",wmls:"text/vnd.wap.wmlscript",wmlsc:"application/vnd.wap.wmlscriptc",word:"application/msword",wp:"application/wordperfect",wp5:"application/wordperfect",wp6:"application/wordperfect",wpd:"application/wordperfect",wq1:"application/x-lotus",wri:"application/x-wri",wrl:"application/x-world",wrz:"model/vrml",wsc:"text/scriplet",wsrc:"application/x-wais-source",wtk:"application/x-wintalk",xbm:"image/x-xbitmap",xdr:"video/x-amt-demorun",xgz:"xgl/drawing",xif:"image/vnd.xiff",xl:"application/excel",xla:"application/x-msexcel",xlb:"application/x-excel",xlc:"application/x-excel",xld:"application/x-excel",xlk:"application/x-excel",xll:"application/x-excel",xlm:"application/x-excel",xls:"application/excel",xlt:"application/excel",xlv:"application/x-excel",xlw:"application/vnd.ms-excel",xm:"audio/xm",xml:"application/xml",xmz:"xgl/movie",xpix:"application/x-vnd.ls-xpix",xpm:"image/x-xpixmap","x-png":"image/png",xsr:"video/x-amt-showrun",xwd:"image/x-xwindowdump",xyz:"chemical/x-pdb",z:"application/x-compressed",zip:"multipart/x-zip",zoo:"application/octet-stream",zsh:"text/x-script.zsh"};function getMineTypeByHeader(ee,Gn){return Gn=Gn||"text/html",isEmpty(ee)?Gn:(ee["Content-Type"]||ee["content-type"]||Gn).split(";")[0].trim()}function getMineTypeByUrl(ee){var Gn="text/html";return isEmpty(ee)?Gn:l$6(c$a(ee),Gn)}function c$a(ee){var Gn,qn,Vn;return isEmpty(ee)?"":(Gn="",(qn=ee.lastIndexOf("."))!=-1&&((Vn=ee.indexOf("?",qn))==-1&&(Vn=ee.length),Gn=ee.substring(qn+1,Vn)),Gn)}function l$6(ee,Gn){return isEmpty(ee)||isEmpty(ee=e$4[ee])?Gn:ee}function getRandom(ee){return ee<0?NaN:ee<=30?0|Math.random()*(1<<ee):ee<=53?(0|Math.random()*(1<<30))+(0|Math.random()*(1<<ee-30))*(1<<30):NaN}function x$7(ee,Gn){for(var qn=ee.toString(16),Vn=Gn-qn.length,Wn="0";0<Vn;Vn>>>=1,Wn+=Wn)1&Vn&&(qn=Wn+qn);return qn}function uuid(){return x$7(getRandom(32),8)+"-"+x$7(getRandom(16),4)+"-"+x$7(16384|getRandom(12),4)+"-"+x$7(32768|getRandom(14),4)+"-"+x$7(getRandom(48),12)}function uuidWithLength(ee){return ee==16?x$7(getRandom(32),8)+x$7(getRandom(32),8):ee==32?x$7(getRandom(32),8)+x$7(getRandom(32),8)+x$7(getRandom(32),8)+x$7(getRandom(32),8):void 0}function skyUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(ee){var Gn=16*Math.random()|0;return(ee==="x"?Gn:3&Gn|8).toString(16)})}function buildViewID(){return Date.now().toString(36)+Math.ceil(1e3*Math.random()).toString(16)}function enBase64(ee){for(var Gn=typeof TextEncoder!="undefined"?new TextEncoder().encode(ee):function(Qn){for(var Jn=[],eo=0;eo<Qn.length;eo++){var Xn=Qn.charCodeAt(eo);Xn<128?Jn.push(Xn):(Xn<2048?Jn.push(192|Xn>>6):(Xn<65536?Jn.push(224|Xn>>12):(Jn.push(240|Xn>>18),Jn.push(128|Xn>>12&63)),Jn.push(128|Xn>>6&63)),Jn.push(128|63&Xn))}return Jn}(ee),qn=[],Vn=0,Wn=Gn.length;Vn<Wn;Vn+=4096){var jn=typeof Gn.subarray=="function"?Gn.subarray(Vn,Vn+4096):Gn.slice(Vn,Vn+4096);qn.push(String.fromCharCode.apply(null,jn))}return compatibleToB64(qn.join(""))}function traceId(ee){return 0<=ee&&ee<=100&&n$3(ee)?"00-"+x$7(getRandom(32),8)+x$7(getRandom(32),8)+x$7(getRandom(32),8)+x$7(getRandom(32),8)+"-"+x$7(getRandom(32),8)+x$7(getRandom(32),8)+"-01":"00-"+x$7(getRandom(32),8)+x$7(getRandom(32),8)+x$7(getRandom(32),8)+x$7(getRandom(32),8)+"-"+x$7(getRandom(32),8)+x$7(getRandom(32),8)+"-00"}var $global=window||{},storageMethod=[!0,!0,!0];function checkStorageSupport(){var ee=isLocalStorageSupported(),Gn=isSessionStorageSupported(),qn=isCookieSupported();storageMethod=[ee,Gn,qn]}function isLocalStorageSupported(){try{var ee="__bonree__";return localStorage.setItem(ee,1),localStorage.removeItem(ee),!0}catch(Gn){return!1}}function isSessionStorageSupported(){try{var ee="__bonree__";return sessionStorage.setItem(ee,1),sessionStorage.removeItem(ee),!0}catch(Gn){return!1}}function isCookieSupported(){if(!navigator.cookieEnabled)return!1;try{document.cookie="bonree=1; SameSite=Lax";var ee=document.cookie.indexOf("bonree=")!==-1;return document.cookie="bonree=; expires=Thu, 01 Jan 1970 00:00:00 GMT",ee}catch(Gn){return!1}}function isDefined(ee){try{return ee!=null}catch(Gn){return!1}}function log(ee,Gn){(Gn||isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.isDebuge)||isDefined(window.BonreeClient)&&isDefined(window.BonreeClient.isDebuge))&&isDefined(window.console)&&(window.console.$bonree=!0,console.log(ee),window.console.$bonree=!1)}function extend(ee,Gn){return ee&&Gn&&forEachOwn(Gn,function(qn,Vn){ee[Vn]=qn}),ee}function isString(ee){return typeof ee=="string"}function isJSON(ee){if(isString(ee))try{var Gn=JSON.parse(ee);return!(typeof Gn!="object"||!Gn)}catch(qn){return!1}}function NIL_FN(){}function hasOwnProperty(ee,Gn){return ee&&Object.prototype.hasOwnProperty.call(ee,Gn)}function isObject(ee){return typeof ee=="object"}function overwrite(ee,Gn){return ee&&Gn&&forEachOwn(Gn,function(qn,Vn){hasOwnProperty(ee,Vn)&&(ee[Vn]=qn)}),ee}function forEachOwn(ee,Gn,qn){if(isDefined(ee)){if(!(typeof ee!="object"&&typeof ee!="function"))for(var Vn in ee)try{if(hasOwnProperty(ee,Vn)&&Gn(ee[Vn],Vn)===!1)break}catch(Wn){}qn&&qn()}}function isFunction(ee){return typeof ee=="function"}function isArray(ee){return isDefined(ee)&&ee instanceof Array}function forEach(ee,Gn,qn){return(isArray(ee)?forEachArray:forEachOwn)(ee,Gn,qn)}function forEachArray(ee,Gn,qn){if(isArray(ee)){for(var Vn=0,Wn=ee.length;Vn<Wn&&Gn(ee[Vn],Vn)!==!1;Vn++);qn&&qn()}}function bind(ee,Gn){return function(){return ee.apply(Gn,arguments)}}function isEmpty(ee){if(isDefined(ee)){if(hasOwnProperty(ee,"length"))return!ee.length;if(hasOwnProperty(ee,"size"))return!ee.size;for(var Gn in ee)if(hasOwnProperty(ee,Gn))return!1}return!0}function setCookie(ee,Gn,qn){try{var Vn=ee+"="+Gn;isDefined((qn=qn||{}).domain)&&(Vn+=";domain="+qn.domain),isDefined(qn.path)&&(Vn+=";path="+qn.path),isDefined(qn.expires)&&(typeof qn.expires=="string"?Vn+=";expires="+qn.expires:Vn+=";expires="+qn.expires.toUTCString()),storageMethod[0]===!0?window.localStorage&&window.localStorage.setItem(ee,Gn):storageMethod[1]===!0?window.sessionStorage&&window.sessionStorage.setItem(ee,Gn):storageMethod[2]===!0&&(document.cookie=Vn)}catch(Wn){log("C107")}}function getLocaStore(ee){var Gn="";return Gn=window.localStorage?window.localStorage.getItem(ee)||"":Gn}function getCookie(ee){try{var Gn=document.cookie.split(/;\s?/);if(document.cookie)for(var qn=0,Vn=Gn.length;qn<Vn;qn++){var Wn=Gn[qn].split("="),jn=Wn[0],Qn=Wn[1];if(jn===ee&&isDefined(Qn))return Qn}return getSession(ee)}catch(Jn){log("C108")}}function getSession(ee){var Gn="";return Gn=window.sessionStorage&&isDefined(Gn=window.sessionStorage.getItem(ee)||"")&&Gn!=""?Gn:getLocaStore(ee)||""}function setLocalStore(ee,Gn){window.localStorage&&window.localStorage.setItem(ee,Gn)}function isReadable(ee){return isDefined(ee)&&!isEmpty(ee)}function dateNow(){return Date.now?Date.now():new Date().getTime()}var nowtime=function(){var ee=dateNow();return function(Gn){var qn;return isDefined($global.performance)&&isDefined($global.performance.now)&&isDefined($global.performance.timing)?[1e3*(qn=Gn===0?0:Math.round($global.performance.now())),1e3*(qn+$global.performance.timing.navigationStart)]:[1e3*((qn=Gn===0?ee:dateNow())-ee),1e3*qn]}}();function now(ee){return 1e3*Date.now()}function tillNow(ee){return nowtime(ee)[0]}function args$1(ee){return isDefined(ee.length)?Array.prototype.slice.call(ee):ee}function map(ee,Gn){var qn=[];return forEach(ee,function(Vn,Wn){Vn=Gn&&Gn(Vn,Wn),isDefined(Vn)&&qn.push(Vn)}),qn}var getFixedMetric=function(ee,Gn){return ee=ee[Gn],isDefined(ee)&&0<ee?Math.round(ee):0},getFixedUsMetric=function(ee,Gn){return ee=ee[Gn],isDefined(ee)&&0<ee?Math.round(1e3*ee):0},getContentSize=function(ee){return isDefined(ee)?window.ArrayBuffer&&ee instanceof ArrayBuffer?ee.byteLength:window.Blob&&ee instanceof Blob||ee instanceof blobPolyfill?ee.size:ee.toJSON?stringify(ee.toJSON()).length:ee.length||0:0},getRequestParam=function(ee){if(!isDefined(ee))return"";var Gn="";if(typeof ee=="string")Gn=ee;else if(window.FormData&&ee instanceof FormData){var qn={};try{forEach(iterate(ee.entries()),function(Qn){var Wn,jn=Qn[0],Qn=Qn[1];window.File&&Qn instanceof File?0<Qn.size&&(Wn=Qn.name?" "+Qn.name:"",qn[jn]="[File]"+Wn):qn[jn]=Qn})}catch(Vn){qn={},log("C145")}Gn="[FormData] "+stringify(qn)}else{if(window.Blob&&ee instanceof Blob||ee instanceof blobPolyfill)return"[Blob]";if(window.ArrayBuffer&&ee instanceof ArrayBuffer)return"[ArrayBuffer]";if(ee.toJSON)Gn=stringify(ee.toJSON());else{if(!ee.toString)return"[unknown]";Gn=ee.toString()}}return withLength(Gn,2e3)};function getIP(ee){return ee?(ee=ee.split("?")[0],/(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\:([0-9]{1,5})/.test(ee)?[RegExp.$1+"."+RegExp.$2+"."+RegExp.$3+"."+RegExp.$4,Number(RegExp.$5)]:/\:([0-9]{1,5})/.test(ee)?["",Number(RegExp.$1)]:-1<ee.indexOf("https")?["",443]:["",80]):["",""]}function getUrl(ee){function Gn(){return window.location.origin||window.location.protocol+"//"+window.location.hostname+(window.location.port?":"+window.location.port:"")}return ee=ee||"",window.location.protocol!=="file:"||startWith(ee,"http")?isDefined(ee)&&ee.indexOf("http")===0?ee:/^\/{2}/.test(ee)?window&&window.location.protocol+ee||"http:"+ee:/^\?/.test(ee)?window&&Gn()+window.location.pathname+ee||ee:/^\/{1}/.test(ee)?window&&Gn()+ee||ee:/^\.{1,}/.test(ee)?window&&Gn()+ee.replace(/^(\.{1,}\/){1,}/,"/")||ee:isDefined(ee)&&window&&Gn()+"/"+ee||ee:ee||window&&window.location.href+ee}var stringify=isDefined($global.JSON)?$global.JSON.stringify:function(ee){var Gn,qn,Vn=typeof ee;return Vn=="undefined"?'"undefined"':Vn=="boolean"?ee?"true":"false":Vn=="number"?ee.toString():Vn=="string"?'"'+(Gn=ee.replace(/(["\\])/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n"))+'"':Vn=="object"?ee?ee instanceof Array?(Gn="[",forEach(ee,function(Wn,jn){0<jn&&(Gn+=","),Gn+=stringify(Wn)}),Gn+="]"):(Gn="{",qn=0,forEachOwn(ee,function(Wn,jn){0<qn&&(Gn+=","),Gn+=stringify(jn)+":"+stringify(Wn),qn++}),Gn+="}"):"null":"[none]"};function addListener(ee,Gn,qn,Vn){ee&&ee.addEventListener?ee.addEventListener(Gn,qn,Vn):ee&&ee.attachEvent&&ee.attachEvent("on"+Gn,qn)}function on$1(ee,Gn,qn,Vn){if(ee&&ee.addEventListener)return ee.addEventListener(Gn,qn,Vn);ee&&ee.attachEvent&&ee.attachEvent("on"+Gn,qn)}function delay(ee,Gn){return ee.$$original=!0,setTimeout(ee,isDefined(Gn)?Gn:0)}function last(ee){if(isArray(ee)&&!isEmpty(ee))return ee[ee.length-1]}var unicode=function(ee){for(var Gn="",qn=0;qn<ee.length;qn++)Gn+="\\u"+("0000"+ee.charCodeAt(qn).toString(16)).slice(-4);return Gn},hack=function(ee,Gn,qn,Vn){function Wn(eo){isDefined(window.console)&&(window.console.$bonree=!0,window.console.error(eo),window.console.$bonree=!1)}var jn;if(isDefined(ee)?typeof ee!="function"&&(jn=ee,ee=function(){return jn}):ee=function(){},ee.$$original)return ee;function Qn(){var eo;try{Xn=args$1(arguments)}catch(io){for(var Xn=[],to=0;to<arguments.length;to++)Xn.push(arguments[to])}try{return isDefined(Gn)&&typeof Gn=="function"&&Gn.apply(this,Xn),eo=ee.apply(this,Xn)}catch(io){if(isDefined(Vn)&&typeof Vn=="function")try{Vn.apply(this,Xn.concat(io))}catch(ro){Wn(ro)}else Wn(io);if(io&&io.name==="TypeError"&&/undefined is not a function/.test(io.message))log("C130");else if(Xn&&Xn[0]&&Xn[0]instanceof Error)throw Xn[0]}finally{try{isDefined(qn)&&typeof qn=="function"&&qn.apply(this,Xn)}catch(io){}if(Xn&&Xn[0]&&Xn[0]instanceof Error&&ee&&ee.name!=="error"&&eo===void 0)throw Xn[0]}return eo}Qn.$$original=ee;try{for(var Jn in ee)hasOwnProperty(ee,Jn)&&Jn!=="prototype"&&(Qn[Jn]=ee[Jn])}catch(eo){}return Qn},xpath=function(ee){if(!ee)return"";var Gn=ee.id;if(Gn)return'//*[@id="'+Gn+'"]';for(var qn=[],Vn=ee;Vn;){var Wn=Vn.tagName&&Vn.tagName.toLowerCase&&Vn.tagName.toLowerCase()||null;if(!Wn)break;var jn=Vn.parentElement&&Vn.parentElement||null;if(!isDefined(jn)){qn.unshift(Wn);break}for(var Qn=jn.children,Jn=Qn.length,eo=0,Xn=0,to=0;to<Jn;to++){var io=Qn[to];if(io.tagName.toLowerCase()===Wn&&(Xn++,0<eo))break;io===Vn&&(eo=Xn)}qn.unshift(Xn===1?Wn:Wn+"["+eo+"]"),Vn=jn}return"/"+qn.join("/")},prop=function(ee,Gn){return isDefined(ee)&&hasOwnProperty(ee,Gn)?ee[Gn]:null};function includes(ee,Gn){for(var qn=0,Vn=ee.length;qn<Vn;++qn)if(ee[qn]===Gn)return!0;return!1}function isObj(ee){return typeof ee=="object"}function getUserAgent(){try{var ee={Chrome:/Chrome/,IE:/MSIE/,Firefox:/Firefox/,Opera:/Presto/,Safari:/Version\/([\d.]+).*Safari/,360:/360SE/,QQBrowswe:/QQ/},Gn={iPhone:/iPhone/,iPad:/iPad/,Android:/Android/,Windows:/Windows/,Mac:/Macintosh/},qn=navigator&&navigator.userAgent;if(!qn)return null;var Vn,Wn={browserName:"",browserVersion:"",osName:"",osVersion:"",deviceName:""};for(Vn in ee)ee[Vn].test(qn)&&(Wn.browserName=Vn,Vn==="Chrome"?Wn.browserVersion=qn.split("Chrome/")[1].split(" ")[0]:Vn==="IE"?Wn.browserVersion=qn.split("MSIE ")[1].split(" ")[1]:Vn==="Firefox"?Wn.browserVersion=qn.split("Firefox/")[1]:Vn==="Opera"?Wn.browserVersion=qn.split("Version/")[1]:Vn==="Safari"?Wn.browserVersion=qn.split("Version/")[1].split(" ")[0]:Vn==="360"?Wn.browserVersion="":Vn==="QQBrowswe"&&(Wn.browserVersion=qn.split("Version/")[1].split(" ")[0]));for(Vn in Gn)Gn[Vn].test(qn)&&(Wn.osName=Vn,Vn==="Windows"?Wn.osVersion=qn.split("Windows NT ")[1].split(";")[0]:Vn==="Mac"?Wn.osVersion=qn.split("Mac OS X ")[1].split(")")[0]:Vn==="iPhone"?Wn.osVersion=qn.split("iPhone OS ")[1].split(" ")[0]:Vn==="iPad"?Wn.osVersion=qn.split("iPad; CPU OS ")[1].split(" ")[0]:Vn==="Android"&&(Wn.osVersion=qn.split("Android ")[1].split(";")[0],Wn.deviceName=qn.split("(Linux; Android ")[1].split("; ")[1].split(" Build")[0]));return Wn}catch(jn){log("C123")}}function getElementForNum(ee,Gn){var qn;return isObj(ee)?(qn={},Object.keys(ee).map(function(Vn,Wn){Wn<=Gn&&(qn[Vn]=ee[Vn])}),qn):{}}function iterate(ee){var Gn=[];if(isDefined(ee)&&isDefined(ee.next))for(var qn=ee.next();!qn.done;)Gn.push(qn.value),qn=ee.next();return Gn}function readBytesFromStream(ee,Gn,qn){if(!ee.getReader)return 0;var Vn=ee.getReader(),Wn=[],jn=0;function Qn(){try{Vn&&Vn.cancel&&Vn.cancel()}catch(to){}var Jn,eo,Xn;return Gn.collectStreamBody?(Wn.length===1?eo=Wn[0]:(eo=new Uint8Array(jn),Xn=0,forEach(Wn,function(to){eo.set(to,Xn),Xn+=to.length})),Jn=eo.slice(0,Gn.bytesLimit),eo.length,Gn.bytesLimit,Jn.length):Jn||0}(function Jn(){Vn.read().then(function(eo){eo.done||(Gn.collectStreamBody&&Wn.push(eo.value),(jn+=eo.value.length)>Gn.bytesLimit)?(eo=Qn(),qn(eo)):Jn()})})()}function tryToClone(ee){try{return ee.clone()}catch(Gn){}}function set_64_keys(data,emit){var reData={},keys;if(isDefined(data)){if(typeof data=="string"&&data!==""){if(data=eval("("+data+")"),!isJSON(data))return data;data=JSON.parse(data)}return typeof data!="object"?data:(keys=Object.keys(data),keys.length>emit?(keys=keys.slice(0,emit),forEach(keys,function(ee){reData[ee]=data[ee]}),reData):data)}return reData}function filter(ee,Gn){var qn=[];return forEach(ee,function(Vn,Wn){Gn&&Gn(Vn,Wn)===!0&&qn.push(Vn)}),qn}function setDeviceForLog(ee){window.bonreeRUM&&ee&&(window.bonreeRUM.fingerId=ee)}function size(ee){if(ee){if(typeof ee=="string")return strSize(ee);if(window.ArrayBuffer&&ee instanceof ArrayBuffer)return ee.byteLength;if(isObject(ee)){var Gn="";try{Gn=JSON.stringify(ee)}catch(qn){}return strSize(Gn)}}return 0}function strSize(ee){for(var Gn=0,qn=0,Vn=ee.length;qn<Vn;qn++){var Wn=ee.charCodeAt(qn);Gn+=Wn<=127?1:Wn<=2047?2:Wn<=65535?3:4}return Gn}function trim(ee){return ee?String.prototype.trim?String.prototype.trim.call(ee):ee.toString().replace(/(^\s+)|(\s+$)/g,""):""}function startWith(ee,Gn){return!(!ee||!ee.indexOf)&&ee.indexOf(Gn)===0}function endWith(ee,Gn){return!!areUrlsEqual(ee,Gn)||!!(ee&&Gn&&ee.length>=Gn.length)&&(ee.slice(-1)==="/"&&(Gn+="/"),ee.substring(ee.length-Gn.length)===Gn)}function areUrlsEqual(ee,Gn){try{var qn=new URL(ee),Vn=new URL(Gn);return qn.protocol===Vn.protocol&&qn.host===Vn.host&&qn.pathname===Vn.pathname&&qn.search===Vn.search&&qn.hash===Vn.hash}catch(Wn){return!1}}function withLength(ee,Gn){try{return ee?ee.length<=Gn?ee:ee.substr(0,Gn-3)+"...":""}catch(qn){return"The data cannot be parsed normally, please handle it manually"}}function toUpper(ee,Gn){return isDefined(Gn)||(Gn=""),ee&&ee.toUpperCase?ee.toUpperCase():Gn}function toLower(ee,Gn){return isDefined(Gn)||(Gn=""),ee&&ee.toLowerCase?ee.toLowerCase():Gn}function toString(ee){return typeof ee=="string"?ee:JSON.stringify(ee)}function set_length(ee,Gn){return ee&&(ee.length>Gn?ee.slice(0,Gn):ee)}function getDataSize(ee,Gn){var qn;typeof ee!="string"&&(ee=JSON.stringify(ee));try{qn=(isSupportBlob()?new Blob([ee]):createCompatibleBlob([ee])).size}catch(Vn){qn=strSize(ee),log("C161")}return ee=qn/1048576,isDefined(Gn)?ee<Gn:ee}function isNumber(ee){return typeof ee=="number"&&!isNaN(ee)}function isRightTimestamp(ee){return isNumber(ee)&&isFinite(ee)&&0<=ee}function arrayFrom(ee){if(Array.from)return Array.from(ee);for(var Gn=[],qn=0;qn<ee.length;qn++)Gn.push(ee[qn]);return Gn}function objectAssign(ee){if(ee==null)throw new TypeError("Cannot convert undefined or null to object");for(var Gn=Object(ee),qn=1;qn<arguments.length;qn++){var Vn=arguments[qn];if(Vn!=null)for(var Wn in Vn)Object.prototype.hasOwnProperty.call(Vn,Wn)&&(Gn[Wn]=Vn[Wn])}return Gn}function compatibleToB64(ee){try{if(typeof window.btoa=="function")return window.btoa(ee);for(var Gn,qn=String(ee),Vn="",Wn=0,jn=0,Qn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";jn<qn.length;){if(255<(Gn=qn.charCodeAt(jn++)))throw new Error("\u4E0D\u662F ASCII \u7F16\u7801\u7684\u5B57\u7B26\u4E32");Wn=Wn<<8|Gn,jn%3==0&&(Vn+=Qn.charAt(Wn>>18&63)+Qn.charAt(Wn>>12&63)+Qn.charAt(Wn>>6&63)+Qn.charAt(63&Wn),Wn=0)}return jn%3==1?Vn+=Qn.charAt((Wn<<=4)>>6&63)+Qn.charAt(63&Wn)+"==":jn%3==2&&(Vn+=Qn.charAt((Wn<<=2)>>12&63)+Qn.charAt(Wn>>6&63)+Qn.charAt(63&Wn)+"="),Vn}catch(Jn){return log("C159"),""}}function compatibleFromB64(ee){try{if(typeof window.atob=="function")return window.atob(ee);var Gn=String(ee).replace(/[=]+$/,""),qn="";if(Gn.length%4==1)throw new Error("base64 \u5B57\u7B26\u4E32\u957F\u5EA6\u9519\u8BEF");for(var Vn,Wn=0,jn=0,Qn=0;Vn=Gn.charAt(Qn++);){var Jn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(Vn);if(Jn===-1)throw new Error("\u5305\u542B\u975E\u6CD5\u5B57\u7B26");Wn=Wn<<6|Jn,8<=(jn+=6)&&(jn-=8,qn+=String.fromCharCode(Wn>>>jn&255))}return qn}catch(eo){return log("C160"),""}}function isSupportBlob(){try{return!!window.Blob&&new Blob(["test"]).size===4}catch(ee){return!1}}function blobPolyfill(ee,Gn){if(Gn=Gn||{},this.data=[],this.size=0,this.type=Gn.type||"",ee)for(var qn=0;qn<ee.length;qn++)typeof ee[qn]=="string"&&(this.data.push(ee[qn]),this.size+=ee[qn].length)}function createCompatibleBlob(ee,Gn){if(Gn=Gn||{},isSupportBlob())try{return new Blob(ee,Gn)}catch(Wn){try{for(var qn=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder),Vn=0;Vn<ee.length;Vn++)qn.append(ee[Vn]);return qn.getBlob(Gn.type)}catch(jn){}}return new blobPolyfill(ee,Gn)}function addClass(ee,Gn){ee.classList?ee.classList.add(Gn):ee.className+=" "+Gn}function generateFingerprint(){var ee,Gn=typeof navigator!="undefined"?navigator:{},qn=typeof screen!="undefined"?screen:{width:0,height:0},Vn={hardwareConcurrency:Gn.hardwareConcurrency||0,language:Gn.language||"",platform:Gn.platform||"",screenResolution:qn.width+"x"+qn.height,userAgent:Gn.userAgent||""};try{window.Intl&&typeof Intl.DateTimeFormat=="function"&&(ee=Intl.DateTimeFormat().resolvedOptions().timeZone)&&(Vn.timeZone=ee)}catch(ro){window.console&&window.console.warn&&window.console.warn("Failed to get timezone:",ro&&ro.message)}var Wn,jn={},Qn=[];for(Wn in Vn)Vn.hasOwnProperty(Wn)&&Qn.push(Wn);Qn.sort();for(var Jn=0;Jn<Qn.length;Jn++)jn[Qn[Jn]]=Vn[Qn[Jn]];for(var eo=JSON.stringify(jn),Xn=5381,to=0;to<eo.length;to++)Xn=33*Xn^eo.charCodeAt(to);for(var io=((Xn=182887013*(Xn^Xn>>>15))>>>0).toString(16);io.length<8;)io="0"+io;return io}function isHashRoute(ee){ee=ee||window.location.href;try{var Gn,qn,Vn,Wn,jn;return ee.indexOf("http")===0||ee.indexOf("https")===0?((Gn=document.createElement("a")).href=ee,Vn=2<(qn=Gn.hash||"").length,Wn=/^#\/[^?#\s]+/.test(qn),jn=-1<qn.indexOf("#")&&qn.indexOf("/")===-1,Vn&&Wn&&!jn):!1}catch(Qn){return!1}}function extractHashRoute(ee){var Gn=ee||window.location.href;try{extractHashRoute._anchor||(extractHashRoute._anchor=document.createElement("a"));var qn=extractHashRoute._anchor,Vn=(qn.href=Gn,qn.hash||"");return qn.href="about:blank",((0<Vn.length?Vn.substring(1):"").split("?")[0]||"").trim()}catch(Wn){}}function i$6(ee,Gn){return ee=JSON.parse(ee),ee.k=Gn,ee}function checkMoudle(ee){return!(!isDefined(D$5.secondSett)||!isDefined(D$5.secondSett.mn))&&D$5.secondSett.mn.indexOf(ee)!==-1}var v$5={addAtt:function(ee){try{var Gn=D$5.secondSett;if(!getDataSize(ee,5))return!1;M$6(Gn.osType)?window.webkit.messageHandlers.brsWKAddEventAttributes.postMessage(stringify(ee)):V$4(Gn.osType)||window.bonreePrivateInterface.addEventAttribute(stringify(ee))}catch(qn){}},delAtt:function(ee){try{var Gn=D$5.secondSett;if(!getDataSize(ee,5))return!1;M$6(Gn.osType)?window.webkit.messageHandlers.brsWKRemoveEventAttributes.postMessage(stringify(ee)):V$4(Gn.osType)||window.bonreePrivateInterface.removeEventAttribute(stringify(ee))}catch(qn){}},delAttAll:function(){try{var ee=D$5.secondSett;M$6(ee.osType)?window.webkit.messageHandlers.brsWKRemoveAllEventAttributes.postMessage(""):V$4(ee.osType)||window.bonreePrivateInterface.removeAllEventAttributes()}catch(Gn){}},setUserProperty:function(ee){try{var Gn=D$5.secondSett;if(!getDataSize(ee,5))return!1;M$6(Gn.osType)?window.webkit.messageHandlers.brsWKUserChangeEvent.postMessage(ee):V$4(Gn.osType)||window.bonreePrivateInterface.webViewEventBus(stringify({userchange:[i$6(ee,"userchange")]}),Gn.webviewID)}catch(qn){}},shadeArea:function(ee,Gn){try{var qn={sensitiveInfos:{masks:ee}};if(!getDataSize(qn,5))return!1;M$6(Gn.osType)?window.webkit.messageHandlers.brsWKSensitiveInfos.postMessage(stringify(qn)):V$4(Gn.osType)||window.bonreePrivateInterface.webViewEventBus(stringify(qn),Gn.webviewID)}catch(Vn){}},log:function(ee,Gn){if(!getDataSize(ee,5))return!1;try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKLog.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({log:ee})):window.bonreePrivateInterface.log(ee)}catch(qn){}},webviewPerformanceTimingEvent:function(ee,Gn,qn){if(!getDataSize(ee,5))return!1;if(checkMoudle("h5")){this.log("webviewPerformanceTimingEvent=>"+ee,Gn);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKWebviewPerformanceTimingEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({h5:[i$6(ee,"h5")],imd:qn})):window.bonreePrivateInterface.webViewEventBus(stringify({h5:[i$6(ee,"h5")],imd:qn}),Gn.webviewID)}catch(Vn){}}},webviewJSErrorEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;if(checkMoudle("jserror")){this.log("webviewJSErrorEvent=>"+ee,Gn);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKWebviewJSErrorEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({jserror:[i$6(ee,"jserror")]})):window.bonreePrivateInterface.webViewEventBus(stringify({jserror:[i$6(ee,"jserror")]}),Gn.webviewID)}catch(qn){}}},NetworkEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;if(checkMoudle("network")){this.log("NetworkEvent=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKAjaxPerformanceTimingEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({network:[i$6(ee,"network")]})):window.bonreePrivateInterface.webViewEventBus(stringify({network:[i$6(ee,"network")],imd:2}),Gn.webviewID)}catch(qn){}}},webviewActionEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;if(checkMoudle("action")){this.log("webviewActionEvent=>"+ee,Gn);var qn=JSON.parse(ee);try{M$6(Gn.osType)?(isDefined(qn.v.ice)?qn.v.isa=!0:qn.v.isa=!1,window.webkit.messageHandlers.brsWKWebviewActionEvent.postMessage(stringify(qn))):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({action:[i$6(ee,"action")]})):window.bonreePrivateInterface.webViewEventBus(stringify({action:[i$6(ee,"action")]}),Gn.webviewID)}catch(Vn){}}},webviewPageEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("webviewPageEvent=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKWebviewPageEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({view:[i$6(ee,"view")]})):window.bonreePrivateInterface.webViewEventBus(stringify({view:[i$6(ee,"view")]}),Gn.webviewID)}catch(qn){}},routeChangeData:function(ee,Gn){if(!getDataSize(ee,5))return!1;if(checkMoudle("routechange")){this.log("routeChangeData=>"+ee,Gn);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKRouteChangeEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({routechange:[i$6(ee,"routechange")]})):window.bonreePrivateInterface.webViewEventBus(stringify({routechange:[i$6(ee,"routechange")]}),Gn.webviewID)}catch(qn){}}},consoleEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;if(checkMoudle("console")){this.log("consoleData=>",ee,Gn);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKConsoleEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({console:[i$6(ee,"console")]})):window.bonreePrivateInterface.webViewEventBus(stringify({console:[i$6(ee,"console")]}),Gn.webviewID)}catch(qn){}}},spanEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;if(checkMoudle("span")){this.log("spanData=>"+ee,Gn);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKSpanEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({span:[i$6(ee,"span")]})):window.bonreePrivateInterface.webViewEventBus(stringify({span:[i$6(ee,"span")]}),Gn.webviewID)}catch(qn){}}},customLogEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("setCustomLog=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKSetCustomLog.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({customlog:[i$6(ee,"customlog")]})):window.bonreePrivateInterface.webViewEventBus(stringify({customlog:[i$6(ee,"customlog")]}),Gn.webviewID)}catch(qn){}},customMetricEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("setCustomMetric=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKSetCustomMetric.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({custommetric:[i$6(ee,"custommetric")]})):window.bonreePrivateInterface.webViewEventBus(stringify({custommetric:[i$6(ee,"custommetric")]}),Gn.webviewID)}catch(qn){}},customExceptionEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("setCustomException=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKSetCustomException.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({crash:[i$6(ee,"crash")]})):window.bonreePrivateInterface.webViewEventBus(stringify({crash:[i$6(ee,"crash")]}),Gn.webviewID)}catch(qn){}},customSpeedTestEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("speedTest=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKsetCustomSpeedTest.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({speedtest:[i$6(ee,"speedtest")]})):window.bonreePrivateInterface.webViewEventBus(stringify({speedtest:[i$6(ee,"speedtest")]}),Gn.webviewID)}catch(qn){}},customEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("setCustomEvent=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKsetCustomEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({customevent:[i$6(ee,"customevent")]})):window.bonreePrivateInterface.webViewEventBus(stringify({customevent:[i$6(ee,"customevent")]}),Gn.webviewID)}catch(qn){}},MainDocumentNetworkEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("MainDocumentNetworkEvent=>"+ee);try{var qn;M$6(Gn.osType)?((qn=JSON.parse(ee)).imd=1,window.webkit.messageHandlers.brsWKMainDocumentNetworkEvent.postMessage(stringify(qn))):V$4(Gn.osType)?window.bonreePrivateInterface.call(stringify({network:[i$6(ee,"network")],imd:1})):window.bonreePrivateInterface.webViewEventBus(stringify({network:[i$6(ee,"network")],imd:1}),Gn.webviewID)}catch(Vn){}},ResourceNetworkEvent:function(ee,Gn){if(!getDataSize(ee,5))return!1;this.log("ResourceNetworkEvent=>"+ee);try{M$6(Gn.osType)?window.webkit.messageHandlers.brsWKResourceNetworkEvent.postMessage(ee):V$4(Gn.osType)?window.bonreePrivateInterface.call(ee):window.bonreePrivateInterface.webViewEventBus(ee,Gn.webviewID)}catch(qn){}}};function u$5(ee,Gn){var qn=null;return qn=(qn=ee.Zone&&typeof ee.Zone.__symbol__=="function"?ee[ee.Zone.__symbol__(Gn)]:qn)||ee[Gn]}function getMutationObserver(){var ee,Gn;return window.MutationObserver?(Gn=null,window.Zone&&(Gn=u$5(window,"MutationObserver")),(Gn=window.MutationObserver&&Gn===window.MutationObserver?(ee=u$5(new window.MutationObserver(function(){}),"originalInstance"))&&ee.constructor:Gn)||window.MutationObserver):null}function PageActivityManager(ee){this.mutationRecord=[],this.performanceRecord=[],this.record=!1,this.unlocked=!0,window.PerformanceObserver&&(this.PerformanceObserver=new PerformanceObserver(function(Vn){var Vn=Vn.getEntries(),Wn=isDefined(window.$bonreeReplayUrl)?window.$bonreeReplayUrl:null;Vn.some(function(jn){if(!c$9([D$5.sett.uploadAddrHttp,D$5.sett.uploadAddrHttps,Wn],jn.name.split("?")[0])&&jn.entryType==="resource")return!0})&&this.record&&(this.performanceRecord.push(now()),isDefined(ee))&&ee(Vn.filter(function(jn){return jn.entryType==="resource"&&!c$9([D$5.sett.uploadAddrHttp,D$5.sett.uploadAddrHttps,Wn],jn.name.split("?")[0])}))}.bind(this)));var Gn=getMutationObserver();isDefined(Gn)&&(this.mutationObserver=new Gn(function(qn){0<this.mutationRecord.lengths&&this.stopPageActivityObserver(1),this.record&&this.mutationRecord.push(now())}.bind(this))),this._legacyDomHandler=null,this._legacyLoadHandler=null}function c$9(ee,Gn){return-1<ee.indexOf(Gn)}function checkRecord(ee,Gn){if(!(ee.length===0||ee[ee.length-1]<Gn)){for(var qn=0;qn<=ee.length;qn++)if(0<=(ee[qn]-Gn)/1e3)return!0}return!1}PageActivityManager.prototype.startPageActivityObserver=function(){var ee;window.PerformanceObserver?this.PerformanceObserver.observe({entryTypes:["resource"]}):((ee=this)._legacyLoadHandler=function(){ee.record&&ee.performanceRecord.push(now())},window.addEventListener?window.addEventListener("load",this._legacyLoadHandler,!0):window.attachEvent&&window.attachEvent("onload",this._legacyLoadHandler)),window.MutationObserver?this.mutationObserver.observe(window.document,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}):((ee=this)._legacyDomHandler=function(){ee.record&&ee.mutationRecord.push(now())},window.addEventListener?window.document.addEventListener("DOMSubtreeModified",this._legacyDomHandler,!1):window.document.attachEvent&&window.document.attachEvent("onpropertychange",this._legacyDomHandler))},PageActivityManager.prototype.turnOnRecord=function(){this.record=!0},PageActivityManager.prototype.turnOffRecord=function(){this.record=!1,this.mutationRecord=[],this.performanceRecord=[]},PageActivityManager.prototype.stopPageActivityObserver=function(ee){isDefined(ee)&&isDefined(this.mutationObserver)?this.mutationObserver.disconnect():(window.PerformanceObserver&&this.PerformanceObserver.disconnect(),isDefined(this.mutationObserver)&&this.mutationObserver.disconnect(),this._legacyDomHandler&&(window.removeEventListener?window.document.removeEventListener("DOMSubtreeModified",this._legacyDomHandler,!1):window.document.detachEvent&&window.document.detachEvent("onpropertychange",this._legacyDomHandler),this._legacyDomHandler=null),this._legacyLoadHandler&&(window.removeEventListener?window.removeEventListener("load",this._legacyLoadHandler,!0):window.detachEvent&&window.detachEvent("onload",this._legacyLoadHandler),this._legacyLoadHandler=null))};var s$8=new PageActivityManager;function c$8(){this.isOpen=!1,this.isScroll=!1,this.elements=["input","textarea","select"],this.stl=null,this.isFirst=!0,this.lastTime=0,this.observer=null}c$8.prototype.isStart=function(ee){this.isOpen=ee;function Gn(Wn){window.removeEventListener("scroll",this.checkElement.bind(qn)),isDefined(Wn)&&(Wn.disconnect(),this.observer=null)}var qn=this,Vn=getMutationObserver();ee?(Gn.call(this,this.observer),setTimeout(function(){qn.observer=new Vn(function(Wn){qn.checkElement.bind(qn)}),qn.observer.observe(window.document,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0})},200),setTimeout(function(){qn.checkElement.call(qn)},500),this.listenScroll()):Gn.call(this,this.observer)},c$8.prototype.listenScroll=function(){function ee(){this.isScroll=!1,this.checkElement.call(qn)}var Gn=null,qn=this;document&&document.body&&document.body.addEventListener("scroll",function(){clearTimeout(Gn),qn.isScroll=!0,Gn=setTimeout(ee.bind(qn),300)}),window&&window.addEventListener&&window.addEventListener("scroll",function(){clearTimeout(Gn),qn.isScroll=!0,Gn=setTimeout(ee.bind(qn),300)})},c$8.prototype.checkElement=function(){function ee(){if(!this.isScroll){for(var Vn=document.querySelectorAll("input"),Wn=document.querySelectorAll("textarea"),jn=document.querySelectorAll("select"),Qn=Array.prototype.concat(arrayFrom(Vn),arrayFrom(Wn),arrayFrom(jn)),Jn=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth,eo=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight,Xn=0,to=Qn.length-1;Xn<=to;Xn++){var io=this.calculatedCoordinates(Qn[Xn],Jn,eo);isDefined(io)&&Gn.push(io)}v$5.shadeArea(Gn,D$5.secondSett)}}var Gn=[],qn=(this.isFirst&&(ee.call(this),this.isFirst=!1),Date.now());return 50<qn-this.lastTime&&ee.call(this),this.lastTime=qn,{sensitiveInfos:{masks:Gn}}},c$8.prototype.calculatedCoordinates=function(ee,Gn,qn){if(isDefined(ee)&&isDefined(Gn)&&isDefined(qn))try{var Vn,Wn,jn,Qn,Jn,eo,Xn=ee.getClientRects()[0];if(isDefined(Xn)&&isDefined(Xn.x)&&isDefined(Xn.y)&&isDefined(Xn.width)&&isDefined(Xn.height))return Vn=Xn.x,Wn=Xn.y,jn=Xn.width,Qn=Xn.height,Jn=Xn.bottom,eo=Xn.right,Gn<Vn||qn<Wn||Jn<0||eo<0?null:Vn<=0&&Wn<=0?((jn+=Vn)>Gn&&(jn=Gn),(Qn+=Wn)>qn&&(Qn=qn),{x:Vn=0,y:Wn=0,w:jn,h:Qn}):Vn<=0&&0<Wn?((jn+=Vn)>Gn&&(jn=Gn),{x:Vn=0,y:Wn,w:jn,h:Qn=qn<Qn+Wn?qn-Wn:Qn}):0<Vn&&Wn<=0?((Qn+=Wn)>qn&&(Qn=qn),{x:Vn,y:Wn=0,w:jn=Gn<jn+Vn?Gn-Vn:jn,h:Qn}):{x:Vn,y:Wn,w:jn,h:Qn}}catch(to){log("calculatedCoordinates"+to)}return null};var E$6=new c$8,GC={BROWSER:0,ANDROID:1,IOS:2,WINDOWS:3,HOS:4,ZERO:0,THOUSAND:1e3,TYPEARR:["","android","ios","pc"],FLUSH_DATA:"data/flush",PAGE_READY:"page/ready",PAGE_LOAD:"page/load",PAGE_DATA:"data/page",RESOURCE_DATA:"data/resource",CONSOLE_DATA:"data/console",USERCHANGE:"userchange",TITLE:"title",DURATION:"duration",NAVIGATION_START:"navigationStart",FETCH_START:"fetchStart",START_TIME:"startTime",UNLOAD_EVENT_START:"unloadEventStart",UNLOAD_EVENT_END:"unloadEventEnd",REDIRECT_START:"redirectStart",REDIRECT_END:"redirectEnd",DOMAIN_LOOKUP_START:"domainLookupStart",DOMAIN_LOOKUP_END:"domainLookupEnd",CONNECT_START:"connectStart",CONNECT_END:"connectEnd",SECURE_CONNECTION_START:"secureConnectionStart",REQUEST_START:"requestStart",RESPONSE_START:"responseStart",RESPONSE_END:"responseEnd",RESPONSE_STATUS:"responseStatus",DOM_LOADING:"domLoading",DOM_INTERACTIVE:"domInteractive",DOM_CONTENT_LOADED_EVENT_START:"domContentLoadedEventStart",DOM_CONTENT_LOADED_EVENT_END:"domContentLoadedEventEnd",DOM_COMPLETE:"domComplete",LOAD_EVENT_START:"loadEventStart",LOAD_EVENT_END:"loadEventEnd",FIRST_PAINT:"fp",FIRST_DAY:"brs_ifd",FIRST_CONTENTFUL_PAINT:"fcp",LARGEST_CONTENTFUL_PAINT:"lcp",REDIRECT_COUNT:"redirectCount",WORKER_START:"workerStart",NEXT_HOP_PROTOCOL:"nextHopProtocol",TRANSFER_SIZE:"transferSize",ENCODED_BODY_SIZE:"encodedBodySize",DECODED_BODY_SIZE:"decodedBodySize",UPLOAD_BODY_SIZE:"uploadBodySize",REQUEST_HEADER:"requestHeader",RESPONSE_HEADER:"responseHeader",CALLBACK_START:"callbackStart",CALLBACK_END:"callbackEnd",CALLBACK_TIME:"callbackTime",USER_TIME:"userTime",TIMESTAMP:"timestamp",INITIATOR_TYPE:"initiatorType",XML_HTTP_REQUEST:"xmlhttprequest",FETCH:"fetch",TAG_NAME:"tagName",TYPE:"type",MODEL:"model",EMBED:"embed",IS_MAIN_DOCUMENT:"isMainDocument",PAGE_ID:"pageViewId",PAGE_URL:"pageUrl",PAGE_TITLE:"pageTitle",MESSAGE:"message",NAME:"name",ERROR_LINE:"line",ERROR_FILE:"file",ERROR_COLUMN:"column",ERROR_STACK:"stack",JS_ERROR:"jsError",IS_SLOW:"isSlow",IS_CUSTOM:"isCustome",URL:"url",NET_METHOD:"method",NET_PORT:"port",NET_IP:"ip",NET_DT:"domainLookupTime",NET_CT:"connectTime",NET_SSLT:"ssl",NET_DTI:"downloadTime",NET_CNA:"cname",NET_PT:"protocalType",NET_TID:"guid",NET_XBR:"xBrResponse",NET_TRACE:"traceResponse",NET_TPAR:"tpar",NET_EOP:"errorOccurrentProcess",NET_EC:"code",NET_TYPE:"requestType",NET_RET:"resourceType",CUSTOM_IC:"customIc",NET_CBBQ:"requestBodyByKey",REQUEST_BODY:"requestBody",NET_CBHQ:"requestHeaderByKey",NET_CBQ:"reqUrlDta",REQUEST_HEADER_ALL:"requestHeaderAll",E_TYPE:"eType",UA:"userAgent",SOURCE_OF_ACTION:"sourceOfAction",FRAME_TYPE:"frameType",STAY_TIME:"stayTime",CORRELATION_ID:"correlationId",IS_EXIT:"isExit",PAGE_CREATE_TIME:"pageCreateTime",REFERRER:"referrer",STATUS:"status",STATUS_TEXT:"statusText",ALIAS:"alias",PATH:"path",ROOT:"root",FULL_URL:"fullUrl",FRAME_WORK:"framework",CLIENT_TYPE:"clientType",VALUE:"value",FULL_RESOURCE_LOAD_TIME:"fullResourceLoadTime",PAGE_INVISIBLE:"page/invisible",PAGE_VISIBLE:"page/visible",RESOURCE_DUMP:"resource/dump",ERROR_DATA:"data/error",REQUEST_INIT:"request/init",REQUEST_DATA:"data/request",ROUTE_DATA:"data/route",ACTION_DATA:"data/event",TRACE_ACTION_DATA:"data/traceAction",SESSION_CHANGE:"session/change",USER_SET:"user/set",PARAM:"param",SPAN_DATA:"data/span",FULLY_COLLECT:1e3,NET:"network",VIEW:"view",ERROR:"jserror",ACTION:"action",ROUTE:"routechange",WEBVIEWDATA:"h5",PAGE:"page",RESOURCE:"resource",CUSTOM_EVENT:"customevent",CUSTOM_METRIC:"custommetric",CUSTOM_LOG:"customlog",CRASH:"crash",SPEED_TEST:"speedtest",CONSOLE:"console",SPAN:"span",GUID_KEY:"br-resp-key",X_BR_RESPONSE:"x-br-response",TRACE_RESPONSE:"traceresponse",BREAK_LINE:"\r\n",LIFESTYLE_XHR_START:"XMLHttpRequest_Start",LIFESTYLE_REQUEST_END:"Request_Completed",LIFESTYLE_FETCH_START:"FETCH_Satrt",CUSTOM_ACTION_END:"customActionEnd",INIT_SESSION_START:"initSessionStart",LIFESTYLE_CALLBACK_REQUEST_START:"CallbackRequest_Start",ROUTE_CHANGE_START:"ROUTE_CHANGE_START",ROUTE_CHANGE_END:"ROUTE_CHANGE_END",SPAN_CALLBACK_DATA:"SPAN_CALLBACK_DATA",WEBSOCKET_CONNECTION_START:"WEBSOCKET_CONNECTION_START",WEBSOCKET_CONNECTION_END:"WEBSOCKET_CONNECTION_END",XHR_CALLBACK_IDENTIFY:"XHR_CALLBACK_IDENTIFY",LIMIT_64:64,LIMIT_256:256,LIMIT_512:512,ATT_REG:/^[:0-9a-zA-Z /@._-]{1,}$/,BR_ATTRIBUTE:"br-att",BR_PARAMS:"br_params",BR_USER_ID:"br_user_id",BR_DEVICE_ID:"br_device_id",BR_SESSION_ID:"br_session_id",BR_APP_ID:"br_app_id",FINGER_ID:"finger_id"};function u$4(ee){return!(!isDefined(ee)||ee instanceof Object||(isString(ee)||(ee=""+ee),!(/^[\u4E00-\u9FFFa-zA-Z0-9:_\-@.\s/*#!]+$/.test(ee)&&ee.length<=256)))}function r$5(){this.useAPI=!1,this.configeManage=[],this.originManageData=null,this.cookieArr=[],this.requestArr=[],this.responseArr=[],this.isStopGetValue=50}r$5.prototype.checkOriginManageData=function(ee){this.useAPI||isDefined(ee=ee&&isString(ee)&&JSON.parse(ee)||ee)&&isArray(ee)&&(50<(this.originManageData=ee).length?this.configeManage=ee.slice(0,50):this.configeManage=ee,this.startCatchDataFun())},r$5.prototype.startCatchDataFun=function(){for(var ee=0,Gn=this.configeManage.length-1;ee<=Gn;ee++)if(this.configeManage[ee]&&this.configeManage[ee].type)switch(this.configeManage[ee].type){case 1:var qn=this.getDataWithType_1(this.configeManage[ee].rule);isDefined(qn)&&setCookie("login_username",qn),this.configeManage[ee].value=qn;break;case 2:this.configeManage[ee].value=this.getDataWithType_2(this.configeManage[ee].rule);break;case 3:this.configeManage[ee].value=this.getDataWithType_3(this.configeManage[ee].rule);break;case 4:this.getDataWithType_4(this.configeManage[ee].rule,ee);break;case 5:this.configeManage[ee].value=this.getDataWithType_5(this.configeManage[ee].rule);break;case 6:this.getDataWithType_6(this.configeManage[ee].rule,ee);break;case 7:this.getDataWithType_7(this.configeManage[ee].rule,ee)}y$5(),this.checkValueRight()},r$5.prototype.checkValueRight=function(){for(var ee=0,Gn=this.configeManage.length-1;ee<=Gn;ee++)if(isDefined(this.configeManage[ee])&&isDefined(this.configeManage[ee].value))return this.isStopGetValue=ee,void w$a.configUser(this.configeManage[ee].value,!0)},r$5.prototype.getDataWithType_1=function(ee){if(!(ee&&256<ee.length))try{if(ee&&0<ee.indexOf("@")){var Gn=ee.split("@");if((qn=document&&document.querySelector(Gn[0])||null)&&qn[Gn[1]]&&u$4(qn[Gn[1]]))return qn[Gn[1]]}else{var qn;if((qn=document&&document.querySelector(ee)||null)&&u$4(qn.innerText))return qn.innerText}}catch(Vn){log("C138")}},r$5.prototype.getDataWithType_2=function(ee){var Gn;if(!(ee&&256<ee.length)&&isDefined(ee)){if(ee.indexOf(".")!=-1){if(ee.split(".").map(function(qn,Vn){Gn=Gn&&Gn[qn]||Vn==0&&window[qn]||void 0}),Gn&&u$4(Gn))return isString(Gn)?Gn:""+Gn}else if(u$4(window[ee]))return""+window[ee]}},r$5.prototype.getDataWithType_3=function(ee){if(!(ee&&256<ee.length))try{var Gn=document.head.querySelector("[name~="+ee+"][content]").content;if(isDefined(Gn)&&u$4(Gn))return Gn}catch(qn){log("C139")}},r$5.prototype.getDataWithType_4=function(ee,Gn){ee&&256<ee.length||this.cookieArr.push({rule:ee,index:Gn})},r$5.prototype.getDataWithType_5=function(ee){try{if(!(ee&&256<ee.length)&&window&&window.location&&window.location.search)for(var Gn=decodeURI(window.location.search).split("?")[1].split("&"),qn=0;qn<Gn.length;qn++){var Vn=Gn[qn];if(Vn.split("=")[0]==ee&&u$4(Vn.split("=")[1]))return Vn.split("=")[1]}}catch(Wn){log("C140")}},r$5.prototype.getDataWithType_6=function(ee,Gn){var qn;ee&&256<ee.length||(qn=!1,this.requestArr.map(function(Vn){Vn.rule==ee&&(qn=!0)}),qn)||this.requestArr.push({rule:ee,index:Gn})},r$5.prototype.getDataWithType_7=function(ee,Gn){var qn;ee&&256<ee.length||(qn=!1,this.responseArr.map(function(Vn){Vn.rule==ee&&(qn=!0)}),qn)||this.responseArr.push({rule:ee,index:Gn})},r$5.prototype.changeUseAPI=function(ee){this.useAPI=ee};var f$6=new r$5;function y$5(){var ee=f$6.cookieArr,Gn=f$6.configeManage;if(0<ee.length)for(var qn=0,Vn=ee.length-1;qn<=Vn;qn++){var Wn=getCookie(ee[qn].rule);if(isDefined(Wn)&&u$4(Wn)&&ee[qn].index<=f$6.isStopGetValue)return Gn[ee[qn].index].value=""+Wn,void f$6.checkValueRight()}}function u$3(){this.listeners={}}extend(u$3.prototype,{on:function(ee,Gn){var qn;isDefined(ee)&&isFunction(Gn)&&forEach(qn=isArray(qn=ee)?qn:[ee],bind(function(Vn){var Wn=this.listeners[Vn];(Wn=isDefined(Wn)?Wn:this.listeners[Vn]=[]).push(Gn)},this))},emit:function(ee){var Gn;isDefined(ee)&&isDefined(ee.t)&&(Gn=ee.t,forEach(this.listeners[Gn],function(qn){qn(ee)}))},remove:function(ee){var Gn;isDefined(ee)&&forEach(Gn=isArray(Gn=ee)?Gn:[ee],bind(function(qn){delete this.listeners[qn]},this))},hasListener:function(ee){return ee=this.listeners[ee],isDefined(ee)&&0<ee.length}});var e$3=new u$3,m$a=bind(e$3.on,e$3),c$7=bind(e$3.emit,e$3),d$5=bind(e$3.remove,e$3),p$9=bind(e$3.hasListener,e$3);function I$a(ee){for(var Gn=k$3.getCurrentSesssionKey(D$5.sett.appId||D$5.secondSett.appId),qn=getCookie(Gn),Vn=qn&&JSON.parse(qn)||[],Wn=0;Wn<Vn.length;Wn++){var jn=Vn[Wn];jn.lastVisitedTime=new Date().getTime(),jn.sessionID=ee,setCookie(Gn,JSON.stringify(Vn),k$3.getSessionConfig())}}function u$2(){try{var ee,Gn;return ee=!isDefined(ee=window.BonreeAgent&&window.BonreeAgent.getDeviceInfo&&(Gn=window.BonreeAgent.getDeviceInfo())&&Gn.di?Gn.di:ee)&&window.BonreeRecord&&window.BonreeRecord.BonreeRecord.getDeviceInfo&&(Gn=window.BonreeRecord.BonreeRecord.getDeviceInfo())&&Gn.di?Gn.di:ee}catch(qn){}}function g$5(ee){var Gn;isDefined(ee)&&(setCookie("br-client",ee,{path:"/",expires:"Thu, 01 Jan 2099 00:00:01 GMT;"}),isDefined(Gn=getCookie("bonree-device-info")))&&!isEmpty(Gn)&&((Gn=JSON.parse(Gn)).di=ee,ee=objectAssign({},Gn),setCookie("bonree-device-info",JSON.stringify(ee)))}function s$7(){this.iframes={},this.config={maxRetries:50,baseInterval:1e3,observerConfig:{childList:!0,attributes:!0,subtree:!0},heartbeatTimeout:15e3},this.scanTimer=null,this.sunData={iframeId:null,heartBeat:null},this.start=!1,this.getFaData=!1,this.getFaDataTime=null,this.init()}s$7.prototype.notifyToSunChangeSidAndRedraw=function(ee){var Gn=Object.keys(this.iframes);0<Gn.length&&forEach(Gn,function(qn){if(this.iframes[qn]){var Vn=this.iframes[qn].element,qn={type:"to-sun-rest-take-full-snapshot",payload:"\u5173\u952E\u6570\u636E",iframeId:qn,sId:ee,appId:D$5.sett.appId,ui:w$a.getUser(),dId:u$2()};if(Vn&&Vn.contentWindow)try{Vn.contentWindow.postMessage(qn,"*")}catch(jn){}}})},s$7.prototype.notifyToParentChangeSidAndRedraw=function(ee){ee={type:"to-parent-rest-take-full-snapshot",sId:ee,appId:D$5.sett.appId,dId:u$2()},window!==window.parent&&window.parent.postMessage(ee,"*")},s$7.prototype.startScanner=function(){var ee=this,Gn=(ee.scanTimer&&clearInterval(ee.scanTimer),ee.config.heartbeatTimeout);ee.scanTimer=setInterval(function(){forEach(Object.keys(ee.iframes),function(qn){Date.now()-ee.iframes[qn].lastPing>Gn&&(ee.iframes[qn].status="inactive",ee.sendInitialData(qn))})},15e3)},s$7.prototype.init=function(){this.bindMessageHandler(),this.startScanner(),window!==window.parent&&this.getFatherData()},s$7.prototype.getFatherData=function(){this.getFaData||(this.getFaDataTime=setInterval(function(){var ee={type:"INIT_SUN",src:window.location.href};window.parent.postMessage(ee,"*")},500),setTimeout(function(){clearInterval(this.getFaDataTime)},5e3))},s$7.prototype.startDOMObserver=function(){var ee;this.start||(this.start=!0,this.observer&&this.observer.disconnect(),ee=this,forEach(document.querySelectorAll("iframe"),function(Gn){ee.registerIframe(Gn)}),window.MutationObserver&&(this.observer=new MutationObserver(function(Gn){forEach(Gn,function(qn){forEach(qn.addedNodes,function(Vn){Vn.tagName==="IFRAME"&&ee.registerIframe(Vn)})})}),this.observer.observe(document.body,this.config.observerConfig)))},s$7.prototype.registerIframe=function(ee){var Gn="iframe_"+uuid(),qn=(ee.dataset?ee.dataset.managedId=Gn:ee.setAttribute("data-managed-id",Gn),this.iframes[Gn]={element:ee,status:"pending",retryCount:0,timer:null,lastPing:Date.now()},this);ee.addEventListener?ee.addEventListener("load",function(){qn.sendInitialData(Gn),qn.startPolling(Gn)}):ee.attachEvent&&ee.attachEvent("onload",function(){qn.sendInitialData(Gn),qn.startPolling(Gn)})},s$7.prototype.validateOrigin=function(ee,Gn){return!0},s$7.prototype.sunReceiveMessageHandleMessage=function(ee){var Gn,qn=this;if(ee&&ee.data&&ee.data.type)switch(ee.data.type){case"INIT_DATA":ee.data.iframeId,Wn=ee.data.sId,Gn=ee.data.appId,ee.data.appId,jn=ee.data.dId,ee.source.postMessage({type:"INIT_CONFIRM",iframeId:ee.data.iframeId},ee.origin),this.getFaDataTime&&clearInterval(this.getFaDataTime),this.sunData.heartBeat&&clearInterval(this.sunData.heartBeat);var Vn=D$5.sett.postMessage.heartTime||1e4,Vn=(this.sunData.heartBeat=setInterval(function(){ee.source.postMessage({type:"HEARTBEAT",iframeId:ee.data.iframeId},ee.origin)},Vn),isDefined(D$5.sett.RecordConfiguration||D$5.sett.mc)&&(isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)?window.BonreeRecord.BonreeRecord(D$5.sett):isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.BonreeRecord)&&window.BonreeAgent.BonreeRecord(D$5.sett)),Gn==D$5.sett.appId&&(I$a(Wn),g$5(jn)),Object.keys(qn.iframes)),Wn=Vn.length,jn=u$2();0<Wn&&forEach(Vn,function(Qn){if(qn.iframes[Qn]){var Jn=qn.iframes[Qn].element,eo={type:"INIT_DATA",payload:"\u5173\u952E\u6570\u636E",iframeId:Qn,sId:k$3.getSession().sessionID,appId:D$5.sett.appId,ui:w$a.getUser(),dId:jn};if(Jn&&Jn.contentWindow&&Jn.contentWindow.postMessage)try{Jn.contentWindow.postMessage(eo,"*")}catch(Xn){}}});break;case"to-sun-rest-take-full-snapshot":(Gn=ee.data.appId)==D$5.sett.appId&&(I$a(ee.data.sId),g$5(ee.data.dId)),k$3.resetTakeFullSnapshotToSun(ee.data.sId);break;case"to-parent-rest-take-full-snapshot":(Gn=ee.data.appId)==D$5.sett.appId&&(I$a(ee.data.sId),g$5(ee.data.dId)),k$3.resetTakeFullSnapshotToParent(ee.data.sId)}},s$7.prototype.handleMessage=function(ee){var Gn=this,qn=Object.keys(this.iframes).filter(function(jn){return Gn.iframes[jn].element.contentWindow===ee.source})[0];if(qn&&this.validateOrigin(ee.origin)){var Vn=this.iframes[qn];switch(ee.data.type){case"INIT_CONFIRM":clearInterval(Vn.timer),Vn.status="active",Vn.lastPing=Date.now();break;case"INIT_SUN":var Wn={type:"INIT_DATA",payload:"\u5173\u952E\u6570\u636E",sId:k$3.getSession().sessionID,appId:D$5.sett.appId,ui:w$a.getUser(),dId:u$2()};if(ee&&ee.source)try{ee.source.postMessage(Wn,ee.data.src)}catch(jn){}break;case"HEARTBEAT":Vn.status="active",Vn.lastPing=Date.now();break;case"to-parent-rest-take-full-snapshot":ee.data.appId==D$5.sett.appId&&(I$a(ee.data.sId),g$5(ee.data.dId)),k$3.resetTakeFullSnapshotToParent(ee.data.sId)}}else Gn.sunReceiveMessageHandleMessage(ee)},s$7.prototype.bindMessageHandler=function(){var ee=this;window.addEventListener?window.addEventListener("message",function(Gn){ee.handleMessage(Gn)}):window.attachEvent("onmessage",function(Gn){ee.handleMessage(Gn)})},s$7.prototype.sendInitialData=function(ee){if(this.iframes[ee]){var Gn=this.iframes[ee].element,qn={type:"INIT_DATA",payload:"\u5173\u952E\u6570\u636E",iframeId:ee,sId:k$3.getSession().sessionID,appId:D$5.sett.appId,ui:w$a.getUser(),dId:u$2()};if(Gn&&Gn.contentWindow)try{Gn.contentWindow.postMessage(qn,"*")}catch(Vn){}}},s$7.prototype.startPolling=function(ee){var Gn=this,qn=this.iframes[ee];qn.timer=setInterval(function(){qn.retryCount>=Gn.config.maxRetries||qn&&qn.status=="active"?Gn.removeIframe(ee):(Gn.sendInitialData(ee),qn.retryCount++)},this.calculateInterval(qn.retryCount))},s$7.prototype.calculateInterval=function(ee){var Gn=navigator.connection||{},qn=Gn.downlink<2?3:1;return Gn.saveData&&(qn*=2),this.config.baseInterval*Math.pow(2,ee)*qn},s$7.prototype.removeIframe=function(ee){ee=this.iframes[ee],ee&&ee.timer&&clearInterval(ee.timer)};var k$4=new s$7;function A$5(){return document.visibilityState!==void 0?document.visibilityState:document.msVisibilityState!==void 0?document.msVisibilityState:document.webkitVisibilityState!==void 0?document.webkitVisibilityState:document.hidden!==void 0?document.hidden?"hidden":"visible":document.msHidden!==void 0?document.msHidden?"hidden":"visible":document.webkitHidden!==void 0&&document.webkitHidden?"hidden":"visible"}function C$8(){return document.hidden!==void 0?document.hidden:document.msHidden!==void 0?document.msHidden:document.webkitHidden!==void 0?document.webkitHidden:A$5()==="hidden"}var d$4=0,f$5=!1;function y$4(){var ee=C$8();ee&&!f$5?(d$4=new Date().getTime(),f$5=!0):!ee&&f$5&&(d$4=0,f$5=!1)}function D$7(){var ee="";document.visibilityState!==void 0?ee="visibilitychange":document.msVisibilityState!==void 0?ee="msvisibilitychange":document.webkitVisibilityState!==void 0&&(ee="webkitvisibilitychange"),ee?document.addEventListener?document.addEventListener(ee,y$4,!1):document.attachEvent&&document.attachEvent("on"+ee,y$4):window.addEventListener?(window.addEventListener("focus",function(){d$4=0,f$5=!1},!1),window.addEventListener("blur",function(){d$4=new Date().getTime(),f$5=!0},!1)):window.attachEvent&&(window.attachEvent("onfocus",function(){d$4=0,f$5=!1}),window.attachEvent("onblur",function(){d$4=new Date().getTime(),f$5=!0}))}function I$9(Vn){var Gn=new Date().getTime(),qn=D$5.sett.sessionTimeout/1e3,Vn=qn<Vn||0<d$4&&qn<Gn-d$4;return Vn&&d$4!==0&&(d$4=Gn),Vn}function a$5(){this.sessionCache="br-session-cache",this.current="br-current-appid"}D$7(),a$5.prototype.newAppid=function(ee){var Gn,qn;!isDefined(ee)||getCookie(Gn=this.getCurrentSesssionKey(ee))&&this.queryAppid(ee)||(this.currentAppid={appId:ee,sessionID:uuid(),lastVisitedTime:new Date().getTime(),startTime:new Date().getTime()},getCookie(Gn)?((qn=JSON.parse(getCookie(Gn))).unshift(this.currentAppid),setCookie(Gn,JSON.stringify(qn),this.getSessionConfig())):setCookie(Gn,JSON.stringify([this.currentAppid]),this.getSessionConfig()),setCookie(this.current,ee,this.getSessionConfig()))},a$5.prototype.setSessionCookie=function(ee,Gn){setCookie(ee,Gn,this.getSessionConfig())},a$5.prototype.getSessionConfig=function(){var ee={path:"/"};return D$5.sett.sessionDomain&&(ee.domain=D$5.sett.sessionDomain),ee},a$5.prototype.resetTakeFullSnapshotToSun=function(ee){try{isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)?window.BonreeRecord.BonreeRecord.takeFullSnapshot():isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.BonreeRecord)&&window.BonreeAgent.BonreeRecord.takeFullSnapshot(),k$4&&k$4.notifyToSunChangeSidAndRedraw(ee)}catch(Gn){log("C155")}},a$5.prototype.resetTakeFullSnapshotToParent=function(ee){try{isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)?window.BonreeRecord.BonreeRecord.takeFullSnapshot():isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.BonreeRecord)&&window.BonreeAgent.BonreeRecord.takeFullSnapshot(),k$4&&k$4.notifyToParentChangeSidAndRedraw(ee)}catch(Gn){log("C155")}},a$5.prototype.queryAppid=function(ee){for(var Gn=this.getCurrentSesssionKey(ee),qn=getCookie(Gn),Vn=isJSON(qn)&&JSON.parse(qn)||[],Wn=0;Wn<Vn.length;Wn++){var jn,Qn=Vn[Wn];if(Qn.appId===ee)return I$9((jn=new Date().getTime())-Qn.lastVisitedTime)?(D$5.setData("isFirstUpload",1),P$6.initUsdTime(),Qn.sessionID=uuid(),c$7({t:GC.INIT_SESSION_START}),this.resetTakeFullSnapshotToSun(Qn.sessionID),window!==window.parent&&this.resetTakeFullSnapshotToParent(Qn.sessionID),Qn.startTime=Qn.lastVisitedTime=jn,Qn.isRestSID=!0):(Qn.isRestSID=!1,Qn.lastVisitedTime=jn),setCookie(Gn,JSON.stringify(Vn),this.getSessionConfig()),Qn}return!1},a$5.prototype.setAppid=function(Vn){D$5.setConfig(Vn,!1,0);var Gn,qn,Vn=Vn.appId;isDefined(Vn)&&(getCookie(Gn=this.getCurrentSesssionKey(Vn))?this.queryAppid(Vn)?setCookie(this.current,Vn,this.getSessionConfig()):(qn=JSON.parse(getCookie(Gn)),D$5.setData("isFirstUpload",1),P$6.initUsdTime(),c$7({t:GC.INIT_SESSION_START}),this.currentAppid={appId:Vn,sessionID:uuid(),lastVisitedTime:new Date().getTime(),startTime:new Date().getTime()},this.resetTakeFullSnapshotToSun(this.currentAppid.sessionID),window!==window.parent&&this.resetTakeFullSnapshotToParent(this.currentAppid.sessionID),qn.unshift(this.currentAppid),20<=qn.length&&qn.pop(),setCookie(Gn,JSON.stringify(qn),this.getSessionConfig())):(D$5.setData("isFirstUpload",1),P$6.initUsdTime(),c$7({t:GC.INIT_SESSION_START}),this.currentAppid={appId:Vn,sessionID:uuid(),lastVisitedTime:new Date().getTime(),startTime:new Date().getTime()},this.resetTakeFullSnapshotToSun(this.currentAppid.sessionID),window!==window.parent&&this.resetTakeFullSnapshotToParent(this.currentAppid.sessionID),setCookie(Gn,JSON.stringify([this.currentAppid]),this.getSessionConfig())),setCookie(this.current,Vn,this.getSessionConfig()))},a$5.prototype.getSession=function(){return this.queryAppid(D$5.sett.appId||D$5.secondSett.appId)},a$5.prototype.getCurrentSesssionKey=function(ee){return this.sessionCache+"-"+ee};var k$3=new a$5;function s$6(ee){if(this._values=[],this.size=0,ee&&ee.length)for(var Gn=0;Gn<ee.length;Gn++)this.add(ee[Gn])}s$6.prototype.add=function(ee){return this.has(ee)||(this._values.push(ee),this.size++),this},s$6.prototype.has=function(ee){for(var Gn=0;Gn<this._values.length;Gn++)if(this._values[Gn]===ee)return!0;return!1},s$6.prototype.remove=function(ee){for(var Gn=this._values.length;Gn--;)if(this._values[Gn]===ee)return this._values.splice(Gn,1),this.size=Math.max(0,this.size-1),!0;return!1},s$6.prototype.clear=function(){this._values=[],this.size=0},s$6.prototype.values=function(){return this._values},s$6.prototype.forEach=function(ee,Gn){for(var qn=0;qn<this._values.length;qn++)ee.call(Gn,this._values[qn],this._values[qn],this)};var f$4,m$9,a$4=[];function T$5(){(window.IntersectionObserver?A$4:y$3)(),m$a([GC.PAGE_READY,GC.PAGE_LOAD],function(){g$4()})}function y$3(){function ee(){if(a$4&&a$4.length)for(var Wn=window.innerWidth||document.documentElement.clientWidth,jn=window.innerHeight||document.documentElement.clientHeight,Qn=0;Qn<a$4.length;Qn++){var Jn,eo=a$4[Qn];eo&&eo.target&&((Jn=0<=(Jn=eo.target.getBoundingClientRect()).top&&0<=Jn.left&&Jn.bottom<=jn&&Jn.right<=Wn)&&!eo.startTime?eo.startTime=Date.now():!Jn&&eo.startTime&&!eo.endTime&&(eo.endTime=Date.now(),f$4)&&typeof f$4=="function"&&f$4(eo.target,eo.endTime-eo.startTime))}}function Gn(){Vn||(qn.apply(this,arguments),Vn=!0,setTimeout(function(){Vn=!1},100))}var qn,Vn;qn=ee,Vn=!1,window.addEventListener?(window.addEventListener("scroll",Gn),window.addEventListener("resize",Gn)):window.attachEvent&&(window.attachEvent("onscroll",Gn),window.attachEvent("onresize",Gn)),ee()}function S$5(ee){var Gn=document.querySelectorAll(ee);if(Gn&&Gn.length)for(var qn=0;qn<Gn.length;qn++){var Vn=Gn[qn];!Vn||u$1(Vn)||a$4.push({target:Vn,startTime:0,endTime:0})}}function g$4(){var ee;if(D$5.sett.mutationObserverEles&&isArray(D$5.sett.mutationObserverEles)&&(ee=D$5.sett.mutationObserverEles),(ee=arrayFrom(ee=D$5.secondSett.mutationObserverEles&&isArray(D$5.secondSett.mutationObserverEles)?new s$6(ee=ee.concat(D$5.secondSett.mutationObserverEles)):ee))&&isArray(ee)&&0<ee.length)for(var Gn=0;Gn<ee.length;Gn++){var qn=ee[Gn];qn&&isString(qn)&&(window.IntersectionObserver?D$6:S$5)(qn)}}function A$4(){m$9=new IntersectionObserver(function(ee){for(var Gn=0;Gn<ee.length;Gn++){var qn=ee[Gn];if(!qn)return;var Vn,Wn=qn.target;0<qn.intersectionRatio?(Vn=u$1(Wn))&&Vn.target?Vn.startTime=Date.now():a$4.push({target:Wn,startTime:Date.now(),endTime:0}):(Vn=u$1(Wn))&&Vn.target&&(Vn.endTime=Date.now(),f$4)&&typeof f$4=="function"&&f$4(Wn,1e3*(Vn.endTime-Vn.startTime))}})}function D$6(ee){var Gn=document.querySelectorAll(ee);if(Gn&&0<Gn.length)for(var qn=0;qn<Gn.length;qn++){var Vn=Gn[qn];if(!Vn)return;u$1(Vn.target)||m$9&&m$9.observe(Vn)}}function u$1(ee){for(var Gn=0;Gn<a$4.length;Gn++){var qn=a$4[Gn];if(qn&&qn.target===ee)return qn}return!1}function I$8(ee){ee&&isFunction(ee)?f$4=ee:console.warn("The parameters passed in are incorrect. Please pass in a function!!")}function f$3(){this.sett={osType:GC.BROWSER,probability:GC.THOUSAND,sessionPeriod:18e8,sessionTimeout:18e8,sessionEvents:2e3,debounce:1e3,ignoreRequestParams:!1,ignoreRequestHeaders:!1,ignoreResources:!1,ignoreUserEvents:!1,isFirstUpload:1,exBrowser:["Firefox/47"],appVersion:"1.0.0",agentVersion:version,appName:"unKnown",appId:"",hcs:1,xhrTimeout:6e4,channelId:null,deviceId:null,pageViewId:uuid(),pageUrl:document&&document.URL,reqBodyKey:null,reqURLKey:null,reqHeaderKey:null,userKey:"br-user",isDebuge:!1,cycleTime:10,useXHR:!1,maxSize:20,enableWebsocket:!0,ac:{ac:!0,cp:30,aot:3e4},traceConfig:{urlTotalList:[],urlWhiteList:[],urlBlackList:[]},typeArr:["","android","ios","pc"],mutationObserverEles:[],postMessage:{whiteArr:[],scanOpen:!0,heartTime:1e4,heartOutTime:9e3},interceptURL:!1,qiankunContainer:"#subapp-viewport"},this.oriArr=[],this.initArr=[],this.secondSett={}}Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector||function(ee){for(var Gn=(this.document||this.ownerDocument).querySelectorAll(ee),qn=Gn.length;0<=--qn&&Gn.item(qn)!==this;);return-1<qn}),f$3.prototype.iptAddAttribute=function(ee){ee?forEach(document.querySelectorAll("input"),function(Gn){Gn.setAttribute("bonree-value","all")}):forEach(this.sett.inputValue,function(Gn){forEach(document.querySelectorAll(Gn),function(qn){qn.setAttribute("bonree-value","part")})})},f$3.prototype.elementAddAttribute=function(){forEach(this.sett.findEles,function(ee){var Gn;isArray(ee)?(Gn=document.querySelectorAll(ee[0]),isDefined(ee[1])&&!isEmpty(ee[1])?isDefined(ee[2])?forEach(Gn,function(qn){qn.setAttribute("bonree-title",ee[1]),qn.setAttribute("bonree-content",ee[2])}):forEach(Gn,function(qn){qn.setAttribute("bonree-title",ee[1])}):isDefined(ee[2])?forEach(Gn,function(qn){qn.setAttribute("bonree-title",""),qn.setAttribute("bonree-content",ee[2])}):forEach(Gn,function(qn){qn.setAttribute("bonree-title","")})):forEach(Gn=document.querySelectorAll(ee),function(qn){qn.setAttribute("bonree-title","")})})},f$3.prototype.setConfig=function(ee,Gn,qn){if(Gn===void 0&&(Gn=!0),qn===void 0&&(qn=0),D$5.sett.appId===""||!isDefined(ee.appId)||qn===0){var Vn;if(!Gn||!isDefined(ee.osType))return Gn||qn!==1?(isDefined(ee.probability)&&(typeof ee.probability=="number"?this.checkProbability(ee.probability):log("C100")),void(this.sett.probability&&(extend(this.sett,ee),this.sett.appId&&Gn===!0&&k$3.newAppid(this.sett.appId),this.sett.userId&&(isDefined(this.sett.extraInfo)?(T$4(this.sett.userId),C$7(this.sett.extraInfo)):T$4(this.sett.userId),f$6.changeUseAPI(!0)),isDefined(this.sett.userdefine)&&f$6.checkOriginManageData(this.sett.userdefine),this.sett.isDebuge&&isDefined(window.BonreeAgent)&&(window.BonreeAgent.isDebuge=!0),this.sett.isDebuge&&isDefined(window.BonreeClient)&&(window.BonreeClient.isDebuge=!0),this.findElesFun()))):isDefined(ee.osType)?void(this.secondSett=extend(this.secondSett,ee)):void extend(this.sett,ee);this.secondSett=extend(this.secondSett,ee),isDefined(this.secondSett.mn)&&isDefined(this.secondSett.sl)&&(qn=this.secondSett.mn,Gn=this.secondSett.sl,qn.indexOf("sessionreplay")!==-1&&Gn!==0?Vn=setInterval(function(){1<w$a.state&&(E$6.isStart(!0),clearInterval(Vn))},50):E$6.isStart(!1))}},f$3.prototype.findElesFun=function(ee){var Gn,qn,Vn,Wn,jn=this,Qn=this;this.sett.inputValue&&(isArray(this.sett.inputValue)?0<this.sett.inputValue.length&&(m$a([GC.PAGE_READY,GC.PAGE_LOAD],function(){Qn.iptAddAttribute.call(jn)}),ee)&&Qn.iptAddAttribute.call(this):this.sett.inputValue===!0&&(m$a([GC.PAGE_READY,GC.PAGE_LOAD],function(){Qn.iptAddAttribute.call(jn)}),ee)&&Qn.iptAddAttribute.call(this,!0)),this.sett.findEles&&isArray(this.sett.findEles)&&(m$a([GC.PAGE_READY,GC.PAGE_LOAD],function(){Qn.elementAddAttribute.call(jn)}),ee&&Qn.elementAddAttribute.call(this),ee=this.sett.findEles||[],Gn=this.sett.inputValue||[],qn=[],0<ee.length&&forEach(ee,function(Jn){isArray(Jn)?qn.push(Jn[0]):qn.push(Jn)}),document)&&0<qn.length&&(Vn=getMutationObserver())&&(Wn=function(){var Jn;document.body?new Vn((Jn=null,function(){Jn||(function(eo){forEach(eo,function(Xn){(Xn.type==="childList"||Xn.type==="subtree")&&(g$4&&g$4(),forEach(Xn.addedNodes,function(to){to.nodeType===1&&(forEach(qn,function(io){to.matches(io)&&Qn.elementAddAttribute.call(Qn)}),isArray(Gn)?Qn.iptAddAttribute.call(Qn):Qn.iptAddAttribute.call(Qn,!0))}))})}.apply(this,arguments),Jn=setTimeout(function(){Jn=null},50))})).observe(document.body,{childList:!0,attributes:!0,subtree:!0}):setTimeout(Wn,50)})()},f$3.prototype.checkProbability=function(ee){ee=Math.random()*GC.THOUSAND<=ee,this.sett.probability=ee?GC.THOUSAND:GC.ZERO},f$3.prototype.setData=function(ee,Gn){if(!isDefined(ee)||!isDefined(Gn))return!1;this.sett[ee]=Gn};var D$5=new f$3;function M$6(ee){return ee===GC.IOS}function V$4(ee){return ee===GC.HOS}function G$4(ee){return ee===GC.BROWSER}function D$4(ee){var Gn=ee.ui;document&&forEach(document.querySelectorAll("iframe"),function(qn){qn.dataset?qn.dataset.bonreeID=Gn:qn.setAttribute("data-bonreeid",Gn);var Vn=qn.contentWindow;setTimeout(function(){Vn&&Vn.postMessage({bonreeID:Gn},"*")},1e3)})}function i$5(){this.state=0,this.userInfoMap={},this.user=void 0,this.firstUserInfoSetMapKey="",this.lastUserInfoSetMapKey="",this.setUserInfoTimeStamp=0,this.title="",this.BonreeRecordState=!1,this.eventViewInfo={n:document&&document.URL},this.eviKey="",this.eviMap={},this.eei={},this.eeiKey="",this.eeiMap={},this.eeiMapBackup={},this.timer=void 0}function h$8(){this.usdTime=0;var ee=this.queryCookie();ee!=0&&(this.usdTime=ee)}function T$4(ee,Gn){isDefined(Gn)||(Gn=!1),w$a.configUser(ee,Gn)}function C$7(ee){w$a.configUserExtraInfo(ee)}i$5.prototype.setEeiData=function(ee){isEmpty(ee)?(this.eeiKey="",this.eei={},this.eeiMap={}):(this.eei=objectAssign({},ee),ee="",isEmpty(this.eei)?this.eeiKey="":(this.eeiKey=ee=hex_md5(String(+new Date)),this.eeiMap[ee]=this.eei))},i$5.prototype.setEeiMapCopy=function(){var ee;this.eeiKey!==""&&isDefined(ee=this.eeiMap[this.eeiKey])&&(this.eeiMapBackup[this.eeiKey]=ee)},i$5.prototype.getEeiData=function(ee){return ee=="key"?(this.setEeiMapCopy(),this.eeiKey):(ee=objectAssign({},this.eeiMapBackup),this.eeiMapBackup={},ee)},i$5.prototype.setEviMap=function(){if(this.eviKey=="")this.eviKey=hex_md5(String(+new Date)),this.eviMap[this.eviKey]=objectAssign({},this.eventViewInfo);else{var ee="";if(this.eviMap[this.eviKey]&&this.eviMap[this.eviKey].n&&(ee=this.eviMap[this.eviKey].n),isHashRoute(this.eventViewInfo.n)){var Gn=extractHashRoute(ee),qn=extractHashRoute(this.eventViewInfo.n);if(Gn&&qn&&qn===Gn)return}else if(ee.split("?")[0]===this.eventViewInfo.n.split("?")[0])return;this.eventViewInfo.ru=ee;for(var Vn=!1,Wn=Object.keys(this.eviMap),jn=0;jn<Wn.length;jn++){var Qn=Wn[jn];if(Object.hasOwnProperty.call(this.eviMap,Qn)){var Jn=this.eviMap[Qn];if(this.eventViewInfo&&this.eventViewInfo.n===Jn.n&&this.eventViewInfo.vt===Jn.vt){var eo=this.eventViewInfo.ru,Jn=Jn.ru;if(eo&&Jn&&eo===Jn||!eo&&!Jn||this.eventViewInfo.n===eo){Vn=!0,this.eviKey=Qn;break}}}}Vn||(this.eviKey=hex_md5(String(+new Date)),this.eviMap[this.eviKey]=objectAssign({},this.eventViewInfo))}},i$5.prototype.setEviData=function(ee,Gn){this.eventViewInfo[ee]=Gn},i$5.prototype.getEviData=function(ee){return ee=="key"?this.eviKey:ee=="value"?this.eventViewInfo:this.eviMap},i$5.prototype.setData=function(ee,Gn){if(!isDefined(ee)||!isDefined(Gn))return!1;this[ee]=Gn,ee=="title"&&Gn!=""&&this.setEviData("vt",Gn)},i$5.prototype.changeState=function(ee){this.state=ee},i$5.prototype.getUserInfo=function(){var ee;return isEmpty(this.userInfoMap)&&(ee=this.getUser())!==""&&this.configUser(ee),this.userInfoMap},i$5.prototype.getUser=function(){try{var ee;return isDefined(this.user)?user:isReadable(ee=this.getLocalUser())?ee:""}catch(Gn){}return""},i$5.prototype.getLocalUser=function(){var ee,Gn;return isDefined(this.user)?this.user:(Gn=D$5.sett.userKey,isDefined(window.sessionStorage)&&isReadable(ee=sessionStorage.getItem(Gn))||isDefined(window.localStorage)&&isReadable(ee=localStorage.getItem(Gn))?ee:getCookie(Gn))},i$5.prototype.updateFirstUserInfoSetMapKey=function(ee){this.firstUserInfoSetMapKey=ee},i$5.prototype.saveUser=function(ee){setCookie(D$5.sett.userKey,ee)},i$5.prototype.configUser=function(ee,qn){if(isDefined(qn)||(qn=!1),!isDefined(ee))return log("C141"),!1;if(ee instanceof Object)return log("C142"),!1;if(isString(ee)||(ee=""+ee),!/^[a-zA-Z\u4E00-\u9FFF0-9_ .@/:*#!-]{1,256}$/.test(ee))return log("C143"),!1;qn||f$6.changeUseAPI(!0);var qn=!1,Vn=hex_md5(ee);return(qn=isDefined(this.userInfoMap[Vn])?!0:qn)?!1:(isEmpty(this.userInfoMap)&&this.updateFirstUserInfoSetMapKey(Vn),this.lastUserInfoSetMapKey=Vn,this.setUserInfoTimeStamp=now(),qn={},qn.ui=ee,this.userInfoMap[Vn]=qn,this.saveUser(ee),Vn=this.userInfoMap[this.lastUserInfoSetMapKey],isDefined(Vn)&&D$4(Vn),!0)},i$5.prototype.configUserExtraInfo=function(ee){try{switch(typeof ee){case"string":if(7e3<ee.length)return;var Gn;ee=JSON.parse(ee),64<Object.getOwnPropertyNames(ee).length&&(Gn={},forEach(Object.getOwnPropertyNames(ee).slice(0,64),function(qn){Gn[qn]=ee[qn]}),ee=Gn);break;case"object":if(7e3<JSON.stringify(ee).length||isEmpty(ee))return;64<Object.getOwnPropertyNames(ee).length&&(Gn={},forEach(Object.getOwnPropertyNames(ee).slice(0,64),function(qn){Gn[qn]=ee[qn]}),ee=Gn);break;default:return}}catch(qn){}isDefined(ee)&&!isEmpty(this.userInfoMap)&&(this.userInfoMap[this.lastUserInfoSetMapKey].ei=JSON.stringify(ee))},h$8.prototype.initUsdTime=function(){this.usdTime=0,this.saveCookie(0)},h$8.prototype.changUsdTime=function(ee){isDefined(ee)&&(this.usdTime=ee,this.saveCookie(ee))},h$8.prototype.saveCookie=function(ee){setCookie("br_usd_time",ee)},h$8.prototype.getUsdTime=function(){return this.queryCookie()||this.usdTime||0},h$8.prototype.queryCookie=function(){return getCookie("br_usd_time")||0};var w$a=new i$5,P$6=new h$8;function i$4(){this.items={}}function r$4(ee){if(this._values=[],ee&&ee.length)for(var Gn=0;Gn<ee.length;Gn++)this.add(ee[Gn])}i$4.prototype.set=function(ee,Gn){this.items[ee]=Gn},i$4.prototype.get=function(ee){return this.items.hasOwnProperty(ee)?this.items[ee]:void 0},i$4.prototype.has=function(ee){return this.items.hasOwnProperty(ee)},i$4.prototype.remove=function(ee){return!!this.has(ee)&&(delete this.items[ee],!0)},i$4.prototype.clear=function(){this.items={}},i$4.prototype.size=function(){var ee,Gn=[];for(ee in this.items)this.items.hasOwnProperty(ee)&&Gn.push(ee);return Gn.length},i$4.prototype.entries=function(){var ee,Gn=[];for(ee in this.items)this.items.hasOwnProperty(ee)&&Gn.push([ee,this.items[ee]]);return Gn},i$4.prototype.keys=function(){var ee,Gn=[];for(ee in this.items)this.items.hasOwnProperty(ee)&&Gn.push(ee);return Gn},i$4.prototype.values=function(){var ee,Gn=[];for(ee in this.items)this.items.hasOwnProperty(ee)&&Gn.push(this.items[ee]);return Gn},r$4.prototype.add=function(ee){return this.has(ee)||ee===null||typeof ee!="object"&&typeof ee!="function"||this._values.push(ee),this},r$4.prototype.has=function(ee){for(var Gn=0;Gn<this._values.length;Gn++)if(this._values[Gn]===ee)return!0;return!1},r$4.prototype.remove=function(ee){for(var Gn=0;Gn<this._values.length;Gn++)if(this._values[Gn]===ee)return this._values.splice(Gn,1),!0;return!1};var R$8="0.7.28",z$4="",N$5="?",A$3="function",C$6="undefined",O$5="object",B$3="string",q$5="major",e$2="model",o$2="name",i$3="type",s$5="vendor",r$3="version",c$6="architecture",v$4="console",d$3="mobile",n$2="tablet",m$8="smarttv",E$5="wearable",M$5="embedded",L$5=255,p$8={extend:function(ee,Gn){var qn,Vn={};for(qn in ee)Gn[qn]&&Gn[qn].length%2==0?Vn[qn]=Gn[qn].concat(ee[qn]):Vn[qn]=ee[qn];return Vn},has:function(ee,Gn){return typeof ee===B$3&&Gn.toLowerCase().indexOf(ee.toLowerCase())!==-1},lowerize:function(ee){return ee.toLowerCase()},major:function(ee){return typeof ee===B$3?ee.replace(/[^\d\.]/g,"").split(".")[0]:void 0},trim:function(ee,Gn){return ee=ee.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),typeof Gn===C$6?ee:ee.substring(0,L$5)}},f$2={rgx:function(ee,Gn){for(var qn,Vn,Wn,jn,Qn,Jn=0;Jn<Gn.length&&!jn;){for(var eo=Gn[Jn],Xn=Gn[Jn+1],to=qn=0;to<eo.length&&!jn;)if(jn=eo[to++].exec(ee))for(Vn=0;Vn<Xn.length;Vn++)Qn=jn[++qn],typeof(Wn=Xn[Vn])===O$5&&0<Wn.length?Wn.length==2?typeof Wn[1]==A$3?this[Wn[0]]=Wn[1].call(this,Qn):this[Wn[0]]=Wn[1]:Wn.length==3?typeof Wn[1]!==A$3||Wn[1].exec&&Wn[1].test?this[Wn[0]]=Qn?Qn.replace(Wn[1],Wn[2]):void 0:this[Wn[0]]=Qn?Wn[1].call(this,Qn,Wn[2]):void 0:Wn.length==4&&(this[Wn[0]]=Qn?Wn[3].call(this,Qn.replace(Wn[1],Wn[2])):void 0):this[Wn]=Qn||void 0;Jn+=2}},str:function(ee,Gn){for(var qn in Gn)if(typeof Gn[qn]===O$5&&0<Gn[qn].length){for(var Vn=0;Vn<Gn[qn].length;Vn++)if(p$8.has(Gn[qn][Vn],ee))return qn===N$5?void 0:qn}else if(p$8.has(Gn[qn],ee))return qn===N$5?void 0:qn;return ee}},y$2={browser:{oldSafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}},oldEdge:{version:{.1:"12.",21:"13.",31:"14.",39:"15.",41:"16.",42:"17.",44:"18."}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},x$6={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[r$3,[o$2,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[r$3,[o$2,"Edge"]],[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]{3,6})\b.+version\/([\w\.-]+)/i,/(opera)(?:.+version\/|[\/\s]+)([\w\.]+)/i],[o$2,r$3],[/opios[\/\s]+([\w\.]+)/i],[r$3,[o$2,"Opera Mini"]],[/\sopr\/([\w\.]+)/i],[r$3,[o$2,"Opera"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(ba?idubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,/(rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i,/(weibo)__([\d\.]+)/i],[o$2,r$3],[/(?:[\s\/]uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[r$3,[o$2,"UCBrowser"]],[/(?:windowswechat)?\sqbcore\/([\w\.]+)\b.*(?:windowswechat)?/i],[r$3,[o$2,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[r$3,[o$2,"WeChat"]],[/konqueror\/([\w\.]+)/i],[r$3,[o$2,"Konqueror"]],[/trident.+rv[:\s]([\w\.]{1,9})\b.+like\sgecko/i],[r$3,[o$2,"IE"]],[/yabrowser\/([\w\.]+)/i],[r$3,[o$2,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o$2,/(.+)/,"$1 Secure Browser"],r$3],[/focus\/([\w\.]+)/i],[r$3,[o$2,"Firefox Focus"]],[/opt\/([\w\.]+)/i],[r$3,[o$2,"Opera Touch"]],[/coc_coc_browser\/([\w\.]+)/i],[r$3,[o$2,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[r$3,[o$2,"Dolphin"]],[/coast\/([\w\.]+)/i],[r$3,[o$2,"Opera Coast"]],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[r$3,[o$2,"MIUI Browser"]],[/fxios\/([\w\.-]+)/i],[r$3,[o$2,"Firefox"]],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[o$2,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[o$2,/(.+)/,"$1 Browser"],r$3],[/(comodo_dragon)\/([\w\.]+)/i],[[o$2,/_/g," "],r$3],[/\s(electron)\/([\w\.]+)\ssafari/i,/(tesla)(?:\sqtcarbrowser|\/(20[12]\d\.[\w\.-]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i],[o$2,r$3],[/(MetaSr)[\/\s]?([\w\.]+)/i,/(LBBROWSER)/i],[o$2],[/;fbav\/([\w\.]+);/i],[r$3,[o$2,"Facebook"]],[/FBAN\/FBIOS|FB_IAB\/FB4A/i],[[o$2,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/\s]([\w\.-]+)/i],[o$2,r$3],[/\bgsa\/([\w\.]+)\s.*safari\//i],[r$3,[o$2,"GSA"]],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[r$3,[o$2,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[o$2,"Chrome WebView"],r$3],[/droid.+\sversion\/([\w\.]+)\b.+(?:mobile\ssafari|safari)/i],[r$3,[o$2,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[o$2,r$3],[/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],[r$3,[o$2,"Mobile Safari"]],[/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],[r$3,o$2],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[o$2,[r$3,f$2.str,y$2.browser.oldSafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[o$2,r$3],[/(navigator|netscape)\/([\w\.-]+)/i],[[o$2,"Netscape"],r$3],[/ile\svr;\srv:([\w\.]+)\).+firefox/i],[r$3,[o$2,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,/(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[o$2,r$3]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[[c$6,"amd64"]],[/(ia32(?=;))/i],[[c$6,p$8.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[[c$6,"ia32"]],[/\b(aarch64|armv?8e?l?)\b/i],[[c$6,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[c$6,"armhf"]],[/windows\s(ce|mobile);\sppc;/i],[[c$6,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[[c$6,/ower/,"",p$8.lowerize]],[/(sun4\w)[;\)]/i],[[c$6,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?:64|(?=v(?:[1-7]|[5-7]1)l?|;|eabi))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[c$6,p$8.lowerize]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus\s10)/i],[e$2,[s$5,"Samsung"],[i$3,n$2]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy\snexus)/i,/\ssamsung[\s-]([\w-]+)/i,/sec-(sgh\w+)/i],[e$2,[s$5,"Samsung"],[i$3,d$3]],[/\((ip(?:hone|od)[\s\w]*);/i],[e$2,[s$5,"Apple"],[i$3,d$3]],[/\((ipad);[\w\s\),;-]+apple/i,/applecoremedia\/[\w\.]+\s\((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[e$2,[s$5,"Apple"],[i$3,n$2]],[/\b((?:agr|ags[23]|bah2?|sht?)-a?[lw]\d{2})/i],[e$2,[s$5,"Huawei"],[i$3,n$2]],[/d\/huawei([\w\s-]+)[;\)]/i,/\b(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?|ele-l\d\d)/i,/\b(\w{2,4}-[atu][ln][01259][019])[;\)\s]/i],[e$2,[s$5,"Huawei"],[i$3,d$3]],[/\b(poco[\s\w]+)(?:\sbuild|\))/i,/\b;\s(\w+)\sbuild\/hm\1/i,/\b(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,/\b(redmi[\s\-_]?(?:note|k)?[\w\s_]+)(?:\sbuild|\))/i,/\b(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i],[[e$2,/_/g," "],[s$5,"Xiaomi"],[i$3,d$3]],[/\b(mi[\s\-_]?(?:pad)(?:[\w\s_]+))(?:\sbuild|\))/i],[[e$2,/_/g," "],[s$5,"Xiaomi"],[i$3,n$2]],[/;\s(\w+)\sbuild.+\soppo/i,/\s(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],[e$2,[s$5,"OPPO"],[i$3,d$3]],[/\svivo\s(\w+)(?:\sbuild|\))/i,/\s(v[12]\d{3}\w?[at])(?:\sbuild|;)/i],[e$2,[s$5,"Vivo"],[i$3,d$3]],[/\s(rmx[12]\d{3})(?:\sbuild|;)/i],[e$2,[s$5,"Realme"],[i$3,d$3]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)\b[\w\s]+build\//i,/\smot(?:orola)?[\s-](\w*)/i,/((?:moto[\s\w\(\)]+|xt\d{3,4}|nexus\s6)(?=\sbuild|\)))/i],[e$2,[s$5,"Motorola"],[i$3,d$3]],[/\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[e$2,[s$5,"Motorola"],[i$3,n$2]],[/((?=lg)?[vl]k\-?\d{3})\sbuild|\s3\.[\s\w;-]{10}lg?-([06cv9]{3,4})/i],[e$2,[s$5,"LG"],[i$3,n$2]],[/(lm-?f100[nv]?|nexus\s[45])/i,/lg[e;\s\/-]+((?!browser|netcast)\w+)/i,/\blg(\-?[\d\w]+)\sbuild/i],[e$2,[s$5,"LG"],[i$3,d$3]],[/(ideatab[\w\-\s]+)/i,/lenovo\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|yt[\d\w-]{6}|tb[\d\w-]{6})/i],[e$2,[s$5,"Lenovo"],[i$3,n$2]],[/(?:maemo|nokia).*(n900|lumia\s\d+)/i,/nokia[\s_-]?([\w\.-]*)/i],[[e$2,/_/g," "],[s$5,"Nokia"],[i$3,d$3]],[/droid.+;\s(pixel\sc)[\s)]/i],[e$2,[s$5,"Google"],[i$3,n$2]],[/droid.+;\s(pixel[\s\daxl]{0,6})(?:\sbuild|\))/i],[e$2,[s$5,"Google"],[i$3,d$3]],[/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[e$2,[s$5,"Sony"],[i$3,d$3]],[/sony\stablet\s[ps]\sbuild\//i,/(?:sony)?sgp\w+(?:\sbuild\/|\))/i],[[e$2,"Xperia Tablet"],[s$5,"Sony"],[i$3,n$2]],[/\s(kb2005|in20[12]5|be20[12][59])\b/i,/\ba000(1)\sbuild/i,/\boneplus\s(a\d{4})[\s)]/i],[e$2,[s$5,"OnePlus"],[i$3,d$3]],[/(alexa)webm/i,/(kf[a-z]{2}wi)(\sbuild\/|\))/i,/(kf[a-z]+)(\sbuild\/|\)).+silk\//i],[e$2,[s$5,"Amazon"],[i$3,n$2]],[/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],[[e$2,"Fire Phone"],[s$5,"Amazon"],[i$3,d$3]],[/\((playbook);[\w\s\),;-]+(rim)/i],[e$2,s$5,[i$3,n$2]],[/((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10;\s(\w+)/i],[e$2,[s$5,"BlackBerry"],[i$3,d$3]],[/(?:\b|asus_)(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus\s7|padfone|p00[cj])/i],[e$2,[s$5,"ASUS"],[i$3,n$2]],[/\s(z[es]6[027][01][km][ls]|zenfone\s\d\w?)\b/i],[e$2,[s$5,"ASUS"],[i$3,d$3]],[/(nexus\s9)/i],[e$2,[s$5,"HTC"],[i$3,n$2]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[s$5,[e$2,/_/g," "],[i$3,d$3]],[/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[e$2,[s$5,"Acer"],[i$3,n$2]],[/droid.+;\s(m[1-5]\snote)\sbuild/i,/\bmz-([\w-]{2,})/i],[e$2,[s$5,"Meizu"],[i$3,d$3]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i,/(microsoft);\s(lumia[\s\w]+)/i,/(lenovo)[_\s-]?([\w-]+)/i,/linux;.+(jolla);/i,/droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[s$5,e$2,[i$3,d$3]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i,/[;\/]\s?(le[\s\-]+pan)[\s\-]+(\w{1,9})\sbuild/i,/[;\/]\s?(trinity)[\-\s]*(t\d{3})\sbuild/i,/\b(gigaset)[\s\-]+(q\w{1,9})\sbuild/i,/\b(vodafone)\s([\w\s]+)(?:\)|\sbuild)/i],[s$5,e$2,[i$3,n$2]],[/\s(surface\sduo)\s/i],[e$2,[s$5,"Microsoft"],[i$3,n$2]],[/droid\s[\d\.]+;\s(fp\du?)\sbuild/i],[e$2,[s$5,"Fairphone"],[i$3,d$3]],[/\s(u304aa)\sbuild/i],[e$2,[s$5,"AT&T"],[i$3,d$3]],[/sie-(\w*)/i],[e$2,[s$5,"Siemens"],[i$3,d$3]],[/[;\/]\s?(rct\w+)\sbuild/i],[e$2,[s$5,"RCA"],[i$3,n$2]],[/[;\/\s](venue[\d\s]{2,7})\sbuild/i],[e$2,[s$5,"Dell"],[i$3,n$2]],[/[;\/]\s?(q(?:mv|ta)\w+)\sbuild/i],[e$2,[s$5,"Verizon"],[i$3,n$2]],[/[;\/]\s(?:barnes[&\s]+noble\s|bn[rt])([\w\s\+]*)\sbuild/i],[e$2,[s$5,"Barnes & Noble"],[i$3,n$2]],[/[;\/]\s(tm\d{3}\w+)\sbuild/i],[e$2,[s$5,"NuVision"],[i$3,n$2]],[/;\s(k88)\sbuild/i],[e$2,[s$5,"ZTE"],[i$3,n$2]],[/;\s(nx\d{3}j)\sbuild/i],[e$2,[s$5,"ZTE"],[i$3,d$3]],[/[;\/]\s?(gen\d{3})\sbuild.*49h/i],[e$2,[s$5,"Swiss"],[i$3,d$3]],[/[;\/]\s?(zur\d{3})\sbuild/i],[e$2,[s$5,"Swiss"],[i$3,n$2]],[/[;\/]\s?((zeki)?tb.*\b)\sbuild/i],[e$2,[s$5,"Zeki"],[i$3,n$2]],[/[;\/]\s([yr]\d{2})\sbuild/i,/[;\/]\s(dragon[\-\s]+touch\s|dt)(\w{5})\sbuild/i],[[s$5,"Dragon Touch"],e$2,[i$3,n$2]],[/[;\/]\s?(ns-?\w{0,9})\sbuild/i],[e$2,[s$5,"Insignia"],[i$3,n$2]],[/[;\/]\s?((nxa|Next)-?\w{0,9})\sbuild/i],[e$2,[s$5,"NextBook"],[i$3,n$2]],[/[;\/]\s?(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05]))\sbuild/i],[[s$5,"Voice"],e$2,[i$3,d$3]],[/[;\/]\s?(lvtel\-)?(v1[12])\sbuild/i],[[s$5,"LvTel"],e$2,[i$3,d$3]],[/;\s(ph-1)\s/i],[e$2,[s$5,"Essential"],[i$3,d$3]],[/[;\/]\s?(v(100md|700na|7011|917g).*\b)\sbuild/i],[e$2,[s$5,"Envizen"],[i$3,n$2]],[/[;\/]\s?(trio[\s\w\-\.]+)\sbuild/i],[e$2,[s$5,"MachSpeed"],[i$3,n$2]],[/[;\/]\s?tu_(1491)\sbuild/i],[e$2,[s$5,"Rotor"],[i$3,n$2]],[/(shield[\w\s]+)\sbuild/i],[e$2,[s$5,"Nvidia"],[i$3,n$2]],[/(sprint)\s(\w+)/i],[s$5,e$2,[i$3,d$3]],[/(kin\.[onetw]{3})/i],[[e$2,/\./g," "],[s$5,"Microsoft"],[i$3,d$3]],[/droid\s[\d\.]+;\s(cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[e$2,[s$5,"Zebra"],[i$3,n$2]],[/droid\s[\d\.]+;\s(ec30|ps20|tc[2-8]\d[kx])\)/i],[e$2,[s$5,"Zebra"],[i$3,d$3]],[/\s(ouya)\s/i,/(nintendo)\s([wids3utch]+)/i],[s$5,e$2,[i$3,v$4]],[/droid.+;\s(shield)\sbuild/i],[e$2,[s$5,"Nvidia"],[i$3,v$4]],[/(playstation\s[345portablevi]+)/i],[e$2,[s$5,"Sony"],[i$3,v$4]],[/[\s\(;](xbox(?:\sone)?(?!;\sxbox))[\s\);]/i],[e$2,[s$5,"Microsoft"],[i$3,v$4]],[/smart-tv.+(samsung)/i],[s$5,[i$3,m$8]],[/hbbtv.+maple;(\d+)/i],[[e$2,/^/,"SmartTV"],[s$5,"Samsung"],[i$3,m$8]],[/(?:linux;\snetcast.+smarttv|lg\snetcast\.tv-201\d)/i],[[s$5,"LG"],[i$3,m$8]],[/(apple)\s?tv/i],[s$5,[e$2,"Apple TV"],[i$3,m$8]],[/crkey/i],[[e$2,"Chromecast"],[s$5,"Google"],[i$3,m$8]],[/droid.+aft([\w])(\sbuild\/|\))/i],[e$2,[s$5,"Amazon"],[i$3,m$8]],[/\(dtv[\);].+(aquos)/i],[e$2,[s$5,"Sharp"],[i$3,m$8]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[s$5,p$8.trim],[e$2,p$8.trim],[i$3,m$8]],[/[\s\/\(](android\s|smart[-\s]?|opera\s)tv[;\)\s]/i],[[i$3,m$8]],[/((pebble))app\/[\d\.]+\s/i],[s$5,e$2,[i$3,E$5]],[/droid.+;\s(glass)\s\d/i],[e$2,[s$5,"Google"],[i$3,E$5]],[/droid\s[\d\.]+;\s(wt63?0{2,3})\)/i],[e$2,[s$5,"Zebra"],[i$3,E$5]],[/(tesla)(?:\sqtcarbrowser|\/20[12]\d\.[\w\.-]+)/i],[s$5,[i$3,M$5]],[/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[e$2,[i$3,d$3]],[/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[e$2,[i$3,n$2]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[i$3,p$8.lowerize]],[/(android[\w\.\s\-]{0,9});.+build/i],[e$2,[s$5,"Generic"]],[/(phone)/i],[[i$3,d$3]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[r$3,[o$2,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[r$3,[o$2,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[o$2,r$3],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[r$3,o$2]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[o$2,r$3],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)(?!.+xbox)/i],[o$2,[r$3,f$2.str,y$2.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[o$2,"Windows"],[r$3,f$2.str,y$2.os.windows.version]],[/ip[honead]{2,4}\b(?:.*os\s([\w]+)\slike\smac|;\sopera)/i,/cfnetwork\/.+darwin/i],[[r$3,/_/g,"."],[o$2,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)(?!.+haiku)/i],[[o$2,"Mac OS"],[r$3,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[r$3,o$2],[/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki|openharmony)[\/\s-]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/\((series40);/i],[o$2,r$3],[/\(bb(10);/i],[r$3,[o$2,"BlackBerry"]],[/(?:symbian\s?os|symbos|s60(?=;)|series60)[\/\s-]?([\w\.]*)/i],[r$3,[o$2,"Symbian"]],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[o$2,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[r$3,[o$2,"webOS"]],[/crkey\/([\d\.]+)/i],[r$3,[o$2,"Chromecast"]],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[o$2,"Chromium OS"],r$3],[/(nintendo|playstation)\s([wids345portablevuch]+)/i,/(xbox);\s+xbox\s([^\);]+)/i,/(mint)[\/\s\(\)]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?=\slinux)|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus|raspbian)(?:\sgnu\/linux)?(?:\slinux)?[\/\s-]?(?!chrom|package)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i,/\s([frentopc-]{0,4}bsd|dragonfly)\s?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku)\s(\w+)/i],[o$2,r$3],[/(sunos)\s?([\w\.\d]*)/i],[[o$2,"Solaris"],r$3],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[o$2,r$3]]},h$7=typeof window!="undefined"&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:z$4;function UAParser(){this.VERSION=R$8,this.BROWSER={NAME:o$2,MAJOR:q$5,VERSION:r$3},this.CPU={ARCHITECTURE:c$6},this.DEVICE={MODEL:e$2,VENDOR:s$5,TYPE:i$3,CONSOLE:v$4,MOBILE:d$3,SMARTTV:m$8,TABLET:n$2,WEARABLE:E$5,EMBEDDED:M$5},this.ENGINE={NAME:o$2,VERSION:r$3},this.OS={NAME:o$2,VERSION:r$3}}UAParser.prototype.getBrowser=function(){var ee={name:void 0,version:void 0};return f$2.rgx.call(ee,h$7,x$6.browser),ee.major=p$8.major(ee.version),ee},UAParser.prototype.getCPU=function(){var ee={architecture:void 0};return f$2.rgx.call(ee,h$7,x$6.cpu),ee},UAParser.prototype.getDevice=function(){var ee={vendor:void 0,model:void 0,type:void 0};return f$2.rgx.call(ee,h$7,x$6.device),ee},UAParser.prototype.getEngine=function(){var ee={name:void 0,version:void 0};return f$2.rgx.call(ee,h$7,x$6.engine),ee},UAParser.prototype.getOS=function(){var ee={name:void 0,version:void 0};return f$2.rgx.call(ee,h$7,x$6.os),ee},UAParser.prototype.getUA=function(){return h$7},UAParser.prototype.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}};var resetSeesionId=function(){for(var ee=D$5.sett.appId,Gn=k$3.getCurrentSesssionKey(ee),qn=getCookie(Gn),Vn=isJSON(qn)&&JSON.parse(qn)||[],Wn=0;Wn<Vn.length;Wn++){var jn=Vn[Wn];jn.appId===ee&&(D$5.setData("isFirstUpload",1),P$6.initUsdTime(),jn.sessionID=uuid(),c$7({t:GC.INIT_SESSION_START}),k$3.resetTakeFullSnapshotToSun(jn.sessionID),window!==window.parent&&k$3.resetTakeFullSnapshotToParent(jn.sessionID),jn.startTime=jn.lastVisitedTime=new Date().getTime(),jn.isRestSID=!0,setCookie(Gn,JSON.stringify(Vn),k$3.getSessionConfig()))}},C$5="br-client",U$a="bonree-device-info",s$4={},o$1={},M$4=function(){var ee=getCookie(C$5);return isReadable(ee)||B$2(ee=uuid()),ee};function B$2(ee){setCookie(C$5,ee,{path:"/",expires:"Thu, 01 Jan 2099 00:00:01 GMT;"})}function D$3(){if(isEmpty(o$1)||!UAParser)try{var ee=new UAParser;o$1=ee&&ee.getResult()||{}}catch(Gn){log(Gn)}}function N$4(ee){if(ee)return/iphone|ipad|ios/i.test(ee)?0:/android/i.test(ee)?1:/windows/i.test(ee)?2:/harmonyOS|OpenHarmony/i.test(ee)?3:/mac/i.test(ee)?4:1}function z$3(){return getCookie(U$a)&&JSON.parse(getCookie(U$a))||null}function getDeviceInfo(){try{R$7()&&D$3(),s$4.di=M$4(),s$4.a="user",s$4.ot=o$1&&o$1.os&&o$1.os.name?o$1.os.name&&N$4(o$1.os.name):"1",s$4.ram=isDefined(navigator.deviceMemory)&&1024*navigator.deviceMemory||-1,s$4.l=navigator.language||navigator.userLanguage,s$4.ds=window.screen.width+"*"+window.screen.height,s$4.rom=-1,s$4.bn=o$1&&o$1.os&&o$1.os.name?o$1.os.name:"unknown",s$4.omv=o$1&&o$1.os&&o$1.os.version?o$1.os.version:"unknown",s$4.ci=o$1&&isDefined(o$1.cpu)&&o$1.cpu.architecture&&o$1.cpu.architecture||"unknown",s$4.ctn=o$1&&isDefined(o$1.browser)?o$1.browser.name:"unknown",s$4.ctv=o$1&&o$1.browser&&o$1.browser.version?o$1.browser.version:"unknown",isDefined(o$1.device)&&isDefined(o$1.device.model)&&(s$4.m=o$1.device.model),setDeviceForLog(s$4.di)}catch(Gn){return log(Gn,!0),{}}var ee=z$3();return ee&&["ctn","ctv","bn","omv"].some(function(Gn){return ee[Gn]!==s$4[Gn]})&&resetSeesionId(),setCookie(U$a,JSON.stringify(s$4)),s$4}function R$7(){try{var ee=["Firefox/47"],Gn=navigator.userAgent&&navigator.userAgent.match(/(firefox|msie|chrome|safari)[/\s]([\d.]+)/gi)||void 0;if(Gn&&0<Gn.length)for(var qn=Gn[0].split("/"),Vn=0,Wn=ee.length-1;Vn<=Wn;Vn++){var jn=ee[Vn];if((jn=jn.split("/"))[0]===qn[0]&&Number(qn[1].split(".")[0])<=Number(jn[1]))return!1}return!0}catch(Qn){log(Qn)}return!1}function w$9(){}w$9.prototype.zero=function(ee){if(0<ee){for(var Gn="0",qn=1;qn<ee;qn++)Gn+=Gn;return Gn}return""},w$9.prototype.stringToArray=function(ee){for(var Gn=[],qn=ee.length,Vn=0;Vn<qn;Vn++){var Wn,jn,Qn,Jn,eo,Xn=ee.charCodeAt(Vn);19968<Xn&&Xn<40869?(jn="1110",Jn=Qn="10",(eo=(Wn=Xn.toString(2)).length)<=6?(Jn=Jn+this.zero(6-eo)+Wn,Qn+=this.zero(6),jn+=this.zero(4)):6<eo&&eo<=12?(Jn+=Wn.slice(-6),Qn=Qn+this.zero(12-eo)+Wn.substr(0,eo-6),jn+=this.zero(4)):(Jn+=Wn.slice(-6),Qn+=Wn.substr(eo-12,6),jn=jn+this.zero(16-eo)+Wn.substr(0,eo-12)),Gn.push(parseInt(jn,2),parseInt(Qn,2),parseInt(Jn,2))):Gn.push(Xn)}return Gn},w$9.prototype.stringToArrayBufferInUtf8=function(ee){return this.stringToArray(ee)},Array.prototype.Bonreefill||Object.defineProperty(Array.prototype,"Bonreefill",{value:function(ee){if(this==null)throw new TypeError("this is null or not defined");for(var Gn=Object(this),qn=Gn.length>>>0,Wn=arguments[1]>>0,Vn=Wn<0?Math.max(qn+Wn,0):Math.min(Wn,qn),Wn=arguments[2],Wn=Wn===void 0?qn:Wn>>0,jn=Wn<0?Math.max(qn+Wn,0):Math.min(Wn,qn);Vn<jn;)Gn[Vn]=ee,Vn++;return Gn}});var y$1=typeof Uint8Array!="undefined"&&typeof Uint32Array!="undefined";function p$7(ee,Gn){return y$1?new(ee==="Uint8"?Uint8Array:Uint32Array)(Gn):Gn}var g$3=16,d$2=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72];function F$4(ee,Gn,qn,Vn){Vn=Vn||ee.length;for(var Wn=qn=qn||0;Wn<Vn;Wn++)ee[Wn]=Gn;return ee}function P$5(ee,Gn,qn){if(y$1&&ee.subarray)return ee.subarray(Gn,qn);var Vn=[];qn=qn||ee.length;for(var Wn=Gn;Wn<qn;Wn++)Vn[Wn-Gn]=ee[Wn];return y$1?new Uint8Array(Vn):Vn}d$2=p$7("Uint8",d$2),y$1&&(Uint8Array.prototype.Bonreefill||(Uint8Array.prototype.Bonreefill=function(){return F$4.apply(null,[this].concat(Array.prototype.slice.call(arguments)))}),Uint8Array.prototype.Bonreeslice||(Uint8Array.prototype.Bonreeslice=function(ee){return P$5(this,ee)}));var T$3=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257],T$3=p$7("Uint32",T$3),l$5=[2746333894,1453994832,1736282519,2993693404];function u(ee){if(this.Crypt=new w$9,ee=this.Crypt.stringToArrayBufferInUtf8(ee.key),ee.length!==16)throw new Error("key should be a 16 bytes string");this.key=ee,this.mode="ecb",this.encryptRoundKeys=p$7("Uint32",new Array(32)),this.spawnEncryptRoundKeys(),ee=this.encryptRoundKeys.slice(0),y$1?Array.prototype.reverse.apply(ee):ee.reverse(),Uint32Array.prototype.reverse=function(){Array.prototype.reverse.apply(this,arguments)},this.decryptRoundKeys=p$7("Uint32",ee)}function q$4(ee){var Gn=!1,qn=0;function Vn(){++qn==3&&ee(Gn)}if(window.webkitRequestFileSystem?window.webkitRequestFileSystem(window.TEMPORARY,1,function(){Gn=!1,Vn()},function(jn){Gn=!0,Vn()}):Vn(),typeof InstallTrigger!="undefined")try{var Wn=indexedDB.open("test");Wn.onerror=function(){Gn=!0,Vn()},Wn.onsuccess=function(){Gn=!1,Vn()}}catch(jn){Vn()}else Vn();(-1<window.navigator.userAgent.indexOf("Edge")||-1<window.navigator.userAgent.indexOf("Chrome"))&&"storage"in navigator&&"estimate"in navigator.storage?navigator.storage.estimate(function(jn){Gn=jn.quota<12e8,Vn()}):Vn()}l$5=p$7("Uint32",l$5),u.prototype.doBlockCrypt=function(ee,Gn){var qn=p$7("Uint32",new Array(36));if(y$1)qn.set(ee,0);else for(var Vn=0;Vn<ee.length;Vn++)qn[Vn]=ee[Vn];for(Vn=0;Vn<32;Vn++)qn[Vn+4]=qn[Vn]^this.tTransform1(qn[Vn+1]^qn[Vn+2]^qn[Vn+3]^Gn[Vn]);var Wn=p$7("Uint32",new Array(4));return Wn[0]=qn[35],Wn[1]=qn[34],Wn[2]=qn[33],Wn[3]=qn[32],Wn},u.prototype.spawnEncryptRoundKeys=function(){var ee=p$7("Uint32",new Array(4)),Gn=(ee[0]=this.key[0]<<24|this.key[1]<<16|this.key[2]<<8|this.key[3],ee[1]=this.key[4]<<24|this.key[5]<<16|this.key[6]<<8|this.key[7],ee[2]=this.key[8]<<24|this.key[9]<<16|this.key[10]<<8|this.key[11],ee[3]=this.key[12]<<24|this.key[13]<<16|this.key[14]<<8|this.key[15],p$7("Uint32",new Array(36)));Gn[0]=ee[0]^l$5[0],Gn[1]=ee[1]^l$5[1],Gn[2]=ee[2]^l$5[2],Gn[3]=ee[3]^l$5[3];for(var qn=0;qn<32;qn++)Gn[qn+4]=Gn[qn]^this.tTransform2(Gn[qn+1]^Gn[qn+2]^Gn[qn+3]^T$3[qn]),this.encryptRoundKeys[qn]=Gn[qn+4]},u.prototype.rotateLeft=function(ee,Gn){return ee<<Gn|ee>>>32-Gn},u.prototype.linearTransform1=function(ee){return ee^this.rotateLeft(ee,2)^this.rotateLeft(ee,10)^this.rotateLeft(ee,18)^this.rotateLeft(ee,24)},u.prototype.linearTransform2=function(ee){return ee^this.rotateLeft(ee,13)^this.rotateLeft(ee,23)},u.prototype.tauTransform=function(ee){return d$2[ee>>>24&255]<<24|d$2[ee>>>16&255]<<16|d$2[ee>>>8&255]<<8|d$2[255&ee]},u.prototype.tTransform1=function(ee){return ee=this.tauTransform(ee),this.linearTransform1(ee)},u.prototype.tTransform2=function(ee){return ee=this.tauTransform(ee),this.linearTransform2(ee)},u.prototype.padding=function(ee){var Gn,qn;return ee===null?null:(Gn=g$3-ee.length%g$3,(qn=p$7("Uint8",ee.length+Gn)).set(ee,0),qn.Bonreefill(Gn,ee.length),qn)},u.prototype.dePadding=function(ee){var Gn;return ee===null?null:(Gn=ee[ee.length-1],ee.Bonreeslice(0,ee.length-Gn))},u.prototype.uint8ToUint32Block=function(ee,Gn){Gn===void 0&&(Gn=0);var qn=p$7("Uint32",new Array(4));return qn[0]=ee[Gn]<<24|ee[Gn+1]<<16|ee[Gn+2]<<8|ee[Gn+3],qn[1]=ee[Gn+4]<<24|ee[Gn+5]<<16|ee[Gn+6]<<8|ee[Gn+7],qn[2]=ee[Gn+8]<<24|ee[Gn+9]<<16|ee[Gn+10]<<8|ee[Gn+11],qn[3]=ee[Gn+12]<<24|ee[Gn+13]<<16|ee[Gn+14]<<8|ee[Gn+15],qn},u.prototype.encrypt=function(ee){for(var ee=this.Crypt.stringToArrayBufferInUtf8(ee),Gn=this.padding(ee),qn=Gn.length/g$3,Vn=p$7("Uint8",Gn.length),Wn=0;Wn<qn;Wn++)for(var jn=Wn*g$3,Qn=this.uint8ToUint32Block(Gn,jn),Jn=this.doBlockCrypt(Qn,this.encryptRoundKeys),eo=0;eo<g$3;eo++)Vn[jn+eo]=Jn[parseInt(eo/4)]>>(3-eo)%4*8&255;return Vn},document&&document.addEventListener("readystatechange",function(ee){ee.target.readyState==="complete"&&R$7()&&q$4(function(Gn){try{isDefined(window.bonreeRUM)&&(bonreeRUM.isPrivate=Gn)}catch(qn){log(qn)}})});var Vt$1=4,Aa=0,Za=1,Jt$1=2;function le$1(ee){for(var Gn=ee.length;0<=--Gn;)ee[Gn]=0}var Qt$1=0,Da=1,qt$1=2,ei=3,ai=258,Je=29,we$1=256,be=we$1+1+Je,he$1=30,Qe$1=19,Ia=2*be+1,ae$1=15,qe$1=16,ti=7,ea$1=256,Oa=16,Ua=17,Na=18,aa$1=typeof Uint8Array!="undefined"&&typeof Uint16Array!="undefined"&&typeof Uint32Array!="undefined";function x$5(ee,Gn){if(Array.isArray(Gn)){if(!aa$1)return Gn.slice();switch(ee){case"Uint8":return new Uint8Array(Gn);case"Uint16":return new Uint16Array(Gn);case"Uint32":return new Uint32Array(Gn);default:return Gn.slice()}}var qn=Gn;if(!aa$1){for(var Vn=new Array(qn),Wn=0;Wn<qn;Wn++)Vn[Wn]=0;return Vn}switch(ee){case"Uint8":return new Uint8Array(qn);case"Uint16":return new Uint16Array(qn);case"Uint32":return new Uint32Array(qn);default:return new Array(qn)}}var ta$1=x$5("Uint8",[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),Ce$1=x$5("Uint8",[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),ii=x$5("Uint8",[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),La=x$5("Uint8",[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),ri=512,Y$3=new Array(2*(be+2)),ge$1=(le$1(Y$3),new Array(2*he$1)),pe$3=(le$1(ge$1),new Array(ri)),xe=(le$1(pe$3),new Array(ai-ei+1)),ia$1=(le$1(xe),new Array(Je)),Fe$1=(le$1(ia$1),new Array(he$1)),Ca,Fa,$a;function ra$1(ee,Gn,qn,Vn,Wn){this.static_tree=ee,this.extra_bits=Gn,this.extra_base=qn,this.elems=Vn,this.max_length=Wn,this.has_stree=ee&&ee.length}function na$1(ee,Gn){this.dyn_tree=ee,this.max_code=0,this.stat_desc=Gn}le$1(Fe$1);var Ma=function(ee){return ee<256?pe$3[ee]:pe$3[256+(ee>>>7)]},ke$1=function(ee,Gn){ee.pending_buf[ee.pending++]=255&Gn,ee.pending_buf[ee.pending++]=Gn>>>8&255},L$4=function(ee,Gn,qn){ee.bi_valid>qe$1-qn?(ee.bi_buf|=Gn<<ee.bi_valid&65535,ke$1(ee,ee.bi_buf),ee.bi_buf=Gn>>qe$1-ee.bi_valid,ee.bi_valid+=qn-qe$1):(ee.bi_buf|=Gn<<ee.bi_valid&65535,ee.bi_valid+=qn)},H$4=function(ee,Gn,qn){L$4(ee,qn[2*Gn],qn[2*Gn+1])},Ha=function(ee,Gn){for(var qn=0;qn|=1&ee,ee>>>=1,qn<<=1,0<--Gn;);return qn>>>1},ni=function(ee){ee.bi_valid===16?(ke$1(ee,ee.bi_buf),ee.bi_buf=0,ee.bi_valid=0):8<=ee.bi_valid&&(ee.pending_buf[ee.pending++]=255&ee.bi_buf,ee.bi_buf>>=8,ee.bi_valid-=8)},fi=function(ee,Gn){for(var qn,Vn,Wn,jn,Qn,Jn=Gn.dyn_tree,eo=Gn.max_code,Xn=Gn.stat_desc.static_tree,to=Gn.stat_desc.has_stree,io=Gn.stat_desc.extra_bits,ro=Gn.stat_desc.extra_base,no=Gn.stat_desc.max_length,oo=0,ao=0;ao<=ae$1;ao++)ee.bl_count[ao]=0;for(Jn[2*ee.heap[ee.heap_max]+1]=0,qn=ee.heap_max+1;qn<Ia;qn++)(ao=Jn[2*Jn[2*(Vn=ee.heap[qn])+1]+1]+1)>no&&(ao=no,oo++),Jn[2*Vn+1]=ao,eo<Vn||(ee.bl_count[ao]++,jn=0,ro<=Vn&&(jn=io[Vn-ro]),Qn=Jn[2*Vn],ee.opt_len+=Qn*(ao+jn),!to)||(ee.static_len+=Qn*(Xn[2*Vn+1]+jn));if(oo!==0){do for(ao=no-1;ee.bl_count[ao]===0;)ao--;while(ee.bl_count[ao]--,ee.bl_count[ao+1]+=2,ee.bl_count[no]--,0<(oo-=2));for(ao=no;ao!==0;ao--)for(Vn=ee.bl_count[ao];Vn!==0;)(Wn=ee.heap[--qn])>eo||(Jn[2*Wn+1]!==ao&&(ee.opt_len+=(ao-Jn[2*Wn+1])*Jn[2*Wn],Jn[2*Wn+1]=ao),Vn--)}},Ba=function(ee,Gn,qn){for(var Vn,Wn=new Array(ae$1+1),jn=0,Qn=1;Qn<=ae$1;Qn++)jn=jn+qn[Qn-1]<<1,Wn[Qn]=jn;for(Vn=0;Vn<=Gn;Vn++){var Jn=ee[2*Vn+1];Jn!==0&&(ee[2*Vn]=Ha(Wn[Jn]++,Jn))}},_i=function(){for(var ee,Gn,qn,Vn=new Array(ae$1+1),Wn=0,jn=0;jn<Je-1;jn++)for(ia$1[jn]=Wn,ee=0;ee<1<<ta$1[jn];ee++)xe[Wn++]=jn;for(xe[Wn-1]=jn,jn=qn=0;jn<16;jn++)for(Fe$1[jn]=qn,ee=0;ee<1<<Ce$1[jn];ee++)pe$3[qn++]=jn;for(qn>>=7;jn<he$1;jn++)for(Fe$1[jn]=qn<<7,ee=0;ee<1<<Ce$1[jn]-7;ee++)pe$3[256+qn++]=jn;for(Gn=0;Gn<=ae$1;Gn++)Vn[Gn]=0;for(ee=0;ee<=143;)Y$3[2*ee+1]=8,ee++,Vn[8]++;for(;ee<=255;)Y$3[2*ee+1]=9,ee++,Vn[9]++;for(;ee<=279;)Y$3[2*ee+1]=7,ee++,Vn[7]++;for(;ee<=287;)Y$3[2*ee+1]=8,ee++,Vn[8]++;for(Ba(Y$3,be+1,Vn),ee=0;ee<he$1;ee++)ge$1[2*ee+1]=5,ge$1[2*ee]=Ha(ee,5);Ca=new ra$1(Y$3,ta$1,we$1+1,be,ae$1),Fa=new ra$1(ge$1,Ce$1,0,he$1,ae$1),$a=new ra$1(new Array(0),ii,0,Qe$1,ti)},Ka=function(ee){for(var Gn=0;Gn<be;Gn++)ee.dyn_ltree[2*Gn]=0;for(Gn=0;Gn<he$1;Gn++)ee.dyn_dtree[2*Gn]=0;for(Gn=0;Gn<Qe$1;Gn++)ee.bl_tree[2*Gn]=0;ee.dyn_ltree[2*ea$1]=1,ee.opt_len=ee.static_len=0,ee.sym_next=ee.matches=0},Pa=function(ee){8<ee.bi_valid?ke$1(ee,ee.bi_buf):0<ee.bi_valid&&(ee.pending_buf[ee.pending++]=ee.bi_buf),ee.bi_buf=0,ee.bi_valid=0},Ya=function(ee,Gn,qn,Vn){var Wn=2*Gn,jn=2*qn;return ee[Wn]<ee[jn]||ee[Wn]===ee[jn]&&Vn[Gn]<=Vn[qn]},fa=function(ee,Gn,qn){for(var Vn=ee.heap[qn],Wn=qn<<1;Wn<=ee.heap_len&&(Wn<ee.heap_len&&Ya(Gn,ee.heap[Wn+1],ee.heap[Wn],ee.depth)&&Wn++,!Ya(Gn,Vn,ee.heap[Wn],ee.depth));)ee.heap[qn]=ee.heap[Wn],qn=Wn,Wn<<=1;ee.heap[qn]=Vn},Xa=function(ee,Gn,qn){var Vn,Wn,jn,Qn,Jn=0;if(ee.sym_next!==0)for(;Vn=255&ee.pending_buf[ee.sym_buf+Jn++],Vn+=(255&ee.pending_buf[ee.sym_buf+Jn++])<<8,Wn=ee.pending_buf[ee.sym_buf+Jn++],Vn==0?H$4(ee,Wn,Gn):(jn=xe[Wn],H$4(ee,jn+we$1+1,Gn),(Qn=ta$1[jn])!==0&&(Wn-=ia$1[jn],L$4(ee,Wn,Qn)),jn=Ma(--Vn),H$4(ee,jn,qn),(Qn=Ce$1[jn])!==0&&(Vn-=Fe$1[jn],L$4(ee,Vn,Qn))),Jn<ee.sym_next;);H$4(ee,ea$1,Gn)},_a=function(ee,Gn){var qn,Vn,Wn,jn=Gn.dyn_tree,Qn=Gn.stat_desc.static_tree,Jn=Gn.stat_desc.has_stree,eo=Gn.stat_desc.elems,Xn=-1;for(ee.heap_len=0,ee.heap_max=Ia,qn=0;qn<eo;qn++)jn[2*qn]!==0?(ee.heap[++ee.heap_len]=Xn=qn,ee.depth[qn]=0):jn[2*qn+1]=0;for(;ee.heap_len<2;)jn[2*(Wn=ee.heap[++ee.heap_len]=Xn<2?++Xn:0)]=1,ee.depth[Wn]=0,ee.opt_len--,Jn&&(ee.static_len-=Qn[2*Wn+1]);for(Gn.max_code=Xn,qn=ee.heap_len>>1;1<=qn;qn--)fa(ee,jn,qn);for(Wn=eo;qn=ee.heap[1],ee.heap[1]=ee.heap[ee.heap_len--],fa(ee,jn,1),Vn=ee.heap[1],ee.heap[--ee.heap_max]=qn,ee.heap[--ee.heap_max]=Vn,jn[2*Wn]=jn[2*qn]+jn[2*Vn],ee.depth[Wn]=(ee.depth[qn]>=ee.depth[Vn]?ee.depth[qn]:ee.depth[Vn])+1,jn[2*qn+1]=jn[2*Vn+1]=Wn,ee.heap[1]=Wn++,fa(ee,jn,1),2<=ee.heap_len;);ee.heap[--ee.heap_max]=ee.heap[1],fi(ee,Gn),Ba(jn,Xn,ee.bl_count)},Ga=function(ee,Gn,qn){var Vn,Wn,jn=-1,Qn=Gn[1],Jn=0,eo=7,Xn=4;for(Qn===0&&(eo=138,Xn=3),Gn[2*(qn+1)+1]=65535,Vn=0;Vn<=qn;Vn++)Wn=Qn,Qn=Gn[2*(Vn+1)+1],++Jn<eo&&Wn===Qn||(Jn<Xn?ee.bl_tree[2*Wn]+=Jn:Wn!==0?(Wn!==jn&&ee.bl_tree[2*Wn]++,ee.bl_tree[2*Oa]++):Jn<=10?ee.bl_tree[2*Ua]++:ee.bl_tree[2*Na]++,jn=Wn,Xn=(Jn=0)===Qn?(eo=138,3):Wn===Qn?(eo=6,3):(eo=7,4))},ja=function(ee,Gn,qn){var Vn,Wn,jn=-1,Qn=Gn[1],Jn=0,eo=7,Xn=4;for(Qn===0&&(eo=138,Xn=3),Vn=0;Vn<=qn;Vn++)if(Wn=Qn,Qn=Gn[2*(Vn+1)+1],!(++Jn<eo&&Wn===Qn)){if(Jn<Xn)for(;H$4(ee,Wn,ee.bl_tree),--Jn!=0;);else Wn!==0?(Wn!==jn&&(H$4(ee,Wn,ee.bl_tree),Jn--),H$4(ee,Oa,ee.bl_tree),L$4(ee,Jn-3,2)):Jn<=10?(H$4(ee,Ua,ee.bl_tree),L$4(ee,Jn-3,3)):(H$4(ee,Na,ee.bl_tree),L$4(ee,Jn-11,7));jn=Wn,Xn=(Jn=0)===Qn?(eo=138,3):Wn===Qn?(eo=6,3):(eo=7,4)}},li=function(ee){var Gn;for(Ga(ee,ee.dyn_ltree,ee.l_desc.max_code),Ga(ee,ee.dyn_dtree,ee.d_desc.max_code),_a(ee,ee.bl_desc),Gn=Qe$1-1;3<=Gn&&ee.bl_tree[2*La[Gn]+1]===0;Gn--);return ee.opt_len+=3*(Gn+1)+5+5+4,Gn},hi=function(ee,Gn,qn,Vn){var Wn;for(L$4(ee,Gn-257,5),L$4(ee,qn-1,5),L$4(ee,Vn-4,4),Wn=0;Wn<Vn;Wn++)L$4(ee,ee.bl_tree[2*La[Wn]+1],3);ja(ee,ee.dyn_ltree,Gn-1),ja(ee,ee.dyn_dtree,qn-1)},di=function(ee){for(var Gn=4093624447,qn=0;qn<=31;qn++,Gn>>>=1)if(1&Gn&&ee.dyn_ltree[2*qn]!==0)return Aa;if(ee.dyn_ltree[18]!==0||ee.dyn_ltree[20]!==0||ee.dyn_ltree[26]!==0)return Za;for(qn=32;qn<we$1;qn++)if(ee.dyn_ltree[2*qn]!==0)return Za;return Aa},Wa=!1,oi=function(ee){Wa||(_i(),Wa=!0),ee.l_desc=new na$1(ee.dyn_ltree,Ca),ee.d_desc=new na$1(ee.dyn_dtree,Fa),ee.bl_desc=new na$1(ee.bl_tree,$a),ee.bi_buf=0,ee.bi_valid=0,Ka(ee)},Va=function(ee,Gn,qn,Vn){L$4(ee,(Qt$1<<1)+(Vn?1:0),3),Pa(ee),ke$1(ee,qn),ke$1(ee,~qn),qn&&ee.pending_buf.set(ee.window.subarray(Gn,Gn+qn),ee.pending),ee.pending+=qn},vi=function(ee){L$4(ee,Da<<1,3),H$4(ee,ea$1,Y$3),ni(ee)},ui=function(ee,Gn,qn,Vn){var Wn,jn,Qn=0;0<ee.level?(ee.strm.data_type===Jt$1&&(ee.strm.data_type=di(ee)),_a(ee,ee.l_desc),_a(ee,ee.d_desc),Qn=li(ee),Wn=ee.opt_len+3+7>>>3,(jn=ee.static_len+3+7>>>3)<=Wn&&(Wn=jn)):Wn=jn=qn+5,qn+4<=Wn&&Gn!==-1?Va(ee,Gn,qn,Vn):ee.strategy===Vt$1||jn===Wn?(L$4(ee,(Da<<1)+(Vn?1:0),3),Xa(ee,Y$3,ge$1)):(L$4(ee,(qt$1<<1)+(Vn?1:0),3),hi(ee,ee.l_desc.max_code+1,ee.d_desc.max_code+1,Qn+1),Xa(ee,ee.dyn_ltree,ee.dyn_dtree)),Ka(ee),Vn&&Pa(ee)},si=function(ee,Gn,qn){return ee.pending_buf[ee.sym_buf+ee.sym_next++]=Gn,ee.pending_buf[ee.sym_buf+ee.sym_next++]=Gn>>8,ee.pending_buf[ee.sym_buf+ee.sym_next++]=qn,Gn===0?ee.dyn_ltree[2*qn]++:(ee.matches++,Gn--,ee.dyn_ltree[2*(xe[qn]+we$1+1)]++,ee.dyn_dtree[2*Ma(Gn)]++),ee.sym_next===ee.sym_end},ci=oi,wi=Va,bi=ui,gi=si,pi=vi,Ee$2={_tr_init:ci,_tr_stored_block:wi,_tr_flush_block:bi,_tr_tally:gi,_tr_align:pi},xi=function(ee,Gn,qn,Vn){for(var Wn=65535&ee|0,jn=ee>>>16&65535|0,Qn=0;qn!==0;){for(qn-=Qn=2e3<qn?2e3:qn;jn=jn+(Wn=Wn+Gn[Vn++]|0)|0,--Qn;);Wn%=65521,jn%=65521}return Wn|jn<<16|0},me$2=xi,ki=function(){for(var ee=[],Gn=0;Gn<256;Gn++){for(var qn=Gn,Vn=0;Vn<8;Vn++)qn=1&qn?3988292384^qn>>>1:qn>>>1;ee[Gn]=qn}return ee},Ei=x$5("Uint32",ki()),mi=function(ee,Gn,qn,Vn){var Wn=Ei,jn=Vn+qn;ee^=-1;for(var Qn=Vn;Qn<jn;Qn++)ee=ee>>>8^Wn[255&(ee^Gn[Qn])];return-1^ee},O$4=mi,te$1={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},w$8={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},Si=Ee$2._tr_init,la$1=Ee$2._tr_stored_block,yi=Ee$2._tr_flush_block,j$1=Ee$2._tr_tally,zi=Ee$2._tr_align,W=w$8.Z_NO_FLUSH,Ri=w$8.Z_PARTIAL_FLUSH,Ti=w$8.Z_FULL_FLUSH,$$4=w$8.Z_FINISH,Ja=w$8.Z_BLOCK,U$9=w$8.Z_OK,Qa=w$8.Z_STREAM_END,B$1=w$8.Z_STREAM_ERROR,Ai=w$8.Z_DATA_ERROR,ha=w$8.Z_BUF_ERROR,Zi=w$8.Z_DEFAULT_COMPRESSION,Di=w$8.Z_FILTERED,$e$1=w$8.Z_HUFFMAN_ONLY,Ii=w$8.Z_RLE,Oi=w$8.Z_FIXED,Ui=w$8.Z_DEFAULT_STRATEGY,Ni=w$8.Z_UNKNOWN,Me$1=w$8.Z_DEFLATED,Li=9,Ci=15,Fi=8,$i=29,Mi=256,da$1=Mi+1+$i,Hi=30,Bi=19,Ki=2*da$1+1,Pi=15,m$7=3,V$3=258,K$4=V$3+m$7+1,Yi=32,de$3=42,oa$1=57,va=69,ua$1=73,sa$1=91,ca$1=103,ie$2=113,Se$1=666,N$3=1,oe$1=2,re$1=3,ve=4,Xi=3,ne$1=function(ee,Gn){return ee.msg=te$1[Gn],Gn},qa=function(ee){return 2*ee-(4<ee?9:0)},J=function(ee){for(var Gn=ee.length;0<=--Gn;)ee[Gn]=0},Gi=function(ee){for(var Gn,qn,Vn=ee.w_size,Wn=Gn=ee.hash_size;qn=ee.head[--Wn],ee.head[Wn]=Vn<=qn?qn-Vn:0,--Gn;);for(Wn=Gn=Vn;qn=ee.prev[--Wn],ee.prev[Wn]=Vn<=qn?qn-Vn:0,--Gn;);},ji=function(ee,Gn,qn){return(Gn<<ee.hash_shift^qn)&ee.hash_mask},Q$2=ji,C$4=function(ee){var Gn=ee.state,qn=Gn.pending;(qn=qn>ee.avail_out?ee.avail_out:qn)!==0&&(ee.output.set(Gn.pending_buf.subarray(Gn.pending_out,Gn.pending_out+qn),ee.next_out),ee.next_out+=qn,Gn.pending_out+=qn,ee.total_out+=qn,ee.avail_out-=qn,Gn.pending-=qn,Gn.pending===0)&&(Gn.pending_out=0)},F$3=function(ee,Gn){yi(ee,0<=ee.block_start?ee.block_start:-1,ee.strstart-ee.block_start,Gn),ee.block_start=ee.strstart,C$4(ee.strm)},R$6=function(ee,Gn){ee.pending_buf[ee.pending++]=Gn},ye=function(ee,Gn){ee.pending_buf[ee.pending++]=Gn>>>8&255,ee.pending_buf[ee.pending++]=255&Gn},wa=function(ee,Gn,qn,Vn){var Wn=ee.avail_in;return(Wn=Vn<Wn?Vn:Wn)===0?0:(ee.avail_in-=Wn,Gn.set(ee.input.subarray(ee.next_in,ee.next_in+Wn),qn),ee.state.wrap===1?ee.adler=me$2(ee.adler,Gn,Wn,qn):ee.state.wrap===2&&(ee.adler=O$4(ee.adler,Gn,Wn,qn)),ee.next_in+=Wn,ee.total_in+=Wn,Wn)},et$1=function(ee,Gn){var qn,Vn,Wn=ee.max_chain_length,jn=ee.strstart,Qn=ee.prev_length,Jn=ee.nice_match,eo=ee.strstart>ee.w_size-K$4?ee.strstart-(ee.w_size-K$4):0,Xn=ee.window,to=ee.w_mask,io=ee.prev,ro=ee.strstart+V$3,no=Xn[jn+Qn-1],oo=Xn[jn+Qn];ee.prev_length>=ee.good_match&&(Wn>>=2),Jn>ee.lookahead&&(Jn=ee.lookahead);do if(Xn[(qn=Gn)+Qn]===oo&&Xn[qn+Qn-1]===no&&Xn[qn]===Xn[jn]&&Xn[++qn]===Xn[jn+1]){for(jn+=2,qn++;Xn[++jn]===Xn[++qn]&&Xn[++jn]===Xn[++qn]&&Xn[++jn]===Xn[++qn]&&Xn[++jn]===Xn[++qn]&&Xn[++jn]===Xn[++qn]&&Xn[++jn]===Xn[++qn]&&Xn[++jn]===Xn[++qn]&&Xn[++jn]===Xn[++qn]&&jn<ro;);if(Vn=V$3-(ro-jn),jn=ro-V$3,Qn<Vn){if(ee.match_start=Gn,Jn<=(Qn=Vn))break;no=Xn[jn+Qn-1],oo=Xn[jn+Qn]}}while((Gn=io[Gn&to])>eo&&--Wn!=0);return Qn<=ee.lookahead?Qn:ee.lookahead},ue=function(ee){var Gn,qn,Vn=ee.w_size;do{if(Gn=ee.window_size-ee.lookahead-ee.strstart,ee.strstart>=Vn+(Vn-K$4)&&(ee.window.set(ee.window.subarray(Vn,Vn+Vn-Gn),0),ee.match_start-=Vn,ee.strstart-=Vn,ee.block_start-=Vn,ee.insert>ee.strstart&&(ee.insert=ee.strstart),Gi(ee),Gn+=Vn),ee.strm.avail_in===0)break;if(Gn=wa(ee.strm,ee.window,ee.strstart+ee.lookahead,Gn),ee.lookahead+=Gn,ee.lookahead+ee.insert>=m$7)for(qn=ee.strstart-ee.insert,ee.ins_h=ee.window[qn],ee.ins_h=Q$2(ee,ee.ins_h,ee.window[qn+1]);ee.insert&&(ee.ins_h=Q$2(ee,ee.ins_h,ee.window[qn+m$7-1]),ee.prev[qn&ee.w_mask]=ee.head[ee.ins_h],ee.head[ee.ins_h]=qn,qn++,ee.insert--,!(ee.lookahead+ee.insert<m$7)););}while(ee.lookahead<K$4&&ee.strm.avail_in!==0)},at=function(ee,Gn){for(var qn,Vn,Wn,jn=ee.pending_buf_size-5>ee.w_size?ee.w_size:ee.pending_buf_size-5,Qn=0,Jn=ee.strm.avail_in;qn=65535,Wn=ee.bi_valid+42>>3,!(ee.strm.avail_out<Wn||(qn=(Wn=ee.strm.avail_out-Wn)<(qn=qn>(Vn=ee.strstart-ee.block_start)+ee.strm.avail_in?Vn+ee.strm.avail_in:qn)?Wn:qn)<jn&&(qn===0&&Gn!==$$4||Gn===W||qn!==Vn+ee.strm.avail_in))&&(Qn=Gn===$$4&&qn===Vn+ee.strm.avail_in?1:0,la$1(ee,0,0,Qn),ee.pending_buf[ee.pending-4]=qn,ee.pending_buf[ee.pending-3]=qn>>8,ee.pending_buf[ee.pending-2]=~qn,ee.pending_buf[ee.pending-1]=~qn>>8,C$4(ee.strm),Vn&&(qn<Vn&&(Vn=qn),ee.strm.output.set(ee.window.subarray(ee.block_start,ee.block_start+Vn),ee.strm.next_out),ee.strm.next_out+=Vn,ee.strm.avail_out-=Vn,ee.strm.total_out+=Vn,ee.block_start+=Vn,qn-=Vn),qn&&(wa(ee.strm,ee.strm.output,ee.strm.next_out,qn),ee.strm.next_out+=qn,ee.strm.avail_out-=qn,ee.strm.total_out+=qn),Qn===0););return(Jn-=ee.strm.avail_in)&&(Jn>=ee.w_size?(ee.matches=2,ee.window.set(ee.strm.input.subarray(ee.strm.next_in-ee.w_size,ee.strm.next_in),0),ee.strstart=ee.w_size,ee.insert=ee.strstart):(ee.window_size-ee.strstart<=Jn&&(ee.strstart-=ee.w_size,ee.window.set(ee.window.subarray(ee.w_size,ee.w_size+ee.strstart),0),ee.matches<2&&ee.matches++,ee.insert>ee.strstart)&&(ee.insert=ee.strstart),ee.window.set(ee.strm.input.subarray(ee.strm.next_in-Jn,ee.strm.next_in),ee.strstart),ee.strstart+=Jn,ee.insert+=Jn>ee.w_size-ee.insert?ee.w_size-ee.insert:Jn),ee.block_start=ee.strstart),ee.high_water<ee.strstart&&(ee.high_water=ee.strstart),Qn?ve:Gn!==W&&Gn!==$$4&&ee.strm.avail_in===0&&ee.strstart===ee.block_start?oe$1:(Wn=ee.window_size-ee.strstart,ee.strm.avail_in>Wn&&ee.block_start>=ee.w_size&&(ee.block_start-=ee.w_size,ee.strstart-=ee.w_size,ee.window.set(ee.window.subarray(ee.w_size,ee.w_size+ee.strstart),0),ee.matches<2&&ee.matches++,Wn+=ee.w_size,ee.insert>ee.strstart)&&(ee.insert=ee.strstart),(Wn=Wn>ee.strm.avail_in?ee.strm.avail_in:Wn)&&(wa(ee.strm,ee.window,ee.strstart,Wn),ee.strstart+=Wn,ee.insert+=Wn>ee.w_size-ee.insert?ee.w_size-ee.insert:Wn),ee.high_water<ee.strstart&&(ee.high_water=ee.strstart),Wn=ee.bi_valid+42>>3,jn=(Wn=65535<ee.pending_buf_size-Wn?65535:ee.pending_buf_size-Wn)>ee.w_size?ee.w_size:Wn,((Vn=ee.strstart-ee.block_start)>=jn||(Vn||Gn===$$4)&&Gn!==W&&ee.strm.avail_in===0&&Vn<=Wn)&&(qn=Wn<Vn?Wn:Vn,Qn=Gn===$$4&&ee.strm.avail_in===0&&qn===Vn?1:0,la$1(ee,ee.block_start,qn,Qn),ee.block_start+=qn,C$4(ee.strm)),Qn?re$1:N$3)},ba=function(ee,Gn){for(var qn,Vn;;){if(ee.lookahead<K$4){if(ue(ee),ee.lookahead<K$4&&Gn===W)return N$3;if(ee.lookahead===0)break}if(qn=0,ee.lookahead>=m$7&&(ee.ins_h=Q$2(ee,ee.ins_h,ee.window[ee.strstart+m$7-1]),qn=ee.prev[ee.strstart&ee.w_mask]=ee.head[ee.ins_h],ee.head[ee.ins_h]=ee.strstart),qn!==0&&ee.strstart-qn<=ee.w_size-K$4&&(ee.match_length=et$1(ee,qn)),ee.match_length>=m$7)if(Vn=j$1(ee,ee.strstart-ee.match_start,ee.match_length-m$7),ee.lookahead-=ee.match_length,ee.match_length<=ee.max_lazy_match&&ee.lookahead>=m$7){for(ee.match_length--;ee.strstart++,ee.ins_h=Q$2(ee,ee.ins_h,ee.window[ee.strstart+m$7-1]),qn=ee.prev[ee.strstart&ee.w_mask]=ee.head[ee.ins_h],ee.head[ee.ins_h]=ee.strstart,--ee.match_length!=0;);ee.strstart++}else ee.strstart+=ee.match_length,ee.match_length=0,ee.ins_h=ee.window[ee.strstart],ee.ins_h=Q$2(ee,ee.ins_h,ee.window[ee.strstart+1]);else Vn=j$1(ee,0,ee.window[ee.strstart]),ee.lookahead--,ee.strstart++;if(Vn&&(F$3(ee,!1),ee.strm.avail_out===0))return N$3}return ee.insert=ee.strstart<m$7-1?ee.strstart:m$7-1,Gn===$$4?(F$3(ee,!0),ee.strm.avail_out===0?re$1:ve):ee.sym_next&&(F$3(ee,!1),ee.strm.avail_out===0)?N$3:oe$1},se=function(ee,Gn){for(var qn,Vn,Wn;;){if(ee.lookahead<K$4){if(ue(ee),ee.lookahead<K$4&&Gn===W)return N$3;if(ee.lookahead===0)break}if(qn=0,ee.lookahead>=m$7&&(ee.ins_h=Q$2(ee,ee.ins_h,ee.window[ee.strstart+m$7-1]),qn=ee.prev[ee.strstart&ee.w_mask]=ee.head[ee.ins_h],ee.head[ee.ins_h]=ee.strstart),ee.prev_length=ee.match_length,ee.prev_match=ee.match_start,ee.match_length=m$7-1,qn!==0&&ee.prev_length<ee.max_lazy_match&&ee.strstart-qn<=ee.w_size-K$4&&(ee.match_length=et$1(ee,qn),ee.match_length<=5)&&(ee.strategy===Di||ee.match_length===m$7&&4096<ee.strstart-ee.match_start)&&(ee.match_length=m$7-1),ee.prev_length>=m$7&&ee.match_length<=ee.prev_length){for(Wn=ee.strstart+ee.lookahead-m$7,Vn=j$1(ee,ee.strstart-1-ee.prev_match,ee.prev_length-m$7),ee.lookahead-=ee.prev_length-1,ee.prev_length-=2;++ee.strstart<=Wn&&(ee.ins_h=Q$2(ee,ee.ins_h,ee.window[ee.strstart+m$7-1]),qn=ee.prev[ee.strstart&ee.w_mask]=ee.head[ee.ins_h],ee.head[ee.ins_h]=ee.strstart),--ee.prev_length!=0;);if(ee.match_available=0,ee.match_length=m$7-1,ee.strstart++,Vn&&(F$3(ee,!1),ee.strm.avail_out===0))return N$3}else if(ee.match_available){if((Vn=j$1(ee,0,ee.window[ee.strstart-1]))&&F$3(ee,!1),ee.strstart++,ee.lookahead--,ee.strm.avail_out===0)return N$3}else ee.match_available=1,ee.strstart++,ee.lookahead--}return ee.match_available&&(Vn=j$1(ee,0,ee.window[ee.strstart-1]),ee.match_available=0),ee.insert=ee.strstart<m$7-1?ee.strstart:m$7-1,Gn===$$4?(F$3(ee,!0),ee.strm.avail_out===0?re$1:ve):ee.sym_next&&(F$3(ee,!1),ee.strm.avail_out===0)?N$3:oe$1},Wi=function(ee,Gn){for(var qn,Vn,Wn,jn,Qn=ee.window;;){if(ee.lookahead<=V$3){if(ue(ee),ee.lookahead<=V$3&&Gn===W)return N$3;if(ee.lookahead===0)break}if(ee.match_length=0,ee.lookahead>=m$7&&0<ee.strstart&&(Vn=Qn[Wn=ee.strstart-1])===Qn[++Wn]&&Vn===Qn[++Wn]&&Vn===Qn[++Wn]){for(jn=ee.strstart+V$3;Vn===Qn[++Wn]&&Vn===Qn[++Wn]&&Vn===Qn[++Wn]&&Vn===Qn[++Wn]&&Vn===Qn[++Wn]&&Vn===Qn[++Wn]&&Vn===Qn[++Wn]&&Vn===Qn[++Wn]&&Wn<jn;);ee.match_length=V$3-(jn-Wn),ee.match_length>ee.lookahead&&(ee.match_length=ee.lookahead)}if(ee.match_length>=m$7?(qn=j$1(ee,1,ee.match_length-m$7),ee.lookahead-=ee.match_length,ee.strstart+=ee.match_length,ee.match_length=0):(qn=j$1(ee,0,ee.window[ee.strstart]),ee.lookahead--,ee.strstart++),qn&&(F$3(ee,!1),ee.strm.avail_out===0))return N$3}return ee.insert=0,Gn===$$4?(F$3(ee,!0),ee.strm.avail_out===0?re$1:ve):ee.sym_next&&(F$3(ee,!1),ee.strm.avail_out===0)?N$3:oe$1},Vi=function(ee,Gn){for(var qn;;){if(ee.lookahead===0&&(ue(ee),ee.lookahead===0)){if(Gn===W)return N$3;break}if(ee.match_length=0,qn=j$1(ee,0,ee.window[ee.strstart]),ee.lookahead--,ee.strstart++,qn&&(F$3(ee,!1),ee.strm.avail_out===0))return N$3}return ee.insert=0,Gn===$$4?(F$3(ee,!0),ee.strm.avail_out===0?re$1:ve):ee.sym_next&&(F$3(ee,!1),ee.strm.avail_out===0)?N$3:oe$1};function P$4(ee,Gn,qn,Vn,Wn){this.good_length=ee,this.max_lazy=Gn,this.nice_length=qn,this.max_chain=Vn,this.func=Wn}var ze$1=[new P$4(0,0,0,0,at),new P$4(4,4,8,4,ba),new P$4(4,5,16,8,ba),new P$4(4,6,32,32,ba),new P$4(4,4,16,16,se),new P$4(8,16,32,32,se),new P$4(8,16,128,128,se),new P$4(8,32,128,256,se),new P$4(32,128,258,1024,se),new P$4(32,258,258,4096,se)],Ji=function(ee){ee.window_size=2*ee.w_size,J(ee.head),ee.max_lazy_match=ze$1[ee.level].max_lazy,ee.good_match=ze$1[ee.level].good_length,ee.nice_match=ze$1[ee.level].nice_length,ee.max_chain_length=ze$1[ee.level].max_chain,ee.strstart=0,ee.block_start=0,ee.lookahead=0,ee.insert=0,ee.match_length=ee.prev_length=m$7-1,ee.match_available=0,ee.ins_h=0};function Qi(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Me$1,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=x$5("Uint16",2*Ki),this.dyn_dtree=x$5("Uint16",2*(2*Hi+1)),this.bl_tree=x$5("Uint16",2*(2*Bi+1)),J(this.dyn_ltree),J(this.dyn_dtree),J(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=x$5("Uint16",Pi+1),this.heap=x$5("Uint16",2*da$1+1),J(this.heap),this.heap_len=0,this.heap_max=0,this.depth=x$5("Uint16",2*da$1+1),J(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}var Re$3=function(ee){var Gn;return!ee||!(Gn=ee.state)||Gn.strm!==ee||Gn.status!==de$3&&Gn.status!==oa$1&&Gn.status!==va&&Gn.status!==ua$1&&Gn.status!==sa$1&&Gn.status!==ca$1&&Gn.status!==ie$2&&Gn.status!==Se$1?1:0},tt$2=function(ee){if(Re$3(ee))return ne$1(ee,B$1);ee.total_in=ee.total_out=0,ee.data_type=Ni;var Gn=ee.state;return Gn.pending=0,Gn.pending_out=0,Gn.wrap<0&&(Gn.wrap=-Gn.wrap),Gn.status=Gn.wrap===2?oa$1:Gn.wrap?de$3:ie$2,ee.adler=Gn.wrap===2?0:1,Gn.last_flush=-2,Si(Gn),U$9},it$1=function(ee){var Gn=tt$2(ee);return Gn===U$9&&Ji(ee.state),Gn},qi=function(ee,Gn){return Re$3(ee)||ee.state.wrap!==2?B$1:(ee.state.gzhead=Gn,U$9)},rt$1=function(ee,Gn,qn,Vn,Wn,jn){if(!ee)return B$1;var Qn=1;if(Gn===Zi&&(Gn=6),Vn<0?(Qn=0,Vn=-Vn):15<Vn&&(Qn=2,Vn-=16),Wn<1||Li<Wn||qn!==Me$1||Vn<8||15<Vn||Gn<0||9<Gn||jn<0||Oi<jn||Vn===8&&Qn!==1)return ne$1(ee,B$1);Vn===8&&(Vn=9);var Jn=new Qi;return(ee.state=Jn).strm=ee,Jn.status=de$3,Jn.wrap=Qn,Jn.gzhead=null,Jn.w_bits=Vn,Jn.w_size=1<<Jn.w_bits,Jn.w_mask=Jn.w_size-1,Jn.hash_bits=Wn+7,Jn.hash_size=1<<Jn.hash_bits,Jn.hash_mask=Jn.hash_size-1,Jn.hash_shift=~~((Jn.hash_bits+m$7-1)/m$7),Jn.window=x$5("Uint8",2*Jn.w_size),Jn.head=x$5("Uint16",Jn.hash_size),Jn.prev=x$5("Uint16",Jn.w_size),Jn.lit_bufsize=1<<Wn+6,Jn.pending_buf_size=4*Jn.lit_bufsize,Jn.pending_buf=x$5("Uint8",Jn.pending_buf_size),Jn.sym_buf=Jn.lit_bufsize,Jn.sym_end=3*(Jn.lit_bufsize-1),Jn.level=Gn,Jn.strategy=jn,Jn.method=qn,it$1(ee)},er=function(ee,Gn){return rt$1(ee,Gn,Me$1,Ci,Fi,Ui)},ar=function(ee,Gn){if(Re$3(ee)||Ja<Gn||Gn<0)return ee?ne$1(ee,B$1):B$1;var qn=ee.state;if(!ee.output||ee.avail_in!==0&&!ee.input||qn.status===Se$1&&Gn!==$$4)return ne$1(ee,ee.avail_out===0?ha:B$1);var Vn=qn.last_flush;if(qn.last_flush=Gn,qn.pending!==0){if(C$4(ee),ee.avail_out===0)return qn.last_flush=-1,U$9}else if(ee.avail_in===0&&qa(Gn)<=qa(Vn)&&Gn!==$$4)return ne$1(ee,ha);if(qn.status===Se$1&&ee.avail_in!==0)return ne$1(ee,ha);if(qn.status===de$3&&qn.wrap===0&&(qn.status=ie$2),qn.status===de$3&&(Vn=Me$1+(qn.w_bits-8<<4)<<8,Vn|=(qn.strategy>=$e$1||qn.level<2?0:qn.level<6?1:qn.level===6?2:3)<<6,qn.strstart!==0&&(Vn|=Yi),ye(qn,Vn+=31-Vn%31),qn.strstart!==0&&(ye(qn,ee.adler>>>16),ye(qn,65535&ee.adler)),ee.adler=1,qn.status=ie$2,C$4(ee),qn.pending!==0))return qn.last_flush=-1,U$9;if(qn.status===oa$1){if(ee.adler=0,R$6(qn,31),R$6(qn,139),R$6(qn,8),qn.gzhead)R$6(qn,(qn.gzhead.text?1:0)+(qn.gzhead.hcrc?2:0)+(qn.gzhead.extra?4:0)+(qn.gzhead.name?8:0)+(qn.gzhead.comment?16:0)),R$6(qn,255&qn.gzhead.time),R$6(qn,qn.gzhead.time>>8&255),R$6(qn,qn.gzhead.time>>16&255),R$6(qn,qn.gzhead.time>>24&255),R$6(qn,qn.level===9?2:qn.strategy>=$e$1||qn.level<2?4:0),R$6(qn,255&qn.gzhead.os),qn.gzhead.extra&&qn.gzhead.extra.length&&(R$6(qn,255&qn.gzhead.extra.length),R$6(qn,qn.gzhead.extra.length>>8&255)),qn.gzhead.hcrc&&(ee.adler=O$4(ee.adler,qn.pending_buf,qn.pending,0)),qn.gzindex=0,qn.status=va;else if(R$6(qn,0),R$6(qn,0),R$6(qn,0),R$6(qn,0),R$6(qn,0),R$6(qn,qn.level===9?2:qn.strategy>=$e$1||qn.level<2?4:0),R$6(qn,Xi),qn.status=ie$2,C$4(ee),qn.pending!==0)return qn.last_flush=-1,U$9}if(qn.status===va){if(qn.gzhead.extra){for(var Wn=qn.pending,jn=(65535&qn.gzhead.extra.length)-qn.gzindex;qn.pending+jn>qn.pending_buf_size;){var Qn=qn.pending_buf_size-qn.pending;if(qn.pending_buf.set(qn.gzhead.extra.subarray(qn.gzindex,qn.gzindex+Qn),qn.pending),qn.pending=qn.pending_buf_size,qn.gzhead.hcrc&&qn.pending>Wn&&(ee.adler=O$4(ee.adler,qn.pending_buf,qn.pending-Wn,Wn)),qn.gzindex+=Qn,C$4(ee),qn.pending!==0)return qn.last_flush=-1,U$9;Wn=0,jn-=Qn}Vn=x$5("Uint8",qn.gzhead.extra),qn.pending_buf.set(Vn.subarray(qn.gzindex,qn.gzindex+jn),qn.pending),qn.pending+=jn,qn.gzhead.hcrc&&qn.pending>Wn&&(ee.adler=O$4(ee.adler,qn.pending_buf,qn.pending-Wn,Wn)),qn.gzindex=0}qn.status=ua$1}if(qn.status===ua$1){if(qn.gzhead.name){Wn=qn.pending;do if(qn.pending===qn.pending_buf_size){if(qn.gzhead.hcrc&&qn.pending>Wn&&(ee.adler=O$4(ee.adler,qn.pending_buf,qn.pending-Wn,Wn)),C$4(ee),qn.pending!==0)return qn.last_flush=-1,U$9;Wn=0}while(Jn=qn.gzindex<qn.gzhead.name.length?255&qn.gzhead.name.charCodeAt(qn.gzindex++):0,R$6(qn,Jn),Jn!==0);qn.gzhead.hcrc&&qn.pending>Wn&&(ee.adler=O$4(ee.adler,qn.pending_buf,qn.pending-Wn,Wn)),qn.gzindex=0}qn.status=sa$1}if(qn.status===sa$1){if(qn.gzhead.comment){var Jn,Wn=qn.pending;do if(qn.pending===qn.pending_buf_size){if(qn.gzhead.hcrc&&qn.pending>Wn&&(ee.adler=O$4(ee.adler,qn.pending_buf,qn.pending-Wn,Wn)),C$4(ee),qn.pending!==0)return qn.last_flush=-1,U$9;Wn=0}while(Jn=qn.gzindex<qn.gzhead.comment.length?255&qn.gzhead.comment.charCodeAt(qn.gzindex++):0,R$6(qn,Jn),Jn!==0);qn.gzhead.hcrc&&qn.pending>Wn&&(ee.adler=O$4(ee.adler,qn.pending_buf,qn.pending-Wn,Wn))}qn.status=ca$1}if(qn.status===ca$1){if(qn.gzhead.hcrc){if(qn.pending+2>qn.pending_buf_size&&(C$4(ee),qn.pending!==0))return qn.last_flush=-1,U$9;R$6(qn,255&ee.adler),R$6(qn,ee.adler>>8&255),ee.adler=0}if(qn.status=ie$2,C$4(ee),qn.pending!==0)return qn.last_flush=-1,U$9}if(ee.avail_in!==0||qn.lookahead!==0||Gn!==W&&qn.status!==Se$1){if(Vn=qn.level===0?at(qn,Gn):qn.strategy===$e$1?Vi(qn,Gn):qn.strategy===Ii?Wi(qn,Gn):ze$1[qn.level].func(qn,Gn),Vn!==re$1&&Vn!==ve||(qn.status=Se$1),Vn===N$3||Vn===re$1)return ee.avail_out===0&&(qn.last_flush=-1),U$9;if(Vn===oe$1&&(Gn===Ri?zi(qn):Gn!==Ja&&(la$1(qn,0,0,!1),Gn===Ti)&&(J(qn.head),qn.lookahead===0)&&(qn.strstart=0,qn.block_start=0,qn.insert=0),C$4(ee),ee.avail_out===0))return qn.last_flush=-1,U$9}return Gn!==$$4||!(qn.wrap<=0)&&(qn.wrap===2?(R$6(qn,255&ee.adler),R$6(qn,ee.adler>>8&255),R$6(qn,ee.adler>>16&255),R$6(qn,ee.adler>>24&255),R$6(qn,255&ee.total_in),R$6(qn,ee.total_in>>8&255),R$6(qn,ee.total_in>>16&255),R$6(qn,ee.total_in>>24&255)):(ye(qn,ee.adler>>>16),ye(qn,65535&ee.adler)),C$4(ee),0<qn.wrap&&(qn.wrap=-qn.wrap),qn.pending!==0)?U$9:Qa},tr=function(ee){var Gn;return Re$3(ee)?B$1:(Gn=ee.state.status,ee.state=null,Gn===ie$2?ne$1(ee,Ai):U$9)},ir=function(ee,Gn){var qn=Gn.length;if(Re$3(ee))return B$1;var Vn=ee.state,Wn=Vn.wrap;if(Wn===2||Wn===1&&Vn.status!==de$3||Vn.lookahead)return B$1;Wn===1&&(ee.adler=me$2(ee.adler,Gn,qn,0)),Vn.wrap=0,qn>=Vn.w_size&&(Wn===0&&(J(Vn.head),Vn.strstart=0,Vn.block_start=0,Vn.insert=0),(jn=x$5("Uint8",Vn.w_size)).set(Gn.subarray(qn-Vn.w_size,qn),0),Gn=jn,qn=Vn.w_size);var jn=ee.avail_in,Qn=ee.next_in,Jn=ee.input;for(ee.avail_in=qn,ee.next_in=0,ee.input=Gn,ue(Vn);Vn.lookahead>=m$7;){for(var eo=Vn.strstart,Xn=Vn.lookahead-(m$7-1);Vn.ins_h=Q$2(Vn,Vn.ins_h,Vn.window[eo+m$7-1]),Vn.prev[eo&Vn.w_mask]=Vn.head[Vn.ins_h],Vn.head[Vn.ins_h]=eo,eo++,--Xn;);Vn.strstart=eo,Vn.lookahead=m$7-1,ue(Vn)}return Vn.strstart+=Vn.lookahead,Vn.block_start=Vn.strstart,Vn.insert=Vn.lookahead,Vn.lookahead=0,Vn.match_length=Vn.prev_length=m$7-1,Vn.match_available=0,ee.next_in=Qn,ee.input=Jn,ee.avail_in=jn,Vn.wrap=Wn,U$9},rr=er,nr=rt$1,fr$1=it$1,_r$1=tt$2,lr$1=qi,hr$1=ar,dr$1=tr,or=ir,vr$1="pako deflate (from Nodeca project)",Te$2={deflateInit:rr,deflateInit2:nr,deflateReset:fr$1,deflateResetKeep:_r$1,deflateSetHeader:lr$1,deflate:hr$1,deflateEnd:dr$1,deflateSetDictionary:or,deflateInfo:vr$1},ur$1=function(ee,Gn){return Object.prototype.hasOwnProperty.call(ee,Gn)},sr$1=function(ee){for(var Gn=Array.prototype.slice.call(arguments,1);Gn.length;){var qn=Gn.shift();if(qn){if(typeof qn!="object")throw new TypeError(qn+"must be non-object");for(var Vn in qn)ur$1(qn,Vn)&&(ee[Vn]=qn[Vn])}}return ee},cr$1=function(ee){for(var Gn=0,qn=0,Vn=ee.length;qn<Vn;qn++)Gn+=ee[qn].length;for(var Wn=x$5("Uint8",Gn),jn=qn=0,Vn=ee.length;qn<Vn;qn++){var Qn=ee[qn];Wn.set(Qn,jn),jn+=Qn.length}return Wn},He$2={assign:sr$1,flattenChunks:cr$1},nt=!0;try{aa$1?String.fromCharCode.apply(null,new Uint8Array(1)):String.fromCharCode.apply(null,[0])}catch(ee){nt=!1}for(var Ae$2=x$5("Uint8",256),q$3=0;q$3<256;q$3++)Ae$2[q$3]=252<=q$3?6:248<=q$3?5:240<=q$3?4:224<=q$3?3:192<=q$3?2:1;Ae$2[254]=Ae$2[254]=1;var wr$1=function(ee){if(typeof TextEncoder=="function"&&TextEncoder.prototype.encode)return new TextEncoder().encode(ee);for(var Gn,qn,Vn,Wn,jn=ee.length,Qn=0,Jn=0;Jn<jn;Jn++)(64512&(qn=ee.charCodeAt(Jn)))==55296&&Jn+1<jn&&(64512&(Vn=ee.charCodeAt(Jn+1)))==56320&&(qn=65536+(qn-55296<<10)+(Vn-56320),Jn++),Qn+=qn<128?1:qn<2048?2:qn<65536?3:4;for(Gn=x$5("Uint8",Qn),Jn=Wn=0;Wn<Qn;Jn++)(64512&(qn=ee.charCodeAt(Jn)))==55296&&Jn+1<jn&&(64512&(Vn=ee.charCodeAt(Jn+1)))==56320&&(qn=65536+(qn-55296<<10)+(Vn-56320),Jn++),qn<128?Gn[Wn++]=qn:(qn<2048?Gn[Wn++]=192|qn>>>6:(qn<65536?Gn[Wn++]=224|qn>>>12:(Gn[Wn++]=240|qn>>>18,Gn[Wn++]=128|qn>>>12&63),Gn[Wn++]=128|qn>>>6&63),Gn[Wn++]=128|63&qn);return Gn},br$1=function(ee,Gn){if(Gn<65534&&ee.subarray&&nt)return String.fromCharCode.apply(null,ee.length===Gn?ee:ee.subarray(0,Gn));for(var qn="",Vn=0;Vn<Gn;Vn++)qn+=String.fromCharCode(ee[Vn]);return qn},gr$1=function(ee,Gn){var qn=Gn||ee.length;if(typeof TextDecoder=="function"&&TextDecoder.prototype.decode)return new TextDecoder().decode(ee.subarray(0,Gn));for(var Vn=new Array(2*qn),Wn=0,jn=0;jn<qn;){var Qn=ee[jn++];if(Qn<128)Vn[Wn++]=Qn;else{var Jn=Ae$2[Qn];if(4<Jn)Vn[Wn++]=65533,jn+=Jn-1;else{for(Qn&=Jn===2?31:Jn===3?15:7;1<Jn&&jn<qn;)Qn=Qn<<6|63&ee[jn++],Jn--;1<Jn?Vn[Wn++]=65533:Qn<65536?Vn[Wn++]=Qn:(Qn-=65536,Vn[Wn++]=55296|Qn>>10&1023,Vn[Wn++]=56320|1023&Qn)}}}return br$1(Vn,Wn)},pr$1=function(ee,Gn){for(var qn=(Gn=(Gn=Gn||ee.length)>ee.length?ee.length:Gn)-1;0<=qn&&(192&ee[qn])==128;)qn--;return!(qn<0||qn===0)&&qn+Ae$2[ee[qn]]>Gn?qn:Gn},Ze$1={string2buf:wr$1,buf2string:gr$1,utf8border:pr$1};function xr$1(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}var ft$1=xr$1,_t$1=Object.prototype.toString,kr$1=w$8.Z_NO_FLUSH,Er$1=w$8.Z_SYNC_FLUSH,mr$1=w$8.Z_FULL_FLUSH,Sr$1=w$8.Z_FINISH,Be=w$8.Z_OK,yr$1=w$8.Z_STREAM_END,zr$1=w$8.Z_DEFAULT_COMPRESSION,Rr$1=w$8.Z_DEFAULT_STRATEGY,Tr$1=w$8.Z_DEFLATED;function De$2(Gn){this.options=He$2.assign({level:zr$1,method:Tr$1,chunkSize:16384,windowBits:15,memLevel:8,strategy:Rr$1},Gn||{});var Gn=this.options,qn=(Gn.raw&&0<Gn.windowBits?Gn.windowBits=-Gn.windowBits:Gn.gzip&&0<Gn.windowBits&&Gn.windowBits<16&&(Gn.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new ft$1,this.strm.avail_out=0,Te$2.deflateInit2(this.strm,Gn.level,Gn.method,Gn.windowBits,Gn.memLevel,Gn.strategy));if(qn!==Be)throw new Error(te$1[qn]);if(Gn.header&&Te$2.deflateSetHeader(this.strm,Gn.header),Gn.dictionary){if(Gn=typeof Gn.dictionary=="string"?Ze$1.string2buf(Gn.dictionary):_t$1.call(Gn.dictionary)==="[object ArrayBuffer]"?x$5("Uint8",Gn.dictionary):Gn.dictionary,(qn=Te$2.deflateSetDictionary(this.strm,Gn))!==Be)throw new Error(te$1[qn]);this._dict_set=!0}}function ga(ee,Gn){if(Gn=new De$2(Gn),Gn.push(ee,!0),Gn.err)throw Gn.msg||te$1[Gn.err];return Gn.result}function Ar$1(ee,Gn){return(Gn=Gn||{}).raw=!0,ga(ee,Gn)}function Zr$1(ee,Gn){return(Gn=Gn||{}).gzip=!0,ga(ee,Gn)}De$2.prototype.push=function(ee,Gn){var qn,Vn,Wn=this.strm,jn=this.options.chunkSize;if(this.ended)return!1;for(Vn=Gn===~~Gn?Gn:Gn===!0?Sr$1:kr$1,typeof ee=="string"?Wn.input=Ze$1.string2buf(ee):_t$1.call(ee)==="[object ArrayBuffer]"?Wn.input=x$5("Uint8",ee):Wn.input=ee,Wn.next_in=0,Wn.avail_in=Wn.input.length;;)if(Wn.avail_out===0&&(Wn.output=x$5("Uint8",jn),Wn.next_out=0,Wn.avail_out=jn),(Vn===Er$1||Vn===mr$1)&&Wn.avail_out<=6)this.onData(Wn.output.subarray(0,Wn.next_out)),Wn.avail_out=0;else{if(Te$2.deflate(Wn,Vn)===yr$1)return 0<Wn.next_out&&this.onData(Wn.output.subarray(0,Wn.next_out)),qn=Te$2.deflateEnd(this.strm),this.onEnd(qn),this.ended=!0,qn===Be;if(Wn.avail_out!==0){if(0<Vn&&0<Wn.next_out)this.onData(Wn.output.subarray(0,Wn.next_out)),Wn.avail_out=0;else if(Wn.avail_in===0)break}else this.onData(Wn.output)}return!0},De$2.prototype.onData=function(ee){this.chunks.push(ee)},De$2.prototype.onEnd=function(ee){ee===Be&&(this.result=He$2.flattenChunks(this.chunks)),this.chunks=[],this.err=ee,this.msg=this.strm.msg};var Dr$1=De$2,Ir$1=ga,Or$1=Ar$1,Ur$1=Zr$1,Nr$1=w$8,Ke$1={Deflate:Dr$1,deflate:Ir$1,deflateRaw:Or$1,gzip:Ur$1,constants:Nr$1},Pe$1=16209,Lr$1=16191,Cr$1=function(ee,Gn){var qn,Vn,Wn,jn,Qn,Jn,eo=ee.state,Xn=ee.next_in,to=ee.input,io=Xn+(ee.avail_in-5),ro=ee.next_out,no=ee.output,oo=ro-(Gn-ee.avail_out),ao=ro+(ee.avail_out-257),so=eo.dmax,co=eo.wsize,uo=eo.whave,fo=eo.wnext,wo=eo.window,lo=eo.hold,po=eo.bits,go=eo.lencode,ho=eo.distcode,mo=(1<<eo.lenbits)-1,$o=(1<<eo.distbits)-1;e:do for(po<15&&(lo+=to[Xn++]<<po,po+=8,lo+=to[Xn++]<<po,po+=8),qn=go[lo&mo];;){if(lo>>>=Vn=qn>>>24,po-=Vn,(Vn=qn>>>16&255)==0)no[ro++]=65535&qn;else{if(!(16&Vn)){if(64&Vn){if(32&Vn){eo.mode=Lr$1;break e}ee.msg="invalid literal/length code",eo.mode=Pe$1;break e}qn=go[(65535&qn)+(lo&(1<<Vn)-1)];continue}for(Wn=65535&qn,(Vn&=15)&&(po<Vn&&(lo+=to[Xn++]<<po,po+=8),Wn+=lo&(1<<Vn)-1,lo>>>=Vn,po-=Vn),po<15&&(lo+=to[Xn++]<<po,po+=8,lo+=to[Xn++]<<po,po+=8),qn=ho[lo&$o];;){if(lo>>>=Vn=qn>>>24,po-=Vn,16&(Vn=qn>>>16&255)){if(jn=65535&qn,po<(Vn&=15)&&(lo+=to[Xn++]<<po,(po+=8)<Vn)&&(lo+=to[Xn++]<<po,po+=8),(jn+=lo&(1<<Vn)-1)>so){ee.msg="invalid distance too far back",eo.mode=Pe$1;break e}if(lo>>>=Vn,po-=Vn,jn>(Vn=ro-oo)){if((Vn=jn-Vn)>uo&&eo.sane){ee.msg="invalid distance too far back",eo.mode=Pe$1;break e}if(Jn=wo,(Qn=0)===fo){if(Qn+=co-Vn,Vn<Wn){for(Wn-=Vn;no[ro++]=wo[Qn++],--Vn;);Qn=ro-jn,Jn=no}}else if(fo<Vn){if(Qn+=co+fo-Vn,(Vn-=fo)<Wn){for(Wn-=Vn;no[ro++]=wo[Qn++],--Vn;);if(Qn=0,fo<Wn){for(Wn-=Vn=fo;no[ro++]=wo[Qn++],--Vn;);Qn=ro-jn,Jn=no}}}else if(Qn+=fo-Vn,Vn<Wn){for(Wn-=Vn;no[ro++]=wo[Qn++],--Vn;);Qn=ro-jn,Jn=no}for(;2<Wn;)no[ro++]=Jn[Qn++],no[ro++]=Jn[Qn++],no[ro++]=Jn[Qn++],Wn-=3;Wn&&(no[ro++]=Jn[Qn++],1<Wn)&&(no[ro++]=Jn[Qn++])}else{for(Qn=ro-jn;no[ro++]=no[Qn++],no[ro++]=no[Qn++],no[ro++]=no[Qn++],2<(Wn-=3););Wn&&(no[ro++]=no[Qn++],1<Wn)&&(no[ro++]=no[Qn++])}break}if(64&Vn){ee.msg="invalid distance code",eo.mode=Pe$1;break e}qn=ho[(65535&qn)+(lo&(1<<Vn)-1)]}}break}while(Xn<io&&ro<ao);Xn-=Wn=po>>3,lo&=(1<<(po-=Wn<<3))-1,ee.next_in=Xn,ee.next_out=ro,ee.avail_in=Xn<io?io-Xn+5:5-(Xn-io),ee.avail_out=ro<ao?ao-ro+257:257-(ro-ao),eo.hold=lo,eo.bits=po},ce$1=15,lt$1=852,ht$1=592,dt$1=0,pa=1,ot$1=2,Fr$1=x$5("Uint16",[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),$r$1=x$5("Uint8",[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),Mr$1=x$5("Uint16",[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),Hr$1=x$5("Uint8",[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),Br$1=function(ee,Gn,qn,Vn,Wn,jn,Qn,Jn){for(var eo,Xn,to,io,ro,no,oo,ao,so,co=Jn.bits,Eo=0,uo=0,fo=0,wo=0,lo=0,po=0,go=0,ho=0,mo=0,$o=0,vo=null,To=x$5("Uint16",ce$1+1),So=x$5("Uint16",ce$1+1),_o=null,Eo=0;Eo<=ce$1;Eo++)To[Eo]=0;for(uo=0;uo<Vn;uo++)To[Gn[qn+uo]]++;for(lo=co,wo=ce$1;1<=wo&&To[wo]===0;wo--);if(wo<lo&&(lo=wo),wo===0)Wn[jn++]=20971520,Wn[jn++]=20971520,Jn.bits=1;else{for(fo=1;fo<wo&&To[fo]===0;fo++);for(lo<fo&&(lo=fo),Eo=ho=1;Eo<=ce$1;Eo++)if((ho=(ho<<1)-To[Eo])<0)return-1;if(0<ho&&(ee===dt$1||wo!==1))return-1;for(So[1]=0,Eo=1;Eo<ce$1;Eo++)So[Eo+1]=So[Eo]+To[Eo];for(uo=0;uo<Vn;uo++)Gn[qn+uo]!==0&&(Qn[So[Gn[qn+uo]]++]=uo);if(no=ee===dt$1?(vo=_o=Qn,20):ee===pa?(vo=Fr$1,_o=$r$1,257):(vo=Mr$1,_o=Hr$1,0),Eo=fo,ro=jn,go=uo=$o=0,to=-1,io=(mo=1<<(po=lo))-1,ee===pa&&lt$1<mo||ee===ot$1&&ht$1<mo)return 1;for(;;){for(so=Qn[uo]+1<no?(ao=0,Qn[uo]):Qn[uo]>=no?(ao=_o[Qn[uo]-no],vo[Qn[uo]-no]):(ao=96,0),eo=1<<(oo=Eo-go),fo=Xn=1<<po;Wn[ro+($o>>go)+(Xn-=eo)]=oo<<24|ao<<16|so|0,Xn!==0;);for(eo=1<<Eo-1;$o&eo;)eo>>=1;if(eo!==0?$o=($o&eo-1)+eo:$o=0,uo++,--To[Eo]==0){if(Eo===wo)break;Eo=Gn[qn+Qn[uo]]}if(lo<Eo&&($o&io)!==to){for(ro+=fo,ho=1<<(po=Eo-(go=go===0?lo:go));po+go<wo&&!((ho-=To[po+go])<=0);)po++,ho<<=1;if(mo+=1<<po,ee===pa&&lt$1<mo||ee===ot$1&&ht$1<mo)return 1;Wn[to=$o&io]=lo<<24|po<<16|ro-jn|0}}$o!==0&&(Wn[ro+$o]=Eo-go<<24|64<<16|0),Jn.bits=lo}return 0},Ie$1=Br$1,Kr$1=0,vt$1=1,ut$1=2,st$1=w$8.Z_FINISH,Pr$1=w$8.Z_BLOCK,Ye$1=w$8.Z_TREES,fe$1=w$8.Z_OK,Yr$1=w$8.Z_STREAM_END,Xr$1=w$8.Z_NEED_DICT,M$3=w$8.Z_STREAM_ERROR,ct$1=w$8.Z_DATA_ERROR,wt$1=w$8.Z_MEM_ERROR,Gr$1=w$8.Z_BUF_ERROR,bt$1=w$8.Z_DEFLATED,Xe$1=16180,gt=16181,pt=16182,xt$1=16183,kt$1=16184,Et$1=16185,mt$1=16186,St$1=16187,yt$1=16188,zt$1=16189,Ge$1=16190,X$3=16191,xa=16192,Rt$1=16193,ka=16194,Tt$1=16195,At$1=16196,Zt$1=16197,Dt$1=16198,je$1=16199,We=16200,It$1=16201,Ot$1=16202,Ut$1=16203,Nt$1=16204,Lt$1=16205,Ea=16206,Ct$1=16207,Ft$1=16208,Z$2=16209,$t=16210,Mt$1=16211,jr$1=852,Wr$1=592,Vr$1=15,Jr$1=Vr$1,Ht$1=function(ee){return(ee>>>24&255)+(ee>>>8&65280)+((65280&ee)<<8)+((255&ee)<<24)};function Qr$1(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=x$5("Uint16",320),this.work=x$5("Uint16",288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}var _e$2=function(ee){var Gn;return!ee||!(Gn=ee.state)||Gn.strm!==ee||Gn.mode<Xe$1||Gn.mode>Mt$1?1:0},Bt$1=function(ee){var Gn;return _e$2(ee)?M$3:(Gn=ee.state,ee.total_in=ee.total_out=Gn.total=0,ee.msg="",Gn.wrap&&(ee.adler=1&Gn.wrap),Gn.mode=Xe$1,Gn.last=0,Gn.havedict=0,Gn.flags=-1,Gn.dmax=32768,Gn.head=null,Gn.hold=0,Gn.bits=0,Gn.lencode=Gn.lendyn=new Int32Array(jr$1),Gn.distcode=Gn.distdyn=new Int32Array(Wr$1),Gn.sane=1,Gn.back=-1,fe$1)},Kt$1=function(ee){var Gn;return _e$2(ee)?M$3:((Gn=ee.state).wsize=0,Gn.whave=0,Gn.wnext=0,Bt$1(ee))},Pt$1=function(ee,Gn){var qn,Vn;return _e$2(ee)||(Vn=ee.state,Gn<0?(qn=0,Gn=-Gn):(qn=5+(Gn>>4),Gn<48&&(Gn&=15)),Gn&&(Gn<8||15<Gn))?M$3:(Vn.window!==null&&Vn.wbits!==Gn&&(Vn.window=null),Vn.wrap=qn,Vn.wbits=Gn,Kt$1(ee))},Yt$1=function(ee,Gn){var qn;return ee?(qn=new Qr$1,(ee.state=qn).strm=ee,qn.window=null,qn.mode=Xe$1,(qn=Pt$1(ee,Gn))!==fe$1&&(ee.state=null),qn):M$3},qr$1=function(ee){return Yt$1(ee,Jr$1)},Xt$1=!0,ma,Sa,en=function(ee){if(Xt$1){ma=new Int32Array(512),Sa=new Int32Array(32);for(var Gn=0;Gn<144;)ee.lens[Gn++]=8;for(;Gn<256;)ee.lens[Gn++]=9;for(;Gn<280;)ee.lens[Gn++]=7;for(;Gn<288;)ee.lens[Gn++]=8;for(Ie$1(vt$1,ee.lens,0,288,ma,0,ee.work,{bits:9}),Gn=0;Gn<32;)ee.lens[Gn++]=5;Ie$1(ut$1,ee.lens,0,32,Sa,0,ee.work,{bits:5}),Xt$1=!1}ee.lencode=ma,ee.lenbits=9,ee.distcode=Sa,ee.distbits=5},Gt$1=function(jn,Gn,qn,Vn){var Wn,jn=jn.state;return jn.window===null&&(jn.wsize=1<<jn.wbits,jn.wnext=0,jn.whave=0,jn.window=x$5("Uint8",jn.wsize)),Vn>=jn.wsize?(jn.window.set(Gn.subarray(qn-jn.wsize,qn),0),jn.wnext=0,jn.whave=jn.wsize):((Wn=jn.wsize-jn.wnext)>Vn&&(Wn=Vn),jn.window.set(Gn.subarray(qn-Vn,qn-Vn+Wn),jn.wnext),(Vn-=Wn)?(jn.window.set(Gn.subarray(qn-Vn,qn),0),jn.wnext=Vn,jn.whave=jn.wsize):(jn.wnext+=Wn,jn.wnext===jn.wsize&&(jn.wnext=0),jn.whave<jn.wsize&&(jn.whave+=Wn))),0},an=function(ee,Gn){var qn,Vn,Wn,jn,Qn,Jn,eo,Xn,to,io,ro,no,oo,ao,so,co,uo,fo,wo,lo,po,go,ho,mo,$o=0,vo=x$5("Uint8",4),To=x$5("Uint8",[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(_e$2(ee)||!ee.output||!ee.input&&ee.avail_in!==0)return M$3;(qn=ee.state).mode===X$3&&(qn.mode=xa),Qn=ee.next_out,Wn=ee.output,eo=ee.avail_out,jn=ee.next_in,Vn=ee.input,Jn=ee.avail_in,Xn=qn.hold,to=qn.bits,io=Jn,ro=eo,go=fe$1;e:for(;;)switch(qn.mode){case Xe$1:if(qn.wrap===0)qn.mode=xa;else{for(;to<16;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}2&qn.wrap&&Xn===35615?(qn.wbits===0&&(qn.wbits=15),vo[qn.check=0]=255&Xn,vo[1]=Xn>>>8&255,qn.check=O$4(qn.check,vo,2,0),to=Xn=0,qn.mode=gt):(qn.head&&(qn.head.done=!1),!(1&qn.wrap)||(((255&Xn)<<8)+(Xn>>8))%31?(ee.msg="incorrect header check",qn.mode=Z$2):(15&Xn)!==bt$1?(ee.msg="unknown compression method",qn.mode=Z$2):(to-=4,po=8+(15&(Xn>>>=4)),qn.wbits===0&&(qn.wbits=po),15<po||po>qn.wbits?(ee.msg="invalid window size",qn.mode=Z$2):(qn.dmax=1<<qn.wbits,qn.flags=0,ee.adler=qn.check=1,qn.mode=512&Xn?zt$1:X$3,to=Xn=0)))}break;case gt:for(;to<16;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(qn.flags=Xn,(255&qn.flags)!==bt$1){ee.msg="unknown compression method",qn.mode=Z$2;break}if(57344&qn.flags){ee.msg="unknown header flags set",qn.mode=Z$2;break}qn.head&&(qn.head.text=Xn>>8&1),512&qn.flags&&4&qn.wrap&&(vo[0]=255&Xn,vo[1]=Xn>>>8&255,qn.check=O$4(qn.check,vo,2,0)),to=Xn=0,qn.mode=pt;case pt:for(;to<32;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}qn.head&&(qn.head.time=Xn),512&qn.flags&&4&qn.wrap&&(vo[0]=255&Xn,vo[1]=Xn>>>8&255,vo[2]=Xn>>>16&255,vo[3]=Xn>>>24&255,qn.check=O$4(qn.check,vo,4,0)),to=Xn=0,qn.mode=xt$1;case xt$1:for(;to<16;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}qn.head&&(qn.head.xflags=255&Xn,qn.head.os=Xn>>8),512&qn.flags&&4&qn.wrap&&(vo[0]=255&Xn,vo[1]=Xn>>>8&255,qn.check=O$4(qn.check,vo,2,0)),to=Xn=0,qn.mode=kt$1;case kt$1:if(1024&qn.flags){for(;to<16;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}qn.length=Xn,qn.head&&(qn.head.extra_len=Xn),512&qn.flags&&4&qn.wrap&&(vo[0]=255&Xn,vo[1]=Xn>>>8&255,qn.check=O$4(qn.check,vo,2,0)),to=Xn=0}else qn.head&&(qn.head.extra=null);qn.mode=Et$1;case Et$1:if(1024&qn.flags&&((no=(no=qn.length)>Jn?Jn:no)&&(qn.head&&(po=qn.head.extra_len-qn.length,qn.head.extra||(qn.head.extra=x$5("Uint8",qn.head.extra_len)),qn.head.extra.set(Vn.subarray(jn,jn+no),po)),512&qn.flags&&4&qn.wrap&&(qn.check=O$4(qn.check,Vn,no,jn)),Jn-=no,jn+=no,qn.length-=no),qn.length))break e;qn.length=0,qn.mode=mt$1;case mt$1:if(2048&qn.flags){if(Jn===0)break e;for(no=0;po=Vn[jn+no++],qn.head&&po&&qn.length<65536&&(qn.head.name+=String.fromCharCode(po)),po&&no<Jn;);if(512&qn.flags&&4&qn.wrap&&(qn.check=O$4(qn.check,Vn,no,jn)),Jn-=no,jn+=no,po)break e}else qn.head&&(qn.head.name=null);qn.length=0,qn.mode=St$1;case St$1:if(4096&qn.flags){if(Jn===0)break e;for(no=0;po=Vn[jn+no++],qn.head&&po&&qn.length<65536&&(qn.head.comment+=String.fromCharCode(po)),po&&no<Jn;);if(512&qn.flags&&4&qn.wrap&&(qn.check=O$4(qn.check,Vn,no,jn)),Jn-=no,jn+=no,po)break e}else qn.head&&(qn.head.comment=null);qn.mode=yt$1;case yt$1:if(512&qn.flags){for(;to<16;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(4&qn.wrap&&Xn!==(65535&qn.check)){ee.msg="header crc mismatch",qn.mode=Z$2;break}to=Xn=0}qn.head&&(qn.head.hcrc=qn.flags>>9&1,qn.head.done=!0),ee.adler=qn.check=0,qn.mode=X$3;break;case zt$1:for(;to<32;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}ee.adler=qn.check=Ht$1(Xn),to=Xn=0,qn.mode=Ge$1;case Ge$1:if(qn.havedict===0)return ee.next_out=Qn,ee.avail_out=eo,ee.next_in=jn,ee.avail_in=Jn,qn.hold=Xn,qn.bits=to,Xr$1;ee.adler=qn.check=1,qn.mode=X$3;case X$3:if(Gn===Pr$1||Gn===Ye$1)break e;case xa:if(qn.last)Xn>>>=7&to,to-=7&to,qn.mode=Ea;else{for(;to<3;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}switch(qn.last=1&Xn,--to,3&(Xn>>>=1)){case 0:qn.mode=Rt$1;break;case 1:if(en(qn),qn.mode=je$1,Gn!==Ye$1)break;Xn>>>=2,to-=2;break e;case 2:qn.mode=At$1;break;case 3:ee.msg="invalid block type",qn.mode=Z$2}Xn>>>=2,to-=2}break;case Rt$1:for(Xn>>>=7&to,to-=7&to;to<32;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if((65535&Xn)!=(Xn>>>16^65535)){ee.msg="invalid stored block lengths",qn.mode=Z$2;break}if(qn.length=65535&Xn,to=Xn=0,qn.mode=ka,Gn===Ye$1)break e;case ka:qn.mode=Tt$1;case Tt$1:if(no=qn.length){if((no=eo<(no=Jn<no?Jn:no)?eo:no)===0)break e;Wn.set(Vn.subarray(jn,jn+no),Qn),Jn-=no,jn+=no,eo-=no,Qn+=no,qn.length-=no}else qn.mode=X$3;break;case At$1:for(;to<14;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(qn.nlen=257+(31&Xn),Xn>>>=5,to-=5,qn.ndist=1+(31&Xn),Xn>>>=5,to-=5,qn.ncode=4+(15&Xn),Xn>>>=4,to-=4,286<qn.nlen||30<qn.ndist){ee.msg="too many length or distance symbols",qn.mode=Z$2;break}qn.have=0,qn.mode=Zt$1;case Zt$1:for(;qn.have<qn.ncode;){for(;to<3;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}qn.lens[To[qn.have++]]=7&Xn,Xn>>>=3,to-=3}for(;qn.have<19;)qn.lens[To[qn.have++]]=0;if(qn.lencode=qn.lendyn,qn.lenbits=7,ho={bits:qn.lenbits},go=Ie$1(Kr$1,qn.lens,0,19,qn.lencode,0,qn.work,ho),qn.lenbits=ho.bits,go){ee.msg="invalid code lengths set",qn.mode=Z$2;break}qn.have=0,qn.mode=Dt$1;case Dt$1:for(;qn.have<qn.nlen+qn.ndist;){for(;co=($o=qn.lencode[Xn&(1<<qn.lenbits)-1])>>>16&255,uo=65535&$o,!((so=$o>>>24)<=to);){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(uo<16)Xn>>>=so,to-=so,qn.lens[qn.have++]=uo;else{if(uo===16){for(mo=so+2;to<mo;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(Xn>>>=so,to-=so,qn.have===0){ee.msg="invalid bit length repeat",qn.mode=Z$2;break}po=qn.lens[qn.have-1],no=3+(3&Xn),Xn>>>=2,to-=2}else if(uo===17){for(mo=so+3;to<mo;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}po=0,no=3+(7&(Xn>>>=so)),Xn>>>=3,to=to-so-3}else{for(mo=so+7;to<mo;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}po=0,no=11+(127&(Xn>>>=so)),Xn>>>=7,to=to-so-7}if(qn.have+no>qn.nlen+qn.ndist){ee.msg="invalid bit length repeat",qn.mode=Z$2;break}for(;no--;)qn.lens[qn.have++]=po}}if(qn.mode===Z$2)break;if(qn.lens[256]===0){ee.msg="invalid code -- missing end-of-block",qn.mode=Z$2;break}if(qn.lenbits=9,ho={bits:qn.lenbits},go=Ie$1(vt$1,qn.lens,0,qn.nlen,qn.lencode,0,qn.work,ho),qn.lenbits=ho.bits,go){ee.msg="invalid literal/lengths set",qn.mode=Z$2;break}if(qn.distbits=6,qn.distcode=qn.distdyn,ho={bits:qn.distbits},go=Ie$1(ut$1,qn.lens,qn.nlen,qn.ndist,qn.distcode,0,qn.work,ho),qn.distbits=ho.bits,go){ee.msg="invalid distances set",qn.mode=Z$2;break}if(qn.mode=je$1,Gn===Ye$1)break e;case je$1:qn.mode=We;case We:if(6<=Jn&&258<=eo){ee.next_out=Qn,ee.avail_out=eo,ee.next_in=jn,ee.avail_in=Jn,qn.hold=Xn,qn.bits=to,Cr$1(ee,ro),Qn=ee.next_out,Wn=ee.output,eo=ee.avail_out,jn=ee.next_in,Vn=ee.input,Jn=ee.avail_in,Xn=qn.hold,to=qn.bits,qn.mode===X$3&&(qn.back=-1);break}for(qn.back=0;co=($o=qn.lencode[Xn&(1<<qn.lenbits)-1])>>>16&255,uo=65535&$o,!((so=$o>>>24)<=to);){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(co&&!(240&co)){for(fo=so,wo=co,lo=uo;co=($o=qn.lencode[lo+((Xn&(1<<fo+wo)-1)>>fo)])>>>16&255,uo=65535&$o,!(fo+(so=$o>>>24)<=to);){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}Xn>>>=fo,to-=fo,qn.back+=fo}if(Xn>>>=so,to-=so,qn.back+=so,qn.length=uo,co===0){qn.mode=Lt$1;break}if(32&co){qn.back=-1,qn.mode=X$3;break}if(64&co){ee.msg="invalid literal/length code",qn.mode=Z$2;break}qn.extra=15&co,qn.mode=It$1;case It$1:if(qn.extra){for(mo=qn.extra;to<mo;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}qn.length+=Xn&(1<<qn.extra)-1,Xn>>>=qn.extra,to-=qn.extra,qn.back+=qn.extra}qn.was=qn.length,qn.mode=Ot$1;case Ot$1:for(;co=($o=qn.distcode[Xn&(1<<qn.distbits)-1])>>>16&255,uo=65535&$o,!((so=$o>>>24)<=to);){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(!(240&co)){for(fo=so,wo=co,lo=uo;co=($o=qn.distcode[lo+((Xn&(1<<fo+wo)-1)>>fo)])>>>16&255,uo=65535&$o,!(fo+(so=$o>>>24)<=to);){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}Xn>>>=fo,to-=fo,qn.back+=fo}if(Xn>>>=so,to-=so,qn.back+=so,64&co){ee.msg="invalid distance code",qn.mode=Z$2;break}qn.offset=uo,qn.extra=15&co,qn.mode=Ut$1;case Ut$1:if(qn.extra){for(mo=qn.extra;to<mo;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}qn.offset+=Xn&(1<<qn.extra)-1,Xn>>>=qn.extra,to-=qn.extra,qn.back+=qn.extra}if(qn.offset>qn.dmax){ee.msg="invalid distance too far back",qn.mode=Z$2;break}qn.mode=Nt$1;case Nt$1:if(eo===0)break e;if(qn.offset>(no=ro-eo)){if((no=qn.offset-no)>qn.whave&&qn.sane){ee.msg="invalid distance too far back",qn.mode=Z$2;break}oo=no>qn.wnext?(no-=qn.wnext,qn.wsize-no):qn.wnext-no,no>qn.length&&(no=qn.length),ao=qn.window}else ao=Wn,oo=Qn-qn.offset,no=qn.length;for(eo-=no=eo<no?eo:no,qn.length-=no;Wn[Qn++]=ao[oo++],--no;);qn.length===0&&(qn.mode=We);break;case Lt$1:if(eo===0)break e;Wn[Qn++]=qn.length,eo--,qn.mode=We;break;case Ea:if(qn.wrap){for(;to<32;){if(Jn===0)break e;Jn--,Xn|=Vn[jn++]<<to,to+=8}if(ro-=eo,ee.total_out+=ro,qn.total+=ro,4&qn.wrap&&ro&&(ee.adler=qn.check=(qn.flags?O$4:me$2)(qn.check,Wn,ro,Qn-ro)),ro=eo,4&qn.wrap&&(qn.flags?Xn:Ht$1(Xn))!==qn.check){ee.msg="incorrect data check",qn.mode=Z$2;break}to=Xn=0}qn.mode=Ct$1;case Ct$1:if(qn.wrap&&qn.flags){for(;to<32;){if(Jn===0)break e;Jn--,Xn+=Vn[jn++]<<to,to+=8}if(4&qn.wrap&&Xn!==(4294967295&qn.total)){ee.msg="incorrect length check",qn.mode=Z$2;break}to=Xn=0}qn.mode=Ft$1;case Ft$1:go=Yr$1;break e;case Z$2:go=ct$1;break e;case $t:return wt$1;default:return M$3}return ee.next_out=Qn,ee.avail_out=eo,ee.next_in=jn,ee.avail_in=Jn,qn.hold=Xn,qn.bits=to,(qn.wsize||ro!==ee.avail_out&&qn.mode<Z$2&&(qn.mode<Ea||Gn!==st$1))&&Gt$1(ee,ee.output,ee.next_out,ro-ee.avail_out),io-=ee.avail_in,ro-=ee.avail_out,ee.total_in+=io,ee.total_out+=ro,qn.total+=ro,4&qn.wrap&&ro&&(ee.adler=qn.check=(qn.flags?O$4:me$2)(qn.check,Wn,ro,ee.next_out-ro)),ee.data_type=qn.bits+(qn.last?64:0)+(qn.mode===X$3?128:0)+(qn.mode===je$1||qn.mode===ka?256:0),go=(io==0&&ro===0||Gn===st$1)&&go===fe$1?Gr$1:go},tn=function(ee){var Gn;return _e$2(ee)?M$3:((Gn=ee.state).window&&(Gn.window=null),ee.state=null,fe$1)},rn=function(ee,Gn){return!_e$2(ee)&&2&(ee=ee.state).wrap?((ee.head=Gn).done=!1,fe$1):M$3},nn=function(ee,Gn){var qn,Vn=Gn.length;return _e$2(ee)||(qn=ee.state).wrap!==0&&qn.mode!==Ge$1?M$3:qn.mode===Ge$1&&me$2(1,Gn,Vn,0)!==qn.check?ct$1:Gt$1(ee,Gn,Vn,Vn)?(qn.mode=$t,wt$1):(qn.havedict=1,fe$1)},fn=Kt$1,_n=Pt$1,ln=Bt$1,hn=qr$1,dn=Yt$1,on=an,vn=tn,un=rn,sn=nn,cn="pako inflate (from Nodeca project)",G$3={inflateReset:fn,inflateReset2:_n,inflateResetKeep:ln,inflateInit:hn,inflateInit2:dn,inflate:on,inflateEnd:vn,inflateGetHeader:un,inflateSetDictionary:sn,inflateInfo:cn};function wn(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}var bn=wn,jt$1=Object.prototype.toString,gn=w$8.Z_NO_FLUSH,pn=w$8.Z_FINISH,Oe$2=w$8.Z_OK,ya=w$8.Z_STREAM_END,za=w$8.Z_NEED_DICT,xn=w$8.Z_STREAM_ERROR,Wt$1=w$8.Z_DATA_ERROR,kn=w$8.Z_MEM_ERROR;function Ue$1(qn){this.options=He$2.assign({chunkSize:65536,windowBits:15,to:""},qn||{});var Gn=this.options,qn=(Gn.raw&&0<=Gn.windowBits&&Gn.windowBits<16&&(Gn.windowBits=-Gn.windowBits,Gn.windowBits===0)&&(Gn.windowBits=-15),!(0<=Gn.windowBits&&Gn.windowBits<16)||qn&&qn.windowBits||(Gn.windowBits+=32),15<Gn.windowBits&&Gn.windowBits<48&&(15&Gn.windowBits||(Gn.windowBits|=15)),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new ft$1,this.strm.avail_out=0,G$3.inflateInit2(this.strm,Gn.windowBits));if(qn!==Oe$2)throw new Error(te$1[qn]);if(this.header=new bn,G$3.inflateGetHeader(this.strm,this.header),Gn.dictionary&&(typeof Gn.dictionary=="string"?Gn.dictionary=Ze$1.string2buf(Gn.dictionary):jt$1.call(Gn.dictionary)==="[object ArrayBuffer]"&&(Gn.dictionary=x$5("Uint8",Gn.dictionary)),Gn.raw)&&(qn=G$3.inflateSetDictionary(this.strm,Gn.dictionary))!==Oe$2)throw new Error(te$1[qn])}function Ra(ee,Gn){if(Gn=new Ue$1(Gn),Gn.push(ee),Gn.err)throw Gn.msg||te$1[Gn.err];return Gn.result}function En(ee,Gn){return(Gn=Gn||{}).raw=!0,Ra(ee,Gn)}Ue$1.prototype.push=function(ee,Gn){var qn,Vn,Wn=this.strm,jn=this.options.chunkSize,Qn=this.options.dictionary;if(this.ended)return!1;for(Vn=Gn===~~Gn?Gn:Gn===!0?pn:gn,jt$1.call(ee)==="[object ArrayBuffer]"?Wn.input=x$5("Uint8",ee):Wn.input=ee,Wn.next_in=0,Wn.avail_in=Wn.input.length;;){for(Wn.avail_out===0&&(Wn.output=x$5("Uint8",jn),Wn.next_out=0,Wn.avail_out=jn),(qn=G$3.inflate(Wn,Vn))===za&&Qn&&((qn=G$3.inflateSetDictionary(Wn,Qn))===Oe$2?qn=G$3.inflate(Wn,Vn):qn===Wt$1&&(qn=za));0<Wn.avail_in&&qn===ya&&0<Wn.state.wrap&&ee[Wn.next_in]!==0;)G$3.inflateReset(Wn),qn=G$3.inflate(Wn,Vn);switch(qn){case xn:case Wt$1:case za:case kn:return this.onEnd(qn),!(this.ended=!0)}var Jn,eo,Xn,to=Wn.avail_out;if(!Wn.next_out||Wn.avail_out!==0&&qn!==ya||(this.options.to==="string"?(Jn=Ze$1.utf8border(Wn.output,Wn.next_out),eo=Wn.next_out-Jn,Xn=Ze$1.buf2string(Wn.output,Jn),Wn.next_out=eo,Wn.avail_out=jn-eo,eo&&Wn.output.set(Wn.output.subarray(Jn,Jn+eo),0),this.onData(Xn)):this.onData(Wn.output.length===Wn.next_out?Wn.output:Wn.output.subarray(0,Wn.next_out))),qn!==Oe$2||to!==0){if(qn===ya)return qn=G$3.inflateEnd(this.strm),this.onEnd(qn),this.ended=!0;if(Wn.avail_in===0)break}}return!0},Ue$1.prototype.onData=function(ee){this.chunks.push(ee)},Ue$1.prototype.onEnd=function(ee){ee===Oe$2&&(this.options.to==="string"?this.result=this.chunks.join(""):this.result=He$2.flattenChunks(this.chunks)),this.chunks=[],this.err=ee,this.msg=this.strm.msg};var mn=Ue$1,Sn=Ra,yn=En,zn=Ra,Rn=w$8,Ve$1={Inflate:mn,inflate:Sn,inflateRaw:yn,ungzip:zn,constants:Rn},Tn=Ke$1.Deflate,An=Ke$1.deflate,Zn=Ke$1.deflateRaw,Dn=Ke$1.gzip,In=Ve$1.Inflate,On=Ve$1.inflate,Un=Ve$1.inflateRaw,Nn=Ve$1.ungzip,Ln=Tn,Cn=An,Fn=Zn,$n=Dn,Mn=In,Hn=On,Bn=Un,Kn=Nn,Pn=w$8,Yn={Deflate:Ln,deflate:Cn,deflateRaw:Fn,gzip:$n,Inflate:Mn,inflate:Hn,inflateRaw:Bn,ungzip:Kn,constants:Pn},we=[],pe$2=0,Ue="",sr=5242880,Ge=!1,he=0,ke=[],X$2=typeof window!="undefined"&&typeof window.WeakMap=="function"?WeakMap:i$4,mt=!1,He$1=null,G$2;function lr(ee){mt=ee}function ur(){return window.top!==window.self}function dr(){return!(!He$1&&!ur())}function cr(ee){return ee.nodeType===ee.ELEMENT_NODE}function Ze(ee){var Gn=ee==null?void 0:ee.host;return(Gn==null?void 0:Gn.shadowRoot)===ee}function Ve(ee){return Object.prototype.toString.call(ee)==="[object ShadowRoot]"}function fr(ee){return ee=ee.includes(" background-clip: text;")&&!ee.includes(" -webkit-background-clip: text;")?ee.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;"):ee}function it(ee){try{var Gn=ee.rules||ee.cssRules;return Gn?fr(arrayFrom(Gn).map(vt).join("")):null}catch(qn){return null}}function vt(ee){var Gn=ee.cssText;if(pr(ee))try{Gn=it(ee.styleSheet)||Gn}catch(qn){}return Gn}function pr(ee){return"styleSheet"in ee}(function(ee){ee[ee.Document=0]="Document",ee[ee.DocumentType=1]="DocumentType",ee[ee.Element=2]="Element",ee[ee.Text=3]="Text",ee[ee.CDATA=4]="CDATA",ee[ee.Comment=5]="Comment"})(G$2=G$2||{});var ht=function(){function ee(){this.idNodeMap=new i$4,this.nodeMetaMap=new X$2}return ee.prototype.getId=function(Gn){return Gn&&(Gn=(Gn=this.getMeta(Gn))&&Gn.id)!=null?Gn:-1},ee.prototype.getNode=function(Gn){return this.idNodeMap.get(Gn)||null},ee.prototype.getIds=function(){return arrayFrom(this.idNodeMap.keys())},ee.prototype.getMeta=function(Gn){return this.nodeMetaMap.get(Gn)||null},ee.prototype.removeNodeFromMap=function(Gn){var qn=this,Vn=this.getId(Gn);this.idNodeMap.remove(Vn),Gn.childNodes&&forEach(Gn.childNodes,function(Wn){return qn.removeNodeFromMap(Wn)})},ee.prototype.has=function(Gn){return this.idNodeMap.has(Gn)},ee.prototype.hasNode=function(Gn){return this.nodeMetaMap.has(Gn)},ee.prototype.add=function(Gn,qn){var Vn=qn.id;this.idNodeMap.set(Vn,Gn),this.nodeMetaMap.set(Gn,qn)},ee.prototype.replace=function(Gn,qn){var Vn=this.getNode(Gn);Vn&&(Vn=this.nodeMetaMap.get(Vn))&&this.nodeMetaMap.set(qn,Vn),this.idNodeMap.set(Gn,qn)},ee.prototype.reset=function(){this.idNodeMap=new i$4,this.nodeMetaMap=new X$2},ee}();function gr(){return new ht}function ot(Wn){var Gn=Wn.maskInputOptions,qn=Wn.tagName,Vn=Wn.type,jn=Wn.value,Wn=Wn.maskInputFn,jn=jn||"";return jn=Gn[qn.toLowerCase()]||Gn[Vn]?Wn?Wn(jn):"*".repeat(jn.length):jn}var It="__rrweb_original__";function mr(ee){var Gn=ee.getContext("2d");if(Gn)for(var qn=0;qn<ee.width;qn+=50)for(var Vn=0;Vn<ee.height;Vn+=50){var Wn=Gn.getImageData,Wn=It in Wn?Wn[It]:Wn;if(new Uint32Array(Wn.call(Gn,qn,Vn,Math.min(50,ee.width-qn),Math.min(50,ee.height-Vn)).data.buffer).some(function(jn){return jn!==0}))return!1}return!0}var vr=1,hr=new RegExp("[^a-z0-9-_:]"),_e$1=-2;function Ct(){return vr++}function Ir(ee){return ee instanceof HTMLFormElement?"form":(ee=ee.tagName.toLowerCase().trim(),hr.test(ee)?"div":ee)}function Cr(ee){return ee.cssRules?arrayFrom(ee.cssRules).map(function(Gn){return Gn.cssText||""}).join(""):""}function yr(ee){return(-1<ee.indexOf("//")?ee.split("/").slice(0,3).join("/"):ee.split("/")[0]).split("?")[0]}var Re$2,yt,Sr=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,br=/^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\\|\/\/|#).*/,Ar=/^(data:)([^,]*),(.*)/i;function Qe(ee,Gn){return(ee||"").replace(Sr,function(qn,Vn,Wn,jn,Qn,Xn){if(Wn=Wn||Qn||Xn,Qn=Vn||jn||"",!Wn)return qn;if(!br.test(Wn)||Ar.test(Wn))return"url(".concat(Qn).concat(Wn).concat(Qn,")");if(Wn[0]==="/")return"url(".concat(Qn).concat(yr(Gn)+Wn).concat(Qn,")");var eo=Gn.split("/"),Xn=Wn.split("/");eo.pop();for(var to=0,io=Xn;to<io.length;to++){var ro=io[to];ro!=="."&&(ro===".."?eo.pop():eo.push(ro))}return"url(".concat(Qn).concat(eo.join("/")).concat(Qn,")")})}var wr=/^[^ \t\n\r\u000c]+/,kr=/^[, \t\n\r\u000c]+/;function Rr(ee,Gn){if(Gn.trim()==="")return Gn;var qn=0;function Vn(to){var to=to.exec(Gn.substring(qn));return to?(to=to[0],qn+=to.length,to):""}for(var Wn=[];Vn(kr),!(qn>=Gn.length);)if((Qn=Vn(wr)).slice(-1)===",")Qn=Me(ee,Qn.substring(0,Qn.length-1)),Wn.push(Qn);else for(var jn="",Qn=Me(ee,Qn),Jn=!1;;){var eo=Gn.charAt(qn);if(eo===""){Wn.push((Qn+jn).trim());break}if(Jn)eo===")"&&(Jn=!1);else{if(eo===","){qn+=1,Wn.push((Qn+jn).trim());break}eo==="("&&(Jn=!0)}jn+=eo,qn+=1}return Wn.join(", ")}function Me(ee,Gn){return Gn&&Gn.trim()!==""?((ee=ee.createElement("a")).href=Gn,ee.href):Gn}function Mr(ee){return!(ee.tagName!=="svg"&&!ee.ownerSVGElement)}function st(){var ee=document.createElement("a");return ee.href="",ee.href}function St(ee,Gn,qn,Vn){return qn==="src"||qn==="href"&&Vn&&(Gn!=="use"||Vn[0]!=="#")||qn==="xlink:href"&&Vn&&Vn[0]!=="#"||qn==="background"&&Vn&&(Gn==="table"||Gn==="td"||Gn==="th")?Me(ee,Vn):qn==="srcset"&&Vn?Rr(ee,Vn):qn==="style"&&Vn?Qe(Vn,st()):Gn==="object"&&qn==="data"&&Vn?Me(ee,Vn):Vn}function Tr(ee,Gn,qn){if(typeof Gn=="string"){if(ee.classList.contains(Gn))return!0}else for(var Vn=ee.classList.length;Vn--;){var Wn=ee.classList[Vn];if(Gn.test(Wn))return!0}return!!qn&&ee.matches(qn)}function Pe(ee,Gn,qn){if(!ee)return!1;if(ee.nodeType===ee.ELEMENT_NODE)for(var Vn=ee.classList.length;Vn--;){var Wn=ee.classList[Vn];if(Gn.test(Wn))return!0}return!!qn&&Pe(ee.parentNode,Gn,qn)}function bt(ee,Gn,qn){if(ee=ee.nodeType===ee.ELEMENT_NODE?ee:ee.parentElement,ee===null)return!1;if(typeof Gn=="string"){if(ee.classList.contains(Gn)||ee.closest(".".concat(Gn)))return!0}else if(Pe(ee,Gn,!0))return!0;return!(!qn||!ee.matches(qn)&&!ee.closest(qn))}function Nr(ee,Gn,qn){var Vn=ee.contentWindow;if(Vn){var Wn,jn,Qn=!1;try{jn=Vn.document.readyState}catch(Jn){return}if(jn==="complete")return Vn.location.href!==(jn="about:blank")||ee.src===jn||ee.src===""?(setTimeout(Gn,0),ee.addEventListener("load",Gn)):void ee.addEventListener("load",Gn);Wn=setTimeout(function(){Qn||(Gn(),Qn=!0)},qn),ee.addEventListener("load",function(){clearTimeout(Wn),Qn=!0,Gn()})}}function Or(ee,Gn,qn){var Vn,Wn,jn=!1;try{Vn=ee.sheet}catch(Qn){return}Vn||(Wn=setTimeout(function(){jn||(Gn(),jn=!0)},qn),ee.addEventListener("load",function(){clearTimeout(Wn),jn=!0,Gn()}))}function Fr(ee,Gn){var qn=Gn.doc,Vn=Gn.mirror,Wn=Gn.blockClass,jn=Gn.blockSelector,Qn=Gn.maskTextClass,Jn=Gn.maskTextSelector,eo=Gn.inlineStylesheet,so=Gn.maskInputOptions,Xn=so===void 0?{}:so,to=Gn.maskTextFn,io=Gn.maskInputFn,so=Gn.dataURLOptions,ro=so===void 0?{}:so,no=Gn.inlineImages,oo=Gn.recordCanvas,ao=Gn.keepIframeSrcFn,so=Gn.newlyAddedElement,co=so!==void 0&&so,uo=Lr(qn,Vn);switch(ee.nodeType){case ee.DOCUMENT_NODE:return ee.compatMode!=="CSS1Compat"?{type:G$2.Document,childNodes:[],compatMode:ee.compatMode}:{type:G$2.Document,childNodes:[]};case ee.DOCUMENT_TYPE_NODE:return{type:G$2.DocumentType,name:ee.name,publicId:ee.publicId,systemId:ee.systemId,rootId:uo};case ee.ELEMENT_NODE:return xr(ee,{doc:qn,blockClass:Wn,blockSelector:jn,inlineStylesheet:eo,maskInputOptions:Xn,maskInputFn:io,dataURLOptions:ro,inlineImages:no,recordCanvas:oo,keepIframeSrcFn:ao,newlyAddedElement:co,rootId:uo});case ee.TEXT_NODE:return Dr(ee,{maskTextClass:Qn,maskTextSelector:Jn,maskTextFn:to,rootId:uo});case ee.CDATA_SECTION_NODE:return{type:G$2.CDATA,textContent:"",rootId:uo};case ee.COMMENT_NODE:return{type:G$2.Comment,textContent:ee.textContent||"",rootId:uo};default:return!1}}function Lr(ee,Gn){return!Gn.hasNode(ee)||(Gn=Gn.getId(ee))===1?void 0:Gn}function Dr(ee,Gn){var qn,Vn=Gn.maskTextClass,Wn=Gn.maskTextSelector,jn=Gn.maskTextFn,Qn=Gn.rootId,Xn=ee.parentNode&&ee.parentNode.tagName,Jn=ee.textContent,eo=Xn==="STYLE"||void 0,Xn=Xn==="SCRIPT"||void 0;if(eo&&Jn){try{ee.nextSibling||ee.previousSibling||(qn=ee.parentNode.sheet)!=null&&qn.cssRules&&(Jn=Cr(ee.parentNode.sheet))}catch(to){console.warn("Cannot get CSS styles from text's parentNode. Error: ".concat(to),ee)}Jn=Qe(Jn,st())}return Xn&&(Jn="SCRIPT_PLACEHOLDER"),!eo&&!Xn&&Jn&&bt(ee,Vn,Wn)&&(Jn=jn?jn(Jn):Jn.replace(/[\S]/g,"*")),{type:G$2.Text,textContent:Jn||"",isStyle:eo,rootId:Qn}}function xr(ee,Gn){for(var qn,Vn,Wn,jn=Gn.doc,ao=Gn.blockClass,Qn=Gn.blockSelector,Jn=Gn.inlineStylesheet,eo=Gn.maskInputOptions,eo=eo===void 0?{}:eo,Xn=Gn.maskInputFn,io=Gn.dataURLOptions,to=io===void 0?{}:io,io=Gn.inlineImages,ro=Gn.recordCanvas,no=Gn.keepIframeSrcFn,oo=Gn.newlyAddedElement,oo=oo!==void 0&&oo,Gn=Gn.rootId,ao=Tr(ee,ao,Qn),so=Ir(ee),co={},uo=ee.attributes.length,fo=0;fo<uo;fo++){var wo=ee.attributes[fo];co[wo.name]=St(jn,so,wo.name,wo.value)}return so==="link"&&Jn&&(qn=null,qn=(Qn=arrayFrom(jn.styleSheets).find(function(lo){return lo.href===ee.href}))?it(Qn):qn)&&(delete co.rel,delete co.href,co._cssText=Qe(qn,Qn.href)),so==="style"&&ee.sheet&&!(ee.innerText||ee.textContent||"").trim().length&&(qn=it(ee.sheet))&&(co._cssText=Qe(qn,st())),so!=="input"&&so!=="textarea"&&so!=="select"||(Jn=ee.value,Qn=ee.checked,co.type!=="radio"&&co.type!=="checkbox"&&co.type!=="submit"&&co.type!=="button"&&Jn?co.value=ot({type:co.type,tagName:so,value:Jn,maskInputOptions:eo,maskInputFn:Xn}):Qn&&(co.checked=Qn)),so==="option"&&(ee.selected&&!eo.select?co.selected=!0:delete co.selected),so==="canvas"&&ro&&(ee.__context==="2d"?mr(ee)||(co.rr_dataURL=ee.toDataURL(to.type,to.quality)):"__context"in ee||(qn=ee.toDataURL(to.type,to.quality),(Jn=document.createElement("canvas")).width=ee.width,Jn.height=ee.height,qn!==Jn.toDataURL(to.type,to.quality)&&(co.rr_dataURL=qn))),so==="img"&&io&&(Re$2||(Re$2=jn.createElement("canvas"),yt=Re$2.getContext("2d")),Wn=(Vn=ee).crossOrigin,Vn.crossOrigin="anonymous",Xn=function(){try{Re$2.width=Vn.naturalWidth,Re$2.height=Vn.naturalHeight,yt.drawImage(Vn,0,0),co.rr_dataURL=Re$2.toDataURL(to.type,to.quality)}catch(lo){console.warn("Cannot inline img src=".concat(Vn.currentSrc,"! Error: ").concat(lo))}Wn?co.crossOrigin=Wn:Vn.removeAttribute("crossorigin")},Vn.complete&&Vn.naturalWidth!==0?Xn():Vn.onload=Xn),so!=="audio"&&so!=="video"||(co.rr_mediaState=ee.paused?"paused":"played",co.rr_mediaCurrentTime=ee.currentTime),oo||(ee.scrollLeft&&(co.rr_scrollLeft=ee.scrollLeft),ee.scrollTop&&(co.rr_scrollTop=ee.scrollTop)),ao&&(eo=(Qn=ee.getBoundingClientRect()).width,ro=Qn.height,co={class:co.class,rr_width:"".concat(eo,"px"),rr_height:"".concat(ro,"px")}),so!=="iframe"||no(co.src)||(ee.contentDocument||(co.rr_src=co.src),delete co.src),{type:G$2.Element,tagName:so,attributes:co,childNodes:[],isSVG:Mr(ee)||void 0,needBlock:ao,rootId:Gn}}function N$2(ee){return ee===void 0?"":ee.toLowerCase()}function Br(ee,Gn){return!!(Gn.comment&&ee.type===G$2.Comment||ee.type===G$2.Element&&(Gn.script&&(ee.tagName==="script"||ee.tagName==="link"&&ee.attributes.rel==="preload"&&ee.attributes.as==="script"||ee.tagName==="link"&&ee.attributes.rel==="prefetch"&&typeof ee.attributes.href=="string"&&ee.attributes.href.endsWith(".js"))||Gn.headFavicon&&(ee.tagName==="link"&&ee.attributes.rel==="shortcut icon"||ee.tagName==="meta"&&(N$2(ee.attributes.name).match(/^msapplication-tile(image|color)$/)||N$2(ee.attributes.name)==="application-name"||N$2(ee.attributes.rel)==="icon"||N$2(ee.attributes.rel)==="apple-touch-icon"||N$2(ee.attributes.rel)==="shortcut icon"))||ee.tagName==="meta"&&(Gn.headMetaDescKeywords&&N$2(ee.attributes.name).match(/^description|keywords$/)||Gn.headMetaSocial&&(N$2(ee.attributes.property).match(/^(og|twitter|fb):/)||N$2(ee.attributes.name).match(/^(og|twitter):/)||N$2(ee.attributes.name)==="pinterest")||Gn.headMetaRobots&&(N$2(ee.attributes.name)==="robots"||N$2(ee.attributes.name)==="googlebot"||N$2(ee.attributes.name)==="bingbot")||Gn.headMetaHttpEquiv&&ee.attributes["http-equiv"]!==void 0||Gn.headMetaAuthorship&&(N$2(ee.attributes.name)==="author"||N$2(ee.attributes.name)==="generator"||N$2(ee.attributes.name)==="framework"||N$2(ee.attributes.name)==="publisher"||N$2(ee.attributes.name)==="progid"||N$2(ee.attributes.property).match(/^article:/)||N$2(ee.attributes.property).match(/^product:/))||Gn.headMetaVerification&&(N$2(ee.attributes.name)==="google-site-verification"||N$2(ee.attributes.name)==="yandex-verification"||N$2(ee.attributes.name)==="csrf-token"||N$2(ee.attributes.name)==="p:domain_verify"||N$2(ee.attributes.name)==="verify-v1"||N$2(ee.attributes.name)==="verification"||N$2(ee.attributes.name)==="shopify-checkout-api-token"))))}function Te$1(ee,ho){var qn=ho.doc,Vn=ho.mirror,Wn=ho.blockClass,jn=ho.blockSelector,Qn=ho.maskTextClass,Jn=ho.maskTextSelector,eo=ho.skipChild,eo=eo!==void 0&&eo,mo=ho.inlineStylesheet,Xn=mo===void 0||mo,mo=ho.maskInputOptions,to=mo===void 0?{}:mo,io=ho.maskTextFn,ro=ho.maskInputFn,no=ho.slimDOMOptions,mo=ho.dataURLOptions,oo=mo===void 0?{}:mo,mo=ho.inlineImages,ao=mo!==void 0&&mo,mo=ho.recordCanvas,so=mo!==void 0&&mo,co=ho.onSerialize,uo=ho.onIframeLoad,mo=ho.iframeLoadTimeout,fo=mo===void 0?5e3:mo,wo=ho.onStylesheetLoad,mo=ho.stylesheetLoadTimeout,lo=mo===void 0?5e3:mo,mo=ho.keepIframeSrcFn,po=mo===void 0?function(){return!1}:mo,mo=ho.newlyAddedElement,ho=ho.preserveWhiteSpace,go=ho===void 0||ho,ho=Fr(ee,{doc:qn,mirror:Vn,blockClass:Wn,blockSelector:jn,maskTextClass:Qn,maskTextSelector:Jn,inlineStylesheet:Xn,maskInputOptions:to,maskTextFn:io,maskInputFn:ro,dataURLOptions:oo,inlineImages:ao,recordCanvas:so,keepIframeSrcFn:po,newlyAddedElement:mo!==void 0&&mo});if(!ho)return console.warn(ee,"not serialized"),null;var mo=Vn.hasNode(ee)?Vn.getId(ee):Br(ho,no)||!go&&ho.type===G$2.Text&&!ho.isStyle&&!ho.textContent.replace(/^\s+|\s+$/gm,"").length?_e$1:Ct(),$o=objectAssign(ho,{id:mo});if(Vn.add(ee,$o),mo===_e$1)return null;if(co&&co(ee),ho=!eo,$o.type===G$2.Element&&(ho=ho&&!$o.needBlock,delete $o.needBlock,mo=ee.shadowRoot)&&Ve(mo)&&($o.isShadowHost=!0),($o.type===G$2.Document||$o.type===G$2.Element)&&ho){no.headWhitespace&&$o.type===G$2.Element&&$o.tagName==="head"&&(go=!1);for(var vo,To={doc:qn,mirror:Vn,blockClass:Wn,blockSelector:jn,maskTextClass:Qn,maskTextSelector:Jn,skipChild:eo,inlineStylesheet:Xn,maskInputOptions:to,maskTextFn:io,maskInputFn:ro,slimDOMOptions:no,dataURLOptions:oo,inlineImages:ao,recordCanvas:so,preserveWhiteSpace:go,onSerialize:co,onIframeLoad:uo,iframeLoadTimeout:fo,onStylesheetLoad:wo,stylesheetLoadTimeout:lo,keepIframeSrcFn:po},So=0,_o=arrayFrom(ee.childNodes);So<_o.length;So++)(vo=Te$1(_o[So],To))&&$o.childNodes.push(vo);if(cr(ee)&&ee.shadowRoot)for(var Eo=0,Ao=arrayFrom(ee.shadowRoot.childNodes);Eo<Ao.length;Eo++)(vo=Te$1(Ao[Eo],To))&&(Ve(ee.shadowRoot)&&(vo.isShadow=!0),$o.childNodes.push(vo))}return ee.parentNode&&Ze(ee.parentNode)&&Ve(ee.parentNode)&&($o.isShadow=!0),$o.type===G$2.Element&&$o.tagName==="iframe"&&Nr(ee,function(){var Io=ee.contentDocument;Io&&uo&&(Io=Te$1(Io,{doc:Io,mirror:Vn,blockClass:Wn,blockSelector:jn,maskTextClass:Qn,maskTextSelector:Jn,skipChild:!1,inlineStylesheet:Xn,maskInputOptions:to,maskTextFn:io,maskInputFn:ro,slimDOMOptions:no,dataURLOptions:oo,inlineImages:ao,recordCanvas:so,preserveWhiteSpace:go,onSerialize:co,onIframeLoad:uo,iframeLoadTimeout:fo,onStylesheetLoad:wo,stylesheetLoadTimeout:lo,keepIframeSrcFn:po}))&&uo(ee,Io)},fo),$o.type===G$2.Element&&$o.tagName==="link"&&$o.attributes.rel==="stylesheet"&&Or(ee,function(){var Io;wo&&(Io=Te$1(ee,{doc:qn,mirror:Vn,blockClass:Wn,blockSelector:jn,maskTextClass:Qn,maskTextSelector:Jn,skipChild:!1,inlineStylesheet:Xn,maskInputOptions:to,maskTextFn:io,maskInputFn:ro,slimDOMOptions:no,dataURLOptions:oo,inlineImages:ao,recordCanvas:so,preserveWhiteSpace:go,onSerialize:co,onIframeLoad:uo,iframeLoadTimeout:fo,onStylesheetLoad:wo,stylesheetLoadTimeout:lo,keepIframeSrcFn:po}))&&wo(ee,Io)},lo),$o}function Er(ee,lo){var lo=lo||{},qn=lo.mirror,qn=qn===void 0?new ht:qn,Vn=lo.blockClass,Wn=lo.blockSelector,jn=lo.maskTextClass,Qn=lo.maskTextSelector,Jn=lo.inlineStylesheet,eo=lo.inlineImages,Xn=lo.recordCanvas,to=lo.maskAllInputs,to=to!==void 0&&to,io=lo.maskTextFn,ro=lo.maskInputFn,no=lo.slimDOM,no=no!==void 0&&no,oo=lo.dataURLOptions,ao=lo.preserveWhiteSpace,so=lo.onSerialize,co=lo.onIframeLoad,uo=lo.iframeLoadTimeout,fo=lo.onStylesheetLoad,wo=lo.stylesheetLoadTimeout,lo=lo.keepIframeSrcFn;return Te$1(ee,{doc:ee,mirror:qn,blockClass:Vn===void 0?"rr-block":Vn,blockSelector:Wn===void 0?null:Wn,maskTextClass:jn===void 0?"rr-mask":jn,maskTextSelector:Qn===void 0?null:Qn,skipChild:!1,inlineStylesheet:Jn===void 0||Jn,maskInputOptions:to===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:to===!1?{password:!0}:to,maskTextFn:io,maskInputFn:ro,slimDOMOptions:no===!0||no==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:no==="all",headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:no===!1?{}:no,dataURLOptions:oo,inlineImages:eo!==void 0&&eo,recordCanvas:Xn!==void 0&&Xn,preserveWhiteSpace:ao,onSerialize:so,onIframeLoad:co,iframeLoadTimeout:uo,onStylesheetLoad:fo,stylesheetLoadTimeout:wo,keepIframeSrcFn:lo===void 0?function(){return!1}:lo,newlyAddedElement:!1})}function _$4(ee,Gn,qn){qn=qn||document;var Vn={capture:!0,passive:!0};return qn.addEventListener(ee,Gn,Vn),function(){return qn.removeEventListener(ee,Gn,Vn)}}var Ne$1="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",At={map:{},getId:function(){return console.error(Ne$1),-1},getNode:function(){return console.error(Ne$1),null},removeNodeFromMap:function(){console.error(Ne$1)},has:function(){return console.error(Ne$1),!1},reset:function(){console.error(Ne$1)}};function Ke(ee,Gn,qn){qn=qn||{};var Vn=null,Wn=0;return function(){var jn=Array.prototype.slice.call(arguments),Qn=Date.now(),Jn=(Wn||qn.leading!==!1||(Wn=Qn),Gn-(Qn-Wn)),eo=this;Jn<=0||Gn<Jn?(Vn&&(clearTimeout(Vn),Vn=null),Wn=Qn,ee.apply(eo,jn)):Vn||qn.trailing===!1||(Vn=setTimeout(function(){Wn=qn.leading===!1?0:Date.now(),Vn=null,ee.apply(eo,jn)},Jn))}}function Xe(ee,Gn,qn,Vn,Wn){var jn=(Wn=Wn||window).Object.getOwnPropertyDescriptor(ee,Gn);return Wn.Object.defineProperty(ee,Gn,Vn?qn:{set:function(Qn){setTimeout(function(){qn.set.call(this,Qn)},0),jn&&jn.set&&jn.set.call(this,Qn)}}),function(){return Xe(ee,Gn,jn||{},!0)}}function Oe$1(ee,Gn,qn){try{var Vn,Wn;return Gn in ee?(typeof(Wn=qn(Vn=ee[Gn]))=="function"&&(Wn.prototype=Wn.prototype||{},Object.defineProperties(Wn,{__rrweb_original__:{enumerable:!1,value:Vn}})),ee[Gn]=Wn,function(){ee[Gn]=Vn}):function(){}}catch(jn){return function(){}}}function wt(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function kt(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function K$3(ee,Gn,qn,Vn){if(!ee)return!1;var Wn=ee.nodeType===ee.ELEMENT_NODE?ee:ee.parentElement;if(!Wn)return!1;if(typeof Gn=="string"){if(Wn.classList.contains(Gn)||Vn&&Wn.closest("."+Gn)!==null)return!0}else if(Pe(Wn,Gn,Vn))return!0;return!(!qn||!(ee.matches(qn)||Vn&&Wn.closest(qn)!==null))}function Wr(ee,Gn){return Gn.getId(ee)!==-1}function lt(ee,Gn){return Gn.getId(ee)===_e$1}function Rt(ee,Gn){var qn;return!Ze(ee)&&(qn=Gn.getId(ee),!Gn.has(qn)||(!ee.parentNode||ee.parentNode.nodeType!==ee.DOCUMENT_NODE)&&(!ee.parentNode||Rt(ee.parentNode,Gn)))}function Mt(ee){return!!ee.changedTouches}function Ur(ee){"NodeList"in(ee=ee||window)&&!ee.NodeList.prototype.forEach&&(ee.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in ee&&!ee.DOMTokenList.prototype.forEach&&(ee.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=function(){var Gn=arguments[0];if(!(0 in args))throw new TypeError("1 argument is required");do if(this===Gn)return!0;while(Gn=Gn&&Gn.parentNode);return!1})}function Tt(ee,Gn){return!(ee.nodeName!=="IFRAME"||!Gn.getMeta(ee))}function Nt(ee,Gn){return!(ee.nodeName!=="LINK"||ee.nodeType!==ee.ELEMENT_NODE||!ee.getAttribute||ee.getAttribute("rel")!=="stylesheet"||!Gn.getMeta(ee))}function Ot(ee){return!(ee==null||!ee.shadowRoot)}function Ie(){this.id=1,this.styleIDMap=new X$2,this.idStyleMap=new i$4}typeof window!="undefined"&&window.Proxy&&window.Reflect&&(At=new Proxy(At,{get:function(ee,Gn,qn){return Gn==="map"&&console.error(Ne$1),Reflect.get(ee,Gn,qn)}})),Ie.prototype.getId=function(ee){return(ee=this.styleIDMap.get(ee))!=null?ee:-1},Ie.prototype.has=function(ee){return this.styleIDMap.has(ee)},Ie.prototype.add=function(ee,Gn){return this.has(ee)?this.getId(ee):(Gn=Gn===void 0?this.id++:Gn,this.styleIDMap.set(ee,Gn),this.idStyleMap.set(Gn,ee),Gn)},Ie.prototype.getStyle=function(ee){return this.idStyleMap.get(ee)||null},Ie.prototype.reset=function(){this.styleIDMap=new X$2,this.idStyleMap=new i$4,this.id=1},Ie.prototype.generateId=function(){return this.id++};var k$2=function(ee){return ee[ee.DomContentLoaded=0]="DomContentLoaded",ee[ee.Load=1]="Load",ee[ee.FullSnapshot=2]="FullSnapshot",ee[ee.IncrementalSnapshot=3]="IncrementalSnapshot",ee[ee.Meta=4]="Meta",ee[ee.Custom=5]="Custom",ee[ee.Plugin=6]="Plugin",ee}(k$2||{}),w$7=function(ee){return ee[ee.Mutation=0]="Mutation",ee[ee.MouseMove=1]="MouseMove",ee[ee.MouseInteraction=2]="MouseInteraction",ee[ee.Scroll=3]="Scroll",ee[ee.ViewportResize=4]="ViewportResize",ee[ee.Input=5]="Input",ee[ee.TouchMove=6]="TouchMove",ee[ee.MediaInteraction=7]="MediaInteraction",ee[ee.StyleSheetRule=8]="StyleSheetRule",ee[ee.CanvasMutation=9]="CanvasMutation",ee[ee.Font=10]="Font",ee[ee.Log=11]="Log",ee[ee.Drag=12]="Drag",ee[ee.StyleDeclaration=13]="StyleDeclaration",ee[ee.Selection=14]="Selection",ee[ee.AdoptedStyleSheet=15]="AdoptedStyleSheet",ee}(w$7||{}),ut=function(ee){return ee[ee.MouseUp=0]="MouseUp",ee[ee.MouseDown=1]="MouseDown",ee[ee.Click=2]="Click",ee[ee.ContextMenu=3]="ContextMenu",ee[ee.DblClick=4]="DblClick",ee[ee.Focus=5]="Focus",ee[ee.Blur=6]="Blur",ee[ee.TouchStart=7]="TouchStart",ee[ee.TouchMove_Departed=8]="TouchMove_Departed",ee[ee.TouchEnd=9]="TouchEnd",ee[ee.TouchCancel=10]="TouchCancel",ee}(ut||{}),Fe=function(ee){return ee[ee["2D"]=0]="2D",ee[ee.WebGL=1]="WebGL",ee[ee.WebGL2=2]="WebGL2",ee}(Fe||{});function Ft(ee){return"__ln"in ee}function qe(){this.nodesArray=[],this.length=0,this.head=null}function Lt(ee,Gn){return ee+"@"+Gn}function ge(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new s$6,this.movedSet=new s$6,this.droppedSet=new s$6;var ee=this;this.processMutations=function(Gn){forEach(Gn,ee.processMutation),ee.emit()},this.emit=function(){if(!ee.frozen&&!ee.locked){for(var Gn=[],qn=new qe;ee.mapRemoves.length;)ee.mirror.removeNodeFromMap(ee.mapRemoves.shift());for(var Vn=arrayFrom(ee.movedSet.values()),Wn=0;Wn<Vn.length;Wn++){var jn=Vn[Wn];Dt(ee.removes,jn,ee.mirror)&&!ee.movedSet.has(jn.parentNode)||wo(jn)}for(var Qn=arrayFrom(ee.addedSet.values()),Jn=0;Jn<Qn.length;Jn++)jn=Qn[Jn],!Bt(ee.droppedSet,jn)&&!Dt(ee.removes,jn,ee.mirror)||Bt(ee.movedSet,jn)?wo(jn):ee.droppedSet.add(jn);if(0<qn.length)for(var eo=null;qn.length;){var Xn,to,io=null;if(!(io=eo&&(Xn=ee.mirror.getId(eo.value.parentNode),to=fo(eo.value),Xn!==-1)&&to!==-1?eo:io))for(var ro=qn.nodesArray.length-1;0<=ro;ro--){var no=qn.nodesArray[ro];if(no&&(Xn=ee.mirror.getId(no.value.parentNode),(to=fo(no.value))!==-1)){if(Xn!==-1){io=no;break}var oo=no.value;if(oo.parentNode&&oo.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&(oo=oo.parentNode.host,(Xn=ee.mirror.getId(oo))!==-1)){io=no;break}}}if(!io){for(;qn.head;)qn.removeNode(qn.head.value);break}eo=io.previous,qn.removeNode(io.value),wo(io.value)}ao=ee.texts,so=ee.attributes,co=ee.removes,uo=Gn,ao={texts:ao.map(function(lo){return{id:ee.mirror.getId(lo.node),value:lo.value}}).filter(function(lo){return ee.mirror.has(lo.id)}),attributes:so.map(function(lo){return{id:ee.mirror.getId(lo.node),attributes:lo.attributes}}).filter(function(lo){return ee.mirror.has(lo.id)}),removes:co,adds:uo},(ao.texts.length||ao.attributes.length||ao.removes.length||ao.adds.length)&&ee.mutationCb(ao),ee.texts=[],ee.attributes=[],ee.removes=[],ee.addedSet=new s$6,ee.movedSet=new s$6,ee.droppedSet=new s$6,ee.movedMap={}}var ao,so,co,uo;function fo(lo){for(var po=lo,go=_e$1;go===_e$1;)go=(po=po&&po.nextSibling)&&ee.mirror.getId(po);return go}function wo(lo){for(var po,go=null,ho=go=((mo=(mo=lo.getRootNode)==null?void 0:mo.call(lo))==null?void 0:mo.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&lo.getRootNode().host?lo.getRootNode().host:go;((po=(po=ho==null?void 0:ho.getRootNode)==null?void 0:po.call(ho))==null?void 0:po.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&ho.getRootNode().host;)ho=ho.getRootNode().host;var mo=!(ee.doc.contains(lo)||ho&&ee.doc.contains(ho));if(lo.parentNode&&!mo){if(mo=Ze(lo.parentNode)?ee.mirror.getId(go):ee.mirror.getId(lo.parentNode),go=fo(lo),mo===-1||go===-1)return qn.addNode(lo);var $o=Te$1(lo,{doc:ee.doc,mirror:ee.mirror,blockClass:ee.blockClass,blockSelector:ee.blockSelector,maskTextClass:ee.maskTextClass,maskTextSelector:ee.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:ee.inlineStylesheet,maskInputOptions:ee.maskInputOptions,maskTextFn:ee.maskTextFn,maskInputFn:ee.maskInputFn,slimDOMOptions:ee.slimDOMOptions,dataURLOptions:ee.dataURLOptions,recordCanvas:ee.recordCanvas,inlineImages:ee.inlineImages,onSerialize:function(vo){Tt(vo,ee.mirror)&&ee.iframeManager.addIframe(vo),Nt(vo,ee.mirror)&&ee.stylesheetManager.trackLinkElement(vo),Ot(lo)&&ee.shadowDomManager.addShadowRoot(lo.shadowRoot,ee.doc)},onIframeLoad:function(vo,To){ee.iframeManager.attachIframe(vo,To),ee.shadowDomManager.observeAttachShadow(vo)},onStylesheetLoad:function(vo,To){ee.stylesheetManager.attachLinkElement(vo,To)}});$o&&Gn.push({parentId:mo,nextId:go,node:$o})}}},this.processMutation=function(Gn){if(!lt(Gn.target,ee.mirror))switch(Gn.type){case"characterData":var Vn=Gn.target.textContent;K$3(Gn.target,ee.blockClass,ee.blockSelector,!1)||Vn===Gn.oldValue||ee.texts.push({value:bt(Gn.target,ee.maskTextClass,ee.maskTextSelector)&&Vn?ee.maskTextFn?ee.maskTextFn(Vn):Vn.replace(/[\S]/g,"*"):Vn,node:Gn.target});break;case"attributes":var qn=Gn.target,Vn=Gn.target.getAttribute(Gn.attributeName);if(Gn.attributeName==="value"&&(Vn=ot({maskInputOptions:ee.maskInputOptions,tagName:Gn.target.tagName,type:Gn.target.getAttribute("type"),value:Vn,maskInputFn:ee.maskInputFn})),!K$3(Gn.target,ee.blockClass,ee.blockSelector,!1)&&Vn!==Gn.oldValue){var Wn=ee.attributes.filter(function(oo){return oo.node===Gn.target})[0];if(qn.tagName==="IFRAME"&&Gn.attributeName==="src"&&!ee.keepIframeSrcFn(Vn)){if(qn.contentDocument)return;Gn.attributeName="rr_src"}if(Wn||(Wn={node:Gn.target,attributes:{}},ee.attributes.push(Wn)),Gn.attributeName==="style"){var jn=ee.doc.createElement("span");Gn.oldValue&&jn.setAttribute("style",Gn.oldValue),Wn.attributes.style!==void 0&&Wn.attributes.style!==null||(Wn.attributes.style={});for(var Qn=Wn.attributes.style,Jn=arrayFrom(qn.style),eo=0;eo<Jn.length;eo++){var Xn=Jn[eo],to=qn.style.getPropertyValue(Xn),io=qn.style.getPropertyPriority(Xn);to===jn.style.getPropertyValue(Xn)&&io===jn.style.getPropertyPriority(Xn)||(Qn[Xn]=io===""?to:[to,io])}for(var ro=arrayFrom(jn.style),no=0;no<ro.length;no++)Xn=ro[no],qn.style.getPropertyValue(Xn)===""&&(Qn[Xn]=!1)}else Wn.attributes[Gn.attributeName]=St(ee.doc,qn.tagName,Gn.attributeName,Vn)}break;case"childList":if(K$3(Gn.target,ee.blockClass,ee.blockSelector,!0))return;forEach(Array.prototype.slice.call(Gn.addedNodes),function(oo){ee.genAdds(oo,Gn.target)}),forEach(Array.prototype.slice.call(Gn.removedNodes),function(oo){var ao=ee.mirror.getId(oo),so=Ze(Gn.target)?ee.mirror.getId(Gn.target.host):ee.mirror.getId(Gn.target);K$3(Gn.target,ee.blockClass,ee.blockSelector,!1)||lt(oo,ee.mirror)||!Wr(oo,ee.mirror)||(ee.addedSet.has(oo)?(dt(ee.addedSet,oo),ee.droppedSet.add(oo)):ee.addedSet.has(Gn.target)&&ao===-1||Rt(Gn.target,ee.mirror)||(ee.movedSet.has(oo)&&ee.movedMap[Lt(ao,so)]?dt(ee.movedSet,oo):ee.removes.push({parentId:so,id:ao,isShadow:!(!Ze(Gn.target)||!Ve(Gn.target))||void 0})),ee.mapRemoves.push(oo))})}},this.genAdds=function(Gn,qn){if(ee.mirror.hasNode(Gn)){if(lt(Gn,ee.mirror))return;ee.movedSet.add(Gn);var Vn=null;(Vn=qn&&ee.mirror.hasNode(qn)?ee.mirror.getId(qn):Vn)&&Vn!==-1&&(ee.movedMap[Lt(ee.mirror.getId(Gn),Vn)]=!0)}else ee.addedSet.add(Gn),ee.droppedSet.remove(Gn);K$3(Gn,ee.blockClass,ee.blockSelector,!1)||forEach(Array.prototype.slice.call(Gn.childNodes),function(Wn){ee.genAdds(Wn)})}}function dt(ee,Gn){ee.remove(Gn),forEach(Gn.childNodes,function(qn){dt(ee,qn)})}function Dt(ee,Gn,qn){return ee.length!==0&&xt(ee,Gn,qn)}function xt(ee,Wn,qn){var Vn,Wn=Wn.parentNode;return!!Wn&&(Vn=qn.getId(Wn),!!ee.some(function(jn){return jn.id===Vn})||xt(ee,Wn,qn))}function Bt(ee,Gn){return ee.size!==0&&Et(ee,Gn)}function Et(ee,Gn){return Gn=Gn.parentNode,!!Gn&&(!!ee.has(Gn)||Et(ee,Gn))}qe.prototype.get=function(ee){return this.nodesArray[ee]},qe.prototype.addNode=function(ee){var Gn,qn={value:ee,previous:null,next:null};ee.__ln=qn,ee.previousSibling&&Ft(ee.previousSibling)?(Gn=ee.previousSibling.__ln.next,qn.next=Gn,qn.previous=ee.previousSibling.__ln,ee.previousSibling.__ln.next=qn,Gn&&(Gn.previous=qn)):ee.nextSibling&&Ft(ee.nextSibling)&&ee.nextSibling.__ln.previous?(Gn=ee.nextSibling.__ln.previous,qn.previous=Gn,qn.next=ee.nextSibling.__ln,ee.nextSibling.__ln.previous=qn,Gn&&(Gn.next=qn)):(this.head&&(this.head.previous=qn),qn.next=this.head,this.head=qn),this.nodesArray.push(qn),this.length++},qe.prototype.removeNode=function(ee){var Gn=this.nodesArray.indexOf(ee.__ln),Gn=(-1<Gn&&this.nodesArray.splice(Gn,1),ee.__ln);this.head&&(Gn.previous?(Gn.previous.next=Gn.next,Gn.next&&(Gn.next.previous=Gn.previous)):(this.head=Gn.next,this.head&&(this.head.previous=null)),ee.__ln&&delete ee.__ln,this.length--)},ge.prototype.init=function(ee){for(var Gn=["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager"],qn=0;qn<Gn.length;qn++){var Vn=Gn[qn];this[Vn]=ee[Vn]}},ge.prototype.freeze=function(){this.frozen=!0,this.canvasManager.freeze()},ge.prototype.unfreeze=function(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()},ge.prototype.isFrozen=function(){return this.frozen},ge.prototype.lock=function(){this.locked=!0,this.canvasManager.lock()},ge.prototype.unlock=function(){this.locked=!1,this.canvasManager.unlock(),this.emit()},ge.prototype.reset=function(){this.shadowDomManager.reset(),this.canvasManager.reset()};var Ce=[],Wt=typeof CSSGroupingRule!="undefined",Ut=typeof CSSMediaRule!="undefined",Gt=typeof CSSSupportsRule!="undefined",Zt=typeof CSSConditionRule!="undefined";function ze(ee){try{if("composedPath"in ee){var Gn=ee.composedPath();if(Gn.length)return Gn[0]}else if("path"in ee&&ee.path.length)return ee.path[0];return ee.target}catch(qn){return ee.target}}function Vt(Vn,Gn){var qn=new ge,Vn=(Ce.push(qn),qn.init(Vn),window.MutationObserver||window.__rrMutationObserver),Wn=(Wn=(jn=window==null?void 0:window.Zone)==null?void 0:jn.__symbol__)==null?void 0:Wn.call(jn,"MutationObserver"),jn=new(Vn=Wn&&window[Wn]?window[Wn]:Vn)(qn.processMutations.bind(qn));return jn.observe(Gn,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),jn}function Gr(ee){var Gn,qn,Vn,Wn,jn=ee.mousemoveCb,Qn=ee.sampling,Jn=ee.doc,eo=ee.mirror;return Qn.mousemove===!1?function(){}:(ee=typeof Qn.mousemove=="number"?Qn.mousemove:50,Qn=typeof Qn.mousemoveCallback=="number"?Qn.mousemoveCallback:500,qn=[],Vn=Ke(function(Xn){var to=Date.now()-Gn;jn(qn.map(function(io){return io.timeOffset-=to,io}),Xn),qn=[],Gn=null},Qn),Qn=Ke(function(Xn){var to=ze(Xn),ro=Mt(Xn)?Xn.changedTouches[0]:Xn,io=ro.clientX,ro=ro.clientY;Gn=Gn||Date.now(),qn.push({x:io,y:ro,id:eo.getId(to),timeOffset:Date.now()-Gn}),Vn(typeof DragEvent!="undefined"&&Xn instanceof DragEvent?w$7.Drag:Xn instanceof MouseEvent?w$7.MouseMove:w$7.TouchMove)},ee,{trailing:!1}),Wn=[_$4("mousemove",Qn,Jn),_$4("touchmove",Qn,Jn),_$4("drag",Qn,Jn)],function(){forEach(Wn,function(Xn){return Xn()})})}function Zr(eo){var Gn,qn,Vn=eo.mouseInteractionCb,Wn=eo.doc,jn=eo.mirror,Qn=eo.blockClass,Jn=eo.blockSelector,eo=eo.sampling;return eo.mouseInteraction===!1?function(){}:(Gn=eo.mouseInteraction===!0||eo.mouseInteraction===void 0?{}:eo.mouseInteraction,qn=[],forEach(Object.keys(ut).filter(function(Xn){return Number.isNaN(Number(Xn))&&!Xn.endsWith("_Departed")&&Gn[Xn]!==!1}),function(Xn){var to,io=Xn.toLowerCase();to=Xn,qn.push(_$4(io,function(ro){var no,oo=ze(ro);K$3(oo,Qn,Jn,!0)||(ro=Mt(ro)?ro.changedTouches[0]:ro)&&(oo=jn.getId(oo),no=ro.clientX,ro=ro.clientY,Vn({type:ut[to],id:oo,x:no,y:ro}))},Wn))}),function(){forEach(qn,function(Xn){return Xn()})})}function _t(ee){var Gn=ee.scrollCb,qn=ee.doc,Vn=ee.mirror,Wn=ee.blockClass,jn=ee.blockSelector;return _$4("scroll",Ke(function(Xn){var Jn,eo,Xn=ze(Xn);Xn&&!K$3(Xn,Wn,jn,!0)&&(Jn=Vn.getId(Xn),Xn===qn?(eo=qn.scrollingElement||qn.documentElement,Gn({id:Jn,x:eo.scrollLeft,y:eo.scrollTop})):Gn({id:Jn,x:Xn.scrollLeft,y:Xn.scrollTop}))},ee.sampling.scroll||100),qn)}function Vr(ee){var Gn=ee.viewportResizeCb,qn=-1,Vn=-1;return _$4("resize",Ke(function(){var Wn=wt(),jn=kt();qn===Wn&&Vn===jn||(Gn({width:Number(jn),height:Number(Wn)}),qn=Wn,Vn=jn)},200),window)}function Kt(ee,Gn){var qn,Vn={};for(qn in ee)ee.hasOwnProperty(qn)&&(Vn[qn]=ee[qn]);return Gn||delete Vn.userTriggered,Vn}var _r=["INPUT","TEXTAREA","SELECT"],zt=new X$2;function Kr(ee){var Gn=ee.inputCb,qn=ee.doc,Vn=ee.mirror,Wn=ee.blockClass,jn=ee.blockSelector,Qn=ee.ignoreClass,Jn=ee.maskInputOptions,eo=ee.maskInputFn,Xn=ee.sampling,to=ee.userTriggeredOnInput;function io(wo){var so,co,uo,fo=ze(wo),wo=wo.isTrusted;!(fo=fo&&fo.tagName==="OPTION"?fo.parentElement:fo)||!fo.tagName||_r.indexOf(fo.tagName)<0||K$3(fo,Wn,jn,!0)||(so=fo.type,fo.classList.contains(Qn))||(uo=fo.value,co=!1,so==="radio"||so==="checkbox"?co=fo.checked:(Jn[fo.tagName.toLowerCase()]||Jn[so])&&(uo=ot({maskInputOptions:Jn,tagName:fo.tagName,type:so,value:uo,maskInputFn:eo})),ro(fo,Kt({text:uo,isChecked:co,userTriggered:wo},to)),uo=fo.name,so==="radio"&&uo&&co&&forEach(qn.querySelectorAll('input[type="radio"][name="'+uo+'"]'),function(lo){lo!==fo&&ro(lo,Kt({text:lo.value,isChecked:!co,userTriggered:!1},to))}))}function ro(ao,so){var co=zt.get(ao);co&&co.text===so.text&&co.isChecked===so.isChecked||(zt.set(ao,so),co=Vn.getId(ao),Gn(objectAssign(objectAssign({},so),{id:co})))}var no=(Xn.input==="last"?["change"]:["input","change"]).map(function(ao){return _$4(ao,io,qn)}),oo=qn.defaultView;return oo?(ee=oo.Object.getOwnPropertyDescriptor(oo.HTMLInputElement.prototype,"value"),Xn=[[oo.HTMLInputElement.prototype,"value"],[oo.HTMLInputElement.prototype,"checked"],[oo.HTMLSelectElement.prototype,"value"],[oo.HTMLTextAreaElement.prototype,"value"],[oo.HTMLSelectElement.prototype,"selectedIndex"],[oo.HTMLOptionElement.prototype,"selected"]],ee&&ee.set&&(ee=Xn.map(function(ao){return Xe(ao[0],ao[1],{set:function(){io({target:this})}},!1,oo)}),no=no.concat(ee)),function(){forEach(no,function(ao){ao()})}):function(){forEach(no,function(ao){ao()})}}function je(ee){return ee=ee,Gn=[],Wt&&ee.parentRule instanceof CSSGroupingRule||Ut&&ee.parentRule instanceof CSSMediaRule||Gt&&ee.parentRule instanceof CSSSupportsRule||Zt&&ee.parentRule instanceof CSSConditionRule?(qn=Array.prototype.slice.call(ee.parentRule.cssRules||[]).indexOf(ee),Gn.unshift(qn)):ee.parentStyleSheet&&(qn=Array.prototype.slice.call(ee.parentStyleSheet.cssRules||[]).indexOf(ee),Gn.unshift(qn)),Gn;var Gn,qn}function me$1(ee,Gn,qn){var Vn,Wn;return ee?(ee.ownerNode?Vn=Gn.getId(ee.ownerNode):Wn=qn.getId(ee),{styleId:Wn,id:Vn}):{}}function zr(ee,Gn){var qn=ee.styleSheetRuleCb,Vn=ee.mirror,Wn=ee.stylesheetManager,jn=Gn.win,Qn=jn.CSSStyleSheet.prototype.insertRule;jn.CSSStyleSheet.prototype.insertRule=function(ro,no){var ao=me$1(this,Vn,Wn.styleMirror),oo=ao.id,ao=ao.styleId;return(oo&&oo!==-1||ao&&ao!==-1)&&qn({id:oo,styleId:ao,adds:[{rule:ro,index:no}]}),Qn.apply(this,[ro,no])};var Jn,eo,Xn=jn.CSSStyleSheet.prototype.deleteRule,to=(jn.CSSStyleSheet.prototype.deleteRule=function(ro){var oo=me$1(this,Vn,Wn.styleMirror),no=oo.id,oo=oo.styleId;return(no&&no!==-1||oo&&oo!==-1)&&qn({id:no,styleId:oo,removes:[{index:ro}]}),Xn.apply(this,[ro])},jn.CSSStyleSheet.prototype.replace&&(Jn=jn.CSSStyleSheet.prototype.replace,jn.CSSStyleSheet.prototype.replace=function(ro){var oo=me$1(this,Vn,Wn.styleMirror),no=oo.id,oo=oo.styleId;return(no&&no!==-1||oo&&oo!==-1)&&qn({id:no,styleId:oo,replace:ro}),Jn.apply(this,[ro])}),jn.CSSStyleSheet.prototype.replaceSync&&(eo=jn.CSSStyleSheet.prototype.replaceSync,jn.CSSStyleSheet.prototype.replaceSync=function(ro){var oo=me$1(this,Vn,Wn.styleMirror),no=oo.id,oo=oo.styleId;return(no&&no!==-1||oo&&oo!==-1)&&qn({id:no,styleId:oo,replaceSync:ro}),eo.apply(this,[ro])}),{}),io=(Wt?to.CSSGroupingRule=jn.CSSGroupingRule:(Ut&&(to.CSSMediaRule=jn.CSSMediaRule),Zt&&(to.CSSConditionRule=jn.CSSConditionRule),Gt&&(to.CSSSupportsRule=jn.CSSSupportsRule)),{});return forEach(Object.keys(to),function(ro){var no=to[ro];io[ro]={insertRule:no.prototype.insertRule,deleteRule:no.prototype.deleteRule},no.prototype.insertRule=function(oo,ao){var so,uo=me$1(this.parentStyleSheet,Vn,Wn.styleMirror),co=uo.id,uo=uo.styleId;return(co&&co!==-1||uo&&uo!==-1)&&((so=je(this)).push(ao||0),qn({id:co,styleId:uo,adds:[{rule:oo,index:so}]})),io[ro].insertRule.apply(this,[oo,ao])},no.prototype.deleteRule=function(oo){var ao,co=me$1(this.parentStyleSheet,Vn,Wn.styleMirror),so=co.id,co=co.styleId;return(so&&so!==-1||co&&co!==-1)&&((ao=je(this)).push(oo),qn({id:so,styleId:co,removes:[{index:ao}]})),io[ro].deleteRule.apply(this,[oo])}}),function(){jn.CSSStyleSheet.prototype.insertRule=Qn,jn.CSSStyleSheet.prototype.deleteRule=Xn,Jn&&(jn.CSSStyleSheet.prototype.replace=Jn),eo&&(jn.CSSStyleSheet.prototype.replaceSync=eo),forEach(Object.keys(to),function(ro){var no=to[ro];no.prototype.insertRule=io[ro].insertRule,no.prototype.deleteRule=io[ro].deleteRule})}}function Yt(jn,Gn){var qn=jn.mirror,Vn=jn.stylesheetManager,Wn=Gn.nodeName==="#document"?qn.getId(Gn):qn.getId(Gn.host),jn=Gn.nodeName==="#document"?Gn.defaultView&&Gn.defaultView.Document:Gn.ownerDocument&&Gn.ownerDocument.defaultView&&Gn.ownerDocument.defaultView.ShadowRoot,Qn=Object.getOwnPropertyDescriptor(jn&&jn.prototype,"adoptedStyleSheets");return Wn!==null&&Wn!==-1&&jn&&Qn?(Object.defineProperty(Gn,"adoptedStyleSheets",{configurable:Qn.configurable,enumerable:Qn.enumerable,get:function(){return Qn.get&&Qn.get.call(this)},set:function(Jn){var eo=Qn.set&&Qn.set.call(this,Jn);if(Wn!==null&&Wn!==-1)try{Vn.adoptStyleSheets(Jn,Wn)}catch(Xn){}return eo}}),function(){Object.defineProperty(Gn,"adoptedStyleSheets",{configurable:Qn.configurable,enumerable:Qn.enumerable,get:Qn.get,set:Qn.set})}):function(){}}function Yr(ee,Gn){var qn=ee.styleDeclarationCb,Vn=ee.mirror,Wn=ee.ignoreCSSAttributes,jn=ee.stylesheetManager,Qn=Gn.win,Jn=Qn.CSSStyleDeclaration.prototype.setProperty,eo=(Qn.CSSStyleDeclaration.prototype.setProperty=function(Xn,to,io){var ro,no;return!Wn.has(Xn)&&(ro=(no=me$1((no=this.parentRule)&&no.parentStyleSheet,Vn,jn.styleMirror)).id,no=no.styleId,ro&&ro!==-1||no&&no!==-1)&&qn({id:ro,styleId:no,set:{property:Xn,value:to,priority:io},index:je(this.parentRule)}),Jn.apply(this,[Xn,to,io])},Qn.CSSStyleDeclaration.prototype.removeProperty);return Qn.CSSStyleDeclaration.prototype.removeProperty=function(Xn){var to,io;return!Wn.has(Xn)&&(to=(io=me$1((io=this.parentRule)&&io.parentStyleSheet,Vn,jn.styleMirror)).id,io=io.styleId,to&&to!==-1||io&&io!==-1)&&qn({id:to,styleId:io,remove:{property:Xn},index:je(this.parentRule)}),eo.apply(this,[Xn])},function(){Qn.CSSStyleDeclaration.prototype.setProperty=Jn,Qn.CSSStyleDeclaration.prototype.removeProperty=eo}}function Jr(ee){function Gn(eo){return Ke(function(oo){var to,io,ro,no,oo=ze(oo);oo&&!K$3(oo,Vn,Wn,!0)&&(to=oo.currentTime,io=oo.volume,ro=oo.muted,no=oo.playbackRate,qn({type:eo,id:jn.getId(oo),currentTime:to,volume:io,muted:ro,playbackRate:no}))},Qn.media||500)}var qn=ee.mediaInteractionCb,Vn=ee.blockClass,Wn=ee.blockSelector,jn=ee.mirror,Qn=ee.sampling,Jn=[_$4("play",Gn(0)),_$4("pause",Gn(1)),_$4("seeked",Gn(2)),_$4("volumechange",Gn(3)),_$4("ratechange",Gn(4))];return function(){forEach(Jn,function(eo){eo()})}}function Hr(jn){var Gn,qn,Vn,Wn=jn.fontCb,jn=jn.doc,Qn=jn.defaultView;return Qn?(Gn=[],qn=new X$2,Vn=Qn.FontFace,Qn.FontFace=function(Jn,eo,Xn){var to=new Vn(Jn,eo,Xn);return qn.set(to,{family:Jn,buffer:typeof eo!="string",descriptors:Xn,fontSource:typeof eo=="string"?eo:JSON.stringify(Array.prototype.slice.call(new Uint8Array(eo)))}),to},jn=Oe$1(jn.fonts,"add",function(Jn){return function(eo){return setTimeout(function(){var Xn=qn.get(eo);Xn&&(Wn(Xn),qn.delete?qn.delete(eo):qn.remove&&qn.remove(eo))},0),Jn.apply(this,[eo])}}),Gn.push(function(){Qn.FontFace=Vn}),Gn.push(jn),function(){forEach(Gn,function(Jn){Jn()})}):function(){}}function Qr(ee){function Gn(){var eo=qn.getSelection();if(eo&&!(Jn&&eo&&eo.isCollapsed)){Jn=eo.isCollapsed||!1;for(var Xn=[],to=eo.rangeCount||0,io=0;io<to;io++){var ao=eo.getRangeAt(io),ro=ao.startContainer,no=ao.startOffset,oo=ao.endContainer,ao=ao.endOffset;K$3(ro,Wn,jn,!0)||K$3(oo,Wn,jn,!0)||Xn.push({start:Vn.getId(ro),startOffset:no,end:Vn.getId(oo),endOffset:ao})}Qn({ranges:Xn})}}var qn=ee.doc,Vn=ee.mirror,Wn=ee.blockClass,jn=ee.blockSelector,Qn=ee.selectionCb,Jn=!0;return Gn(),_$4("selectionchange",Gn)}function Pr(ee,Gn){var qn=ee.mutationCb,Vn=ee.mousemoveCb,Wn=ee.mouseInteractionCb,jn=ee.scrollCb,Qn=ee.viewportResizeCb,Jn=ee.inputCb,eo=ee.mediaInteractionCb,Xn=ee.styleSheetRuleCb,to=ee.styleDeclarationCb,io=ee.canvasMutationCb,ro=ee.fontCb,no=ee.selectionCb;ee.mutationCb=function(){Gn.mutation&&Gn.mutation.apply(null,arguments),qn.apply(null,arguments)},ee.mousemoveCb=function(){Gn.mousemove&&Gn.mousemove.apply(null,arguments),Vn.apply(null,arguments)},ee.mouseInteractionCb=function(){Gn.mouseInteraction&&Gn.mouseInteraction.apply(null,arguments),Wn.apply(null,arguments)},ee.scrollCb=function(){Gn.scroll&&Gn.scroll.apply(null,arguments),jn.apply(null,arguments)},ee.viewportResizeCb=function(){Gn.viewportResize&&Gn.viewportResize.apply(null,arguments),Qn.apply(null,arguments)},ee.inputCb=function(){Gn.input&&Gn.input.apply(null,arguments),Jn.apply(null,arguments)},ee.mediaInteractionCb=function(){Gn.mediaInteaction&&Gn.mediaInteaction.apply(null,arguments),eo.apply(null,arguments)},ee.styleSheetRuleCb=function(){Gn.styleSheetRule&&Gn.styleSheetRule.apply(null,arguments),Xn.apply(null,arguments)},ee.styleDeclarationCb=function(){Gn.styleDeclaration&&Gn.styleDeclaration.apply(null,arguments),to.apply(null,arguments)},ee.canvasMutationCb=function(){Gn.canvasMutation&&Gn.canvasMutation.apply(null,arguments),io.apply(null,arguments)},ee.fontCb=function(){Gn.font&&Gn.font.apply(null,arguments),ro.apply(null,arguments)},ee.selectionCb=function(){Gn.selection&&Gn.selection.apply(null,arguments),no.apply(null,arguments)}}function Xr(ee,Gn){var qn=ee.doc.defaultView;if(!qn)return function(){};Pr(ee,Gn=Gn||{});for(var Vn=Vt(ee,ee.doc),Wn=Gr(ee),jn=Zr(ee),Qn=_t(ee),Jn=Vr(ee),eo=Kr(ee),Xn=Jr(ee),to=zr(ee,{win:qn}),io=Yt(ee,ee.doc),ro=Yr(ee,{win:qn}),no=ee.collectFonts?Hr(ee):function(){},oo=Qr(ee),ao=[],so=0;so<ee.plugins.length;so++){var co=ee.plugins[so];ao.push(co.observer(co.callback,qn,co.options))}return function(){forEach(Ce,function(uo){uo.reset()}),Vn.disconnect(),Wn(),jn(),Qn(),Jn(),eo(),Xn(),to(),io(),ro(),no(),oo(),forEach(ao,function(uo){uo()})}}function de$2(ee){this.generateIdFn=ee,this.iframeIdToRemoteIdMap=new X$2,this.iframeRemoteIdToIdMap=new X$2}function ie$1(ee){this.iframes=new X$2,this.crossOriginIframeMap=new X$2,this.crossOriginIframeMirror=new de$2(Ct),this.mutationCb=ee.mutationCb,this.wrappedEmit=ee.wrappedEmit,this.stylesheetManager=ee.stylesheetManager,this.recordCrossOriginIframes=ee.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new de$2(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=ee.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}function $e(ee){this.shadowDoms=new r$4,this.restorePatches=[],this.mutationCb=ee.mutationCb,this.scrollCb=ee.scrollCb,this.bypassOptions=ee.bypassOptions,this.mirror=ee.mirror;var Gn=this;this.restorePatches.push(Oe$1(Element.prototype,"attachShadow",function(qn){return function(Vn){return Vn=qn.call(this,Vn),this.shadowRoot&&Gn.addShadowRoot(this.shadowRoot,this.ownerDocument),Vn}}))}function qr(ee,Gn){var qn={};for(Wn in ee)Object.prototype.hasOwnProperty.call(ee,Wn)&&Gn.indexOf(Wn)<0&&(qn[Wn]=ee[Wn]);if(ee!=null&&typeof Object.getOwnPropertySymbols=="function")for(var Vn=0,Wn=Object.getOwnPropertySymbols(ee);Vn<Wn.length;Vn++)Gn.indexOf(Wn[Vn])<0&&Object.prototype.propertyIsEnumerable.call(ee,Wn[Vn])&&(qn[Wn[Vn]]=ee[Wn[Vn]]);return qn}de$2.prototype.getId=function(ee,Gn,qn,Vn){return qn=qn||this.getIdToRemoteIdMap(ee),Vn=Vn||this.getRemoteIdToIdMap(ee),ee=qn.get(Gn),ee||(ee=this.generateIdFn(),qn.set(Gn,ee),Vn.set(ee,Gn)),ee},de$2.prototype.getIds=function(ee,Gn){var qn=this,Vn=this.getIdToRemoteIdMap(ee),Wn=this.getRemoteIdToIdMap(ee);return Gn.map(function(jn){return qn.getId(ee,jn,Vn,Wn)})},de$2.prototype.getRemoteId=function(ee,Gn,qn){return qn=qn||this.getRemoteIdToIdMap(ee),typeof Gn!="number"?Gn:qn.get(Gn)||-1},de$2.prototype.getRemoteIds=function(ee,Gn){var qn=this,Vn=this.getRemoteIdToIdMap(ee);return Gn.map(function(Wn){return qn.getRemoteId(ee,Wn,Vn)})},de$2.prototype.reset=function(ee){ee?this.iframeIdToRemoteIdMap.delete?this.iframeIdToRemoteIdMap.delete(ee):this.iframeIdToRemoteIdMap.remove&&this.iframeIdToRemoteIdMap.remove(ee):(this.iframeIdToRemoteIdMap=new X$2,this.iframeRemoteIdToIdMap=new X$2)},de$2.prototype.getIdToRemoteIdMap=function(ee){var Gn=this.iframeIdToRemoteIdMap.get(ee);return Gn||(Gn=new i$4,this.iframeIdToRemoteIdMap.set(ee,Gn)),Gn},de$2.prototype.getRemoteIdToIdMap=function(ee){var Gn=this.iframeRemoteIdToIdMap.get(ee);return Gn||(Gn=new i$4,this.iframeRemoteIdToIdMap.set(ee,Gn)),Gn},ie$1.prototype.addIframe=function(ee){this.iframes.set(ee,!0),ee.contentWindow&&this.crossOriginIframeMap.set(ee.contentWindow,ee)},ie$1.prototype.addLoadListener=function(ee){this.loadListener=ee},ie$1.prototype.attachIframe=function(ee,Gn){this.mutationCb({adds:[{parentId:this.mirror.getId(ee),nextId:null,node:Gn}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),this.loadListener&&this.loadListener.call(this,ee),ee.contentDocument&&ee.contentDocument.adoptedStyleSheets&&0<ee.contentDocument.adoptedStyleSheets.length&&this.stylesheetManager.adoptStyleSheets(ee.contentDocument.adoptedStyleSheets,this.mirror.getId(ee.contentDocument))},ie$1.prototype.handleMessage=function(ee){var Gn;ee.data.type==="rrweb"&&ee.source&&(Gn=this.crossOriginIframeMap.get(ee.source))&&(Gn=this.transformCrossOriginEvent(Gn,ee.data.event))&&this.wrappedEmit(Gn,ee.data.isCheckout)},ie$1.prototype.transformCrossOriginEvent=function(ee,Gn){var qn=this;switch(Gn.type){case k$2.FullSnapshot:return this.crossOriginIframeMirror.reset(ee),this.crossOriginIframeStyleMirror.reset(ee),this.replaceIdOnNode(Gn.data.node,ee),{timestamp:Gn.timestamp,type:k$2.IncrementalSnapshot,data:{source:w$7.Mutation,adds:[{parentId:this.mirror.getId(ee),nextId:null,node:Gn.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case k$2.Meta:case k$2.Load:case k$2.DomContentLoaded:return!1;case k$2.Plugin:return Gn;case k$2.Custom:return this.replaceIds(Gn.data.payload,ee,["id","parentId","previousId","nextId"]),Gn;case k$2.IncrementalSnapshot:switch(Gn.data.source){case w$7.Mutation:return forEach(Gn.data.adds,function(Vn){qn.replaceIds(Vn,ee,["parentId","nextId","previousId"]),qn.replaceIdOnNode(Vn.node,ee)}),forEach(Gn.data.removes,function(Vn){qn.replaceIds(Vn,ee,["parentId","id"])}),forEach(Gn.data.attributes,function(Vn){qn.replaceIds(Vn,ee,["id"])}),forEach(Gn.data.texts,function(Vn){qn.replaceIds(Vn,ee,["id"])}),Gn;case w$7.Drag:case w$7.TouchMove:case w$7.MouseMove:return forEach(Gn.data.positions,function(Vn){qn.replaceIds(Vn,ee,["id"])}),Gn;case w$7.ViewportResize:return!1;case w$7.MediaInteraction:case w$7.MouseInteraction:case w$7.Scroll:case w$7.CanvasMutation:case w$7.Input:return this.replaceIds(Gn.data,ee,["id"]),Gn;case w$7.StyleSheetRule:case w$7.StyleDeclaration:return this.replaceIds(Gn.data,ee,["id"]),this.replaceStyleIds(Gn.data,ee,["styleId"]),Gn;case w$7.Font:return Gn;case w$7.Selection:return forEach(Gn.data.ranges,function(Vn){qn.replaceIds(Vn,ee,["start","end"])}),Gn;case w$7.AdoptedStyleSheet:return this.replaceIds(Gn.data,ee,["id"]),this.replaceStyleIds(Gn.data,ee,["styleIds"]),Gn.data.styles&&forEach(Gn.data.styles,function(Vn){qn.replaceStyleIds(Vn,ee,["styleId"])}),Gn}}},ie$1.prototype.replace=function(ee,Gn,qn,Vn){for(var Wn=0;Wn<Vn.length;Wn++){var jn=Vn[Wn];!Array.isArray(Gn[jn])&&typeof Gn[jn]!="number"||(Array.isArray(Gn[jn])?Gn[jn]=ee.getIds(qn,Gn[jn]):Gn[jn]=ee.getId(qn,Gn[jn]))}return Gn},ie$1.prototype.replaceIds=function(ee,Gn,qn){return this.replace(this.crossOriginIframeMirror,ee,Gn,qn)},ie$1.prototype.replaceStyleIds=function(ee,Gn,qn){return this.replace(this.crossOriginIframeStyleMirror,ee,Gn,qn)},ie$1.prototype.replaceIdOnNode=function(ee,Gn){var qn=this;this.replaceIds(ee,Gn,["id"]),"childNodes"in ee&&forEach(ee.childNodes,function(Vn){qn.replaceIdOnNode(Vn,Gn)})},$e.prototype.addShadowRoot=function(ee,Gn){var qn=this;Ve(ee)&&!this.shadowDoms.has(ee)&&(this.shadowDoms.add(ee),Vt(objectAssign(objectAssign({},this.bypassOptions),{doc:Gn,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),ee),_t(objectAssign(objectAssign({},this.bypassOptions),{scrollCb:this.scrollCb,doc:ee,mirror:this.mirror})),setTimeout(function(){ee.adoptedStyleSheets&&0<ee.adoptedStyleSheets.length&&qn.bypassOptions.stylesheetManager.adoptStyleSheets(ee.adoptedStyleSheets,qn.mirror.getId(ee.host)),Yt({mirror:qn.mirror,stylesheetManager:qn.bypassOptions.stylesheetManager},ee)},0))},$e.prototype.observeAttachShadow=function(ee){var Gn;ee.contentWindow&&(Gn=this).restorePatches.push(Oe$1(ee.contentWindow.HTMLElement.prototype,"attachShadow",function(qn){return function(Vn){return Vn=qn.call(this,Vn),this.shadowRoot&&Gn.addShadowRoot(this.shadowRoot,ee.contentDocument),Vn}}))},$e.prototype.reset=function(){forEach(this.restorePatches,function(ee){ee()}),this.shadowDoms=new r$4};for(var Le="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",jr=typeof Uint8Array=="undefined"?[]:new Uint8Array(256),et=0;et<Le.length;et++)jr[Le.charCodeAt(et)]=et;var $r=function(ee){for(var Gn=new Uint8Array(ee),qn=Gn.length,Vn="",Wn=0;Wn<qn;Wn+=3)Vn=(Vn=(Vn=(Vn+=Le[Gn[Wn]>>2])+Le[(3&Gn[Wn])<<4|Gn[Wn+1]>>4])+Le[(15&Gn[Wn+1])<<2|Gn[Wn+2]>>6])+Le[63&Gn[Wn+2]];return qn%3==2?Vn=Vn.substring(0,Vn.length-1)+"=":qn%3==1&&(Vn=Vn.substring(0,Vn.length-2)+"=="),Vn},Jt=new i$4;function ea(ee,Gn){var qn=Jt.get(ee);return qn||(qn=new i$4,Jt.set(ee,qn)),qn.has(Gn)||qn.set(Gn,[]),qn.get(Gn)}function Ht(ee,Gn,qn){if(ee&&(Pt(ee,Gn)||typeof ee=="object"))return(qn=(Gn=ea(qn,ee.constructor.name)).indexOf(ee))===-1&&(qn=Gn.length,Gn.push(ee)),qn}function tt$1(ee,Gn,qn){return ee instanceof Array?ee.map(function(Vn){return tt$1(Vn,Gn,qn)}):ee===null?ee:ee instanceof Float32Array||ee instanceof Float64Array||ee instanceof Int32Array||ee instanceof Uint32Array||ee instanceof Uint8Array||ee instanceof Uint16Array||ee instanceof Int16Array||ee instanceof Int8Array||ee instanceof Uint8ClampedArray?{rr_type:ee.constructor.name,args:[Object.values(ee)]}:window.ArrayBuffer&&ee instanceof ArrayBuffer?{rr_type:ee.constructor.name,base64:$r(ee)}:ee instanceof DataView?{rr_type:ee.constructor.name,args:[tt$1(ee.buffer,Gn,qn),ee.byteOffset,ee.byteLength]}:ee instanceof HTMLImageElement?{rr_type:ee.constructor.name,src:ee.src}:ee instanceof HTMLCanvasElement?{rr_type:"HTMLImageElement",src:ee.toDataURL()}:ee instanceof ImageData?{rr_type:ee.constructor.name,args:[tt$1(ee.data,Gn,qn),ee.width,ee.height]}:Pt(ee,Gn)||typeof ee=="object"?{rr_type:ee.constructor.name,index:Ht(ee,Gn,qn)}:ee}function Qt(ee,Gn,qn){return Array.prototype.slice.call(ee).map(function(Vn){return tt$1(Vn,Gn,qn)})}function Pt(ee,Gn){return!!["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter(function(qn){return typeof Gn[qn]=="function"}).some(function(qn){return ee instanceof Gn[qn]})}function ta(ee,Gn,qn,Vn){for(var Wn=[],jn=Object.getOwnPropertyNames(Gn.CanvasRenderingContext2D.prototype),Qn=0;Qn<jn.length;Qn++){var Jn,eo=jn[Qn];try{typeof Gn.CanvasRenderingContext2D.prototype[eo]=="function"&&(Jn=Oe$1(Gn.CanvasRenderingContext2D.prototype,eo,function(to){return function(){var io=Array.prototype.slice.call(arguments),ro=this;return K$3(this.canvas,qn,Vn,!0)||setTimeout(function(){var no=Qt(io,Gn,ro);ee(ro.canvas,{type:Fe["2D"],property:eo,args:no})},0),to.apply(this,io)}}),Wn.push(Jn))}catch(to){var Xn=Xe(Gn.CanvasRenderingContext2D.prototype,eo,{set:function(io){ee(this.canvas,{type:Fe["2D"],property:eo,args:[io],setter:!0})}});Wn.push(Xn)}}return function(){forEach(Wn,function(to){to()})}}function Xt(ee,Gn,qn){var Vn=[];try{var Wn=Oe$1(ee.HTMLCanvasElement.prototype,"getContext",function(jn){return function(){var Qn=arguments[0],Jn=Array.prototype.slice.call(arguments,1);return K$3(this,Gn,qn,!0)||"__context"in this||(this.__context=Qn),jn.apply(this,[Qn].concat(Jn))}});Vn.push(Wn)}catch(jn){console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return function(){forEach(Vn,function(jn){jn()})}}function qt(ee,Gn,qn,Vn,Wn,jn,Qn){for(var Jn=[],eo=Object.getOwnPropertyNames(ee),Xn=0;Xn<eo.length;Xn++){var to,io=eo[Xn];if(["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].indexOf(io)===-1)try{typeof ee[io]=="function"&&(to=Oe$1(ee,io,function(no){return function(){var oo=Array.prototype.slice.call(arguments),ao=no.apply(this,oo);return Ht(ao,Qn,this),K$3(this.canvas,Vn,Wn,!0)||(oo=Qt(oo,Qn,this),qn(this.canvas,{type:Gn,property:io,args:oo})),ao}}),Jn.push(to))}catch(no){var ro=Xe(ee,io,{set:function(oo){qn(this.canvas,{type:Gn,property:io,args:[oo],setter:!0})}});Jn.push(ro)}}return Jn}function ra(ee,Gn,qn,Vn,Wn){for(var jn=[],Qn=qt(Gn.WebGLRenderingContext.prototype,Fe.WebGL,ee,qn,Vn,Wn,Gn),Jn=0;Jn<Qn.length;Jn++)jn.push(Qn[Jn]);if(Gn.WebGL2RenderingContext!==void 0)for(var eo=qt(Gn.WebGL2RenderingContext.prototype,Fe.WebGL2,ee,qn,Vn,Wn,Gn),Xn=0;Xn<eo.length;Xn++)jn.push(eo[Xn]);return function(){forEach(jn,function(to){to()})}}function aa(ee,Gn){return compatibleFromB64(ee)}function na(Vn,Gn,qn){var Vn=aa(Vn),Wn=Vn.indexOf("\n",10)+1,Vn=Vn.substring(Wn)+"",Wn=createCompatibleBlob([Vn],{type:"application/javascript"});return Wn instanceof blobPolyfill?"data:application/javascript;base64,"+compatibleToB64(Vn):URL.createObjectURL(Wn)}function ia(ee,Gn,qn){var Vn;return typeof Worker!="undefined"?function(Wn){return Vn=Vn||na(ee),new Worker(Vn,Wn)}:function(){return{postMessage:function(){},onmessage:null}}}function oa(ee,Gn,qn){return ia(ee)}var sa=oa("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");function q$2(ee){var Gn=this,qn=(this.pendingCanvasMutations=new i$4,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=function(eo,Xn){(Gn.rafStamps.invokeId&&Gn.rafStamps.latestId!==Gn.rafStamps.invokeId||!Gn.rafStamps.invokeId)&&(Gn.rafStamps.invokeId=Gn.rafStamps.latestId),Gn.pendingCanvasMutations.has(eo)||Gn.pendingCanvasMutations.set(eo,[]),Gn.pendingCanvasMutations.get(eo).push(Xn)},ee.sampling||"all"),Vn=ee.win,Wn=ee.blockClass,jn=ee.blockSelector,Qn=ee.recordCanvas,Jn=ee.dataURLOptions;this.mutationCb=ee.mutationCb,this.mirror=ee.mirror,Qn&&qn==="all"&&this.initCanvasMutationObserver(Vn,Wn,jn),Qn&&typeof qn=="number"&&this.initCanvasFPSObserver(qn,Vn,Wn,jn,{dataURLOptions:Jn})}function De$1(ee){this.trackedLinkElements=new r$4,this.styleMirror=new Ie,this.mutationCb=ee.mutationCb,this.adoptedStyleSheetCb=ee.adoptedStyleSheetCb}function U$8(ee){return objectAssign(objectAssign({},ee),{timestamp:Date.now()})}q$2.prototype.reset=function(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()},q$2.prototype.freeze=function(){this.frozen=!0},q$2.prototype.unfreeze=function(){this.frozen=!1},q$2.prototype.lock=function(){this.locked=!0},q$2.prototype.unlock=function(){this.locked=!1},q$2.prototype.initCanvasFPSObserver=function(ee,Gn,qn,Vn,Wn){var jn=this,Qn=Xt(Gn,qn,Vn),Jn=new i$4,eo=new sa;eo.onmessage=function(no){var oo,ao,so,co=no.data.id;Jn.set(co,!1),"base64"in no.data&&(oo=no.data.base64,ao=no.data.type,so=no.data.width,no=no.data.height,jn.mutationCb({id:co,type:Fe["2D"],commands:[{property:"clearRect",args:[0,0,so,no]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:oo}],type:ao}]},0,0]}]}))};var Xn=1e3/ee,to=0,io=function(no){if(!(to&&no-to<Xn)){to=no;for(var oo=function(){for(var so=[],co=Gn.document.querySelectorAll("canvas"),uo=0;uo<co.length;uo++){var fo=co[uo];K$3(fo,qn,Vn,!0)||so.push(fo)}return so}(),ao=0;ao<oo.length;ao++)(function(so){var co,uo=jn.mirror.getId(so);Jn.get(uo)||(Jn.set(uo,!0),["webgl","webgl2"].indexOf(so.__context)!==-1&&(co=so.getContext(so.__context))&&co.getContextAttributes&&co.getContextAttributes()&&co.getContextAttributes().preserveDrawingBuffer===!1&&co.clear(co.COLOR_BUFFER_BIT),createImageBitmap(so).then(function(fo){eo.postMessage({id:uo,bitmap:fo,width:so.width,height:so.height,dataURLOptions:Wn.dataURLOptions},[fo])}))})(oo[ao])}ro=requestAnimationFrame(io)},ro=requestAnimationFrame(io);this.resetObservers=function(){Qn(),cancelAnimationFrame(ro)}},q$2.prototype.initCanvasMutationObserver=function(ee,Gn,qn){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();var Vn=Xt(ee,Gn,qn),Wn=ta(this.processMutation.bind(this),ee,Gn,qn),jn=ra(this.processMutation.bind(this),ee,Gn,qn,this.mirror);this.resetObservers=function(){Vn(),Wn(),jn()}},q$2.prototype.startPendingCanvasMutationFlusher=function(){var ee=this;requestAnimationFrame(function(){return ee.flushPendingCanvasMutations()})},q$2.prototype.startRAFTimestamping=function(){function ee(qn){Gn.rafStamps.latestId=qn,requestAnimationFrame(ee)}var Gn=this;requestAnimationFrame(ee)},q$2.prototype.flushPendingCanvasMutations=function(){var ee=this,Gn=this;forEach(this.pendingCanvasMutations,function(qn,Vn){var Wn=Gn.mirror.getId(Vn);Gn.flushPendingCanvasMutationFor(Vn,Wn)}),requestAnimationFrame(function(){return ee.flushPendingCanvasMutations()})},q$2.prototype.flushPendingCanvasMutationFor=function(ee,Gn){var qn,Vn;this.frozen||this.locked||(Vn=this.pendingCanvasMutations.get(ee))&&Gn!==-1&&(qn=Vn.map(function(Wn){return qr(Wn,["type"])}),Vn=Vn[0].type,this.mutationCb({id:Gn,type:Vn,commands:qn}),this.pendingCanvasMutations.remove(ee))},De$1.prototype.attachLinkElement=function(ee,Gn){"_cssText"in Gn.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:Gn.id,attributes:Gn.attributes}]}),this.trackLinkElement(ee)},De$1.prototype.trackLinkElement=function(ee){this.trackedLinkElements.has(ee)||(this.trackedLinkElements.add(ee),this.trackStylesheetInLinkElement(ee))},De$1.prototype.adoptStyleSheets=function(ee,Gn){if(ee.length!==0){for(var qn={id:Gn,styleIds:[]},Vn=[],Wn=0;Wn<ee.length;Wn++){var jn,Qn=ee[Wn];this.styleMirror.has(Qn)?jn=this.styleMirror.getId(Qn):(jn=this.styleMirror.add(Qn),Qn=Array.prototype.slice.call(Qn.rules||CSSRule),Vn.push({styleId:jn,rules:Qn.map(function(Jn,eo){return{rule:vt(Jn),index:eo}})})),qn.styleIds.push(jn)}0<Vn.length&&(qn.styles=Vn),this.adoptedStyleSheetCb(qn)}},De$1.prototype.reset=function(){this.styleMirror.reset(),this.trackedLinkElements=new r$4},De$1.prototype.trackStylesheetInLinkElement=function(ee){};var x$4,rt,ct,Ye=!1,te=gr();function ce(ee){var Gn=(ee=ee||{}).emit,qn=ee.checkoutEveryNms,Vn=ee.checkoutEveryNth,Wn=ee.blockClass||"rr-block",jn=ee.blockSelector||null,Qn=ee.ignoreClass||"rr-ignore",Jn=ee.maskTextClass||"rr-mask",eo=ee.maskTextSelector||null,Xn=ee.inlineStylesheet===void 0||ee.inlineStylesheet,to=ee.maskAllInputs,io=ee.maskInputOptions,ro=ee.slimDOMOptions,no=ee.maskInputFn,oo=ee.maskTextFn,ao=ee.hooks,so=ee.packFn,co=ee.sampling||{},uo=ee.dataURLOptions||{},fo=ee.mousemoveWait,wo=ee.recordCanvas||!1,lo=ee.recordCrossOriginIframes||!1,po=ee.userTriggeredOnInput||!1,go=ee.collectFonts||!1,ho=ee.inlineImages||!1,mo=ee.plugins,$o=ee.keepIframeSrcFn||function(){return!1},vo=ee.ignoreCSSAttributes||new s$6([]),To=!lo||window.parent===window,So=!1;if(!To)try{window.parent.document,So=!1}catch(Do){So=!0}if(To&&!Gn)throw new Error("emit function is required");fo!==void 0&&co.mousemove===void 0&&(co.mousemove=fo),te.reset();var _o=to===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:io!==void 0?io:{password:!0},Eo=ro===!0||ro==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:ro==="all",headMetaDescKeywords:ro==="all"}:ro||{};Ur();function Ao(Do){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.Mutation},Do)}))}function Io(Do){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.Scroll},Do)}))}function Lo(Do){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.CanvasMutation},Do)}))}var Uo,Oo=0,Bo=function(Do){for(var bo=[],yo=0;yo<bo.length;yo++){var Co=bo[yo];Co.eventProcessor&&(Do=Co.eventProcessor(Do))}return Do=so?so(Do):Do},Ro=(x$4=function(Do,bo){var yo;(yo=Ce[0])==null||!yo.isFrozen()||Do.type===k$2.FullSnapshot||Do.type===k$2.IncrementalSnapshot&&Do.data.source===w$7.Mutation||forEach(Ce,function(Co){Co.unfreeze()}),To?Gn!=null&&Gn(Bo(Do),bo):So&&(yo={type:"rrweb",event:Bo(Do),isCheckout:bo},window.parent.postMessage(yo,"*")),Do.type===k$2.FullSnapshot?(Uo=Do,Oo=0):Do.type!==k$2.IncrementalSnapshot||Do.data.source===w$7.Mutation&&Do.data.isAttachIframe||(Oo++,bo=Vn&&Vn<=Oo,yo=qn&&Do.timestamp-Uo.timestamp>qn,(bo||yo)&&rt(!0))},new De$1({mutationCb:Ao,adoptedStyleSheetCb:function(Do){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.AdoptedStyleSheet},Do)}))}})),ko=new ie$1({mirror:te,mutationCb:Ao,stylesheetManager:Ro,recordCrossOriginIframes:lo,wrappedEmit:x$4});if(mo)for(var No=0;No<mo.length;No++){var Fo=mo[No];Fo.getMirror&&Fo.getMirror({nodeMirror:te,crossOriginIframeMirror:ko.crossOriginIframeMirror,crossOriginIframeStyleMirror:ko.crossOriginIframeStyleMirror})}ct=new q$2({recordCanvas:wo,mutationCb:Lo,win:window,blockClass:Wn,blockSelector:jn,mirror:te,sampling:co.canvas,dataURLOptions:uo});var Mo=new $e({mutationCb:Ao,scrollCb:Io,bypassOptions:{blockClass:Wn,blockSelector:jn,maskTextClass:Jn,maskTextSelector:eo,inlineStylesheet:Xn,maskInputOptions:_o,dataURLOptions:uo,maskTextFn:oo,maskInputFn:no,recordCanvas:wo,inlineImages:ho,sampling:co,slimDOMOptions:Eo,iframeManager:ko,stylesheetManager:Ro,canvasManager:ct,keepIframeSrcFn:$o},mirror:te});rt=function(bo){bo=bo||!1,x$4(U$8({type:k$2.Meta,data:{href:window.location.href,width:kt(),height:wt()}}),bo),Ro.reset(),forEach(Ce,function(yo){yo.lock()});var bo=Er(document,{mirror:te,blockClass:Wn,blockSelector:jn,maskTextClass:Jn,maskTextSelector:eo,inlineStylesheet:Xn,maskAllInputs:_o,maskTextFn:oo,slimDOM:Eo,dataURLOptions:uo,recordCanvas:wo,inlineImages:ho,onSerialize:function(yo){Tt(yo,te)&&ko.addIframe(yo),Nt(yo,te)&&Ro.trackLinkElement(yo),Ot(yo)&&Mo.addShadowRoot(yo.shadowRoot,document)},onIframeLoad:function(yo,Co){ko.attachIframe(yo,Co),Mo.observeAttachShadow(yo)},onStylesheetLoad:function(yo,Co){Ro.attachLinkElement(yo,Co)},keepIframeSrcFn:$o});if(!bo)return console.warn("Failed to snapshot the document");x$4(U$8({type:k$2.FullSnapshot,data:{node:bo,initialOffset:{left:window.pageXOffset!==void 0?window.pageXOffset:(document==null?void 0:document.documentElement.scrollLeft)||((bo=(bo=document==null?void 0:document.body)==null?void 0:bo.parentElement)==null?void 0:bo.scrollLeft)||((bo=document==null?void 0:document.body)==null?void 0:bo.scrollLeft)||0,top:window.pageYOffset!==void 0?window.pageYOffset:(document==null?void 0:document.documentElement.scrollTop)||((bo=(bo=document==null?void 0:document.body)==null?void 0:bo.parentElement)==null?void 0:bo.scrollTop)||((bo=document==null?void 0:document.body)==null?void 0:bo.scrollTop)||0}}})),forEach(Ce,function(yo){yo.unlock()}),document.adoptedStyleSheets&&0<document.adoptedStyleSheets.length&&Ro.adoptStyleSheets(document.adoptedStyleSheets,te.getId(document))};try{var Do=function(yo){return Xr({mutationCb:Ao,mousemoveCb:function(Co,Go){x$4(U$8({type:k$2.IncrementalSnapshot,data:{source:Go,positions:Co}}))},mouseInteractionCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.MouseInteraction},Co)}))},scrollCb:Io,viewportResizeCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.ViewportResize},Co)}))},inputCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.Input},Co)}))},mediaInteractionCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.MediaInteraction},Co)}))},styleSheetRuleCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.StyleSheetRule},Co)}))},styleDeclarationCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.StyleDeclaration},Co)}))},canvasMutationCb:Lo,fontCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.Font},Co)}))},selectionCb:function(Co){x$4(U$8({type:k$2.IncrementalSnapshot,data:objectAssign({source:w$7.Selection},Co)}))},blockClass:Wn,ignoreClass:Qn,maskTextClass:Jn,maskTextSelector:eo,maskInputOptions:_o,inlineStylesheet:Xn,sampling:co,recordCanvas:wo,inlineImages:ho,userTriggeredOnInput:po,collectFonts:go,doc:yo,maskInputFn:no,maskTextFn:oo,keepIframeSrcFn:$o,blockSelector:jn,slimDOMOptions:Eo,dataURLOptions:uo,mirror:te,iframeManager:ko,stylesheetManager:Ro,shadowDomManager:Mo,canvasManager:ct,ignoreCSSAttributes:vo,plugins:((yo=mo==null?void 0:mo.filter(function(Co){return Co.observer}))==null?void 0:yo.map(function(Co){return{observer:Co.observer,options:Co.options,callback:function(Go){x$4(U$8({type:k$2.Plugin,data:{plugin:Co.name,payload:Go}}))}}}))||[]},ao)},bo=function(){try{rt(),xo.push(Do(document)),Ye=!0}catch(yo){Ye=!0,console.warn(yo)}},xo=[];return xo.push(_$4("DOMContentLoaded",function(){x$4(U$8({type:k$2.DomContentLoaded,data:{}}))})),ko.addLoadListener(function(yo){xo.push(Do(yo.contentDocument))}),document.readyState==="interactive"||document.readyState==="complete"?bo():xo.push(_$4("load",function(){x$4(U$8({type:k$2.Load,data:{}})),bo()},window)),function(){forEach(xo,function(yo){yo()}),Ye=!1}}catch(Do){console.warn(Do)}}function la(ee){return ee=JSON.stringify(ee),ee=Yn.gzip(ee,{level:9}),compatibleToB64(new Uint8Array(ee).reduce(function(Gn,qn){return Gn+String.fromCharCode(qn)},""))}ce.addCustomEvent=function(ee,Gn){if(!Ye)throw new Error("please add custom event after start recording");x$4(U$8({type:k$2.Custom,data:{tag:ee,payload:Gn}}))},ce.freezePage=function(){forEach(Ce,function(ee){return ee.freeze()})},ce.takeFullSnapshot=function(ee){if(!Ye)throw new Error("please take full snapshot after start recording");rt(ee)},ce.mirror=te;var ft=!1;function jt(Wn,qn){var Wn=da(Wn,Ue,qn),qn=Wn.data,Vn=Wn.url,Wn=Wn.sectionInfo;Wn&&Wn.isRestSID===void 0||Wn&&Wn.isRestSID===!1?(ft=!1,fetch(Vn,{method:"POST",body:qn,$$inner:!0}).then(function(jn){jn.ok?ke=[]:he++,Ge=!1,he=0}).catch(function(jn){Ge=!1,he++})):(Ge=!1,ft=!(he=0))}function ua(ee){return createCompatibleBlob([ee],{type:"application/json"}).size}function da(ee,Gn,qn){if(ft===!0){for(var Vn=-1,Wn=0;Wn<ee.length;Wn++){var jn=ee[Wn];if(jn.type===4&&jn.data&&jn.data.href===window.location.href){Vn=Wn;break}}-1<Vn&&ee.splice(0,Vn)}pe$2===0&&(pe$2=1e3*ee[0].timestamp);var Qn,Jn=new FormData,eo=(Jn.append("di",JSON.stringify(getDeviceInfo())),bonreeRUM.getSession());if(Jn.append("s",eo.sessionID),Jn.append("v",bonreeRUM&&bonreeRUM.version||"1.0.0"),Jn.append("mt",String(now())),Jn.append("cmt",String(qn.initTime)),Jn.append("ai",JSON.stringify({ai:qn.appId,av:qn.appVersion||"unknown",an:qn.appName||"unknown",at:4})),qn.dataFusionInfo=="net"&&Jn.append("dfi",JSON.stringify({net:"net"})),pe$2===0&&(pe$2=1e3*ee[0].timestamp),Jn.append("st",pe$2),isDefined(window.bonreeRUM)||!isDefined(window.bonreeRUM.getUserInfo))return Object.getOwnPropertyNames(bonreeRUM.getUserInfo()).length!==0&&(Qn=Object.entries(bonreeRUM.getUserInfo())[0][1],Jn.append("ui",JSON.stringify(Qn))),Qn=1e3*ee[ee.length-1].timestamp-pe$2,Jn.append("ud",Qn),pe$2=1e3*ee[ee.length-1].timestamp,Jn.append("wev","2.0.0-alpha.8"),Qn=createCompatibleBlob([JSON.stringify({wsd:la(ee)})],{type:"application/json"}),Jn.append("event",Qn),{data:Jn,url:Gn+"?a="+qn.appId+"&brkey="+uuid()+"&d="+getDeviceInfo().di+"&v=2024083001",sectionInfo:eo};log("C154")}function ca(ee){if(window.fetch&&!Ge){if(mt)we=[];else if(!isDefined(window.stopBonreeRecordUpload)&&we.length!==0){Ge=!0;var Gn=0;for(3<=he&&(ke=[],he=0);0<we.length&&he===0;){var qn=we.shift(),Vn=ua(JSON.stringify(qn));sr<Gn+Vn&&(jt(ke,ee),Gn=0),ke.push(qn),Gn+=Vn}0<ke.length&&jt(ke,ee)}}}function BonreeRecord(ee){if((!(Gn=getDeviceInfo())||!Gn.ctn||Gn.ctn.toLowerCase()!=="ie")&&(w$a.BonreeRecordState=!0,(qn=ee.RecordConfiguration||ee.mc)instanceof Array&&forEach(qn,function(Wn){Wn.n==="sessionreplay"&&(Vn=Wn)}),qn=Vn||qn,isDefined(qn)&&(!(Gn=getDeviceInfo())||!Gn.ctn||Gn.ctn.toLowerCase()!=="ie"))){var Wn=function(jn){return!(jn<=Math.floor(100*Math.random()))},Gn;if(isDefined(qn)&&isDefined(qn.Rate)&&Wn(qn.Rate)||isDefined(qn)&&isDefined(qn.c)&&Wn(qn.c))return Ue=window.location.protocol==="https:"?qn.UploadHttps||qn.src&&qn.src.uas:qn.UploadHttp||qn.src&&qn.src.ua,isDefined(window.$bonreeReplayUrl=Ue)&&window===window.top&&(He$1&&clearInterval(He$1),He$1=setInterval(ca,1e3*(qn.UploadCycle||qn.src&&qn.src.uc),ee)),Gn={emit:function(jn){window===window.top&&we.push(jn)},maskAllInputs:!0,recordCanvas:!0,sampling:{canvas:5},dataURLOptions:{type:"image/webp",quality:.3},recordCrossOriginIframes:!0,maskTextSelector:"body",maskTextFn:function(jn){var Qn;return jn.indexOf("\n")===-1?"*".repeat(jn.length):(Qn=jn.trim(),jn.replace(Qn,"*".repeat(Qn.length)))}},ee={emit:function(jn){window===window.top&&we.push(jn)},maskAllInputs:!0,recordCanvas:!0,recordCrossOriginIframes:!0,sampling:{canvas:5},dataURLOptions:{type:"image/webp",quality:.3}},qn.src&&qn.src.sl!==void 0?qn.src.sl===1?ce(Gn):ce(ee):qn.Sensitivity!==void 0?qn.Sensitivity===1?ce(Gn):ce(ee):void 0}var qn,Vn}function i$2(){this.firstDayEnd=0,this.isFirstDay=!1}BonreeRecord.takeFullSnapshot=ce.takeFullSnapshot,BonreeRecord.stopRecordUpload=lr,BonreeRecord.getRecordState=dr,i$2.prototype.getTodayEnd=function(){var qn=new Date,ee=qn.getFullYear(),Gn=qn.getMonth(),qn=qn.getDate();return+new Date(ee,Gn,qn+1)},i$2.prototype.unserialize=function(ee){try{return JSON.parse(ee)}catch(Gn){return ee}},i$2.prototype.updateFirstDay=function(ee){var Gn=this.unserialize(getLocaStore(GC.FIRST_DAY));return Gn&&isRightTimestamp(Gn)?(this.firstDayEnd=Gn,this.isFirstDay=ee<=this.firstDayEnd):(this.firstDayEnd=this.getTodayEnd(),this.isFirstDay=!0,setLocalStore(GC.FIRST_DAY,this.firstDayEnd)),this.isFirstDay},i$2.prototype.getIsFirstDay=function(ee){return this.updateFirstDay(ee)};var h$6=new i$2,R$5=3e4;function p$6(ee){var Gn,qn,Vn;window.decodeURIComponent&&D$5.sett.interceptURL&&(Gn=(Gn=S$4(window.location.href))&&decodeURIComponent(Gn))&&ee===(Gn=(ee=JSON.parse(Gn))[GC.BR_APP_ID])&&(qn=ee[GC.FINGER_ID],_$3(ee[GC.TIMESTAMP],qn))&&(qn=ee[GC.BR_DEVICE_ID],Vn=ee[GC.BR_SESSION_ID],ee=ee[GC.BR_USER_ID],qn)&&Vn&&(E$4(Gn),U$7(ee),g$2(qn),P$3(Vn))}function S$4(ee){return(ee=ee&&ee.match(/[?&]br_params=([^&#]*)/))?decodeURIComponent(ee[1]):""}function _$3(ee,Gn){return!(new Date().getTime()-ee>R$5)&&generateFingerprint()===Gn}function U$7(ee){ee&&T$4(ee)}function E$4(ee){ee&&(D$5.sett.appId=ee)}function P$3(ee){ee&&I$a(ee)}function g$2(ee){ee&&g$5(ee)}function m$6(){function ee(){for(var jn=0;jn<Vn.length;jn++)Vn[jn]()}var Gn=!(!window.attachEvent||window.opera),qn=/webkit\/(\d+)/i.test(navigator.userAgent)&&RegExp.$1<525,Vn=[],Wn=document;Wn.ready=function(jn){if(!Gn&&!qn&&Wn.addEventListener)return Wn.readyState==="complete"||Wn.readyState==="interactive"?typeof jn!="function"?void 0:jn():Wn.addEventListener("DOMContentLoaded",jn,!1);var Qn;1<Vn.push(jn)||(Gn?function Jn(){try{Wn.documentElement.doScroll("left"),ee()}catch(eo){setTimeout(Jn,0)}}():qn&&(Qn=setInterval(function(){/^(loaded|complete)$/.test(Wn.readyState)&&(clearInterval(Qn),ee())},0)))}}function A$2(){return!/BonreeRUM=0/.test(window.location.search)||(D$5.checkProbability(0),!1)}function C$3(){m$a([GC.PAGE_READY,GC.PAGE_LOAD],function(ee){w$a.changeState(ee.t===GC.PAGE_READY?1:2),k$4&&k$4.startDOMObserver()})}function D$2(){document&&(w$a.setData(GC.TITLE,document.title),w$a.setEviData("n",document.URL),document.title&&w$a.setEviData("vt",document.title),document.referrer&&w$a.setEviData("ru",document.referrer),w$a.setEviMap())}function v$3(ee){ee=ee||window.event,ee=ee&&(ee.data||ee.message||ee.detail),ee&&isDefined(ee.bonreeID)&&T$4(ee.bonreeID)}function p$5(){try{var ee,Gn;window.addEventListener?window.addEventListener("message",v$3,!1):window.attachEvent&&window.attachEvent("onmessage",function(){v$3.call(window,window.event)}),window.parent&&(ee=window.parent.document.getElementsByTagName("iframe"),Gn=!1,forEach(ee,function(qn){!Gn&&isDefined(qn.dataset.bonreeID)&&(T$4(qn.dataset.bonreeID),Gn=!0)}))}catch(qn){}}function L$3(){if(checkStorageSupport(),p$5(),!A$2())return log("C106",!0),!1;m$6(),C$3(),document&&(T$5(),m$a([GC.PAGE_LOAD],function(){D$2()})),isDefined(window)&&(window.setJsBridge=function(qn){window.bonreePrivateInterface=qn});try{var ee=document.querySelector("#BonreeAgent");if(!isDefined(ee))return!1;var Gn=ee.getAttribute("data");if(!isJSON(Gn=window.decodeURIComponent&&window.decodeURIComponent(Gn)))return log("C102",!0),!1;if(isDefined((Gn=JSON.parse(Gn)).appId)||log("C101",!0),isDefined(window.getIsRunWithNetClient)&&(Gn.dataFusionInfo="net"),isDefined(window.bonreeRUM)?window.bonreeRUM.config(Gn,!1,1):D$5.setConfig(Gn),p$6(Gn.appId),D$5.sett.initTime=now(),D$5.sett.probability)return!0;log("C103",!0)}catch(qn){return log("C102",!0),!1}return!0}function Protocol(){this.json={v:bonreeRUM&&bonreeRUM.version||"1.0.0",e:[]}}Protocol.prototype.setMonitorTime=function(ee){return this.json.mt=ee,this},Protocol.prototype.setConfigTime=function(ee){return this.json.cmt=ee,this},Protocol.prototype.setSessionId=function(ee){return this.json.s=ee,this},Protocol.prototype.setDeviceInfo=function(ee){return this.json.di=ee,this},Protocol.prototype.setAppInfo=function(ee){return this.json.ai=ee,this},Protocol.prototype.setFirstUserInfoIndex=function(){var ee=w$a.firstUserInfoSetMapKey;return ee!==""&&(this.json.fui=ee,w$a.updateFirstUserInfoSetMapKey("")),this},Protocol.prototype.setUserInfo=function(ee){return this.json.ui=ee,this},Protocol.prototype.setEventViewInfo=function(ee){return this.json.evi=ee,this},Protocol.prototype.setIfd=function(ee){return this.json.ifd=ee,this},Protocol.prototype.setEei=function(ee){return isEmpty(ee)||(this.json.eei=ee),this},Protocol.prototype.setSessionDuration=function(ee){return this.json.usd=ee,this},Protocol.prototype.pushEventData=function(ee){var Gn={k:ee.type,ent:ee.ent,sin:ee.sin,v:ee.data};return isDefined(ee.eei)&&!isEmpty(ee.eei)&&(Gn.eei=ee.eei),isDefined(ee.revts)&&(Gn.revts=ee.revts),this.json.e.push(Gn),this},Protocol.prototype.build=function(ee){return this.json},Protocol.prototype.setDataFusionInfo=function(ee){return this.json.dfi={net:ee},this},Protocol.prototype.setSessionStartTime=function(ee){return this.json.sst=ee,this};var c$5=3e4,w$6=[],a$3=!0;function B(){return D$5.sett.isFirstUpload?(extend(D$5.sett,{isFirstUpload:0}),1):0}function S$3(ee,Gn){var qn="";return window.location.protocol==="https:"?(isDefined(D$5.sett.uploadAddrHttps)&&(qn=D$5.sett.uploadAddrHttps),isDefined(D$5.secondSett.uploadAddrHttps)&&(qn=D$5.secondSett.uploadAddrHttps)):(isDefined(D$5.sett.uploadAddrHttp)&&(qn=D$5.sett.uploadAddrHttp),isDefined(D$5.secondSett.uploadAddrHttp)&&(qn=D$5.secondSett.uploadAddrHttp)),qn=(qn=(qn=(qn=(qn=(qn=qn+"?v=2024083001"+("&a="+ee.ai.ai))+("&d="+ee.di.di))+("&mt="+ee.mt))+("&cmt="+ee.cmt))+("&s="+ee.s)+("&brkey="+Gn))+("&if="+B()),D$5.sett.sm4Config!==void 0&&/^[A-Za-z0-9_-]{1,256}$/.test(D$5.sett.sm4Config.identify||D$5.sett.sm4Config.identifier)&&(qn+="&BR-Encryption-Method=sm4:"+(D$5.sett.sm4Config.identify||D$5.sett.sm4Config.identifier)),isDefined(window.bonreeRUM)&&window.bonreeRUM.isPrivate?qn+="&isp=1":qn+="&isp=0",qn}function C$2(ee,Gn,qn,Vn,Wn){if(window.XDomainRequest)(Qn=new window.XDomainRequest).open("POST",ee),Qn.timeout=c$5,Qn.onload=function(){qn&&qn(!0),a$3=!0},Qn.onerror=Qn.ontimeout=function(){qn&&qn(!1),a$3=!0},delay(function(){Qn.send(Gn)});else{if(!window.XMLHttpRequest)return!1;var jn=-1<navigator.userAgent.indexOf("Trident/7.0"),Qn=new window.XMLHttpRequest;try{Qn.$$inner=!0,Qn.overrideMimeType("text/plain"),Qn.open("POST",ee,!0),jn||Qn.setRequestHeader("brkey",Wn),Qn.timeout=c$5,Qn.onreadystatechange=function(){Qn.readyState===4&&(qn&&qn(Qn.status===200),h$5(Qn))}}catch(Jn){}Qn.send(Gn)}return!0}function H$3(){return/\bQQ\b/i.test(navigator.userAgent)}function h$5(ee){a$3=!0,p$4()}function y(ee){var Gn;a$3===!0&&0<w$6.length&&(Gn=w$6.shift(),a$3=!1,C$2(Gn.url,Gn.data,ee,!0,Gn.uid))}function p$4(ee,Gn,qn,Vn,Wn){ee&&Gn&&w$6.length<100&&w$6.push({data:Gn,uid:Wn,url:ee}),y(qn)}function sendUpload(ee,Gn,qn){var Vn=uuid(),Wn=S$3(ee,Vn);if(!startWith(Wn,"http"))return!1;var jn,Qn=D$5.sett.useXHR||D$5.secondSett.useXHR||!1;if(window.navigator&&navigator.sendBeacon&&!H$3()&&Qn===!1){if(D$5.sett.sm4Config!==void 0&&/^[A-Za-z0-9_-]{1,256}$/.test(D$5.sett.sm4Config.identify||D$5.sett.sm4Config.identifier)){try{isDefined(window.BonreeRecord)&&window.BonreeRecord.Sm4&&(jn=new window.BonreeRecord.Sm4(D$5.sett.sm4Config)),isDefined(window.BonreeAgent)&&window.BonreeAgent.Sm4&&(jn=new window.BonreeAgent.Sm4(D$5.sett.sm4Config))}catch(Jn){return log("C156"),Qn=D$5.sett.sm4Config.identify||D$5.sett.sm4Config.identifier,qn(navigator.sendBeacon(Wn.replace("&BR-Encryption-Method=sm4:"+Qn,""),stringify(ee)))}return qn(navigator.sendBeacon(Wn,jn.encrypt(stringify(ee))))}return qn(navigator.sendBeacon(Wn,stringify(ee)))}if(D$5.sett.sm4Config!==void 0){if(isDefined(window.BonreeRecord)&&window.BonreeRecord.Sm4)return p$4(Wn,(jn=new window.BonreeRecord.Sm4(D$5.sett.sm4Config)).encrypt(stringify(ee)),qn,Gn,Vn);if(isDefined(window.BonreeAgent)&&window.BonreeAgent.Sm4)return p$4(Wn,(jn=new window.BonreeAgent.Sm4(D$5.sett.sm4Config)).encrypt(stringify(ee)),qn,Gn,Vn)}return p$4(Wn,stringify(ee),qn,Gn,Vn)}var l$4=[],ALL_DATAS=[],n$1={WORKER:null,IDLE:1e4,CAPACITY:20480,RETRY:3},K$2="br-session-cache";function Q$1(){var ee=Number(D$5.sett.cycleTime)||Number(D$5.secondSett.cycleTime)||10;n$1.IDLE=ee&&0<ee&&ee<=60?1e3*ee:1e4}function getMaxSize(){var ee=Number(D$5.sett.maxSize)||Number(D$5.secondSett.maxSize)||20;n$1.CAPACITY=ee&&0<ee&&ee<=60?1024*ee:20480}function U$6(ee){var Gn=0;if(ee.length!==0)return forEach(ee,function(qn){(Gn===0||Gn>qn.data.ent)&&(Gn=qn.data.ent)}),Gn}function F$2(ee){ee&&ee.sin&&(ee.sin[0]===""&&w$a.lastUserInfoSetMapKey!==""&&(ee.sin[0]=w$a.lastUserInfoSetMapKey),ee.sin[3]===""&&w$a.getEeiData("key")!==""&&(ee.sin[3]=w$a.getEeiData("key")),ee.sin[4]==="")&&w$a.getEviData("key")!==""&&(ee.sin[4]=w$a.getEviData("key"))}function Z$1(){m$a(GC.FLUSH_DATA,function(ee){var Gn;Q$1(),D$5.sett.osType!==1||isDefined(n$1.WORKER)||(Gn=setInterval(E$3,n$1.IDLE),n$1.changeData(Gn)),(1<w$a.state&&!isDefined(n$1.WORKER)||!isDefined(n$1.WORKER))&&(Gn=setInterval(E$3,n$1.IDLE),n$1.changeData(Gn))}),m$a(GC.INIT_SESSION_START,function(ee){var Gn=U$6(l$4);D$5.sett.initTime=Gn||now()})}function q$1(ee,Gn,qn){var Vn=new Protocol,Wn={ai:D$5.sett.appId||D$5.secondSett.appId,av:D$5.sett.appVersion||D$5.secondSett.appVersion||"unknown",an:D$5.sett.appName||D$5.secondSett.appName||"unknown",at:4},jn=(isDefined(D$5.sett.channelId)&&(Wn.ci=D$5.sett.channelId),isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.getDeviceInfo)?Vn.setDeviceInfo(window.BonreeAgent.getDeviceInfo()):log("C153"),isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.getDeviceInfo)?Vn.setDeviceInfo(window.BonreeRecord.getDeviceInfo()):log("C153"),k$3.getSession().sessionID);Vn.setMonitorTime(now()).setConfigTime(D$5.sett.initTime).setSessionId(jn).setAppInfo(Wn).setUserInfo(w$a.getUserInfo()).setEventViewInfo(w$a.getEviData()).setEei(w$a.getEeiData()).setIfd(h$6.getIsFirstDay(+new Date)).setFirstUserInfoIndex(),isDefined(D$5.sett.dataFusionInfo)&&D$5.sett.dataFusionInfo=="net"&&Vn.setDataFusionInfo("net");var jn=(jn=1e3*k$3.getSession().startTime)>U$6(l$4)&&U$6(l$4)||jn,Qn=(Vn.setSessionStartTime(jn),D$5.sett.initTime=jn,Vn.setConfigTime(D$5.sett.initTime),0),Jn=0;forEach(ee,function(eo){var Xn=eo.ent;(Xn<Qn||Qn===0)&&(Qn=Xn),(Jn<Xn||Jn===0)&&(Jn=Xn),F$2(eo),isDefined(eo.revts)&&function to(io){for(var ro=0;ro<io.length;ro++){var no=io[ro];no.ent<Qn&&(Qn=no.ent),no.ent>Jn&&(Jn=no.ent),F$2(no),isDefined(no.revts)&&to(no.revts)}}(eo.revts),Vn.pushEventData(eo)},function(){for(var eo=getCookie(K$2),Xn=isJSON(eo)&&JSON.parse(eo)||[],to=0;to<Xn.length;to++){var io=Xn[to];isDefined(io.startTime)&&io.startTime==0&&(io.startTime=Qn,k$3.setSessionCookie(K$2,JSON.stringify(Xn)))}}),Wn=0,jn=P$6.getUsdTime(),jn&&jn!=0?0<=(ee=Jn-jn)&&(Wn=Math.abs(ee),P$6.changUsdTime(Jn)):Qn!=0&&0<=(jn=Jn-Qn)&&(Wn=Math.abs(jn),P$6.changUsdTime(Jn)),Vn.setSessionDuration(Wn),sendUpload(Vn.build(),Gn,qn)}function X$1(ee,Gn,qn){if(isDefined(ee)&&!isEmpty(ee)){try{q$1(ee,Gn,qn),qn&&qn(!0)}catch(Vn){log(Vn),qn&&qn(!1)}return!0}}function $$3(ee,Gn){var qn,Vn=[],Wn=0,jn=0;if(Wn>=n$1.RETRY)return log("C117");function Qn(Jn,eo){!isEmpty(Vn)&&(getMaxSize(),Jn||jn>n$1.CAPACITY)&&(X$1(Vn,Gn,function(Xn){Xn?0<Wn&&(Wn=0):(Wn+=1,Gn||delay(function(){var to=size(ee);jn+=to,Vn.push(ee)},n$1.IDLE/10))}),Vn=[],jn=0,qn=Jn===1?eo:null)}forEach(ee,function(Jn){var Xn=Jn.data&&Jn.data.e;if(Jn.data&&Jn.data.eei&&delete Jn.data.eei,!isDefined(Xn))return jn+=Jn.size,Vn.push(Jn.data),Qn.call(this);var eo=Xn[0],Xn=Xn[1],to=Jn.data&&Jn.data.p.info;to.userTime=Math.max(0,to.timestamp-Xn),(qn=isDefined(qn)?qn:eo)!==eo&&Qn.call(this,1,eo),jn+=Jn.size,Vn.push(Jn.data),Qn.call(this)}),Qn.call(this,2)}function E$3(ee){isEmpty(l$4=ee&&ALL_DATAS.length?l$4.concat(ALL_DATAS.splice(0)):l$4)||(f$6.useAPI||(p$5(),f$6.startCatchDataFun()),$$3(l$4,ee),l$4=[])}n$1.changeData=function(ee){this.WORKER=ee};var a$2={};function n(ee,Gn,qn,Vn,Wn,jn,Qn){this.isChild=qn||!1,isDefined(Vn)&&(this.startTime=Vn),isDefined(ee)&&isDefined(Gn)&&typeof ee=="string"&&typeof Gn=="string"&&/^[a-zA-Z0-9:_-\s@./]+$/.test(Gn)&&Gn.trim()!==""&&/^[a-zA-Z0-9:_-\s@./]+$/.test(ee)&&ee.trim()!==""?(this.spanEvent={n:ee.slice(0,256),t:Gn.slice(0,256),da:[],tag:[],m:[],ic:!0,st:isDefined(Vn)?1e3*new Date().getTime()-Vn:0},this.special=jn||!1):this.special=!0,qn||this.special||(this.spanEvent.timestamp=1e3*new Date().getTime(),this.startTime=this.spanEvent.timestamp),qn?(this.identify=Qn,a$2[this.identify]=a$2[this.identify]+1):(this.identify=uuid(),a$2[this.identify]=0),this.dataSave=[],this.tagSave=[],this.metricSave=[],this.childSave=[],this.hasFinished=!1,this.deep=(Wn||-1)+1}function o(){this.varObj={},this.localObj={},this.pushData={}}n.prototype.startChild=function(ee,Gn){return this.hasFinished||!(isDefined(ee)&&isDefined(Gn)&&typeof ee=="string"&&typeof Gn=="string"&&/^[a-zA-Z0-9:_-\s@./]+$/.test(Gn)&&Gn.trim()!==""&&/^[a-zA-Z0-9:_-\s@./]+$/.test(ee)&&ee.trim()!=="")||this.special||10<=this.deep||isDefined(this.childSave)&&50<=this.childSave.length||isDefined(a$2)&&isDefined(a$2[this.identify])&&200<=a$2[this.identify]?new n("name","type",!0,this.startTime,this.deep,!0,this.identify):isDefined(this.spanEvent)?(ee=new n(ee,Gn,!0,this.startTime,this.deep,!1,this.identify),isDefined(this.spanEvent.sub)?this.spanEvent.sub.push(ee.spanEvent):this.spanEvent.sub=[ee.spanEvent],this.childSave.push(ee),ee):void 0},n.prototype.setData=function(ee,Gn){if(!this.special&&!this.hasFinished&&isDefined(ee)&&isDefined(Gn)&&typeof ee=="string"&&typeof Gn=="string"&&!(200<ee.length)&&/^[a-zA-Z][a-zA-Z._-]{0,199}$/.test(ee)&&ee.trim()!==""&&Gn.trim()!==""&&!(isDefined(this.spanEvent.da)&&64<=this.spanEvent.da.length)&&isDefined(this.spanEvent.da))if(0<this.spanEvent.da.length)if(isDefined(this.dataSave)&&-1<this.dataSave.indexOf(ee))for(var qn=0;qn<this.spanEvent.da.length;qn++)isDefined(this.spanEvent.da[qn].k)&&this.spanEvent.da[qn].k===ee&&(this.spanEvent.da[qn].k=ee,this.spanEvent.da[qn].v=Gn.slice(0,7e3));else this.spanEvent.da.push({k:ee,v:Gn.slice(0,7e3)}),this.dataSave.push(ee);else this.spanEvent.da.push({k:ee,v:Gn.slice(0,7e3)}),this.dataSave.push(ee)},n.prototype.removeData=function(ee){if(!this.special&&!this.hasFinished&&isDefined(ee)&&typeof ee=="string"&&!(200<ee.length)&&/^[a-zA-Z][a-zA-Z._-]{0,199}$/.test(ee)&&ee.trim()!==""&&isDefined(this.dataSave)&&-1<this.dataSave.indexOf(ee))for(var Gn,qn=0;qn<this.spanEvent.da.length;qn++)isDefined(this.spanEvent.da[qn].k)&&this.spanEvent.da[qn].k===ee&&(this.spanEvent.da.splice(qn,1),Gn=this.dataSave.indexOf(ee),this.dataSave.splice(Gn,1))},n.prototype.setTag=function(ee,Gn){if(!this.special&&!this.hasFinished&&isDefined(ee)&&isDefined(Gn)&&typeof ee=="string"&&typeof Gn=="string"&&!(200<ee.length)&&/^[a-zA-Z][a-zA-Z._-]{0,199}$/.test(ee)&&ee.trim()!==""&&Gn.trim()!==""&&!(isDefined(this.spanEvent.tag)&&64<=this.spanEvent.tag.length)&&isDefined(this.spanEvent.tag))if(0<this.spanEvent.tag.length)if(isDefined(this.tagSave)&&-1<this.tagSave.indexOf(ee))for(var qn=0;qn<this.spanEvent.tag.length;qn++)isDefined(this.spanEvent.tag[qn].k)&&this.spanEvent.tag[qn].k===ee&&(this.spanEvent.tag[qn].k=ee,this.spanEvent.tag[qn].v=Gn.slice(0,7e3));else this.spanEvent.tag.push({k:ee,v:Gn.slice(0,7e3)}),this.tagSave.push(ee);else this.spanEvent.tag.push({k:ee,v:Gn.slice(0,7e3)}),this.tagSave.push(ee)},n.prototype.removeTag=function(ee){if(!this.special&&!this.hasFinished&&isDefined(ee)&&typeof ee=="string"&&!(200<ee.length)&&/^[a-zA-Z][a-zA-Z._-]{0,199}$/.test(ee)&&ee.trim()!==""&&isDefined(this.tagSave)&&-1<this.tagSave.indexOf(ee))for(var Gn,qn=0;qn<this.spanEvent.tag.length;qn++)isDefined(this.spanEvent.tag[qn].k)&&this.spanEvent.tag[qn].k===ee&&(this.spanEvent.tag.splice(qn,1),Gn=this.tagSave.indexOf(ee),this.tagSave.splice(Gn,1))},n.prototype.setMetric=function(ee,Gn,qn){if(!this.special&&!this.hasFinished&&isDefined(ee)&&isDefined(Gn)&&typeof Gn=="number"&&typeof ee=="string"&&!(200<ee.length)&&/^[a-zA-Z][a-zA-Z._-]{0,199}$/.test(ee)&&ee.trim()!==""&&!(isDefined(this.spanEvent.m)&&64<=this.spanEvent.m.length)&&isDefined(this.spanEvent.m))if(0<this.spanEvent.m.length)if(isDefined(this.metricSave)&&-1<this.metricSave.indexOf(ee))for(var Vn=0;Vn<this.spanEvent.m.length;Vn++)isDefined(this.spanEvent.m[Vn].k)&&this.spanEvent.m[Vn].k===ee&&(this.spanEvent.m[Vn].k=ee,this.spanEvent.m[Vn].v=Math.round(Gn),isDefined(qn)&&typeof qn=="string"&&qn.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(qn)&&qn.trim()!==""?this.spanEvent.m[Vn].u=qn:delete this.spanEvent.m[Vn].u);else isDefined(qn)&&typeof qn=="string"&&qn.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(qn)&&qn.trim()!==""?this.spanEvent.m.push({k:ee,v:Math.round(Gn),u:qn}):this.spanEvent.m.push({k:ee,v:Math.round(Gn)}),this.metricSave.push(ee);else isDefined(qn)&&typeof qn=="string"&&qn.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(qn)&&qn.trim()!==""?this.spanEvent.m.push({k:ee,v:Math.round(Gn),u:qn}):this.spanEvent.m.push({k:ee,v:Math.round(Gn)}),this.metricSave.push(ee)},n.prototype.removeMetric=function(ee){if(!this.special&&!this.hasFinished&&isDefined(ee)&&typeof ee=="string"&&!(200<ee.length)&&/^[a-zA-Z][a-zA-Z._-]{0,199}$/.test(ee)&&ee.trim()!==""&&isDefined(this.metricSave)&&-1<this.metricSave.indexOf(ee))for(var Gn,qn=0;qn<this.spanEvent.m.length;qn++)isDefined(this.spanEvent.m[qn].k)&&this.spanEvent.m[qn].k===ee&&(this.spanEvent.m.splice(qn,1),Gn=this.metricSave.indexOf(ee),this.metricSave.splice(Gn,1))},n.prototype.setDuration=function(ee){this.special||this.hasFinished||!isDefined(ee)||typeof ee!="number"||(this.spanEvent.du=Math.round(ee))},n.prototype.setStatus=function(ee){this.special||this.hasFinished||!isDefined(ee)||typeof ee!="number"||ee!==0&&ee!==1&&ee!==2||(this.spanEvent.sta=ee)},n.prototype.setStatusCode=function(ee){this.special||this.hasFinished||!isDefined(ee)||typeof ee!="string"||7e3<ee.length||ee.trim()===""||(this.spanEvent.stac=ee)},n.prototype.finish=function(ee){if(!this.special&&!this.hasFinished&&(!isDefined(ee)&&typeof ee!="number"||ee!==0&&ee!==1&&ee!==2||(this.spanEvent.sta=ee),this.hasFinished=!0,isDefined(this.spanEvent.da)&&this.spanEvent.da.length===0&&delete this.spanEvent.da,isDefined(this.spanEvent.tag)&&this.spanEvent.tag.length===0&&delete this.spanEvent.tag,isDefined(this.spanEvent.m)&&this.spanEvent.m.length===0&&delete this.spanEvent.m,isDefined(this.spanEvent.du)||(this.spanEvent.du=1e3*new Date().getTime()-this.startTime-this.spanEvent.st),isDefined(this.spanEvent.sta)||(this.spanEvent.sta=1),isDefined(this.spanEvent.st))&&isDefined(this.spanEvent.n)&&isDefined(this.spanEvent.t)&&isDefined(this.spanEvent.ic)){if(isDefined(this.childSave)&&0<this.childSave.length)for(var Gn=0;Gn<this.childSave.length;Gn++)this.childSave[Gn].finish();this.isChild||(isDefined(a$2)&&isDefined(a$2[this.identify])&&delete a$2[this.identify],c$7({t:GC.SPAN_DATA,p:{info:this.spanEvent}}))}},o.prototype.objDeal=function(ee){var Gn,qn,Vn=0;for(Gn in ee)Vn>=GC.LIMIT_64&&delete ee[Gn],Vn+=1,Object.hasOwnProperty.call(ee,Gn)&&(qn=ee[Gn],isString(Gn)&&GC.ATT_REG.test(Gn)&&!(Gn.length>GC.LIMIT_256)&&isDefined(qn)&&typeof qn!="object"||(delete ee[Gn],--Vn),isDefined(qn))&&typeof qn=="string"&&qn.length>GC.LIMIT_512&&(ee[Gn]=set_length(qn,GC.LIMIT_512));return ee},o.prototype.addWithObj=function(ee,Gn){for(var qn in ee)Object.hasOwnProperty.call(ee,qn)&&(qn==null||/^\s{0,}$/.test(qn)||typeof qn=="number"||typeof qn=="boolean")&&delete ee[qn];typeof this.varObj=="object"&&64<=Object.keys(this.varObj).length||(Gn?(Gn=getLocaStore(GC.BR_ATTRIBUTE)||"{}",this.localObj=objectAssign({},JSON.parse(Gn),this.objDeal(ee)),setLocalStore(GC.BR_ATTRIBUTE,JSON.stringify(this.localObj))):(Gn=objectAssign({},this.varObj),this.varObj=objectAssign({},Gn,this.objDeal(ee))),this.sendObj())},o.prototype.addWithKey=function(ee,Gn,qn){var Vn;ee==null||/^\s{0,}$/.test(ee)||typeof ee=="number"||typeof ee=="boolean"||typeof this.varObj=="object"&&64<=Object.keys(this.varObj).length||!isString(ee)||!GC.ATT_REG.test(ee)||ee.length>GC.LIMIT_256||!isDefined(Gn)||typeof Gn=="object"||(typeof Gn!="number"&&Gn.length>GC.LIMIT_512&&(Gn=set_length(Gn,GC.LIMIT_512)),(Vn={})[ee]=Gn,qn?(ee=getLocaStore(GC.BR_ATTRIBUTE)||"{}",this.localObj=objectAssign({},JSON.parse(ee),Vn),setLocalStore(GC.BR_ATTRIBUTE,JSON.stringify(this.localObj))):(Gn=objectAssign({},this.varObj),this.varObj=objectAssign({},Gn,Vn)),this.sendObj())},o.prototype.delWithKeys=function(ee){var Gn=JSON.parse(getLocaStore(GC.BR_ATTRIBUTE)||"{}"),qn=this.varObj;isArray(ee)&&forEach(ee,function(Vn){delete Gn[Vn],delete qn[Vn]}),this.localObj=objectAssign({},Gn),setLocalStore(GC.BR_ATTRIBUTE,JSON.stringify(this.localObj)),this.varObj=objectAssign({},qn),this.sendObj()},o.prototype.delAll=function(){c$7({t:GC.FLUSH_DATA,p:!1}),this.localObj={},setLocalStore(GC.BR_ATTRIBUTE,JSON.stringify(this.localObj)),this.varObj={},this.sendObj()},o.prototype.sendObj=function(){var ee=getLocaStore(GC.BR_ATTRIBUTE)||"{}",Gn=objectAssign({},JSON.parse(ee),this.varObj);if(64<Object.keys(Gn).length){var qn,Vn=0;for(qn in Gn)Object.hasOwnProperty.call(Gn,qn)&&(64<=Vn?delete Gn[qn]:Vn+=1)}this.pushData=Gn,w$a.setEeiData(this.pushData)};var I$7=new o;function Metric(ee,Gn){this.d_type=Gn,this.data=ee||{},this.$metric={},this.ent=ee&&ee.info&&ee.info.timestamp&&ee.info.timestamp||now()}function R$4(ee,Gn){this.$metric[ee]=Gn}Metric.prototype.info=function(ee,Gn,qn,Vn){Gn===void 0&&(Gn=""),Vn===void 0&&(Vn=!1);var Wn=this.data.info;return isDefined(Wn)&&(isDefined(Wn=Wn[ee])||(Wn=Gn),isDefined(qn)&&(ee=qn),Vn&&Wn===""||(this.$metric[ee]=Wn)),this},Metric.prototype.metric=function(ee,Gn,qn){Gn===void 0&&(Gn="");var Vn=this.data.metric;return isDefined(Vn)&&(isDefined(Vn=Vn[ee])||(Vn=isDefined(Gn)?Gn:-1),isDefined(qn)&&(ee=qn),R$4.call(this,ee,Vn)),this},Metric.prototype.build=function(Qn){var Gn=w$a.getEviData("key"),qn=w$a.getEeiData("key"),Vn=w$a.lastUserInfoSetMapKey,Qn=(Qn&&Qn.ent?Qn:this).ent,Wn=I$7.pushData,jn=w$a.getEviData("value"),Qn={type:this.d_type,ent:Qn,data:{},sin:[Vn,"","",qn,Gn],evi:jn},Jn=(isEmpty(Wn)||(Qn.eei=Wn),this.$metric);if(isDefined(this.data.ext))switch(this.d_type){case GC.ACTION:case GC.NET:case GC.ERROR:case GC.ROUTE:break;case GC.PAGE:case GC.RESOURCE:case GC.WEBVIEWDATA:default:Jn.ext=this.data.ext}return Qn.data=Jn,Qn};var h$4=0;function pushQueueData(ee,Gn){var qn;isDefined(ee)&&isDefined(l$4)&&(qn=size(ee),ALL_DATAS.push({data:ee,size:qn}),getMaxSize(),h$4+qn>n$1.CAPACITY?(E$3(!0),h$4=0):(h$4+=qn,l$4.push(ALL_DATAS.shift())))}function U$5(ee,Vn){var qn=Vn.fullCollect||Vn.fc,Vn=Vn.whiteList||Vn.wl;if(!isDefined(qn)||qn)return!0;if(isDefined(Vn)){var Jn=function(io,ro){return io=io.split("?")[0],Qn(io,ro)},eo=function(io,ro){for(var no=ro===1?Qn:Jn,oo=ro===1?jn:Wn,ao=0;ao<io.length;ao++)if(no(oo,io[ao]))return!0;return!1};try{var Wn=ee,jn="";if(Wn.indexOf("http")!==0&&Wn.indexOf("file:")!==0&&Wn.indexOf("ws")!==0)return!1;jn=(isDefined(Wn.match(/:\/\/\[([^\]]+)\]/))?Wn.match(/:\/\/\[([^\]]+)\]/):Wn.match(/:\/\/(.[^/:]+)/))[1]}catch(io){return!1}var qn=isDefined(Vn.hostRule||Vn.hr)?(Vn.hostRule||Vn.hr).slice(0,20):[],Vn=isDefined(Vn.pathRule||Vn.pr)?(Vn.pathRule||Vn.pr).slice(0,20):[],Qn=function(io,ro){var no=ro.split("*");return!(2<no.length)&&(ro.indexOf("*")===-1?ro===io:no[0]!==""&&no[1]!==""?new RegExp("("+no[0]+").+("+no[1]+")").test(io):no[0]==""&&no[1]!==""?!!endWith(io,no[1]):no[0]!==""&&no[1]==""?!!startWith(io,no[0]):void 0)};return eo(qn,1)||eo(Vn,2)}return!1}function Q(ee){var Gn=void 0;return isDefined(ee.collectionStrategy)?Gn=ee.collectionStrategy:isDefined(ee.mc)&&ee.mc instanceof Array&&forEach(ee.mc,function(qn){qn.n==="network"&&isDefined(qn.cs)&&(Gn=qn.cs)}),Gn}function k$1(jn,Gn,qn){var Vn=jn.mk||jn.maskKeys,Wn=jn.mt||jn.maskType,jn=isDefined(Vn.qka||Vn.urlKeys)&&(Vn.qka||Vn.urlKeys);if(Vn.aqk||Vn.allQueryKeys)switch(Wn){case 1:Gn.p.info.url=Gn.p.info.url.split("?")[0],isDefined(qn.reqURLKey)&&(Gn.p.info.reqUrlDta=null);break;case 2:Gn.p.info.url=Gn.p.info.url.replace(/(\?|&)([^=&]+)=([^&]+)/g,"$1$2=**"),isDefined(qn.reqURLKey)&&(Gn.p.info.reqUrlDta=Gn.p.info.url.split("?")[1]);break;default:return}else if(isDefined(jn)&&0<jn.length&&Gn.p.info&&Gn.p.info.url&&-1<Gn.p.info.url.indexOf("?")){for(var Qn=Gn.p.info.url.split("?")[1].split("&"),Jn="",eo=0;eo<Qn.length;eo++){var Xn=Qn[eo].split("=");switch(Wn){case 1:includes(Vn.qka||Vn.urlKeys,Xn[0])||(Jn+=Qn[eo]+"&");break;case 2:includes(Vn.qka||Vn.urlKeys,Xn[0])?Jn+=Qn[eo].split("=")[0]+"=**&":Jn+=Qn[eo]+"&"}}isDefined(qn.reqURLKey)&&(Gn.p.info.reqUrlDta=Jn.slice(0,-1)),Jn.slice(0,-1)!==""?Gn.p.info.url=Gn.p.info.url.split("?")[0]+"?"+Jn.slice(0,-1):Gn.p.info.url=Gn.p.info.url.split("?")[0]}var to=(to=isDefined(Vn.reqhka||Vn.requestHeadersKeys)?Vn.reqhka||Vn.requestHeadersKeys:[]).map(function(oo){return oo.toLowerCase()});if(Vn.areqh||Vn.allRequestHeaders)switch(Wn){case 1:Gn.p.info[GC.REQUEST_HEADER]=void 0,Gn.p.info.reqhtData={};break;case 2:isDefined(Gn.p.info[GC.REQUEST_HEADER])&&(Gn.p.info[GC.REQUEST_HEADER]=Gn.p.info[GC.REQUEST_HEADER].replace(/:(.*?)($|\r\n)/g,":**$2")),isDefined(qn.reqHeaderTraceKey)&&0<qn.reqHeaderTraceKey.length&&(isEmpty(Gn.p.info.reqhtData)||forEach(Gn.p.info.reqhtData,function(oo,ao){Gn.p.info.reqhtData[ao]="**"}));break;default:return}else switch(Wn){case 1:if(isDefined(Gn.p.info[GC.REQUEST_HEADER])&&isDefined(to)&&0<to.length)for(eo=0;eo<to.length;eo++){var io=to[eo];(ro=new RegExp(io+":(.*?)(\r\n|$)","i")).test(Gn.p.info[GC.REQUEST_HEADER])&&(Gn.p.info[GC.REQUEST_HEADER]=Gn.p.info[GC.REQUEST_HEADER].replace(ro,""))}if(isDefined(qn.reqHeaderTraceKey)&&0<qn.reqHeaderTraceKey.length&&!isEmpty(Gn.p.info.reqhtData)){for(var io in Gn.p.info.reqhtData)if(includes(to,io.toLowerCase())){delete Gn.p.info.reqhtData[io];continue}}break;case 2:if(isDefined(Gn.p.info[GC.REQUEST_HEADER])&&isDefined(to)&&0<to.length)for(eo=0;eo<to.length;eo++)io=to[eo],(ro=new RegExp(io+":(.*?)(\r\n|$)","i")).test(Gn.p.info[GC.REQUEST_HEADER])&&(Gn.p.info[GC.REQUEST_HEADER]=Gn.p.info[GC.REQUEST_HEADER].replace(ro,function(oo,ao){return oo.replace(ao,"**")}));if(isDefined(qn.reqHeaderTraceKey)&&0<qn.reqHeaderTraceKey.length&&!isEmpty(Gn.p.info.reqhtData)){for(var io in Gn.p.info.reqhtData)if(includes(to,io.toLowerCase())){Gn.p.info.reqhtData[io]="**";continue}}break;default:return}var ro,no=(no=isDefined(Vn.reshka||Vn.responseHeadersKeys)?Vn.reshka||Vn.responseHeadersKeys:[]).map(function(oo){return oo.toLowerCase()});if(Vn.aresh||Vn.allResponseHeaders)switch(Wn){case 1:isDefined(Gn.p.info.responseHeader)&&delete Gn.p.info.responseHeader,isEmpty(Gn.p.info.reshtData)||delete Gn.p.info.reshtData,isDefined(Gn.p.info.guid)&&delete Gn.p.info.guid,isDefined(Gn.p.info.xBrResponse)&&delete Gn.p.info.xBrResponse,isDefined(Gn.p.info.traceResponse)&&delete Gn.p.info.traceResponse;break;case 2:isDefined(Gn.p.info.responseHeader)&&(Gn.p.info.responseHeader=Gn.p.info.responseHeader.replace(/:(.*?)($|\r\n)/g,":**$2")),isEmpty(Gn.p.info.reshtData)||forEach(Gn.p.info.reshtData,function(oo,ao){Gn.p.info.reshtData[ao]="**"}),isDefined(Gn.p.info.guid)&&Gn.p.info.guid!==""&&(Gn.p.info.guid="**"),isDefined(Gn.p.info.xBrResponse)&&Gn.p.info.xBrResponse!=""&&(Gn.p.info.xBrResponse="**"),isDefined(Gn.p.info.traceResponse)&&Gn.p.info.traceResponse!==""&&(Gn.p.info.traceResponse="**")}else switch(Wn){case 1:if(isDefined(Gn.p.info.responseHeader))for(eo=0;eo<no.length;eo++)io=no[eo],(ro=new RegExp(io+":(.*?)(\r\n|$)","i")).test(Gn.p.info.responseHeader)&&(Gn.p.info.responseHeader=Gn.p.info.responseHeader.replace(ro,""));isEmpty(Gn.p.info.reshtData)||forEach(Gn.p.info.reshtData,function(oo,ao){includes(no,ao.toLowerCase())&&delete Gn.p.info.reshtData[ao]}),isDefined(Gn.p.info.guid)&&includes(no,"br-resp-key")&&delete Gn.p.info.guid,isDefined(Gn.p.info.xBrResponse)&&includes(no,"x-br-response")&&delete Gn.p.info.xBrResponse,isDefined(Gn.p.info.traceResponse)&&includes(no,"traceresponse")&&delete Gn.p.info.traceResponse;break;case 2:if(isDefined(Gn.p.info.responseHeader))for(eo=0;eo<no.length;eo++)io=no[eo],(ro=new RegExp(io+":(.*?)(\r\n|$)","i")).test(Gn.p.info.responseHeader)&&(Gn.p.info.responseHeader=Gn.p.info.responseHeader.replace(ro,function(oo,ao){return oo.replace(ao,"**")}));isEmpty(Gn.p.info.reshtData)||forEach(Gn.p.info.reshtData,function(oo,ao){includes(no,ao.toLowerCase())&&(Gn.p.info.reshtData[ao]="**")}),isDefined(Gn.p.info.guid)&&includes(no,"br-resp-key")&&(Gn.p.info.guid="**"),isDefined(Gn.p.info.xBrResponse)&&includes(no,"x-br-response")&&(Gn.p.info.xBrResponse="**"),isDefined(Gn.p.info.traceResponse)&&includes(no,"traceresponse")&&(Gn.p.info.traceResponse="**")}}function d$1(ee,Gn){if(isDefined(Gn)&&(isDefined(Gn.sensitiveNetworkRule)||isDefined(Gn.snr))){var Jn=function(Xn){if(Xn.type===1||Xn.t===1){if(!isDefined(Xn.c||Xn.content))return!1;switch(Xn.r||Xn.rule){case 1:if(qn.toLowerCase()===(Xn.c||Xn.content).toLowerCase())return!0;break;case 2:if(-1<qn.toLowerCase().indexOf((Xn.c||Xn.content).toLowerCase()))return!0;break;case 3:if(startWith(qn.toLowerCase(),(Xn.c||Xn.content).toLowerCase()))return!0;break;case 4:if(endWith(qn.toLowerCase(),(Xn.c||Xn.content).toLowerCase()))return!0;default:return!1}}if(Xn.type===2||Xn.t===2)switch(Xn.r||Xn.rule){case 1:if(Vn===(Xn.c||Xn.content))return!0;break;case 2:if(-1<Vn.indexOf(Xn.c||Xn.content))return!0;break;case 3:if(startWith(Vn,Xn.c||Xn.content))return!0;break;case 4:if(endWith(Vn,Xn.c||Xn.content))return!0;default:return!1}return!1},eo=function(Xn,to){switch(to){case 1:for(var io=0;io<Xn.length;io++){var ro=Xn[io];if(Jn(ro))return!0}return!1;case 2:for(io=0;io<Xn.length;io++)if(ro=Xn[io],!Jn(ro))return!1;return!0;default:return!1}};try{var qn,Vn=ee.p.info.url;if(Vn.indexOf("http")!==0&&Vn.indexOf("file:")!==0&&Vn.indexOf("ws")!==0)return ee;qn=Vn.match(/:\/\/(.[^/:?]+)/)[1]}catch(Xn){return ee}for(var Wn=Gn.sensitiveNetworkRule||Gn.snr,jn=0;jn<Wn.length;jn++){var Qn=Wn[jn];Qn.t!==1&&Qn.type!==1?Qn.t!==2&&Qn.type!==2||eo(Qn.sa||Qn.scopes,2)&&k$1(Qn,ee,Gn):eo(Qn.sa||Qn.scopes,1)&&k$1(Qn,ee,Gn)}}return ee}function P$2(ee){var Gn,qn=0,Vn=0,Wn=0,jn=0,Qn=0,Jn=0,eo=ee.p.metric.domainLookupStart,Xn=ee.p.metric.domainLookupEnd,to=ee.p.metric.connectStart,io=ee.p.metric.connectEnd,ro=ee.p.metric.responseStart,no=ee.p.metric.responseEnd,so=ee.p.metric.secureConnectionStart,oo=ee.p.metric.fetchStart,ao=ee.p.metric.requestStart,so=(isDefined(eo)&&isDefined(Xn)&&0<eo&&(qn=0<(Xn=Xn-eo||-1)?Xn:0),isDefined(to)&&isDefined(io)&&0<to&&(Vn=0<(eo=io-to||-1)?eo:0),isDefined(io)&&isDefined(so)&&0<so&&(Wn=0<(Xn=io-so||-1)?Xn:0),isDefined(ro)&&0<ro?(Qn=(to=0)<(to=isDefined(ao)&&0<ao?ro-ao||-1:to)?to:999,jn=(eo=0)<(eo=isDefined(io)&&0<io?ao-io||-1:eo)?eo:999,Jn=(Gn=0)<(Gn=isDefined(no)?no-ro||-1:Gn)?Gn:999):isDefined(oo)&&0<oo&&(Jn=0<=(Gn=no-oo||-1)?Gn:999),isDefined(ee.p.metric)?ee.p.metric:{});so.domianTime=qn,so.connetcTime=Vn,so.sslt=Wn,so.rti=Qn,so.dti=Jn,so.rt=jn}function I$6(ee,Gn){if(isDefined(Gn.reqBodyKey)){var qn="";if(isJSON(ee.p.info.requestBody))try{var Vn=new Function("","return "+ee.p.info.requestBody)();forEach(Gn.reqBodyKey,function(Qn,Jn){isDefined(Vn[Qn])&&(qn+=Qn+"="+encodeURIComponent(Vn[Qn])+"&")}),ee.p.info[GC.NET_CBBQ]=qn.slice(0,-1)}catch(Qn){}else if(isString(ee.p.info.requestBody))try{forEach(ee.p.info.requestBody.split("&"),function(Qn){forEach(Gn.reqBodyKey,function(Jn,eo){Jn&&Qn.indexOf(Jn+"=")!==-1&&(qn+=Qn.split("=")[0]+"="+encodeURIComponent(Qn.split("=")[1])+"&")})}),ee.p.info[GC.NET_CBBQ]=qn.slice(0,-1)}catch(Qn){}}if(isDefined(Gn.reqURLKey)&&isString(ee.p.info.reqUrlDta))try{var Wn="";forEach(ee.p.info.reqUrlDta.split("&"),function(Qn){forEach(Gn.reqURLKey,function(Jn,eo){Jn&&Qn.split("=")[0]===Jn&&(Wn+=Qn.split("=")[0]+"="+encodeURIComponent(Qn.split("=")[1])+"&")})}),ee.p.info[GC.NET_CBQ]=Wn.slice(0,-1)}catch(Qn){}else delete ee.p.info[GC.NET_CBQ];if(isDefined(ee.p.metric)&&isDefined(ee.p.metric[GC.CALLBACK_END])&&isDefined(ee.p.metric[GC.CALLBACK_START])&&(ee.p.metric[GC.CALLBACK_TIME]=ee.p.metric[GC.CALLBACK_END]-ee.p.metric[GC.CALLBACK_START],ee.p.metric[GC.CALLBACK_TIME]=0<ee.p.metric[GC.CALLBACK_TIME]&&1e3*ee.p.metric[GC.CALLBACK_TIME]||0),isDefined(Gn.reqHeaderKey)&&isDefined(ee.p.info)&&isDefined(ee.p.info[GC.REQUEST_HEADER]))try{var jn="";forEach(ee.p.info[GC.REQUEST_HEADER].split("\r\n"),function(Qn){forEach(Gn.reqHeaderKey,function(Jn){isDefined(Jn)&&Jn!=""&&Qn.split(":")[0].toLowerCase()===Jn.toLowerCase()&&(jn+=Qn.split(":")[0]+"="+encodeURIComponent(Qn.split(":")[1])+"&")})}),ee.p.info[GC.NET_CBHQ]=jn.slice(0,-1)}catch(Qn){}return ee}function O$3(ee){var Gn=ee.data;return isDefined(Gn.tid)&&Gn.tid===""&&delete Gn.tid,isDefined(Gn.xbr)&&Gn.xbr===""&&delete Gn.xbr,isDefined(Gn.trsp)&&Gn.trsp===""&&delete Gn.trsp,isDefined(Gn.tpar)&&Gn.tpar===""&&delete Gn.tpar,isDefined(Gn.cbhq)&&Gn.cbhq===""&&delete Gn.cbhq,isDefined(Gn.cbbq)&&Gn.cbbq===""&&delete Gn.cbbq,isDefined(Gn.cbq)&&Gn.cbq===""&&delete Gn.cbq,isDefined(Gn.ti)&&Gn.ti===""&&delete Gn.ti,isDefined(Gn.ec)&&200<=Gn.ec&&Gn.ec<400&&(isDefined(Gn.ep)&&delete Gn.ep,isDefined(Gn.eop))&&delete Gn.eop,isDefined(Gn.ec)&&400<Gn.ec&&Gn.ec<=602&&isDefined(Gn.eop)&&(Gn.eop=4),isDefined(Gn.cbhq)&&(Gn.cbhq=Gn.cbhq.replace("/","%2F")),isDefined(Gn.ec)&&Gn.ec===602&&isDefined(Gn.ru)&&Gn.ru.indexOf("file:")===0&&(Gn.ec=200,delete Gn.eop,delete Gn.ep,delete Gn.em),ee}function $$2(ee,Gn){var qn;if(isDefined(Gn.reqHeaderTraceKey)&&0<Gn.reqHeaderTraceKey.length&&(qn={},isDefined(ee.p.info.reqhtData&&Object.getOwnPropertyNames(ee.p.info.reqhtData).length<=64)?qn=ee.p.info.reqhtData:Object.getOwnPropertyNames(ee.p.info.reqhtData).slice(0,64).forEach(function(Jn){qn[Jn]=ee.p.info.reqhtData[Jn]}),isEmpty(qn)||(ee.p.info.reqht=qn)),isDefined(Gn.respHeaderTraceKey)&&0<Gn.respHeaderTraceKey.length){for(var Vn={},Wn=0,jn=0,Qn=Gn.respHeaderTraceKey.length-1;jn<=Qn;jn++)Wn<64&&isDefined(ee.p.info.reshtData)&&isDefined(ee.p.info.reshtData[Gn.respHeaderTraceKey[jn]])&&(Wn+=1,Vn[Gn.respHeaderTraceKey[jn]]=ee.p.info.reshtData[Gn.respHeaderTraceKey[jn]]);isEmpty(Vn)||(ee.p.info.resht=Vn)}}function Y$2(ee,Gn,qn){qn=qn||!1;var Vn={};if(ee){var Vn=JSON.parse(JSON.stringify(ee)),Wn=Q(Gn);if(!qn&&isDefined(Wn)&&!U$5(Vn.p.info.url||"",Wn))return null;try{if(isDefined(window.$bonreeReplayUrl)&&-1<Vn.p.info.url.indexOf(window.$bonreeReplayUrl))return null}catch(Qn){}return isDefined(Vn.p.metric)&&P$2.call(this,Vn),isDefined(Gn.sensitiveNetworkRule||Gn.snr)&&d$1.call(this,Vn,Gn),I$6.call(this,Vn,Gn),$$2.call(this,Vn,Gn),qn=Vn.p.info,Wn=Gn.hcs,Gn=(isDefined(Wn)&&(isDefined(qn.requestHeader)||isDefined(qn.responseHeader))&&(Wn===0||Wn===1&&qn.status<400||Wn===1&&qn.code<400)&&(delete Vn.p.info.requestHeader,delete Vn.p.info.responseHeader),Vn&&Vn.p.info.url&&-1<Vn.p.info.url.indexOf("https")?2:1),Wn=getMineTypeByHeader(Vn.p.info[GC.RESPONSE_HEADER])||getMineTypeByHeader(Vn.p.info[GC.REQUEST_HEADER])||getMineTypeByUrl(Vn.p.info[GC.URL])||"text/html",qn=new Metric(Vn.p,GC.NET),(isDefined(qn.data.info[GC.NET_EC])&&400<qn.data.info[GC.NET_EC]||!isDefined(qn.data.info[GC.NET_EC]))&&isDefined(window.BRLog)&&window.BRLog.__sendNetInfo(qn.data.info),qn.info("id").info(GC.URL,"","ru").info(GC.NET_METHOD,"","m").info(GC.NET_IP,"","ti").info(GC.NET_PORT,0,"tp").info(GC.REQUEST_HEADER,"","rh",!0).info(GC.RESPONSE_HEADER,"","rhe",!0).info(GC.NET_TID,"","tid").info(GC.NET_XBR,"","xbr").info(GC.NET_TRACE,"","trsp").info("reqht","","reqht",!0).info("resht","","resht",!0).info(GC.E_TYPE,"http","ep",!0).info(GC.NET_EOP,"","eop",!0).info(GC.NET_TYPE,3,"art").info(GC.PAGE_ID,"","pvid").info(GC.CUSTOM_IC,!1,"ic").info(GC.NET_CBBQ,"","cbbq").info(GC.NET_CBHQ,"","cbhq").info(GC.NET_CBQ,"","cbq").info("ret",Wn,"ret").metric("domianTime",0,"dt").metric("connetcTime",0,"ct").metric("sslt",0,"sslt").metric("rt",0,"rt").metric("rti",0,"rti").metric("dti",0,"dti").metric(GC.DECODED_BODY_SIZE,0,"ds").metric(GC.UPLOAD_BODY_SIZE,0,"rds").metric(GC.NET_PT,Gn,"pt"),Vn.p&&Vn.p.info.ai&&qn.info("ai","","ai"),(Vn.p&&Vn.p.info[GC.NET_EC]==200||Vn.p&&Vn.p[GC.STATUS]==200)&&qn.info(GC.NET_EOP,0,"eop"),Vn.p&&isDefined(Vn.p.info[GC.NET_EC])&&qn.info(GC.NET_EC,200,"ec"),Vn.p&&isDefined(Vn.p.info[GC.STATUS])&&qn.info(GC.STATUS,200,"ec"),Vn.p&&Vn.p.info[GC.E_TYPE]==="XHR"&&qn.info(GC.MESSAGE,"","em",!0),Vn.p&&Vn.p.info[GC.E_TYPE]==="FETCH"&&qn.info(GC.STATUS_TEXT,"","em",!0),O$3(qn.build())}}function F$1(ee){var Gn;if(isDefined(ee))return Gn=new Metric(ee,GC.NET),ee=getMineTypeByUrl(ee.info[GC.URL])||"text/html",Gn.info("id").info("ru").info("m").info("ti").info("tp").info("art").info("pvid","","pvid").info("ic").info("ret",ee,"ret").info("dt").info("ct").info("sslt").info("rt").info("rti").info("dti").info("ds",0,"ds").info("rds",0,"rds").info("pt").info("ec",200,"ec"),O$3(Gn.build())}var r$2=1e3;function s$3(ee,Gn){var qn;return function(){clearTimeout(qn),qn=setTimeout(function(){ee.apply(this,arguments)},Gn)}}function a$1(){return document.hidden!==void 0?{hidden:"hidden",visibilityChange:"visibilitychange",visibilityState:"visibilityState"}:document.msHidden!==void 0?{hidden:"msHidden",visibilityChange:"msvisibilitychange",visibilityState:"msVisibilityState"}:document.webkitHidden!==void 0?{hidden:"webkitHidden",visibilityChange:"webkitvisibilitychange",visibilityState:"webkitVisibilityState"}:{hidden:"hidden",visibilityChange:"visibilitychange",visibilityState:"visibilityState"}}var t$2=a$1();function c$4(){var ee;window.BonreeRecord&&window.BonreeRecord.BonreeRecord&&(ee=window.BonreeRecord.BonreeRecord.getRecordState(),document[t$2.visibilityState]==="visible"&&ee?isDefined(D$5.sett.RecordConfiguration||D$5.sett.mc)&&(window.BonreeRecord.BonreeRecord(D$5.sett),window.BonreeRecord.BonreeRecord.stopRecordUpload(!1)):window.BonreeRecord.BonreeRecord.stopRecordUpload(!0))}function l$3(){var ee=s$3(c$4,r$2);document.addEventListener?document.addEventListener(t$2.visibilityChange,ee,!1):document.attachEvent&&document.attachEvent("on"+t$2.visibilityChange,ee)}function I$5(){D$5.sett.interceptURL&&(U$4(),h$3(),l$3())}function m$5(ee){for(;ee&&ee!==document;){if(ee.tagName&&ee.tagName.toLowerCase()==="a")return ee;ee=ee.parentNode}return null}function R$3(ee){return/^(https?|ftp):\/\/(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&\/=]*)$/.test(ee)}function U$4(){document.addEventListener("click",function(ee){var Gn,qn=m$5(ee.target);qn&&qn.href&&(Gn=qn.getAttribute("target")||"_self",R$3(qn=qn.href))&&f$1(qn)&&(ee.preventDefault&&ee.preventDefault(),ee=c$3(qn),window.open.$ignore=!0,window.open(ee,Gn),window.open.$ignore=!1)},!0)}function h$3(){var ee=window.open;window.open=function(Gn){var qn,Vn=Array.prototype.slice.call(arguments,1);return window.open.$ignore===!0?ee.apply(window,[Gn].concat(Vn)):(qn=f$1(Gn)?c$3(Gn):Gn,ee.apply(window,[qn].concat(Vn)))}}function _$2(){return w$a.getUser()}function A$1(){return window.bonreeRUM&&window.bonreeRUM.fingerId||u$2()}function D$1(){var ee=window.bonreeRUM&&window.bonreeRUM.getSession()||{};return ee&&ee.sessionID}function S$2(){return D$5.sett.appId}function f$1(ee){try{var Gn=document.createElement("a");return Gn.href=ee,Gn.host!==location.host}catch(qn){return!1}}function c$3(ee){var Gn,qn,Vn;return D$5.sett.interceptURL&&(Gn=p$3())?(qn="",(Vn=ee.indexOf("#"))!==-1&&(qn=ee.substring(Vn),ee=ee.substring(0,Vn)),Vn=ee.indexOf("?")===-1?"?":"&",ee+Vn+GC.BR_PARAMS+"="+Gn+qn):ee}function b$4(){var ee=p$3();return ee?GC.BR_PARAMS+"="+ee:""}function p$3(){var ee,Gn,qn,Vn,Wn,jn,Qn;return D$5.sett.interceptURL&&(ee=_$2(),Gn=A$1(),qn=D$1(),Vn=S$2(),Wn=generateFingerprint(),jn=new Date().getTime(),Gn)&&qn&&Vn&&window.encodeURIComponent?((Qn={})[GC.BR_USER_ID]=ee,Qn[GC.BR_SESSION_ID]=qn,Qn[GC.BR_DEVICE_ID]=Gn,Qn[GC.BR_APP_ID]=Vn,Qn[GC.FINGER_ID]=Wn,Qn[GC.TIMESTAMP]=jn,encodeURIComponent(JSON.stringify(Qn))):""}var m$4=function(ee,Gn,qn,Vn,Wn,jn){return!(ee===void 0||(Gn==null||Gn===""?(v$5.log(qn+" Parameter has a value or key ["+ee+"] that does not exist!"),jn):Vn!==null&&Gn.length>Vn?(v$5.log(qn+" Parameter has a value or key ["+ee+"] that the value length exceeds "+Gn.length+"!"),0):!Wn||!isNaN(Gn)||(v$5.log(qn+" Parameter has a value or key ["+ee+"] value is not a valid number!"),0)))},S$1=2083,c$2=256,U$3=512,V$2=function(ee){var Gn={};if(ee!==void 0&&Object.prototype.toString.call(ee)==="[object Object]"){if(64<Object.getOwnPropertyNames(ee).length){var qn,Vn=Object.getOwnPropertyNames(ee).slice(0,64);for(qn in Vn)typeof Vn[qn]!="string"||256<Vn[qn].length||Vn[qn]===""||(Gn[Vn[qn]]=ee[Vn[qn]])}else for(var Wn in ee)typeof Wn!="string"||256<Wn.length||Wn===""||(Gn[Wn]=ee[Wn]);return Object.getOwnPropertyNames(Gn).length===0?void 0:Gn}},A={},R$2={},H$2={},I$4={},L$2=[],x$3=[],re=w$a.getEviData("value");function w$5(ee,Gn){!isDefined(D$5.secondSett)||isEmpty(D$5.secondSett)||isEmpty(ee)||(isDefined(ee.data.url)&&(ee.data.url=d$1({p:{info:{url:ee.data.url}}},D$5.secondSett).p.info.url),isDefined(D$5.secondSett.osType)&&D$5.secondSett.osType!==GC.BROWSER?Gn.call(v$5,stringify({ent:ee.ent,v:ee.data,evi:re}),D$5.secondSett):pushQueueData(ee)),!isDefined(D$5.sett)||isEmpty(D$5.sett)||isEmpty(ee)||(isDefined(D$5.sett.osType)&&D$5.sett.osType!==GC.BROWSER?Gn.call(v$5,stringify({ent:ee.ent,v:ee.data}),D$5.sett):pushQueueData(ee))}var ne={returnShadeArea:function(){return E$6.checkElement()},setCustomSpeedTest:function(ee,Gn){var qn;arguments.length<1||2<arguments.length||ee&&256<ee.length||(Array.isArray(Gn)&&0<Gn.length&&Gn.length<=1e3?(qn={},isDefined(ee)&&ee!==""&&(qn.oip=ee,qn.sti=Gn,qn.timestamp=now(),(qn=new Metric({info:qn},"speedtest")).info("oip").info("sti"),w$5(qn.build(),v$5.customSpeedTestEvent))):log("C156"))},setExtraInfo:function(ee){v$5.log("setExtraInfo=>"+ee);var Gn=ee&&set_64_keys(ee,64)||ee;try{C$7(Gn),M$6(D$5.sett.osType)||M$6(D$5.secondSett.osType)?window.webkit.messageHandlers.brsWKSetExtraInfo.postMessage(stringify(Gn)):V$4(D$5.sett.osType)||V$4(D$5.secondSett.osType)?window.bonreePrivateInterface.call(stringify({setExtraInfo:Gn})):window.bonreePrivateInterface.setExtraInfo(stringify(Gn))}catch(qn){}},setUserID:function(ee){if(v$5.log("setUserID=>"+ee),!m$4("userId",ee,"setUserID",c$2,!1)&&/^[\u4E00-\u9FFFa-zA-Z0-9:_\-@.\s/*#!]+$/.test(ee))try{T$4(ee),M$6(D$5.sett.osType)||M$6(D$5.secondSett.osType)?window.webkit.messageHandlers.brsWKSetUserId.postMessage(ee):V$4(D$5.sett.osType)||V$4(D$5.secondSett.osType)?window.bonreePrivateInterface.call(stringify({setUserID:ee})):window.bonreePrivateInterface.setUserID(ee)}catch(Gn){}},setCustomEventWithLabel:function(ee,Gn,qn,Vn,Wn){var jn,Qn,Jn;arguments.length<1||5<arguments.length||m$4("eventId",ee,"setCustomEvent",c$2,!1)||isDefined(Wn)&&7e3<JSON.stringify(Wn).length||(jn={},Jn=set_length(Gn,c$2),Qn=set_length(Vn,7e3),jn.eventId=isDefined(ee)?ee:"",jn.eventName=Jn,jn.eventParam=Qn,jn.eventLabel=set_length(qn,256),jn.info=V$2(Wn),jn.timestamp=now(),(Jn=new Metric({info:jn},GC.CUSTOM_EVENT)).info("eventId","","i").info("t",0).info("d",0),jn.eventName!==void 0&&jn.eventName!==""&&jn.eventName!==null&&Jn.info("eventName","","n"),jn.eventLabel!==void 0&&jn.eventLabel!=="unuse"&&jn.eventLabel!==""&&jn.eventLabel!==null&&Jn.info("eventLabel","","l"),jn.eventParam!==void 0&&jn.eventParam!==""&&jn.eventParam!==null&&Jn.info("eventParam","","p"),jn.info!==void 0&&jn.info instanceof Object&&Jn.info("info","","info"),w$5(Jn.build(),v$5.customEvent))},setCustomEvent:function(ee,Gn,qn,Vn){this.setCustomEventWithLabel(ee,Gn,"unuse",qn,Vn)},setCustomLog:function(ee,Gn){var qn,Vn,Wn;arguments.length<1||2<arguments.length?v$5.log("setcustomlog custom log parameter number error"):m$4("logInfo",ee,"setCustomLog",null,!1)||(qn={},Wn=isDefined(ee)&&set_length(ee,1e4)||"",Vn=isDefined(Gn)?set_length(Gn,1e4):Gn,qn.logInfo=Wn,qn.logParam=Vn,qn.timestamp=now(),(Wn=new Metric({info:qn},GC.CUSTOM_LOG)).info("logInfo","","i"),qn.logParam!==void 0&&Wn.info("logParam","","p"),w$5(Wn.build(),v$5.customLogEvent))},setCustomMetric:function(ee,Gn,qn){var Vn;arguments.length<1||3<arguments.length?v$5.log("setCustomMetric\u81EA\u5B9A\u4E49\u6307\u6807\u53C2\u6570\u4E2A\u6570\u9519\u8BEF"):m$4("metricName",ee,"setCustomMetric",c$2,!1)||m$4("metricValue",Gn,"setCustomMetric",null,!1)||((Vn={}).metricName=isDefined(ee)?ee:"",Vn.metricValue=isDefined(Gn)?Math.round(Gn):"",isDefined(qn)&&(Vn.metricParam=1e4<qn.length?qn.slice(0,1e4):qn),Vn.timestamp=now(),(Vn=new Metric({info:Vn},GC.CUSTOM_METRIC)).info("metricName","","n").info("metricValue",0,"v"),qn!==void 0&&Vn.info("metricParam","","p"),w$5(Vn.build(),v$5.customMetricEvent))},setCustomException:function(ee,Gn,qn){var Vn,Wn,jn;arguments.length<1||3<arguments.length?v$5.log("onEvent\u81EA\u5B9A\u4E49\u4E8B\u4EF6\u53C2\u6570\u4E2A\u6570\u9519\u8BEF"):m$4("exceptionType",ee,"setCustomException",c$2,!1)||(Vn={},jn=isDefined(Gn)&&set_length(Gn,512)||"",Wn=isDefined(qn)&&set_length(qn,1e4)||"",Vn.exceptionType=isDefined(ee)?ee:"",Vn.causedBy=jn,Vn.errorDump=Wn,Vn.timestamp=now(),(jn=new Metric({info:Vn},GC.CRASH)).info("causedBy","","cab").info("exceptionType","","t").info("errorDump","","p").info("ic",!0).info("id",uuid()),w$5(jn.build(),v$5.customExceptionEvent))},setCustomPageStart:function(ee,Gn){var qn;arguments.length<1||2<arguments.length||m$4("pageName",ee,"setCustomPageStart",c$2,!1)||includes(L$2,ee)||(L$2.push(ee),A[ee]=dateNow(),R$2[ee]=buildViewID(),!ee)||((qn=new Metric({info:{pageName:ee,param:set_length(Gn,c$2),timestamp:now()}},GC.VIEW)).info("pageName","","n").info("ci",R$2[ee]).info("lt",0).info("m",1).info("t",1).info("ic",!0),Gn!==void 0&&qn.info("param","","p"),w$5(qn.build(),v$5.webviewPageEvent))},setCustomPageEnd:function(ee,Gn){var qn,Vn;arguments.length<1||2<arguments.length||m$4("pageName",ee,"setCustomPageEnd",c$2,!1)||!includes(L$2,ee)||(Vn=0,isDefined(dateNow)&&A&&isDefined(A[ee])&&(Vn=dateNow()-A[ee],Vn*=1e3,ee)&&(qn=set_length(Gn,c$2),qn={pageName:ee,param:qn,timestamp:now()},L$2=filter(L$2,function(Wn){return Wn!==ee}),delete A[ee],(qn=new Metric({info:qn},GC.VIEW)).info("pageName","","n").info("ci",R$2[ee]).info("st",Vn).info("lt",0).info("m",2).info("t",1).info("ic",!0),Gn!==void 0&&qn.info("param","","p"),w$5(qn.build(),v$5.webviewPageEvent),delete R$2[ee]))},setCustomH5performanceData:function(ee){var Gn={};try{Gn=JSON.parse(ee)}catch(jn){return void v$5.log("setCustomH5performanceData has error! ==>"+jn)}var qn=!0;if(qn&&forEach(["url"],function(jn){m$4(jn,Gn[jn],"setCustomH5performanceData",S$1,!1)&&(Gn[jn]=set_length(Gn[jn],S$1))}),qn&&forEach(["imd","ns","ues","uee","rds","rde","fs","dls","dle","cs","scs","ce","reqs","rsps","rspe","dl","di","dcles","dclee","dc","les","lee","fp","fcp","lcp"],function(jn){if(m$4(jn,Gn[jn],"setCustomH5performanceData",null,!0))return qn=!1}),qn&&((Gn.pvid===void 0||Gn.pvid===null||Gn.pvid===""||Gn.pvid.length>c$2)&&(Gn.pvid=uuid()),Gn={timestamp:now(),url:Gn.url,pvid:Gn.pvid,ic:!0,wpi:{ns:isDefined(Gn.ns)&&0<Gn.ns?Gn.ns:0,ues:isDefined(Gn.ues)&&0<Gn.ues?Gn.ues:-1,uee:isDefined(Gn.uee)&&0<Gn.uee?Gn.uee:-1,rds:isDefined(Gn.rds)&&0<Gn.rds?Gn.rds:-1,rde:isDefined(Gn.rde)&&0<Gn.rde?Gn.rde:-1,fs:isDefined(Gn.fs)&&0<Gn.fs?Gn.fs:0,dls:isDefined(Gn.dls)&&0<Gn.dls?Gn.dls:-1,dle:isDefined(Gn.dle)&&0<Gn.dle?Gn.dle:-1,cs:isDefined(Gn.cs)&&0<Gn.cs?Gn.cs:-1,scs:isDefined(Gn.scs)&&0<Gn.scs?Gn.scs:-1,ce:isDefined(Gn.ce)&&0<Gn.ce?Gn.ce:-1,reqs:isDefined(Gn.reqs)&&0<Gn.reqs?Gn.reqs:0,rsps:isDefined(Gn.rsps)&&0<Gn.rsps?Gn.rsps:0,rspe:isDefined(Gn.rspe)&&0<Gn.rspe?Gn.rspe:0,dl:isDefined(Gn.dl)&&0<Gn.dl?Gn.dl:0,di:isDefined(Gn.di)&&0<Gn.di?Gn.di:0,dcles:isDefined(Gn.dcles)&&0<Gn.dcles?Gn.dcles:0,dclee:isDefined(Gn.dclee)&&0<Gn.dclee?Gn.dclee:0,dc:isDefined(Gn.dc)&&0<Gn.dc?Gn.dc:0,les:isDefined(Gn.les)&&0<Gn.les?Gn.les:0,lee:isDefined(Gn.lee)&&0<Gn.lee?Gn.lee:-1,fp:isDefined(Gn.fp)&&0<Gn.fp?Gn.fp:-1,fcp:isDefined(Gn.fcp)&&0<Gn.fcp?Gn.fcp:-1,lcp:isDefined(Gn.lcp)&&0<Gn.lcp?Gn.lcp:-1}},isDefined(Gn.url))&&Gn.url.trim("")!==""){(G$4(D$5.sett.osType)||G$4(D$5.secondSett.osType))&&((Wn=new Metric({info:Gn},GC.WEBVIEWDATA)).info("url","").info("ic",!0).info("pvid").info("wpi"),pushQueueData(Wn.build()));var Wn=w$a.getEviData("value"),Vn=stringify({h5:[{ent:now(),v:Gn,k:"h5",evi:Wn}],imd:Gn.imd}),Wn=stringify({ent:now(),v:Gn,evi:Wn});v$5.log("setCustomH5performanceData=> "+Wn);try{M$6(D$5.sett.osType)||M$6(D$5.secondSett.osType)?window.webkit.messageHandlers.brsWKSetCustomH5performanceData.postMessage(Wn):V$4(D$5.sett.osType)||V$4(D$5.secondSett.osType)?window.bonreePrivateInterface.call(Vn):window.bonreePrivateInterface.webViewEventBus(Vn,D$5.sett.webviewID||D$5.secondSett.webviewID)}catch(jn){}}},setCustomRouteChangeData:function(ee){var Gn={timestamp:now()};try{(Gn=JSON.parse(ee)).d=1e3*Math.round(Gn.d)}catch(Wn){return void v$5.log("setCustomRouteChangeData has error! ==>"+Wn)}var qn,Vn=!0;Vn&&forEach(["tu","fu","rt","pu"],function(Wn){(!isDefined(Gn[Wn])||Gn[Wn]===""||Gn[Wn].length>S$1)&&(Vn=!1)}),Vn&&forEach(["pt","fw"],function(Wn){(!isDefined(Gn[Wn])||Gn[Wn]===""||Gn[Wn].length>c$2)&&(Vn=!1)}),Vn&&forEach(["d","sta"],function(Wn){isDefined(Gn[Wn])&&typeof Gn[Wn]=="number"||(Vn=!1)}),Vn&&(Gn.fu===void 0&&Gn.fu===null?v$5.log("setCustomRouteChangeData Parameter has a value or key [fu] that does not exist!"):(Gn.al!==void 0&&Gn.al!==null&&Gn.al.length>c$2&&(v$5.log("setCustomRouteChangeData Parameter has a value or key [al] that the value length exceeds "+c$2+"!"),Gn.al=""),Gn.timestamp=now(),(qn=new Metric({info:Gn},GC.ROUTE)).info("tu","").info("fu","").info("d",0).info("sta",0).info("pt","").info("rt","").info("pu","").info("fw","").info("ic",!0).info("ctp",2).info("pvid",isDefined(D$5.sett.pageViewId)?D$5.sett.pageViewId:""),isDefined(Gn.al)&&Gn.al!==""&&qn.info("al",""),isDefined(window.performance)&&isDefined(window.performance.timing)&&qn.info("ns",window.performance.timing.navigationStart),w$5(qn.build(),v$5.routeChangeData)))},setCustomNetworkData:function(ee){var Gn={};try{Gn=JSON.parse(ee)}catch(Wn){return void v$5.log("setCustomNetworkData has error! ==>"+Wn)}var qn,Vn=!0;forEach(["tp","dt","ct","sslt","rt","rti","dti","ds","pt","rds"],function(Wn){if(m$4(Wn,Gn[Wn],"setCustomNetworkData",null,!0)||Gn[Wn]<0)return Vn=!1}),m$4("ti",Gn.ti,"setCustomNetworkData",null,!1)&&(Vn=!1),isDefined(Gn.ru)&&Gn.ru!==""&&(m$4("ru",Gn.ru,"setCustomNetworkData",S$1,!1)&&(Gn.ru=set_length(Gn.ru,S$1)),m$4("m",Gn.m,"setCustomNetworkData",S$1,!1)&&(Gn.m=set_length(Gn.m,S$1)),Vn)&&(Gn.timestamp=1e3*new Date().getTime(),(qn=new Metric({info:Gn},GC.NET)).info("ru","").info("m","").info("ti","").info("tp",1e3).info("dt",1e3).info("ct",1e3).info("sslt",1e3).info("rt",1e3).info("rti",1e3).info("dti",1e3).info("ds",0).info("pt",0).info("rh","").info("rhe","").info("ret","").info("rds",0).info("id",uuid()).info("ic",!0).info("art",0).info("ec",200),w$5(qn.build(),v$5.NetworkEvent))},setCustomEventStart:function(ee,Gn,qn,Vn,Wn){var jn,Qn,Jn;arguments.length<1||5<arguments.length||m$4("eventId",ee,"setCustomEventStart",null,!1)||isDefined(Wn)&&7e3<JSON.stringify(Wn).length||includes(x$3,ee)||(x$3.push(ee),!ee)||(I$4[ee]=dateNow(),H$2[ee]=buildViewID(),jn={},Jn=set_length(Gn,c$2),Qn=set_length(Vn,7e3),jn.eventId=isDefined(ee)?ee:"",256<ee.length||ee.length===0||(jn.eventName=Jn,jn.eventParam=Qn,jn.eventLabel=set_length(qn,256),jn.info=V$2(Wn),jn.timestamp=now(),(Jn=new Metric({info:jn},GC.CUSTOM_EVENT)).info("eventId","","i").info("t",1).info("d",0).info("ci",H$2[ee]),jn.eventName!==void 0&&jn.eventName!==""&&Jn.info("eventName","","n"),jn.eventLabel!==void 0&&jn.eventLabel!==""&&Jn.info("eventLabel","","l"),jn.eventParam!==void 0&&jn.eventParam!==""&&Jn.info("eventParam","","p"),jn.info!==void 0&&jn.info instanceof Object&&Jn.info("info","","info"),w$5(Jn.build(),v$5.customEvent)))},setCustomEventEnd:function(ee,Gn,qn,Vn,Wn){if(!(arguments.length<1||5<arguments.length)&&!m$4("eventId",ee,"setCustomEventEnd",null,!1)&&!(isDefined(Wn)&&7e3<JSON.stringify(Wn).length)&&includes(x$3,ee)){if(ee){var jn=0,Qn=(isDefined(dateNow)&&I$4&&isDefined(I$4[ee])&&(jn=dateNow()-I$4[ee],jn*=1e3),{}),Jn=set_length(Gn,c$2),eo=set_length(Vn,7e3);if(Qn.eventId=isDefined(ee)?ee:"",256<ee.length||ee.length===0)return;Qn.eventName=Jn,Qn.eventParam=eo,Qn.eventLabel=set_length(qn,256),Qn.info=V$2(Wn),Qn.timestamp=now(),Jn=new Metric({info:Qn},GC.CUSTOM_EVENT),Jn.info("eventId","","i").info("t",2).info("d",jn).info("ci",H$2[ee]),Qn.eventName!==void 0&&Qn.eventName!==""&&Jn.info("eventName","","n"),Qn.eventLabel!==void 0&&Qn.eventLabel!==""&&Jn.info("eventLabel","","l"),Qn.eventParam!==void 0&&Qn.eventParam!==""&&Jn.info("eventParam","","p"),Qn.info!==void 0&&Qn.info instanceof Object&&Jn.info("info","","info"),w$5(Jn.build(),v$5.customEvent)}x$3=filter(x$3,function(Xn){return Xn!==ee}),delete I$4[ee],delete H$2[ee]}},startSpan:function(ee,Gn){return new n(ee,Gn)},addUserExtraInfo:function(ee,Gn){var qn;m$4("key",ee,"setUserProperty",c$2,!1,!1)||!/^[:0-9a-zA-Z /@._-]{1,}$/.test(ee)||/^\s{0,}$/.test(ee)||typeof ee=="boolean"||typeof ee=="number"||!isDefined(Gn)||typeof Gn=="object"||/^\s{0,}$/.test(Gn)||(typeof Gn!="number"&&Gn.length>U$3&&(Gn=set_length(Gn,U$3)),(qn=w$a.getUser()||"")!=""&&((qn={ui:qn,c:2,i:{},timestamp:now()}).i[ee]=Gn,(ee=new Metric({info:qn},GC.USERCHANGE)).info("ui","").info("c").info("i"),w$5(ee.build(),v$5.setUserProperty)))},setUserExtraInfo:function(ee){if(isObj(ee)&&!isEmpty(ee)){for(var Gn in ee){var qn,Vn;Object.hasOwnProperty.call(ee,Gn)&&(qn=ee[Gn],!m$4("key",Gn,"setUserProperty",c$2,!(Vn=!0),!1)&&/^[:0-9a-zA-Z /@._-]{1,}$/.test(Gn)&&!/^\s{0,}$/.test(Gn)||(delete ee[Gn],Vn=!1),isDefined(qn)&&typeof qn!="object"&&!/^\s{0,}$/.test(qn)||(delete ee[Gn],Vn=!1),Vn)&&typeof qn!="number"&&qn.length>U$3&&(ee[Gn]=set_length(qn,U$3))}var Wn,jn=set_64_keys(ee,64);isEmpty(jn)||(Wn=w$a.getUser()||"")!=""&&((Wn=new Metric({info:{ui:Wn,c:1,i:jn,timestamp:now()}},GC.USERCHANGE)).info("ui","").info("c").info("i"),w$5(Wn.build(),v$5.setUserProperty))}},increaseUserExtraInfo:function(ee,Gn){var qn;!m$4("key",ee,"setUserProperty",c$2,!1,!1)&&/^[:0-9a-zA-Z /@._-]{1,}$/.test(ee)&&!/^\s{0,}$/.test(ee)&&typeof ee=="string"&&isDefined(Gn)&&typeof Gn!="object"&&typeof Gn=="number"&&(typeof Gn!="number"&&Gn.length>U$3&&(Gn=set_length(Gn,U$3)),(qn=w$a.getUser()||"")!="")&&((qn={ui:qn,c:4,i:{},timestamp:now()}).i[ee]=Gn,(ee=new Metric({info:qn},GC.USERCHANGE)).info("ui","").info("c").info("i"),w$5(ee.build(),v$5.setUserProperty))},removeUserExtraInfo:function(ee){var Gn;m$4("key",ee,"setUserProperty",c$2,!1,!1)||!/^[:0-9a-zA-Z /@._-]{1,}$/.test(ee)||typeof ee!="string"||/^\s{0,}$/.test(ee)||(Gn=w$a.getUser()||"")!=""&&(Gn={ui:Gn,c:3,i:{},timestamp:now()},ee&&(Gn.i[ee]=""),(ee=new Metric({info:Gn},GC.USERCHANGE)).info("ui","").info("c").info("i"),w$5(ee.build(),v$5.setUserProperty))},addEventAttributes:function(ee,Gn){var qn;Gn=Gn||!1,typeof ee=="object"&&(qn=objectAssign({},ee),I$7.addWithObj(ee,Gn),v$5.addAtt({local:Gn,attributes:qn}))},addEventAttributeWithKey:function(ee,Gn,qn){I$7.addWithKey(ee,Gn,qn=qn||!1),qn={local:qn,attributes:{}},qn.attributes[ee]=Gn,v$5.addAtt(qn)},removeEventAttributeWithKeys:function(ee){I$7.delWithKeys(ee),v$5.delAtt(ee)},removeAllEventAttributes:function(){I$7.delAll(),v$5.delAttAll()},getIntersectionObserverInfo:function(ee){I$8(ee)},actionExternalData:function(ee){tt(ee)},getUrlParam:b$4};function i$1(ee){this.n=ee&&typeof ee=="string"?ee:ee.name,this.t=12,this.to=!1,this.ice=!1,this.st=1,this.et=0,this.ti=1225,this.im=!0,this.ne={}}function e$1(){this.lastTarget=null,this.count=0,this.lastTime=0}i$1.prototype.funActionModify=function(ee,Gn){return isDefined(ee)&&isDefined(Gn)&&(this[ee]=Gn),this},i$1.prototype.funActionBuild=function(){var ee={n:this.n,t:this.t,to:this.to,ice:this.ice,st:this.st,et:this.et||now(),ti:this.ti,im:this.im,ne:this.ne};return isEmpty(ee.ne)&&delete ee.ne,ee},e$1.prototype.isClick=function(ee,Gn){return Gn=Gn&&Gn/1e3||+new Date,this.lastTarget===null&&this.count===0?(this.lastTarget=ee,this.count+=1,this.lastTime=Gn,0):this.lastTarget!==ee?(this.lastTarget=ee,this.count=1,this.lastTime=Gn,0):this.lastTarget==ee&&300<=Gn-this.lastTime?(this.count=1,this.lastTime=Gn,0):this.lastTarget==ee&&this.count<2&&Gn-this.lastTime<300?(this.count+=1,this.lastTime=Gn,0):this.lastTarget==ee&&2<=this.count&&Gn-this.lastTime<300?(this.count+=1,this.lastTime=Gn,1):void 0};var h$2=new e$1,m$3;function z$2(ee){if(isDefined(ee)){var Gn=D$5.sett.findGenerate||0,qn=getActionText(ee,Gn,D$5.sett.specialCharacter),Vn=D$5.sett.findEleReg||null;if(isDefined(Vn)){for(var Wn=!0,jn=0;jn<Vn.length;jn++)try{if(!new RegExp(Vn[jn]).test(qn)){Wn=!1;break}}catch(Qn){Wn=!1;break}return Wn?qn:getActionText(ee,0,D$5.sett.specialCharacter)}return qn&&!isEmpty(qn)?qn:getActionText(ee,Gn,D$5.sett.specialCharacter)}}function Z(ee){var Gn;return isDefined(ee)?(ee.$xpath||(200<(Gn=xpath(ee)).length?ee.$xpath="[Error: Overflow with more than 200 characters.]":ee.$xpath=Gn),ee.$xpath):""}function l$2(ee,Gn){var qn;Gn=Gn||!1,isDefined(ee)&&(isDefined(ee.$timeoutTimer)&&clearTimeout(ee.$timeoutTimer),d$5(GC.LIFESTYLE_REQUEST_END+ee.identify),d$5(GC.CUSTOM_ACTION_END),d$5(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify),d$5(GC.ROUTE_CHANGE_END+ee.identify),d$5(GC.WEBSOCKET_CONNECTION_END),d$5(GC.SPAN_CALLBACK_DATA),(qn=new i$1("anonymous")).funActionModify("st",ee.startTime),qn.funActionModify("et",ee.endTime),Gn&&(qn.funActionModify("ice",!0),ee.startEvent.lt=ee.endTime-ee.startTime,ee.startEvent.ice=!0,isDefined(ee.hasEmitStart))&&!ee.hasEmitStart&&(c$7({t:GC.TRACE_ACTION_DATA,p:{info:ee.startEvent},type:1}),isEmpty(D$5.secondSett)||c$7({t:GC.TRACE_ACTION_DATA,p:{info:ee.startEvent},type:2}),ee.hasEmitStart=!0),(Gn=extend({},ee.startEvent)).m=2,Gn.me=qn.funActionBuild(),c$7({t:GC.TRACE_ACTION_DATA,p:{info:Gn},revts:ee.handledRequestStruct,type:1}),isEmpty(D$5.secondSett)||c$7({t:GC.TRACE_ACTION_DATA,p:{info:Gn},revts:ee.handledRequestStructSecond,type:2}))}function K$1(ee){try{d$5(GC.CUSTOM_ACTION_END);var Gn,qn,Vn,Wn,jn,Qn,Jn,eo,Xn=now(),to=(ee.startTime=Xn,ee.requestRecord=[],ee.handledRequestStruct=[],ee.handledRequestStructSecond=[],ee.requestCount=0,ee.routeCount=0,ee.wsCount=0,ee.identify=uuid(),ee.handledRouteRequest=[],ee.handledRouteRequestSecond=[],ee.handledSpan=[],m$a(GC.ROUTE_CHANGE_START,function(io){ee.routeCount=ee.routeCount+1,ee.relatedRoute=!0,io.info.id=ee.identify}),m$a(GC.ROUTE_CHANGE_END+ee.identify,function(io){var ro;ee.routeCount=ee.routeCount-1,isDefined(io.routeData)&&ee.relatedRoute&&0<ee.handledRouteRequest.length&&ee.routeCount===0?((ro=JSON.parse(JSON.stringify(io.routeData))).revts=ee.handledRouteRequest,ee.handledRequestStruct.push(ro)):ee.handledRequestStruct.push(io.routeData),!isEmpty(D$5.secondSett)&&checkMoudle("routechange")&&(isDefined(io.routeData)&&ee.relatedRoute&&0<ee.handledRouteRequestSecond.length&&ee.routeCount===0?((ro=JSON.parse(JSON.stringify(io.routeData))).revts=ee.handledRouteRequestSecond,ee.handledRequestStructSecond.push(ro)):ee.handledRequestStructSecond.push(io.routeData)),ee.hasEmitStart&&ee.requestCount===0&&ee.wsCount===0&&(d$5(GC.LIFESTYLE_REQUEST_END+ee.identify),d$5(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify),d$5(GC.ROUTE_CHANGE_END+ee.identify),d$5(GC.WEBSOCKET_CONNECTION_END),l$2(ee))}),m$a(GC.LIFESTYLE_FETCH_START,function(io){isDefined(io.info)&&(io.info.ai=ee.identify),ee.requestRecord.push(now()),ee.requestCount=ee.requestCount+1}),m$a(GC.LIFESTYLE_XHR_START,function(io){isDefined(io.info)&&(io.info.ai=ee.identify),ee.requestRecord.push(now()),ee.requestCount=ee.requestCount+1}),m$a(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify,function(io){ee.requestCount=ee.requestCount+1}),m$a(GC.LIFESTYLE_REQUEST_END+ee.identify,function(io){ee.requestCount=ee.requestCount-1;var ro,no,oo=w$a.getEviData("value");isDefined(io)&&isDefined(io.requestData)&&isDefined(io.endTime)&&!ee.relatedRoute&&(ro=Y$2(io.requestData,D$5.sett,!0),ee.handledRequestStruct.push({k:ro.type,ent:ro.ent,sin:ro.sin,v:ro.data,evi:oo}),!isEmpty(D$5.secondSett))&&checkMoudle("network")&&(no=Y$2(io.requestData,D$5.secondSett,!0),ee.handledRequestStructSecond.push({k:no.type,ent:no.ent,sin:no.sin,v:no.data,evi:oo})),ee.relatedRoute&&(ro=Y$2(io.requestData,D$5.sett,!0),oo=w$a.getEviData("value"),ee.handledRouteRequest.push({k:ro.type,ent:ro.ent,sin:ro.sin,v:ro.data,evi:oo}),!isEmpty(D$5.secondSett))&&checkMoudle("network")&&(no=Y$2(io.requestData,D$5.secondSett,!0),ee.handledRouteRequestSecond.push({k:no.type,ent:no.ent,sin:no.sin,v:no.data,evi:oo})),ee.requestCount===0&&ee.routeCount===0&&ee.wsCount===0&&(d$5(GC.LIFESTYLE_REQUEST_END+ee.identify),d$5(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify),d$5(GC.ROUTE_CHANGE_END+ee.identify),d$5(GC.WEBSOCKET_CONNECTION_END),isDefined(ee.loadTime)&&l$2(ee),s$8.turnOffRecord(),s$8.stopPageActivityObserver())}),m$a(GC.CUSTOM_ACTION_END,function(io){ee.endTime=now(),l$2(ee,!0)}),m$a(GC.SPAN_CALLBACK_DATA,function(io){var ro,no,oo,ao,so;isDefined(io.websocketData)&&(ro=io.websocketData.type,no=io.websocketData.ent,oo=io.websocketData.sin,ao=io.websocketData.data,so=io.websocketData.evi,io.websocketData.isRelated=!0,ee.handledRequestStruct.push({k:ro,ent:no,sin:oo,v:ao,evi:so}),checkMoudle("span")&&ee.handledRequestStructSecond.push({k:ro,ent:no,sin:oo,v:ao,evi:so}),ee.handledSpan.push(io.websocketData))}),m$a(GC.WEBSOCKET_CONNECTION_START,function(){ee.wsCount=ee.wsCount+1,ee.requestRecord.push(now())}),m$a(GC.WEBSOCKET_CONNECTION_END,function(){0<ee.wsCount&&(ee.wsCount=ee.wsCount-1,ee.requestCount===0)&&ee.routeCount===0&&ee.wsCount===0&&(d$5(GC.LIFESTYLE_REQUEST_END+ee.identify),d$5(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify),d$5(GC.ROUTE_CHANGE_END+ee.identify),d$5(GC.WEBSOCKET_CONNECTION_END),isDefined(ee.loadTime))&&l$2(ee)}),D$5.sett.pageUrl=d$1({p:{info:{url:isDefined(window.location)?window.location.href:""}}},D$5.sett).p.info.url,isDefined(s$8)&&(s$8.startPageActivityObserver(),s$8.turnOnRecord()),ee.target);(isString(to.tagName)||to.name||"")!=="BODY"&&(Gn=Z(to),qn="xpath"+unicode("=")+Gn+",outerHTML"+unicode("=")+to.outerHTML,Vn=ee&&ee.type==="click"?1:4,Wn=ee&&ee.target&&ee.target.tagName||"",jn={t:z$2(to)||to.innerText||"",c:String(to.className||""),tag:to.nodeName||to.localName||"",id:isDefined(to.id)?to.id:""},Qn=D$5.sett.pageUrl,Jn=h$2.isClick(Gn,Xn),ee.startEvent={t:Vn,n:Wn,sa:1,i:qn,vn:Qn,ic:!1,lt:0,is:!1,id:ee.identify,m:1,ice:!1,ci:jn,timestamp:Xn,vci:D$5.sett.pageViewId,ir:Jn},isDefined(m$3)&&isFunction(m$3)&&(eo={xpath:Gn,target:to,t:getActionText(to,0)||to.innerText||""},m$3(eo)),ee.$startTimer=setTimeout(function(){isDefined(s$8)&&isDefined(checkRecord)&&(checkRecord(s$8.mutationRecord,Xn)||checkRecord(s$8.performanceRecord,Xn)||checkRecord(ee.requestRecord,Xn)?(c$7({t:GC.TRACE_ACTION_DATA,p:{info:ee.startEvent},type:1}),isEmpty(D$5.secondSett)||c$7({t:GC.TRACE_ACTION_DATA,p:{info:ee.startEvent},type:2}),d$5(GC.LIFESTYLE_REQUEST_END+ee.identify),d$5(GC.LIFESTYLE_FETCH_START),d$5(GC.LIFESTYLE_XHR_START),d$5(GC.CUSTOM_ACTION_END),d$5(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify),d$5(GC.ROUTE_CHANGE_START),d$5(GC.ROUTE_CHANGE_END+ee.identify),d$5(GC.SPAN_CALLBACK_DATA),d$5(GC.WEBSOCKET_CONNECTION_START),d$5(GC.WEBSOCKET_CONNECTION_END),s$8.turnOffRecord(),s$8.stopPageActivityObserver(),ee.hasEmitStart=!0):(d$5(GC.LIFESTYLE_REQUEST_END+ee.identify),d$5(GC.LIFESTYLE_FETCH_START),d$5(GC.LIFESTYLE_XHR_START),d$5(GC.CUSTOM_ACTION_END),d$5(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify),d$5(GC.ROUTE_CHANGE_START),d$5(GC.ROUTE_CHANGE_END+ee.identify),d$5(GC.SPAN_CALLBACK_DATA),d$5(GC.WEBSOCKET_CONNECTION_START),d$5(GC.WEBSOCKET_CONNECTION_END),s$8.turnOffRecord(),s$8.stopPageActivityObserver()))},1500))}catch(io){log("C151")}}function Y$1(ee){try{var Gn=now(),qn=(isDefined(ee.$startTimer)&&clearTimeout(ee.$startTimer),d$5(GC.LIFESTYLE_FETCH_START),d$5(GC.LIFESTYLE_XHR_START),d$5(GC.ROUTE_CHANGE_START),d$5(GC.WEBSOCKET_CONNECTION_START),Gn-ee.startTime);if(ee.loadTime=qn,ee.endTime=Gn,isDefined(s$8))if(!ee.hasEmitStart&&(checkRecord(ee.requestRecord,ee.startTime)||checkRecord(s$8.mutationRecord,ee.startTime)||checkRecord(s$8.performanceRecord,ee.startTime)))ee.startEvent.lt=qn,c$7({t:GC.TRACE_ACTION_DATA,p:{info:ee.startEvent},type:1}),isEmpty(D$5.secondSett)||c$7({t:GC.TRACE_ACTION_DATA,p:{info:ee.startEvent},type:2}),ee.hasEmitStart=!0,ee.$timeoutTimer=setTimeout(function(){l$2(ee)},12e4),ee.requestCount===0&&ee.routeCount===0&&ee.wsCount===0&&l$2(ee),s$8.turnOffRecord(),s$8.stopPageActivityObserver();else{if(0<ee.handledSpan.length)for(var Vn=0;Vn<ee.handledSpan.length;Vn++)w$5(ee.handledSpan[Vn],v$5.spanEvent);d$5(GC.LIFESTYLE_REQUEST_END+ee.identify),d$5(GC.CUSTOM_ACTION_END),d$5(GC.LIFESTYLE_CALLBACK_REQUEST_START+ee.identify),d$5(GC.ROUTE_CHANGE_END+ee.identify),d$5(GC.WEBSOCKET_CONNECTION_END),d$5(GC.SPAN_CALLBACK_DATA)}}catch(Wn){log("C152")}}function x$2(){c$7({t:GC.CUSTOM_ACTION_END})}function v$2(){var ee;window.__POWERED_BY_QIANKUN__?D$5.sett.qiankunContainer&&(ee=document.querySelector(D$5.sett.qiankunContainer))&&(on$1(ee,"click",K$1,!0),on$1(ee,"click",function(Gn){return setTimeout(function(){return Y$1(Gn)},600)},!1)):(on$1(window,"click",K$1,!0),on$1(window,"click",function(Gn){return setTimeout(function(){return Y$1(Gn)},600)},!1))}function tt(ee){ee&&isFunction(ee)?m$3=ee:console.warn("The parameters passed in are incorrect. Please pass in a function!!")}function e(ee,Gn){this.lastTimer=null,this.lastEvent=null,this.period=Gn||1e3,this.compare=function(){return!1},this.submit=ee||NIL_FN}function s$2(ee){var Gn=ee.data;return isDefined(Gn.al)&&Gn.al===""&&delete Gn.al,ee}function p$2(){try{return navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)?2:1}catch(ee){log("C119")}}function routeService(ee){var Gn=new Metric(ee.p,GC.ROUTE),qn=p$2(),qn=(Gn.info(GC.URL,"","tu").info(GC.REFERRER,"","fu").info(GC.DURATION,0,"d").info(GC.STATUS,0,"sta").info(GC.ALIAS,"","al").info(GC.PATH,"","pt").info(GC.ROOT,"","rt").info(GC.FULL_URL,"","pu").info(GC.FRAME_WORK,"","fw").info(GC.CUSTOM_IC,!1,"ic").info(GC.CLIENT_TYPE,qn,"ctp").info("pvid","","pvid").info("ns",0,"ns"),isDefined(ee.p.info.wri)&&Gn.info("wri",[],"wri"),Gn.build());return isDefined(ee.revts)&&0<ee.revts.length&&(qn.revts=ee.revts),s$2(qn)}extend(e.prototype,{$schedule:function(ee){isDefined(this.lastTimer)&&clearTimeout(this.lastTimer);var Gn=this;this.lastEvent=ee,this.lastTimer=delay(function(){Gn.submit(Gn.lastEvent),Gn.lastEvent=null},this.period)},event:function(ee){isDefined(this.lastEvent)&&(this.compare(this.lastEvent,ee)?(ee.info[GC.DURATION]=ee.info[GC.START_TIME]-this.lastEvent.info[GC.START_TIME],ee.info[GC.TIMESTAMP]=this.lastEvent.info[GC.TIMESTAMP],ee.info[GC.START_TIME]=this.lastEvent.info[GC.START_TIME],ee.info.count=this.lastEvent.info.count+1):this.submit(this.lastEvent)),this.$schedule(ee)},flush:function(){isDefined(this.lastTimer)&&(clearTimeout(this.lastTimer),this.lastTimer=null),isDefined(this.lastEvent)&&(this.submit(this.lastEvent),this.lastEvent=null)},equalsWith:function(ee){"apply"in ee&&(this.compare=ee)}});var T$2=window.location&&window.location.href;function getFramework(){return window?window.Vue!==void 0||window.__VUE__||window.$nuxt&&window.$nuxt._isVue?"Vue":window.React?"React":window.ng!==void 0||isDefined(window.Zone)||isDefined(window.angular)?"Angular":"Other":"Other"}var m$2=[],a={};function q(){document&&(w$a.setData(GC.TITLE,document.title),w$a.setEviData("n",document.URL),document.title!=""&&w$a.setEviData("vt",document.title),document.referrer&&w$a.setEviData("ru",document.referrer),w$a.setEviMap())}function j(ee){return-1<(1<(ee=ee||"").split("?").length?ee.split("?")[1]:"").indexOf("#/")}function b$3(ee){delay(function(){return D$5.findElesFun(!0)},1e3);var Gn=!1;if(isDefined(T$2)){if(isHashRoute(ee)){var qn=extractHashRoute(T$2),Vn=extractHashRoute(ee),Gn=j(ee);if(qn&&Vn&&Vn===qn)return}else if(T$2.split("?")[0]===(ee&&ee.split("?")[0]))return;document&&document.ready&&document.ready(q);var Wn={id:""};if(p$9&&p$9(GC.ROUTE_CHANGE_START)){c$7({t:GC.ROUTE_CHANGE_START,info:Wn}),isDefined(a)&&0<Object.getOwnPropertyNames(a).length&&Object.keys(a).map(function(no){typeof a[no]=="function"&&(a[no](),delete a[no])}),D$5.sett.isSinglePage=!0,D$5.sett.pageViewId=uuid();var Vn=extend({},location),jn={info:{uuid:uuid(),timestamp:now(),url:Vn.href,referrer:T$2,duration:1e3,status:0,path:Vn.hash===""?Vn.pathname:Vn.hash,root:Vn.hash===""?Vn.origin+Vn.pathname:Vn.origin+Vn.pathname+Vn.hash.replace("#/",""),fullUrl:Vn.hash!==""?Vn.origin+Vn.pathname:Vn.origin+"/",framework:getFramework(),pvid:D$5.sett.pageViewId,ns:isDefined(performance)&&isDefined(performance.timing)?performance.timing.navigationStart:0}};if(!Gn)for(var Qn=["url","referrer","path","root","fullUrl"],Jn=0;Jn<Qn.length;Jn++){var eo=Qn[Jn];-1<jn.info[eo].indexOf("?")&&(jn.info[eo]=jn.info[eo].split("?")[0])}var qn=function(){var no,oo,ao,so,co;isDefined(a)&&isDefined(a[jn.info.uuid])&&(isDefined(a)&&isDefined(a[jn.info.uuid])&&delete a[jn.info.uuid],no=(so=routeService({p:jn})).type,oo=so.ent,ao=so.sin,so=so.data,co=w$a.getEviData("value"),c$7({t:GC.ROUTE_CHANGE_END+Wn.id,routeData:{k:no,ent:oo,sin:ao,v:so,evi:co}}))},ee=(isDefined(a)&&(a[jn.info.uuid]=qn),setTimeout(qn,1e3)),Xn=new PageActivityManager(function(no){forEach(no,function(oo){var ao={st:getFixedMetric(oo,GC.START_TIME),name:d$1({p:{info:{url:oo[GC.NAME]}}},D$5.sett).p.info.url,dura:isDefined(oo[GC.DURATION])?getFixedMetric(oo,GC.DURATION):oo[GC.RESPONSE_END]-oo[GC.START_TIME],rt:getMineTypeByUrl(oo[GC.NAME]),fs:getFixedMetric(oo,GC.FETCH_START),dls:getFixedMetric(oo,GC.DOMAIN_LOOKUP_START),dle:getFixedMetric(oo,GC.DOMAIN_LOOKUP_END),cs:getFixedMetric(oo,GC.CONNECT_START),ce:getFixedMetric(oo,GC.CONNECT_END),scs:getFixedMetric(oo,GC.SECURE_CONNECTION_START),reqs:getFixedMetric(oo,GC.REQUEST_START),rsps:getFixedMetric(oo,GC.RESPONSE_START),rspe:getFixedMetric(oo,GC.RESPONSE_END),rs:isDefined(oo[GC.RESPONSE_STATUS])?oo[GC.RESPONSE_STATUS]:-1};isDefined(oo[GC.INITIATOR_TYPE])&&(ao.it=oo[GC.INITIATOR_TYPE]),isDefined(oo[GC.DECODED_BODY_SIZE])?(ao.ts=oo[GC.TRANSFER_SIZE],ao.ebs=oo[GC.ENCODED_BODY_SIZE]>=Math.pow(2,64)?Math.pow(2,60):oo[GC.ENCODED_BODY_SIZE],ao.dbs=oo[GC.DECODED_BODY_SIZE]):(ao.ts=0,ao.ebs=0,ao.dbs=0),isDefined(ao.it)&&(ao.it==="xmlhttprequest"||ao.it==="fetch")||m$2.push(ao)})});isDefined(ee)&&(clearTimeout(ee),setTimeout(qn,1e3)),Xn.turnOnRecord(),Xn.startPageActivityObserver(),qn=function(){var no,oo,ao,so,co;isDefined(a)&&isDefined(a[jn.info.uuid])?(isDefined(a)&&isDefined(a[jn.info.uuid])&&delete a[jn.info.uuid],0<Xn.mutationRecord.length?0<Xn.performanceRecord.length?(so=Xn.performanceRecord.pop(),no=Xn.mutationRecord.pop(),jn.info.duration=0<so-no?so-jn.info.timestamp:no-jn.info.timestamp):jn.info.duration=Xn.mutationRecord.pop()-jn.info.timestamp:0<Xn.performanceRecord.length&&(jn.info.duration=Xn.performanceRecord.pop()-jn.info.timestamp),Xn.turnOffRecord(),Xn.stopPageActivityObserver(),isDefined(jn)&&isDefined(m$2)&&0<m$2.length&&(jn.info.wri=m$2),no=(so=routeService({p:jn})).type,oo=so.ent,ao=so.sin,so=so.data,co=w$a.getEviData("value"),c$7({t:GC.ROUTE_CHANGE_END+Wn.id,routeData:{k:no,ent:oo,sin:ao,v:so,evi:co}}),isDefined(m$2)&&(m$2=[])):(Xn.turnOffRecord(),Xn.stopPageActivityObserver())},isDefined(a)&&(a[jn.info.uuid]=qn),T$2=Vn.href}}}function S(){}function K(){var ee=new S(isDefined(window.location)?window.location:"");return ee.init(),ee}function U$2(ee,Gn,qn){if(!isReadable(ee.file)&&Gn){try{for(var no=function(oo){return oo=Number(oo),isNaN(oo)?0:oo},Vn,Wn=/[@(\s]+?([\S]+):(\d+):(\d+)\)?/,jn=/[@(\s]+?([\S]+):(\d+)\)?/,Qn=/([^@(\s)]+):(\d+):(\d+)/,Jn=/([^@(\s)]+):(\d+)/,eo=Gn.split("\n"),Xn=!0,to=0;to<eo.length;to++)if(Vn=eo[to],Wn.test(Vn)||jn.test(Vn)){if(!Xn||!qn){var io=last(Vn.split(/\s+/)),ro=Qn.exec(io)||Jn.exec(io);ee.file=ro[1].replace(/^\(/,""),ee.line=no(ro[2]),ee.column=no(ro[3]);break}Xn=!1,eo[to]=""}qn&&(ee.stack=eo.join("\n").replace(/\n+/g,"\n").replace(/^\n/,""))}catch(no){log("C146")}return ee}}function d(ee,Gn,qn){var Vn;return isDefined(Gn)&&(Vn=Gn.name||Gn.constructor&&Gn.constructor.name,ee.name=Vn&&toString(Vn)||"",isReadable(ee.message)||(ee.message=(isReadable(Vn)?Vn+": ":"")+Gn.message),isReadable(ee.file)||(ee.file=Gn.fileName),isDefined(ee.line)||(ee.line=Gn.lineNumber,ee.column=Gn.columnNumber),ee.stack=Gn.stack,ee.framework=getFramework(),U$2(ee,ee.stack,qn)),ee}S.prototype.trackHistory=function(){if(isDefined(history.pushState)&&isDefined(history.replaceState)){var ee=window.location.href,Gn=history.pushState,qn=history.replaceState;if(isDefined(Gn)&&!isDefined(Gn.$hasHooked))try{history.pushState=function(Wn,jn,Qn){Gn.apply(this,[Wn,jn,Qn]),Vn(Qn?Qn.toString():null)},history.pushState.$hasHooked=!0}catch(Wn){log("C161")}if(isDefined(qn)&&!isDefined(history.replaceState.$hasHooked))try{history.replaceState=function(Wn,jn,Qn){qn.apply(this,[Wn,jn,Qn]),Vn(Qn?Qn.toString():null)},history.replaceState.$hasHooked=!0}catch(Wn){log("C161")}window.addEventListener?window.addEventListener("popstate",function(){Vn()},!1):window.attachEvent&&window.attachEvent("onpopstate",function(){Vn()})}function Vn(Wn){Wn=Wn||window.location.href,Wn!==ee&&b$3(ee=Wn)}},S.prototype.trackHashchage=function(){var ee=window.location.href;function Gn(qn){qn=qn||window.location.href,qn!==ee&&b$3(ee=qn)}window.addEventListener?window.addEventListener("hashchange",function(qn){Gn(qn&&qn.newURL)},!1):window.attachEvent&&window.attachEvent("onhashchange",function(qn){Gn(qn&&qn.newURL)})},S.prototype.init=function(){this.trackHistory(),this.trackHashchage()};var E$2=new e(function(ee){c$7({t:GC.ERROR_DATA,p:ee})},D$5.sett.debounce);function w$4(ee,Gn){isDefined(ee.line)||(ee.line=1),isDefined(ee.column)||(ee.column=1),ee={info:extend(ee,{message:withLength(ee.message,200),duration:0,type:isDefined(ee.type)?ee.type:0,count:1,file:withLength(ee.file,200)||"<anonymous>",stack:withLength(ee.stack,5e3),framework:getFramework()})},isDefined(Gn)&&(ee.ext=Gn),E$2.event(ee)}function T$1(){window.onerror=hack(window.onerror,function(ee,Gn,qn,Vn,Wn){var jn={message:ee,line:qn,column:Vn,file:Gn,type:3,timestamp:now()};if(!isDefined(Wn))try{(Wn=new Error(ee||"")).fileName=Gn,Wn.lineNumber=qn,Wn.columnNumber=Vn,Wn.name=Wn.name||"Error",Wn.message=ee}catch(Qn){}d(jn,Wn),w$4(jn)})}function $$1(){T$1(),m$a(GC.PAGE_READY,function(){T$1()}),addListener(window,"error",function(ee){var Gn=ee.target||ee.srcElement;isDefined(Gn)&&(Gn instanceof HTMLScriptElement||Gn instanceof HTMLLinkElement||Gn instanceof HTMLImageElement)&&c$7({t:GC.ERROR_DATA,p:{info:{message:xpath(Gn),duration:0,type:1,count:1,file:withLength(Gn.src||ee.filename,200),line:0,column:0,stack:"",framework:getFramework(),timestamp:now()}}})},!0),addListener(window,"unhandledrejection",function(ee){var Gn;isDefined(ee)&&(isString(ee.reason)?(Gn={message:ee.reason,line:0,column:0,timestamp:now()},isDefined(ee.type)&&(Gn.type=2),w$4(Gn)):F(ee.reason))},!0)}function F(ee){if(!isDefined(ee))return log("C122");var Gn,qn={timestamp:now()};typeof ee=="string"?(qn.message=ee,d(qn,ee=new Error(ee),!0)):window.ErrorEvent&&ee instanceof ErrorEvent?(extend(qn,{message:ee.message,line:ee.lineno,column:ee.colno,file:ee.filename}),d(qn,ee.error),isReadable(qn.file)||ee.target&&(qn.file=ee.target.baseURI)):(d(qn,ee),typeof ee.type=="number"?qn.type=ee.type:qn.type=3,Gn=ee.ext),w$4(qn,Gn)}function initConsole(){typeof console=="undefined"&&(window.console={log:function(){},info:function(){},warn:function(){},error:function(){},debug:function(){}}),console&&console.error&&(console.error=hackConsole(console.error,null,errorHandler))}function hackConsole(fn,before,after){var hackedFn;return fn.$original?fn:(hackedFn=function(){var params=args$1(arguments);try{if(typeof before=="function"&&before.apply(this,params),params[0]instanceof Error&&F(params[0]),typeof fn.apply=="function")return fn.apply(this,params);switch(params.length){case 0:return fn();case 1:return fn(params[0]);case 2:return fn(params[0],params[1]);case 3:return fn(params[0],params[1],params[2]);case 4:return fn(params[0],params[1],params[2],params[3]);default:for(var argsStr=[],i=0;i<params.length;i++)argsStr.push("params["+i+"]");return eval("fn("+argsStr.join(",")+")")}}catch(ee){throw ee}finally{typeof after=="function"&&after.apply(this,params)}},hackedFn.$original=fn,hackedFn)}function errorHandler(){var ee;console.$bonree||(ee={lv:"error",msg:formatArg(args$1(arguments)),timestamp:now()},c$7({t:GC.CONSOLE_DATA,p:{info:ee}}))}function formatArg(ee){return ee&&isArray(ee)?map(ee,function(Gn){return formatMessage(Gn=Gn===void 0?String(Gn):Gn)}).join(" "):ee}function formatMessage(ee){var Gn;if(isString(ee))return ee;if(ee instanceof Error||ee instanceof Function)return String(ee);try{Gn=stringify(ee)}catch(qn){Gn=String(ee)}return Gn}function Reactive(ee){for(var Gn in this.data=ee,this.computed={},ee)ee.hasOwnProperty(Gn)&&this.defineReactive(Gn,ee[Gn])}E$2.equalsWith(function(ee,Gn){function qn(Vn){return Vn.message+Vn.line+Vn.column}return qn(ee.info)===qn(Gn.info)}),m$a(GC.FLUSH_DATA,function(){E$2.flush()}),Reactive.prototype.defineReactive=function(ee,Gn){var qn=this;Object.defineProperty(this.data,ee,{get:function(){return Gn},set:function(Vn){Gn=Vn,qn.notifyComputed(ee)}})},Reactive.prototype.defineComputed=function(ee,Gn){var qn=this;Object.defineProperty(this.computed,ee,{get:function(){return Gn.call(qn)}})},Reactive.prototype.notifyComputed=function(ee){for(var Gn in this.computed)this.computed.hasOwnProperty(Gn)&&(this.computed[Gn].value=this.computed[Gn].get())};var I$3=!1,z$1=[],v$1=[],R$1,O$2,p$1,L$1=[],P$1=[],Te=!1,s$1=0,l$1={performance:window.performance&&window.performance.timing,observer:typeof window!="undefined"&&window.PerformanceObserver,getEntries:window.performance&&window.performance.getEntriesByType},N$1=new Reactive({FP:null,FCP:null,LCP:null}),H$1=function(ee,Gn){if(l$1.observer)try{PerformanceObserver.supportedEntryTypes.includes(ee)&&new PerformanceObserver(function(qn){Gn(qn.getEntries())}).observe({type:ee,buffered:!0})}catch(qn){log("C114")}},Ee$1=function(ee){forEach(ee,function(Gn){Gn.name==="first-paint"&&(R$1=Gn[GC.START_TIME]&&Gn[GC.START_TIME]||"",N$1.data.FP=Gn[GC.START_TIME]&&Gn[GC.START_TIME]||"",N$1.computed.allin),Gn.name==="first-contentful-paint"&&(O$2=Gn[GC.START_TIME]&&Gn[GC.START_TIME]||"",N$1.data.FCP=Gn[GC.START_TIME]&&Gn[GC.START_TIME]||"",N$1.computed.allin)}),!R$1&&O$2?(R$1=O$2,N$1.data.FP=O$2,N$1.computed.allin):!O$2&&R$1&&(O$2=R$1,N$1.data.FCP=R$1,N$1.computed.allin),!p$1&&R$1&&(p$1=R$1,N$1.data.LCP=R$1,N$1.computed.allin)},oe=function(ee){for(var Gn=0,qn=0,Vn=0;Vn<ee.length;Vn++){var Wn=ee[Vn];Wn.size>Gn&&(Gn=Wn.size,qn=Wn.startTime)}p$1=qn,N$1.data.LCP=qn||0,N$1.computed.allin},ie=function(ee){z$1=ee};function ae(){H$1("largest-contentful-paint",oe),H$1("longtask",ie)}function _e(){H$1("paint",Ee$1)}function fe(){var ee,Gn,qn;return w$a.state?(isDefined(R$1)&&isDefined(O$2)||!l$1.getEntries||forEach(window.performance.getEntriesByType("paint"),function(Vn){Vn.name==="first-paint"&&(R$1=Vn[GC.START_TIME]&&Vn[GC.START_TIME]||0),Vn.name==="first-contentful-paint"&&(O$2=Vn[GC.START_TIME]&&Vn[GC.START_TIME]||0)}),!isDefined(R$1)&&(Gn=window.performance&&window.performance.timing)&&(qn=Gn[GC.NAVIGATION_START],Gn.msFirstPaint&&(log("C111"),R$1=Gn.msFirstPaint-qn),window.chrome&&window.chrome.loadTimes&&(ee=window.chrome.loadTimes())&&ee.firstPaintTime&&(log("C112"),R$1=ee.firstPaintTime-qn),/^((?!chrome|android).)*safari/i.test(navigator.userAgent))&&(log("C159"),Gn.responseEnd?R$1=Gn.responseEnd-qn:Gn.domInteractive&&(R$1=Gn.domInteractive-qn)),isDefined(O$2)&&O$2!=0||(O$2=R$1),isDefined(p$1)&&p$1!=0||(isDefined(O$2)?p$1=O$2:l$1.performance&&(qn=(Gn=window.performance.timing)[GC.NAVIGATION_START]||Gn[GC.FETCH_START],p$1=Gn.domComplete-qn)),{fp:R$1?Math.round(R$1):0,fcp:O$2?Math.round(O$2):0,lcp:p$1?Math.round(p$1):0}):log("C110")}function f(ee,Gn){return 0<ee?ee-Gn:0}function Re$1(){var ee=window.performance||window.msPerformance||window.webkitPerformance,Gn=ee.getEntriesByType("resource")||[];return Gn.length===0?isDefined(ee.timing)?0<ee.timing[GC.LOAD_EVENT_END]-ee.timing[GC.NAVIGATION_START]?ee.timing[GC.LOAD_EVENT_END]-ee.timing[GC.NAVIGATION_START]:0:10:(ee=map(Gn,function(qn){return qn.responseEnd}).sort(function(qn,Vn){return Vn-qn}),Math.round(ee[0]))}function de$1(){var ee=document.querySelector(".tox-sidebar-wrap");ee&&addClass(ee,"rr-block")}function $(){de$1();var ee=d$1({p:{info:{url:D$5.sett.pageUrl}}},D$5.sett).p.info.url;D$5.setData("pageUrl",ee),c$7({t:GC.PAGE_LOAD}),_e(),window===window.top&&(l$1.observer&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("largest-contentful-paint")?N$1.defineComputed("allin",function(){return this.data.FP!==null&&this.data.FCP!==null&&this.data.LCP!==null&&delay(function(){h$1()},1e3),this.data.FP!==null&&this.data.FCP!==null&&this.data.LCP!==null}):delay(function(){h$1()},1e3),isDefined(D$5.sett.RecordConfiguration||D$5.sett.mc))&&(isDefined(window.BonreeRecord)&&isDefined(window.BonreeRecord.BonreeRecord)?window.BonreeRecord.BonreeRecord(D$5.sett):isDefined(window.BonreeAgent)&&isDefined(window.BonreeAgent.BonreeRecord)&&window.BonreeAgent.BonreeRecord(D$5.sett)),delay(function(){h$1()},1e3)}function h$1(ee){I$3||(I$3=!0,c$7({t:GC.RESOURCE_DATA,p:Ne(),type:1}),c$7({t:GC.RESOURCE_DATA,p:Oe(),type:2,special:Te}),s$1===0&&(d$5(GC.LIFESTYLE_XHR_START),d$5(GC.LIFESTYLE_FETCH_START),d$5(GC.LIFESTYLE_REQUEST_END+"H5"),c$7({t:GC.RESOURCE_DATA,p:{},type:3,revts:L$1}),isEmpty(D$5.secondSett)||c$7({t:GC.RESOURCE_DATA,p:{},type:4,revts:P$1}))),c$7({t:GC.FLUSH_DATA,p:!!ee})}function Oe(){var ee;return window.performance&&window.performance.getEntriesByType?(ee=v$1.concat(window.performance.getEntriesByType("resource")),v$1=[],map(ee,function(Gn){var qn,Vn,Wn=Gn[GC.INITIATOR_TYPE],jn=Gn.entryType,Qn=getMineTypeByUrl(Gn.name);if(isDefined(Gn.name)&&Gn.name.indexOf(D$5.sett.uploadAddrHttps)===-1||isDefined(Gn.name)&&Gn.name.indexOf(D$5.sett.uploadAddrHttp)===-1)return Vn={},(qn={}).name=d$1({p:{info:{url:Gn.name}}},D$5.sett).p.info.url,qn[GC.START_TIME]=getFixedMetric(Gn,GC.START_TIME),qn[GC.INITIATOR_TYPE]=Wn,qn.entryType=jn,qn.ret=Qn,Vn[GC.WORKER_START]=getFixedMetric(Gn,GC.WORKER_START),Vn[GC.REDIRECT_START]=getFixedMetric(Gn,GC.REDIRECT_START),Vn[GC.REDIRECT_END]=getFixedMetric(Gn,GC.REDIRECT_END),Vn[GC.FETCH_START]=getFixedMetric(Gn,GC.FETCH_START),Vn[GC.DOMAIN_LOOKUP_START]=getFixedMetric(Gn,GC.DOMAIN_LOOKUP_START),Vn[GC.DOMAIN_LOOKUP_END]=getFixedMetric(Gn,GC.DOMAIN_LOOKUP_END),Vn[GC.CONNECT_START]=getFixedMetric(Gn,GC.CONNECT_START),Vn[GC.CONNECT_END]=getFixedMetric(Gn,GC.CONNECT_END),Vn[GC.SECURE_CONNECTION_START]=getFixedMetric(Gn,GC.SECURE_CONNECTION_START),Vn[GC.REQUEST_START]=getFixedMetric(Gn,GC.REQUEST_START),Vn[GC.RESPONSE_START]=getFixedMetric(Gn,GC.RESPONSE_START),Vn[GC.RESPONSE_END]=getFixedMetric(Gn,GC.RESPONSE_END),Vn[GC.RESPONSE_STATUS]=Gn[GC.RESPONSE_STATUS],qn[GC.DURATION]=isDefined(Gn[GC.DURATION])?getFixedMetric(Gn,GC.DURATION):Vn[GC.RESPONSE_END]-Vn[GC.START_TIME],isDefined(Gn[GC.DECODED_BODY_SIZE])&&(qn[GC.NEXT_HOP_PROTOCOL]=Gn[GC.NEXT_HOP_PROTOCOL],Vn[GC.TRANSFER_SIZE]=Gn[GC.TRANSFER_SIZE],Vn[GC.ENCODED_BODY_SIZE]=Gn[GC.ENCODED_BODY_SIZE]>=Math.pow(2,64)?Math.pow(2,60):Gn[GC.ENCODED_BODY_SIZE],Vn[GC.DECODED_BODY_SIZE]=Gn[GC.DECODED_BODY_SIZE]),{info:qn,metric:Vn}})):(log("C116"),[])}function Se(ee,Gn){for(var qn=performance.timing.domContentLoadedEventEnd-performance.timing.domContentLoadedEventStart,Vn=ee,Wn=Gn,jn=performance.getEntriesByType("resource"),Qn=qn,Jn=Vn;Vn+5e3<=5e4;){var Jn=Vn,eo=Wn.filter(function(to){return to.startTime<Vn+5e3&&to.startTime+to.duration>Vn});if(eo.length){var eo=eo[eo.length-1];Vn=eo.startTime+eo.duration}else{if(!(2<(eo=jn.filter(function(to){return!(to.startTime>=Vn+5e3||to.startTime+to.duration<=Vn)})).length))return Math.max(Jn,Qn);eo=eo[0],Vn=eo.startTime+eo.duration}}return Math.max(Jn,Qn)}function Ne(){var ee,Gn,qn=window.screen||{},qn={timestamp:now(),title:w$a.title,referrer:document.referrer,charset:document.characterSet||document.charset||"",embed:window.top&&window.self&&window.top!==window.self?1:0,width:qn.width||0,height:qn.height||0,completed:w$a.state,param:location&&location.search||""},Vn=window.performance&&window.performance.timing;return qn.supported=isDefined(Vn)?1:0,Vn?(Vn={},ee=(Gn=window.performance.timing)[GC.NAVIGATION_START]||Gn[GC.FETCH_START],Vn[GC.NAVIGATION_START]=ee||"",Vn[GC.UNLOAD_EVENT_START]=f(Gn[GC.UNLOAD_EVENT_START],ee),Vn[GC.UNLOAD_EVENT_END]=f(Gn[GC.UNLOAD_EVENT_END],ee),Vn[GC.REDIRECT_START]=f(Gn[GC.REDIRECT_START],ee),Vn[GC.REDIRECT_END]=f(Gn[GC.REDIRECT_END],ee),Vn[GC.FETCH_START]=f(Gn[GC.FETCH_START],ee),Vn[GC.DOMAIN_LOOKUP_START]=f(Gn[GC.DOMAIN_LOOKUP_START],ee),Vn[GC.DOMAIN_LOOKUP_END]=f(Gn[GC.DOMAIN_LOOKUP_END],ee),Vn[GC.CONNECT_START]=f(Gn[GC.CONNECT_START],ee),Vn[GC.CONNECT_END]=f(Gn[GC.CONNECT_END],ee),Vn[GC.SECURE_CONNECTION_START]=f(Gn[GC.SECURE_CONNECTION_START],ee),Vn[GC.REQUEST_START]=f(Gn[GC.REQUEST_START],ee),Vn[GC.RESPONSE_START]=f(Gn[GC.RESPONSE_START],ee),Vn[GC.RESPONSE_END]=f(Gn[GC.RESPONSE_END],ee),Vn[GC.DOM_LOADING]=f(Gn[GC.DOM_LOADING],ee),Vn[GC.DOM_INTERACTIVE]=f(Gn[GC.DOM_INTERACTIVE],ee),Vn[GC.DOM_CONTENT_LOADED_EVENT_START]=f(Gn[GC.DOM_CONTENT_LOADED_EVENT_START],ee),Vn[GC.DOM_CONTENT_LOADED_EVENT_END]=f(Gn[GC.DOM_CONTENT_LOADED_EVENT_END],ee),Vn[GC.DOM_COMPLETE]=f(Gn[GC.DOM_COMPLETE],ee),Vn[GC.LOAD_EVENT_START]=f(Gn[GC.LOAD_EVENT_START],ee),Vn[GC.LOAD_EVENT_END]=f(Gn[GC.LOAD_EVENT_END],ee),Vn[GC.FULL_RESOURCE_LOAD_TIME]=Re$1(),qn[GC.DURATION]=w$a.state?Math.max(0,Vn[GC.LOAD_EVENT_END],Vn[GC.LOAD_EVENT_START]):Math.round(tillNow()/1e3),l$1.getEntries&&isDefined(Gn=last(window.performance.getEntriesByType("navigation")))&&(qn.type=Gn.type,qn[GC.NEXT_HOP_PROTOCOL]=Gn[GC.NEXT_HOP_PROTOCOL],Vn[GC.REDIRECT_COUNT]=Gn[GC.REDIRECT_COUNT],Vn[GC.TRANSFER_SIZE]=Gn[GC.TRANSFER_SIZE],Vn[GC.ENCODED_BODY_SIZE]=Gn[GC.ENCODED_BODY_SIZE],Vn[GC.DECODED_BODY_SIZE]=Gn[GC.DECODED_BODY_SIZE],Vn[GC.RESPONSE_STATUS]=Gn[GC.RESPONSE_STATUS]),extend(Vn,ee=fe()),ee&&ee.fcp&&(Gn=Se(ee.fcp,z$1)||0,extend(Vn,{tti:Math.round(Gn)})),{info:qn,metric:Vn}):(log("C109"),qn[GC.DURATION]=tillNow(),{info:qn})}function Ae$1(){var ee;l$1.getEntries&&(ee=function(Gn){return function(){log("Dump when resource buffer is full");var qn=window.performance.getEntriesByType("resource"),qn=(c$7({t:GC.RESOURCE_DUMP,p:qn}),I$3||(v$1=v$1.concat(qn)),Gn+"learResourceTimings");qn in window.performance&&window.performance[qn]()}},addListener(window.performance,"resourcetimingbufferfull",ee("c"),!1),addListener(window.performance,"webkitresourcetimingbufferfull",ee("webkitC"),!1))}function pe$1(){m$a(GC.LIFESTYLE_XHR_START,function(ee){isDefined(ee.info)&&(ee.info.isRelatedH5=!0),s$1+=1}),m$a(GC.LIFESTYLE_FETCH_START,function(ee){isDefined(ee.info)&&(ee.info.isRelatedH5=!0),s$1+=1}),m$a(GC.LIFESTYLE_REQUEST_END+"H5",function(ee){--s$1;var Gn=Y$2(JSON.parse(JSON.stringify(ee.requestData)),D$5.sett,!0),qn=w$a.getEviData("value");L$1.push({k:Gn.type,ent:Gn.ent,sin:Gn.sin,v:Gn.data,evi:qn}),!isEmpty(D$5.secondSett)&&checkMoudle("network")&&(Gn=Y$2(JSON.parse(JSON.stringify(ee.requestData)),D$5.secondSett,!0),P$1.push({k:Gn.type,ent:Gn.ent,sin:Gn.sin,v:Gn.data,evi:qn})),s$1===0&&(d$5(GC.LIFESTYLE_XHR_START),d$5(GC.LIFESTYLE_FETCH_START),d$5(GC.LIFESTYLE_REQUEST_END+"H5"),c$7({t:GC.RESOURCE_DATA,p:{},type:3,revts:L$1}),isEmpty(D$5.secondSett)||c$7({t:GC.RESOURCE_DATA,p:{},type:4,revts:P$1}))}),addListener(document,"DOMContentLoaded",function(){c$7({t:GC.PAGE_READY})}),document.readyState!=="complete"||I$3?on$1(window,"load",$):startWith(document.location.href,"about:")&&startWith(document.URL,"about:")||$.call(),on$1(window,"pagehide",function(){E$3(!0)},!1),ae(),Ae$1()}var C$1=[],I$2,b$2=[],m$1=[GC.XML_HTTP_REQUEST,GC.FETCH],L=function(ee,Gn){return ee[GC.START_TIME]-Gn[GC.START_TIME]};function getCookieUserID(){var ee=f$6.cookieArr,Gn=f$6.configeManage;if(0<ee.length)for(var qn=0,Vn=ee.length-1;qn<=Vn;qn++){var Wn=getCookie(ee[qn].rule);if(isDefined(Wn)&&u$4(Wn)&&ee[qn].index<=f$6.isStopGetValue)return Gn[ee[qn].index].value=""+Wn,void f$6.checkValueRight()}}function G$1(jn){var Gn,qn=jn.url,Vn=jn[GC.TIMESTAMP],Wn=m$1[jn.type],jn=map(b$2,function(Jn){if(Jn.url===qn&&m$1[Jn.type]===Wn)return Jn}),Qn=(jn.sort(L),-1);if(forEach(jn,function(Jn,eo){if(Jn[GC.TIMESTAMP]===Vn)return Qn=eo,!1}),!(Qn<0))return jn=map(C$1,function(Jn,eo){if(x$1(Jn,qn,Wn))return Jn}),Qn>=jn.length?(log("C131"),Gn=[],window.performance.getEntriesByType&&(Gn=map(window.performance.getEntriesByType("resource"),function(Jn){if(x$1(Jn,qn,Wn))return Jn})),jn=jn.concat(Gn)):log("C132"),jn.sort(L),jn.length<1||Qn>=jn.length?void log("C133"):jn[Qn];log("C130")}function U$1(ee){return(ee.initiatorType===GC.XML_HTTP_REQUEST||ee.initiatorType===GC.FETCH)&&ee[GC.START_TIME]>I$2}function x$1(ee,Gn,qn){return U$1(ee)&&endWith(ee.name,Gn)&&ee.initiatorType===qn}function w$3(ee){return 0<ee&&ee<600}var extendMetrics=function(ee){var Gn,qn,Vn;return window.performance&&window.performance.now?w$3((Gn=ee.info).status||Gn.code)?isDefined(qn=G$1(Gn))?(Vn=ee.metric,Gn[GC.START_TIME]=1e3*getFixedMetric(qn,GC.START_TIME),Gn[GC.TIMESTAMP]=now(),Gn[GC.DURATION]=1e3*getFixedMetric(qn,GC.DURATION),Vn[GC.REDIRECT_START]=1e3*getFixedMetric(qn,GC.REDIRECT_START),Vn[GC.REDIRECT_END]=1e3*getFixedMetric(qn,GC.REDIRECT_END),Vn[GC.FETCH_START]=1e3*getFixedMetric(qn,GC.FETCH_START),Vn[GC.DOMAIN_LOOKUP_START]=getFixedUsMetric(qn,GC.DOMAIN_LOOKUP_START),Vn[GC.DOMAIN_LOOKUP_END]=getFixedUsMetric(qn,GC.DOMAIN_LOOKUP_END),Vn[GC.CONNECT_START]=getFixedUsMetric(qn,GC.CONNECT_START),Vn[GC.CONNECT_END]=getFixedUsMetric(qn,GC.CONNECT_END),Vn[GC.SECURE_CONNECTION_START]=getFixedUsMetric(qn,GC.SECURE_CONNECTION_START),Vn[GC.RESPONSE_START]=getFixedUsMetric(qn,GC.RESPONSE_START),Vn[GC.REQUEST_START]=getFixedUsMetric(qn,GC.REQUEST_START),Vn[GC.RESPONSE_END]=getFixedUsMetric(qn,GC.RESPONSE_END),isDefined(qn[GC.DECODED_BODY_SIZE])&&(Gn[GC.NEXT_HOP_PROTOCOL]=qn[GC.NEXT_HOP_PROTOCOL],Vn[GC.WORKER_START]=1e3*getFixedMetric(qn,GC.WORKER_START),Vn[GC.TRANSFER_SIZE]=getFixedMetric(qn,GC.TRANSFER_SIZE),Vn[GC.ENCODED_BODY_SIZE]=getFixedMetric(qn,GC.ENCODED_BODY_SIZE),Vn[GC.DECODED_BODY_SIZE]=getFixedMetric(qn,GC.DECODED_BODY_SIZE))):log("C137"):log("C135"):log("C134"),ee},polyfillMetric=function(ee){var Gn=ee.info,qn=ee.metric;return isDefined(Gn[GC.DURATION])||(Gn[GC.DURATION]=qn[GC.RESPONSE_END]-Gn[GC.START_TIME]),isDefined(Gn.callbackError)||(Gn.callbackError=0),ee},queueRequest=function(ee){var Gn=ee.info;w$3(Gn.status||Gn.code)&&b$2.push(ee.info)};function z(){I$2=tillNow()/1e3||0,m$a(GC.RESOURCE_DUMP,function(ee){ee=map(ee.p,function(Gn){if(U$1(Gn))return Gn}),C$1=C$1.concat(ee)})}function T(){this.list={},this.isOpen=!1,this.blackKey=[]}T.prototype.checkIsOpen=function(){isDefined(D$5.sett.brss)&&!D$5.sett.brss||isDefined(D$5.secondSett.brss)&&!D$5.secondSett.brss?this.isOpen=!1:this.isOpen=!0},T.prototype.getItem=function(ee,Gn,qn){var Vn=ee+"+"+Gn;return isDefined(ee)&&this.list[Vn]?this.list[Vn]:this.setItem(ee,Gn,qn)},T.prototype.setItem=function(ee,Gn,qn){var Vn=isDefined(D$5.sett.traceConfig)?D$5.sett.traceConfig:{urlTotalList:[],urlBlackList:[],urlWhiteList:[]},Wn=Vn.urlTotalList,jn=Vn.urlWhiteList,Qn=Vn.urlBlackList,Jn=(isDefined(D$5.secondSett.traceConfig)&&(isDefined(D$5.secondSett.traceConfig.urlTotalList)&&(Wn=isDefined(Wn)?Wn.concat(D$5.secondSett.traceConfig.urlTotalList):D$5.secondSett.traceConfig.urlTotalList),isDefined(D$5.secondSett.traceConfig.urlWhiteList)&&(jn=isDefined(jn)?jn.concat(D$5.secondSett.traceConfig.urlWhiteList):D$5.secondSett.traceConfig.urlWhiteList),isDefined(D$5.secondSett.traceConfig.urlBlackList))&&(Qn=isDefined(Qn)?Qn.concat(D$5.secondSett.traceConfig.urlBlackList):D$5.secondSett.traceConfig.urlBlackList),{});if(isDefined(Wn)&&0<Wn.length)for(var eo=0,Xn=(Wn=200<Wn.length?Wn.slice(0,200):Wn).length-1;eo<=Xn;eo++)extend(Jn,this.totalHead(Wn[eo],ee,Gn,qn));if(isDefined(jn)&&0<jn.length)for(var to=0,io=(jn=200<jn.length?jn.slice(0,200):jn).length-1;to<=io;to++)this.whiteCheck(ee,jn[to])&&extend(Jn,this.whiteHead(jn[to],ee,Gn,qn));if(isDefined(Qn)&&0<Qn.length)for(var ro=0,no=(Qn=200<Qn.length?Qn.slice(0,200):Qn).length-1;ro<=no;ro++)this.black(ee,Qn[ro])?extend(Jn,this.blackHead(Qn[ro],ee,Gn,qn)):this.getBlackKey(Qn[ro]);return 200<Object.keys(Jn).length&&(Jn=getElementForNum(Jn,200)),Jn=this.delBlackKey(Jn),this.list[ee+"+"+Gn]=Jn},T.prototype.delBlackKey=function(ee){if(0<this.blackKey.length)for(var Gn=0,qn=this.blackKey.length-1;Gn<=qn;Gn++)isDefined(ee[this.blackKey[Gn]])&&delete ee[this.blackKey[Gn]];return this.blackKey=[],ee},T.prototype.getBlackKey=function(ee){var Gn=ee&&ee.reqHeaderRules;if(isDefined(Gn)&&0<Gn.length&&this.blackKey)for(var qn=0,Vn=Gn.length-1;qn<=Vn;qn++)Gn[qn]&&Gn[qn].key&&this.blackKey.push(Gn[qn].key)},T.prototype.totalHead=function(ee,Gn,qn,Vn){var Wn={};return extend(Wn,this.constructData(ee,Gn,qn,Vn)),Wn},T.prototype.whiteCheck=function(ee,Gn){if(Gn.reqHeaderRules&&0<Gn.reqHeaderRules.length){if(includes([0,1,2,3,4],Gn.type)===!1)return!1;switch(Gn.type){case 0:if(ee==Gn.rule)return!0;break;case 1:if(startWith(ee,Gn.rule))return!0;break;case 2:if(endWith(ee,Gn.rule))return!0;break;case 3:var qn=new RegExp(Gn.rule);if(qn instanceof RegExp&&qn.test(ee))return!0;break;case 4:if(-1<ee.indexOf(Gn.rule))return!0;break;default:return!1}}return!1},T.prototype.whiteHead=function(ee,Gn,qn,Vn){var Wn=ee.reqHeaderRules,jn={};if(isDefined(Wn)&&0<Wn.length)for(var Qn=0,Jn=Wn.length-1;Qn<=Jn;Qn++)extend(jn,this.constructData(Wn[Qn],Gn,qn,Vn));return jn},T.prototype.black=function(ee,Gn){if(Gn.reqHeaderRules&&0<Gn.reqHeaderRules.length){if(includes([0,1,2,3,4],Gn.type)===!1)return!1;switch(Gn.type){case 0:if(ee==Gn.rule)return!1;break;case 1:if(startWith(ee,Gn.rule))return!1;break;case 2:if(endWith(ee,Gn.rule))return!1;break;case 3:var qn=new RegExp(Gn.rule);if(qn instanceof RegExp&&qn.test(ee))return!1;break;case 4:if(-1<ee.indexOf(Gn.rule))return!1;break;default:return!0}}return!0},T.prototype.blackHead=function(ee,Gn,qn,Vn){var Wn=ee.reqHeaderRules,jn={};if(isDefined(Wn)&&0<Wn.length)for(var Qn=0,Jn=Wn.length-1;Qn<=Jn;Qn++)extend(jn,this.constructData(Wn[Qn],Gn,qn,Vn));return jn},T.prototype.checkKey=function(ee,Gn,qn){return!(!qn||qn===1)||!(!isDefined(ee)||ee.length>Gn)&&(Gn===256?!!/^[0-9a-zA-Z_-]{1,256}$/.test(ee):trim(ee)!="")},T.prototype.constructData=function(ee,Gn,qn,Vn){var Wn={},jn=getUserAgent(),Qn=jn&&jn.osName+"/"+jn.osVersion||D$5.sett.osType&&D$5.sett.typeArr[D$5.sett.osType]||D$5.sett.typeArr[1],Jn=ee.rhsr;if(isDefined(ee)&&isDefined(ee.type)&&this.checkKey(ee.key,256)){if(isDefined(ee.value)&&this.checkKey(ee.value,512,ee.type)===!1)return;switch(ee.type){case 1:isDefined(ee.value)&&(Wn[ee.key]=ee.value);break;case 2:Wn[ee.key]={fun:uuidWithLength,len:16};break;case 3:Wn[ee.key]={fun:uuidWithLength,len:32};break;case 4:Wn[ee.key]={fun:skyData,len:32,sky:!0,url:Gn,pathname:qn,rhsr:Jn};break;case 5:Wn[ee.key]={fun:traceId,len:32,sky:!1,rhsr:Jn};break;case 6:Wn[ee.key]="bnro="+Qn+"_js/"+D$5.sett.agentVersion}}return Wn};var BWexample=new T;function checkBlackAndWhite(ee,Gn){if(isDefined(ee.url)&&(ee=(ee=ee.url).indexOf("http")===0?(qn=ee.match(/^(https?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/))&&{href:ee,protocol:qn[1],host:qn[2],hostname:qn[3],port:qn[4],pathname:qn[5],search:qn[6],hash:qn[7]}:(qn=ee.match(/^(file?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/))&&{href:ee,protocol:qn[1],host:qn[2],hostname:qn[3],port:qn[4],pathname:qn[5],search:qn[6],hash:qn[7]},isDefined(ee)))return qn=isDefined(ee.hostname)?ee.hostname:"",ee=isDefined(ee.pathname)?ee.pathname:"",isDefined(qn)?BWexample.getItem(qn,ee,Gn):void 0;var qn}function skyData(eo,Jn,qn){var Vn=String(enBase64(skyUuid())),Wn=String(enBase64(skyUuid())),jn=String(enBase64(D$5.sett.appName)),Qn=String(enBase64(D$5.sett.appVersion)),Jn=Jn&&String(enBase64(Jn))||String(enBase64("/")),eo=String(enBase64(eo));return!(qn&&0<=qn&&qn<=100)||n$3(qn)?"1-"+Vn+"-"+Wn+"-0-"+jn+"-"+Qn+"-"+Jn+"-"+eo:"0-"+Vn+"-"+Wn+"-0-"+jn+"-"+Qn+"-"+Jn+"-"+eo}var c$1={isPeriod:!1,actionID:"",networkID:""};function E$1(ee,Gn,qn){!ee.$metric||isDefined((ee=ee.$metric.metric)[Gn])&&!qn||(ee[Gn]=tillNow())}function de(ee,Gn,qn){ee.$metric&&(ee.$metric.info[Gn]=qn)}function safetySetErrorInfo(ee,Gn,qn,Vn,Wn){ee.$metric&&((ee=ee.$metric.info)[Gn]=qn,ee[GC.MESSAGE]=Wn,ee[GC.E_TYPE]=Vn)}function Y(ee,Vn){var qn=Vn,Vn=getIP(qn=getUrl(qn=typeof Vn!="string"?Vn.href||"":qn));this.$$inner||(this.$metric={info:{id:uuid(),url:qn.slice(0,4096),ip:Vn[0]||"",port:Vn[1]||"",method:toUpper(ee),type:0,protocalType:-1<qn.indexOf("https")?2:1,reqUrlDta:isDefined(D$5.sett.reqURLKey||D$5.secondSett.reqURLKey)&&getParams(qn)||"",reqhtData:{},reshtData:{}}},isDefined(D$5.sett)&&isDefined(D$5.sett.pageViewId)&&(this.$metric.info.pageViewId=D$5.sett.pageViewId))}function M$2(){var ee,Gn=this;BWexample.checkIsOpen(),this.$metric&&isDefined(this.$metric.info)&&BWexample.isOpen&&(this.$metric.info.timestamp=now(),isDefined(ee=checkBlackAndWhite(this.$metric.info,"_xhr")))&&isObj(ee)&&forEachOwn(ee,function(qn,Vn){var Wn;isObj(qn)?(Wn=(Wn=qn).sky?qn.fun(qn.url,qn.pathname,qn.rhsr):qn.sky===!1?qn.fun(qn.rhsr):qn.fun(qn.len),Gn.setRequestHeader(Vn,Wn),X(Vn,Wn,Gn.$metric)):(startWith(qn,"bnro=")&&(isDefined(D$5.secondSett.appId)&&isDefined(D$5.secondSett.sessionId)?qn+="_xhr_XMLHttpRequest,rum="+D$5.sett.agentVersion+"&js&"+D$5.secondSett.appId+"&"+D$5.secondSett.sessionId:qn+="_xhr_XMLHttpRequest"),X(Vn,qn,Gn.$metric),Gn.setRequestHeader(Vn,qn))})}function getParams(ee){return isDefined(ee)?String.prototype.split.call(ee,"?")[1]:""}function b$1(ee){if(this.$metric&&!this.$$done){this.$$done=!0;var Gn,qn=this.$metric,Vn=qn.info,Wn=qn.metric;extendMetrics(qn),polyfillMetric(qn);try{if((!isDefined(Wn[GC.DECODED_BODY_SIZE])||Wn[GC.DECODED_BODY_SIZE]<=0)&&(Gn=this.responseType===""||this.responseType==="text"?getContentSize(this.responseText):getContentSize(this.response),Wn[GC.DECODED_BODY_SIZE]=Gn),this.getAllResponseHeaders){var jn=this.getAllResponseHeaders(),Qn=jn||"",Jn=toLower(Qn),eo=Qn.trim().split(/[\r\n]+/),Xn={},to=(forEach(eo,function(ao){var ao=ao.split(": "),oo=ao.shift(),ao=ao.join(": ");Xn[oo]=ao}),Vn.guid=isDefined(Xn[GC.GUID_KEY])&&Xn[GC.GUID_KEY].split(",")[0]||"",Vn.xBrResponse=isDefined(Xn[GC.X_BR_RESPONSE])&&Xn[GC.X_BR_RESPONSE].split(",")[0]||"",Vn.traceResponse=isDefined(Xn[GC.TRACE_RESPONSE])&&Xn[GC.TRACE_RESPONSE].split(",")[0]||"",Vn[GC.RESPONSE_HEADER]=withLength(Qn,2e3),[]);if(isDefined(D$5.sett.respHeaderTraceKey)&&0<D$5.sett.respHeaderTraceKey.length||isDefined(D$5.secondSett.respHeaderTraceKey)&&0<D$5.secondSett.respHeaderTraceKey.length){isDefined(D$5.secondSett.respHeaderTraceKey)&&0<D$5.secondSett.respHeaderTraceKey.length?to=isDefined(D$5.sett.respHeaderTraceKey)?D$5.sett.respHeaderTraceKey.concat(D$5.secondSett.respHeaderTraceKey):D$5.secondSett.respHeaderTraceKey:isDefined(D$5.sett.respHeaderTraceKey)&&(to=D$5.sett.respHeaderTraceKey);for(var io=0,ro=to.length-1;io<=ro;io++)-1<Jn.indexOf(toLower(to[io]))&&(Vn.reshtData[to[io]]=this.getResponseHeader(to[io]))}getResponseHead(jn)}}catch(no){log("C123")}c$7({t:GC.REQUEST_DATA,p:qn,isRelatedH5:!!isDefined(qn.info.isRelatedH5)}),setTimeout(function(){getCookieUserID()},50)}}function getResponseHead(ee){if(0<(ee=ee.trim()).length){var ee=ee.split(/[\r\n]+/),Gn={},qn=(forEach(ee,function(to){var to=to.split(": "),Xn=to.shift(),to=to.join(": ");Gn[Xn]=to}),f$6.responseArr),Vn=f$6.configeManage;if(0<qn.length)for(var Wn=0,jn=qn.length-1;Wn<=jn;Wn++){var Qn=qn[Wn].rule.toLowerCase();if(Gn.hasOwnProperty(Qn)&&qn[Wn].index<=f$6.isStopGetValue&&u$4(Gn[Qn]))return Vn[qn[Wn].index].value=""+Gn[Qn],void f$6.checkValueRight()}}}function C(ee){return isFunction(prop(ee.onreadystatechange,"$$original"))}function g$1(ee){return ee.readyState===0||ee.readyState===4}function Ee(){D.call(this),C(this)||(g$1(this)?H.call(this):this.onreadystatechange=hack(this.onreadystatechange,D,H,I$1))}function D(){isDefined(this)&&(isDefined(this.$metric)&&isDefined(this.$metric.info)&&isDefined(this.$metric.info.ai)&&!c$1.isPeriod&&c$1.networkID!==this.$metric.info.id&&(c$1.isPeriod=!0,c$1.actionID=this.$metric.info.ai,c$1.networkID=this.$metric.info.id,c$7({t:GC.XHR_CALLBACK_IDENTIFY,info:c$1})),this.readyState===2&&E$1(this,GC.RESPONSE_START),g$1(this))&&(E$1(this,GC.RESPONSE_START),E$1(this,GC.RESPONSE_END),E$1(this,GC.CALLBACK_START))}function H(){if(g$1(this)){c$1.isPeriod=!1,c$7({t:GC.XHR_CALLBACK_IDENTIFY,info:c$1});var ee=now(),Gn=(E$1(this,GC.CALLBACK_END),this);if(this.$metric){var qn=0;try{qn=this.status,this.$metric.info[GC.E_TYPE]="http"}catch(Vn){log("C124")}qn===0&&(this.$metric.info[GC.E_TYPE]="XHR",this.$metric.info[GC.MESSAGE]="XHR internal services not available or Cross domain request"),this.$metric.info.code=qn,queueRequest(this.$metric),delay(function(){log("C125"),b$1.call(Gn,ee)})}}}function I$1(){log("C126"),F(last(args$1(arguments))),de(this,"callbackError",1)}function X(ee,Gn,qn){var Vn;(isDefined(D$5.sett.reqHeaderTraceKey)||isDefined(D$5.secondSett.reqHeaderTraceKey))&&(Vn=[],isDefined(D$5.secondSett.reqHeaderTraceKey)&&0<D$5.secondSett.reqHeaderTraceKey.length?Vn=isDefined(D$5.sett.reqHeaderTraceKey)?D$5.sett.reqHeaderTraceKey.concat(D$5.secondSett.reqHeaderTraceKey):D$5.secondSett.reqHeaderTraceKey:isDefined(D$5.sett.reqHeaderTraceKey)&&(Vn=D$5.sett.reqHeaderTraceKey),isDefined(Vn))&&includes(Vn.map(function(Wn){return Wn.toLowerCase()}),ee.toLowerCase())&&(qn.info.reqhtData[ee]=Gn)}function k(ee){if(!this.$$inner&&isDefined(this.$metric)){c$1.isPeriod&&(this.$metric.info.ai=c$1.actionID,this.$metric.info.parentNetworkID=c$1.networkID,c$7({t:GC.LIFESTYLE_CALLBACK_REQUEST_START+this.$metric.info.ai})),c$7({t:GC.LIFESTYLE_XHR_START,info:isDefined(this.$metric.info)?this.$metric.info:null});var Gn,qn,Vn=this,Wn=isDefined(Vn.timeout)&&0<Vn.timeout,jn=6e4,Qn=(isDefined(D$5.sett.xhrTimeout)&&0<D$5.sett.xhrTimeout?jn=D$5.sett.xhrTimeout:isDefined(D$5.secondSett.xhrTimeout)&&0<D$5.secondSett.xhrTimeout&&(jn=D$5.secondSett.xhrTimeout),null),Jn=(0<jn&&!Wn&&(Qn=setTimeout(function(){!g$1(Vn)&&Qn&&(clearTimeout(Qn),Qn=null,c$1.isPeriod&&c$1.networkID===Vn.$metric.info.id&&(c$1.isPeriod=!1,c$7({t:GC.XHR_CALLBACK_IDENTIFY,info:c$1})),safetySetErrorInfo(Vn,"code",601,"XHR","XHR request timeout \u2014 Bonree "+jn+"ms"),Vn.$metric)&&(E$1(Vn,GC.RESPONSE_START,!0),E$1(Vn,GC.RESPONSE_END,!0),E$1(Vn,GC.CALLBACK_START,!0),E$1(Vn,GC.CALLBACK_END,!0),Vn.$$done=!1,queueRequest(Vn.$metric),delay(function(){b$1.call(Vn,now())}))},jn)),function(){Qn&&(clearTimeout(Qn),Qn=null)});isFunction(Vn.addEventListener)?(forEach(["abort","timeout","error"],function(to,io){var ro=["User terminates XHR request","XHR request timeout \u2014 custom "+(Wn?Vn.timeout+"ms":"unKnown"),"XHR request exception"];addListener(Vn,to,function(){Jn(),safetySetErrorInfo(Vn,"code",600+Number(io),"XHR",ro[io])},!1)}),addListener(Vn,"readystatechange",Ee,!1)):(Gn=3,(qn=function(){delay(function(){if(g$1(Vn)&&!C(Vn))return Jn(),H.call(Vn);g$1(Vn)||(Vn.onreadystatechange=hack(Vn.onreadystatechange,D,function(){Jn(),H.call(Vn)},I$1),0<=--Gn&&qn())})})()),eo=window.navigator.userAgent,0<(Xn=eo.indexOf("MSIE "))&&parseInt(eo.substring(Xn+5,eo.indexOf(".",Xn)),10)===9?delay(function(){C(Vn)||(g$1(Vn)?H.call(Vn):Vn.onreadystatechange=hack(Vn.onreadystatechange,D,H,I$1))}):Vn.onreadystatechange=hack(this.onreadystatechange,D,H,I$1),eo=this.$metric.info,Xn=(extend(eo,{timestamp:now()}),this.$metric.metric={});try{eo[GC.REQUEST_BODY]=getRequestParam(ee),Xn[GC.UPLOAD_BODY_SIZE]=getContentSize(ee)}catch(to){log("C127")}}var eo,Xn}function pe(ee,Gn){if(u$4(Gn)){var qn=f$6.requestArr,Vn=f$6.configeManage;if(0<qn.length){for(var Wn=0,jn=qn.length-1;Wn<=jn;Wn++)if(qn[Wn].rule==ee&&qn[Wn].index<=f$6.isStopGetValue)return Vn[qn[Wn].index].value=""+Gn,void f$6.checkValueRight()}}}function V$1(ee,Gn){var qn;pe(ee,Gn),isDefined(this.$metric)&&(X(ee,Gn,this.$metric),isDefined((qn=this.$metric.info)[GC.REQUEST_HEADER])||(qn[GC.REQUEST_HEADER]=""),qn[GC.REQUEST_HEADER]=qn[GC.REQUEST_HEADER]&&qn[GC.REQUEST_HEADER]+GC.BREAK_LINE+ee+":"+Gn||ee+":"+Gn)}function le(){if(!window.XMLHttpRequest)return log("C128");var ee;XMLHttpRequest.prototype?(XMLHttpRequest.prototype.open=hack(XMLHttpRequest.prototype.open,Y,M$2),XMLHttpRequest.prototype.send=hack(XMLHttpRequest.prototype.send,k),XMLHttpRequest.prototype.setRequestHeader=hack(XMLHttpRequest.prototype.setRequestHeader,V$1)):(log("C129"),ee=window.XMLHttpRequest,window.XMLHttpRequest=function(){var Gn=new ee;return Gn.open=hack(Gn.open,Y,M$2),Gn.send=hack(Gn.send,k),Gn.setRequestHeader=hack(Gn.setRequestHeader,V$1),Gn})}var v={isPeriod:!1,actionID:"",networkID:""};function Re(ee){return getRequestParam(ee)}function me(ee){return getContentSize(ee)}function b(ee){if(!isDefined(ee))return"";var Gn="";try{var qn,Gn=(window.Headers&&ee instanceof Headers?map(iterate(ee.entries()),function(Wn){return Wn.join(":")}):(qn=[],forEachOwn(ee,function(Wn,jn){qn.push(jn+":"+Wn)}),qn)).join(GC.BREAK_LINE)}catch(Vn){Gn="",log("C150")}return Gn}function R(ee){return isDefined(ee)&&typeof ee!="string"}function w$2(ee,Gn,qn){return qn==="headers"&&Gn?R(ee)&&ee[qn]?extend(Gn[qn],ee[qn]):Gn[qn]:R(ee)?ee[qn]:Gn?Gn[qn]:null}function De(ee,Gn){return{requestBody:Re(w$2(ee,Gn,"body")),requestHeader:b(w$2(ee,Gn,"headers"))}}function He(Vn,Wn){var qn=getUrl(qn=R(Vn)?Vn.url:Vn),Vn=toUpper(w$2(Vn,Wn,"method"))||"GET",Wn=getIP(qn);return{id:uuid(),url:qn.slice(0,4096),method:Vn,ip:Wn[0]||"",port:Wn[1]||"",protocalType:-1<qn.indexOf("https")?2:1,reqUrlDta:isDefined(D$5.sett.reqURLKey||D$5.secondSett.reqURLKey)&&getParams(qn)||"",requestType:4}}function O$1(ee,Gn){var qn,Vn;ee&&(extendMetrics(ee),polyfillMetric(ee),(!isDefined((qn=ee.metric)[GC.DECODED_BODY_SIZE])||qn[GC.DECODED_BODY_SIZE]<=0)&&(Vn=ee.ext||{},qn[GC.DECODED_BODY_SIZE]=Vn.originalResponseSize||0),c$7({t:GC.REQUEST_DATA,p:ee,isRelatedH5:!!isDefined(ee.info.isRelatedH5)}),setTimeout(function(){getCookieUserID()},50))}function Ae(){var ee=window.fetch;window.fetch=function(eo,qn){if(qn&&qn.$$inner===!0)return delete(Vn=objectAssign({},qn)).$$inner,ee(eo,Vn);var Vn=eo,eo=(eo instanceof Request||typeof eo!="string"&&eo&&(eo.href||eo.url)&&(Vn=eo.href||eo.url||""),isDefined(window.$bonreeReplayUrl)?window.$bonreeReplayUrl:null),Wn=He(Vn,qn);if((!R(Vn)&&Vn.indexOf(eo)===-1||R(Vn))&&c$7({t:GC.LIFESTYLE_FETCH_START,info:Wn}),v.isPeriod&&(Wn.ai=v.actionID,Wn.parentNetworkID=v.networkID,c$7({t:GC.LIFESTYLE_CALLBACK_REQUEST_START+Wn.ai})),(Wn=extend(Wn,{reqhtData:{},reshtData:{}})).timestamp=now(),isDefined(D$5.sett)&&isDefined(D$5.sett.pageViewId)&&(Wn.pageViewId=D$5.sett.pageViewId),BWexample.checkIsOpen(),BWexample.isOpen&&(extend(Wn,{reqhtData:{},reshtData:{}}),isDefined(eo=checkBlackAndWhite(Wn,"_fetch")))&&isObj(eo)&&forEachOwn(eo,function(io,ro){if(startWith(io=isObj(io)?io.sky?io.fun(io.url,io.pathname,io.rhsr):io.sky===!1?io.fun(io.rhsr):io.fun(io.len):io,"bnro=")&&(isDefined(D$5.secondSett.appId)&&isDefined(D$5.secondSett.sessionId)?io+="_fetch_fetch,rum="+D$5.sett.agentVersion+"&js&"+D$5.secondSett.appId+"&"+D$5.secondSett.sessionId:io+="_fetch_fetch"),isDefined(qn))if(isDefined(qn.headers)){if(qn.headers instanceof Headers){for(var no={},oo=qn.headers.entries();!(ao=oo.next()).done;){var ao=ao.value;no[ao[0]]=ao[1]}qn.headers=no}var so={};so[ro]=io,extend(qn.headers,so)}else so={},so[ro]=io,extend(qn,{headers:so});else so={},so[ro]=io,qn={headers:so}}),qn&&qn.headers){if(qn.headers instanceof Headers){for(var jn={},Qn=qn.headers.entries();!(Jn=Qn.next()).done;){var Jn=Jn.value;jn[Jn[0]]=Jn[1]}qn.headers=jn}M$1(qn.headers,Wn)}Wn.type=1,extend(Wn,{timestamp:now()});var eo=ee(Vn,qn),Xn=(extend(Wn,De(Vn,qn)),{info:Wn}),to=Xn.metric={};return to[GC.UPLOAD_BODY_SIZE]=me(w$2(Vn,qn,"body")),eo.then(function(io){Wn.status=io.status,Wn.statusText=io.statusText;var ro=to[GC.RESPONSE_END]=tillNow(),ro=(to[GC.RESPONSE_START]=to[GC.CALLBACK_START]=ro,queueRequest(Xn),b(io.headers)),no=ro,oo=toLower(no),ao=no.trim().split(/[\r\n]+/),so={};forEach(ao,function(go){var go=go.split(":"),po=go.shift(),go=go.join(": ");so[po]=go}),Wn.guid=isDefined(so[GC.GUID_KEY])&&so[GC.GUID_KEY].split(",")[0]||"",Wn.xBrResponse=isDefined(so[GC.X_BR_RESPONSE])&&so[GC.X_BR_RESPONSE].split(",")[0]||"",Wn.traceResponse=isDefined(so[GC.TRACE_RESPONSE])&&so[GC.TRACE_RESPONSE].split(",")[0]||"",Wn[GC.RESPONSE_HEADER]=withLength(no,2e3);try{var co=[];if(isDefined(D$5.sett.respHeaderTraceKey)&&0<D$5.sett.respHeaderTraceKey.length||isDefined(D$5.secondSett.respHeaderTraceKey)&&0<D$5.secondSett.respHeaderTraceKey.length){isDefined(D$5.secondSett.respHeaderTraceKey)&&0<D$5.secondSett.respHeaderTraceKey.length?co=isDefined(D$5.sett.respHeaderTraceKey)?D$5.sett.respHeaderTraceKey.concat(D$5.secondSett.respHeaderTraceKey):D$5.secondSett.respHeaderTraceKey:isDefined(D$5.sett.respHeaderTraceKey)&&(co=D$5.sett.respHeaderTraceKey);for(var uo=0,fo=co.length-1;uo<=fo;uo++)-1<oo.indexOf(toLower(co[uo]))&&(Wn.reshtData[co[uo]]=io.headers.get(co[uo]))}}catch(lo){log("C148")}if(getResponseHead(ro),io.$metric=Xn,to[GC.CALLBACK_END]=tillNow(),ao=tryToClone(io),ao)try{var wo={bytesLimit:Number.POSITIVE_INFINITY,collectStreamBody:!0};readBytesFromStream(ao.body,wo,function(lo){Xn.ext={originalResponseSize:lo},O$1(Xn,io)})}catch(lo){log("C149")}else O$1(Xn);return io},function(io){var ro=to[GC.RESPONSE_END]=tillNow();throw to[GC.RESPONSE_START]=to[GC.CALLBACK_START]=to[GC.CALLBACK_END]=ro,Wn.status=window.TypeError&&io instanceof TypeError?602:600,Wn.eType="FETCH",Wn[GC.STATUS_TEXT]=Wn.status==602?"FETCH request exception":"User terminates FETCH request",O$1(Xn),io}).catch(function(io){throw io.type=2,io})}}function M$1(ee,Gn){if(isDefined(ee)){var qn,Vn=f$6.requestArr,Wn=f$6.configeManage;if(0<Vn.length){for(var jn=0,Qn=Vn.length-1;jn<=Qn;jn++)if(ee.hasOwnProperty(Vn[jn].rule)&&Vn[jn].index<=f$6.isStopGetValue&&u$4(ee[Vn[jn].rule]))return Wn[Vn[jn].index].value=""+ee[Vn[jn].rule],void f$6.checkValueRight()}(isDefined(D$5.sett.reqHeaderTraceKey)&&0<D$5.sett.reqHeaderTraceKey.length||isDefined(D$5.secondSett.reqHeaderTraceKey)&&0<D$5.secondSett.reqHeaderTraceKey.length)&&(qn=[],isDefined(D$5.secondSett.reqHeaderTraceKey)&&0<D$5.secondSett.reqHeaderTraceKey.length?qn=isDefined(D$5.sett.reqHeaderTraceKey)?D$5.sett.reqHeaderTraceKey.concat(D$5.secondSett.reqHeaderTraceKey):D$5.secondSett.reqHeaderTraceKey:isDefined(D$5.sett.reqHeaderTraceKey)&&(qn=D$5.sett.reqHeaderTraceKey),Object.keys(ee).map(function(Jn){includes(qn.map(function(eo){return eo.toLowerCase()}),Jn.toLowerCase())&&(Gn.reqhtData[Jn]=ee[Jn])}))}}function i(){if(!isFunction(window.fetch))return log("C147");m$a(GC.XHR_CALLBACK_IDENTIFY,function(ee){v=ee.info}),Ae()}function r$1(){le(),i(),window.performance&&window.performance.now&&z()}var p=0;function O(){p=1e3*new Date().getTime()}function N(ee){if(isDefined(ee))try{var Gn,qn,Vn=0,Wn=(ee&&ee.constructor&&-1<ee.constructor.toString().indexOf("String")&&(Vn=(isDefined(window.TextEncoder)?new TextEncoder().encode(ee):ee).length),ee&&ee.constructor&&-1<ee.constructor.toString().indexOf("ArrayBuffer")&&(Vn=ee.byteLength),ee&&ee.constructor&&-1<ee.constructor.toString().indexOf("Blob")&&(Vn=ee.size),ee&&ee.constructor&&-1<ee.constructor.toString().indexOf("Array")&&(Vn=ee.byteLength),ee&&ee.constructor&&-1<ee.constructor.toString().indexOf("Uint8Array")&&(Vn=ee.toString().length||0),isDefined(this.name)?this.name:this.url.split("?")[0]);/^[a-zA-Z0-9:_-\s@./]+$/.test(Wn)!==!1&&((Gn=new n(Wn,"websocket")).setStatus(1),isDefined(Gn.spanEvent)&&isDefined(Gn.spanEvent.ic)&&(Gn.spanEvent.ic=!1),Gn.setTag("process","send"),Gn.setTag("connectID",isDefined(this.identify)?this.identify:uuid()),Gn.setData("url",isDefined(this.handledUrl)?this.handledUrl:this.url),(qn=1e3*new Date().getTime()-p)===0&&(qn=999),!isDefined(this.readyState)||this.readyState!==2&&this.readyState!==3||(Gn.setStatus(2),Gn.setStatusCode("WebSocket is already in CLOSING or CLOSED state.")),Gn.setMetric("send",qn,"us"),Gn.setDuration(qn),Gn.setMetric("size",Vn,"byte"),Gn.finish())}catch(jn){window.console&&window.console.error&&console.error(jn)}}function x(ee){function Gn(Vn,Jn){var jn=Jn?new ee(Vn,Jn):new ee(Vn),Qn=(c$7({t:GC.WEBSOCKET_CONNECTION_START}),Vn&&-1<Vn.indexOf("?")?Vn.split("?")[0]:Vn),Jn=d$1({p:{info:{url:Vn}}},D$5.sett).p.info.url,eo=uuid(),Xn=1e3*new Date().getTime(),to=!1,io=(jn.handledUrl=isDefined(Jn)?Jn:"",jn.identify=eo,jn.name=isDefined(Qn)?Qn:"",jn.onopen),ro=jn.onerror,no=(jn.onopen=function(oo){var ao;/^[a-zA-Z0-9:_-\s@./]+$/.test(Qn)!==!1&&((ao=new n(Qn,"websocket")).spanEvent.st=0,ao.spanEvent.timestamp=Xn,ao.setStatus(1),isDefined(ao.spanEvent)&&isDefined(ao.spanEvent.ic)&&(ao.spanEvent.ic=!1),ao.setTag("process","connect"),ao.setTag("connectID",isDefined(eo)?eo:uuid()),ao.setData("url",jn.handledUrl),ao.setDuration(1e3*new Date().getTime()-Xn),ao.finish(),to=!0,c$7({t:GC.WEBSOCKET_CONNECTION_END}),io)&&io.call(this,oo)},jn.onerror=function(oo){var ao;to||/^[a-zA-Z0-9:_-\s@./]+$/.test(Qn)===!1||((ao=new n(Qn,"websocket")).spanEvent.st=0,ao.spanEvent.timestamp=Xn,ao.setStatus(2),isDefined(ao.spanEvent)&&isDefined(ao.spanEvent.ic)&&(ao.spanEvent.ic=!1),ao.setTag("process","connect"),ao.setTag("connectID",isDefined(eo)?eo:uuid()),ao.setData("url",jn.handledUrl),ao.setDuration(1e3*new Date().getTime()-Xn),ao.setStatusCode("Websocket connection failed"),ao.finish(),to=!0,c$7({t:GC.WEBSOCKET_CONNECTION_END})),ro&&ro.call(this,oo)},jn.send);return jn.send=function(oo){O(),no.call(this,oo),N.call(this,oo)},jn}for(var qn in ee)ee.hasOwnProperty(qn)&&(Gn[qn]=ee[qn]);return Gn.prototype=ee.prototype,Gn.constructor=ee,Gn}function _$1(){D$5.sett.enableWebsocket!==!1&&(window.WebSocket===void 0?log("C157"):window.WebSocket.prototype===void 0?log("C158"):window.WebSocket=x(window.WebSocket))}var w$1={config:NIL_FN,error:NIL_FN,recordCustomActionEnd:NIL_FN,setUserProperties:NIL_FN,incUserProperty:NIL_FN,removeUserProperty:NIL_FN},c="bonree-version";function _(){if(typeof document=="undefined"||!D$5.sett.probability||window.bonreeRUM)return log("C105"),window.bonreeRUM||w$1;K(),$$1(),pe$1(),r$1(),initConsole(),v$2(),_$1(),I$5();var ee=overwrite(w$1,{config:function(Gn,qn,Vn){qn===void 0&&(qn=!0),Vn===void 0&&(Vn=0),isDefined(Gn.osType)&&Gn.osType===GC.ANDROID&&D$5.sett.osType===GC.ANDROID||(D$5.setConfig.call(D$5,Gn,qn,Vn),!isDefined(Gn.appId))||k$3.setAppid(Gn)},error:F,recordCustomActionEnd:x$2}),ee=(isDefined(window)&&(window.bonreeRUM=ee),getCookie(c));window.bonreeRUM.version=version,ee&&ee===version||(setCookie(c,version),!ee)||resetSeesionId(),window.bonreeRUM.startSpan=function(Gn,qn){return new n(Gn,qn)},window.bonreeRUM.getSession=function(){return k$3.getSession()},window.bonreeRUM.getUserInfo=function(){return w$a.getUserInfo()},window.bonreeRUM.updateConfig=function(Gn){isDefined(window.stopBonreeRecord)&&(window.stopBonreeRecord(),window.stopBonreeRecordUpload=!0),E$3(),k$3.setAppid(Gn)},window.bonreeJsBridge=ne,window.bonreeRUM=extend(window.bonreeRUM,ne)}function t$1(ee){var Gn=new Metric(ee.p,GC.ACTION),Gn=(Gn.info("t").info("n").info("sa").info("i").info("vn").info("ic").info("p","","p",!0).info("lt").info("is").info("id").info("m").info("ice").info("ci").info("vt",1,"vt").info("vci","","vci").info("ir"),isDefined(ee.p.info.me)&&Gn.info("me"),Gn.build());return isDefined(ee.revts)&&0<ee.revts.length&&(Gn.revts=ee.revts),Gn}function t(ee){return ee=new Metric(ee.p,GC.CONSOLE),ee.info("lv").info("msg","","msg",!0),ee.build()}function E(ee){var Gn=typeof window!="undefined"&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:"",qn=new Metric(ee.p,GC.ERROR),Vn=isDefined(window.performance)&&isDefined(window.performance.timing)&&performance.timing.navigationStart||now();return isDefined(window.BRLog)&&window.BRLog.__sendErrorInfo(qn.data.info),qn.info(GC.PAGE_ID,"","pvid").info(GC.PAGE_URL,"","url").info(GC.ERROR_FILE,"","n").info(GC.MESSAGE,"","m").info(GC.ERROR_LINE,0,"l").info(GC.ERROR_COLUMN,0,"col").info(GC.PAGE_CREATE_TIME,1e3*Vn,"pct").info(GC.PAGE_TITLE,document.title||"","t").info("type",3,"ect").info("ua",Gn,"ua").info(GC.FRAME_WORK,"","fw"),isDefined(ee.p.info[GC.NAME]&&ee.p.info[GC.NAME]!=="")&&qn.info(GC.NAME,"","et"),isDefined(ee.p.info[GC.ERROR_STACK])&&ee.p.info[GC.ERROR_STACK]!==""?qn.info(GC.ERROR_STACK,"","sta"):isDefined(qn.data.info.file)&&(qn.data.info[GC.ERROR_STACK]="undefined at @"+qn.data.info.file,qn.info(GC.ERROR_STACK,"at undefined @ "+qn.data.info.file,"sta")),isDefined(D$5.sett.isSinglePage)?qn.info("pt",2,"pt"):qn.info("pt",1,"pt"),qn.build()}var m={pvid:"",url:"",wpi:{},wri:[],ic:!1,timestamp:isDefined(performance.timing)&&performance.timing.navigationStart?1e3*performance.timing.navigationStart:now()};function U(ee){isDefined(ee.p.info[GC.PAGE_ID])&&(m.pvid=ee.p.info[GC.PAGE_ID]),isDefined(ee.p.info[GC.PAGE_URL])&&(m.url=ee.p.info[GC.PAGE_URL]),isDefined(ee.p.info[GC.TITLE])&&ee.p.info[GC.TITLE]!=""&&(m.vt=ee.p.info[GC.TITLE]);var Gn=new Metric(ee.p,GC.WEBVIEWDATA),Gn=(Gn.metric(GC.NAVIGATION_START,"","ns").metric(GC.UNLOAD_EVENT_START,0,"ues").metric(GC.FULL_RESOURCE_LOAD_TIME,0,"frlt").metric(GC.UNLOAD_EVENT_END,0,"uee").metric(GC.REDIRECT_START,0,"rds").metric(GC.REDIRECT_END,0,"rde").metric(GC.FETCH_START,0,"fs").metric(GC.DOMAIN_LOOKUP_START,0,"dls").metric(GC.DOMAIN_LOOKUP_END,0,"dle").metric(GC.CONNECT_START,0,"cs").metric(GC.SECURE_CONNECTION_START,0,"scs").metric(GC.CONNECT_END,0,"ce").metric(GC.REQUEST_START,0,"reqs").metric(GC.RESPONSE_START,0,"rsps").metric(GC.RESPONSE_END,0,"rspe").metric(GC.DOM_LOADING,0,"dl").metric(GC.DOM_INTERACTIVE,0,"di").metric(GC.DOM_CONTENT_LOADED_EVENT_START,0,"dcles").metric(GC.DOM_CONTENT_LOADED_EVENT_END,0,"dclee").metric(GC.DOM_COMPLETE,0,"dc").metric(GC.LOAD_EVENT_START,0,"les").metric(GC.LOAD_EVENT_END,0,"lee").metric(GC.FIRST_PAINT,0,"fp").metric(GC.FIRST_CONTENTFUL_PAINT,0,"fcp").metric(GC.LARGEST_CONTENTFUL_PAINT,0,"lcp").metric(GC.TRANSFER_SIZE,0,"ts").metric(GC.ENCODED_BODY_SIZE,0,"ebs").metric(GC.DECODED_BODY_SIZE,0,"dbs").metric("tti",0,"tti").metric(GC.RESPONSE_STATUS,-1,"rs"),m.wpi=Gn.build().data,window.top&&window.self&&window.top===window.self?1:0);m.wpi.imd=Gn;try{var qn=F$1(G(m.wpi,m.url,m.pvid)),Vn=qn.type,Wn=qn.ent,jn=qn.sin,Qn=qn.data,Jn=w$a.getEviData("value");L$1.push(JSON.parse(JSON.stringify({k:Vn,ent:Wn,sin:jn,v:Qn,evi:Jn}))),Qn.ru=d$1({p:{info:{url:Qn.ru}}},D$5.secondSett).p.info.url,P$1.push({k:Vn,ent:Wn,sin:jn,v:Qn,evi:Jn})}catch(eo){log("C99-WebviewPerformanceInfoHandle"+eo)}}function M(ee){forEach(ee.p||[],function(Qn){var qn,Vn,Wn,jn,Jn=new Metric(Qn,GC.RESOURCE),Qn=(Jn.info(GC.START_TIME,0,"st").info(GC.NAME,"","name").info(GC.DURATION,0,"dura").info("ret","","rt").metric(GC.FETCH_START,0,"fs").metric(GC.DOMAIN_LOOKUP_START,0,"dls").metric(GC.DOMAIN_LOOKUP_END,0,"dle").metric(GC.CONNECT_START,0,"cs").metric(GC.CONNECT_END,0,"ce").metric(GC.SECURE_CONNECTION_START,0,"scs").metric(GC.REQUEST_START,0,"reqs").metric(GC.RESPONSE_START,0,"rsps").metric(GC.RESPONSE_END,0,"rspe").metric(GC.TRANSFER_SIZE,0,"ts").metric(GC.ENCODED_BODY_SIZE,0,"ebs").metric(GC.DECODED_BODY_SIZE,0,"dbs").metric(GC.RESPONSE_STATUS,-1,"rs"),isDefined(Qn.info)&&isDefined(Qn.info[GC.INITIATOR_TYPE])&&Jn.info(GC.INITIATOR_TYPE,"","it"),Jn.build().data),Jn=w$a.getEviData("value");Jn&&(Qn.evi=Jn),isDefined(Qn.it)&&!ee.special?Qn.it!=="xmlhttprequest"&&Qn.it!=="fetch"&&(m.wri.push(Qn),qn=(jn=F$1(P(Qn))).type,Vn=jn.ent,Wn=jn.sin,jn=jn.data,L$1.push(JSON.parse(JSON.stringify({k:qn,ent:Vn,sin:Wn,v:jn,evi:Jn}))),jn.ru=d$1({p:{info:{url:jn.ru}}},D$5.secondSett).p.info.url,P$1.push({k:qn,ent:Vn,sin:Wn,v:jn,evi:Jn})):m.wri.push(Qn)})}function V(ee){var Gn,qn;return isEmpty(m.wpi)?null:(qn=w$a.getEviData("value"),(Gn=new Metric({info:m},GC.WEBVIEWDATA)).info("pvid","","pvid").info("url","","url").info("wpi",{},"wpi").info("ic",!1,"ic").info("vt","","vt",!0).info("evi",qn),0<m.wri.length&&Gn.info("wri",[],"wri"),isDefined(ee.revts)&&0<ee.revts.length?((qn=Gn.build()).revts=ee.revts,qn):Gn.build())}function G(ee,Gn,qn){var Vn,Wn,jn,Qn,Jn,eo,Xn,to;if(Gn!=="about:blank")return ee=ee,to=getIP(Gn),Vn=getMineTypeByUrl(Gn)||"text/html",(Wn=1e3*(ee.dle-ee.dls))<0&&(Wn=0),((jn=1e3*(ee.ce-ee.scs)||-1)<0||ee.scs===0)&&(jn=0),(Qn=1e3*(ee.reqs-ee.ce)||-1)<-1?Qn=999:Qn===-1&&(Qn=0),(Jn=1e3*(ee.rsps-ee.reqs)||-1)<-1?Jn=999:Jn===-1&&(Jn=0),(eo=(eo=0)===ee.rsps?1e3*(ee.rspe-ee.fs)||-1:1e3*(ee.rspe-ee.rsps)||-1)<-1?eo=999:eo===-1&&(eo=0),Xn=g(ee.rs),(to={info:{timestamp:1e3*ee.ns,id:uuid(),ru:Gn,m:"GET",ti:to[0]||"",tp:to[1]||"",dt:Wn,sslt:jn,rt:Qn,rti:Jn,dti:eo,ds:0,rds:0,pt:-1<Gn.indexOf("https")?2:1,ec:Xn,art:1,ret:Vn,pvid:qn,ic:!1}}).info.ct=0<ee.ce-ee.cs?1e3*(ee.ce-ee.cs):0,to.info.ti.length<1&&delete to.info.ti,to}function P(ee){var Gn=ee.name.slice(0,4096),qn=3;if(isDefined(ee.it))switch(ee.it){case"xmlhttprequest":qn=3;break;case"beacon":qn=5;break;case"fetch":qn=4;break;default:qn=2}var Xn=getIP(Gn),Vn=getMineTypeByUrl(Gn)||"text/html",to=1e3*(ee.dle-ee.dls),Wn=((to<0||ee.dura<=10)&&(to=0),1e3*(ee.ce-ee.scs)||-1),jn=((Wn<0||ee.dura<=10||ee.scs===0)&&(Wn=0),1e3*(ee.reqs-ee.ce)||-1),Qn=(jn<-1||ee.dura<=10?jn=999:jn===-1&&(jn=0),1e3*(ee.rsps-ee.reqs)||-1),Jn=(Qn<-1||ee.dura<=10?Qn=999:Qn===-1&&(Qn=0),0),eo=((Jn=ee.rsps===0?1e3*(ee.rspe-ee.fs)||-1:1e3*(ee.rspe-ee.rsps)||-1)<-1||ee.dura<=10?Jn=999:Jn===-1&&(Jn=0),g(ee.rs)),Xn={info:{timestamp:isDefined(ee.st)&&isDefined(window.performance)&&isDefined(window.performance.timing)?1e3*(window.performance.timing.navigationStart+ee.st):now(),id:uuid(),ru:Gn,m:"GET",ti:Xn[0]||"",tp:Xn[1]||"",dt:to,sslt:Wn,rt:jn,rti:Qn,dti:Jn,ds:0,rds:0,pt:-1<Gn.indexOf("https")?2:1,ec:eo,art:qn,ret:Vn,ic:!1,pvid:isDefined(D$5.sett.pageViewId)?D$5.sett.pageViewId:""}},to=1e3*(ee.ce-ee.cs);return Xn.info.ct=0<to?to:0,Xn}function g(ee){return ee===-1||ee===0?200:ee}function r(ee){var Gn=new Metric(ee.p,GC.SPAN);return Gn.info("st").info("n","","n",!0).info("t","","t").info("du",0,"du").info("sta",0,"sta").info("ic",!1,"ic"),isDefined(ee.p.info)&&isDefined(ee.p.info.da)&&Gn.info("da",[],"da"),isDefined(ee.p.info)&&isDefined(ee.p.info.m)&&Gn.info("m",[],"m"),isDefined(ee.p.info)&&isDefined(ee.p.info.tag)&&Gn.info("tag",[],"tag"),isDefined(ee.p.info)&&isDefined(ee.p.info.stac)&&Gn.info("stac","","stac"),isDefined(ee.p.info)&&isDefined(ee.p.info.sub)&&Gn.info("sub",{},"sub"),Gn.build()}function I(ee){m$a(ee,function(Gn){if(!isDefined(Gn.p))return log("C144");var qn,Vn,Wn=Gn.t,jn=(isDefined(Gn.p)&&isDefined(Gn.p.info)&&!isDefined(Gn.p.info[GC.PAGE_ID])&&(Gn.p.info[GC.PAGE_ID]=D$5.sett.pageViewId,Gn.p.info[GC.PAGE_URL]=d$1({p:{info:{url:isDefined(window.location)?window.location.href:""}}},D$5.sett).p.info.url),w$a.getEviData("value")),Qn={};switch(Wn){case GC.ERROR_DATA:w$5(Qn=E(Gn),v$5.webviewJSErrorEvent);break;case GC.CONSOLE_DATA:w$5(Qn=t(Gn),v$5.consoleEvent);break;case GC.REQUEST_DATA:isDefined(Gn)&&isDefined(Gn.p)&&isDefined(Gn.p.info.ai)||isDefined(Gn.isRelatedH5)&&Gn.isRelatedH5||(isEmpty(D$5.secondSett)||isEmpty(Qn=Y$2(JSON.parse(stringify(Gn)),D$5.secondSett))||(isDefined(D$5.secondSett.osType)&&D$5.secondSett.osType!==GC.BROWSER?v$5.NetworkEvent(stringify({ent:Qn.ent,v:Qn.data,evi:jn}),D$5.secondSett):pushQueueData(Qn)),isEmpty(Qn=Y$2(JSON.parse(stringify(Gn)),D$5.sett)))||(isDefined(D$5.sett.osType)&&D$5.sett.osType!==GC.BROWSER?v$5.NetworkEvent(stringify({ent:Qn.ent,v:Qn.data,evi:jn}),D$5.sett):pushQueueData(Qn)),isDefined(Gn)&&isDefined(Gn.p.info)&&isDefined(Gn.p.info.ai)&&setTimeout(function(){c$7({t:GC.LIFESTYLE_REQUEST_END+Gn.p.info.ai,requestData:JSON.parse(stringify(Gn)),endTime:now()})},0),isDefined(Gn.isRelatedH5)&&Gn.isRelatedH5&&c$7({t:GC.LIFESTYLE_REQUEST_END+"H5",requestData:JSON.parse(stringify(Gn)),endTime:now()}),c$7({t:GC.LIFESTYLE_REQUEST_END,requestData:JSON.parse(stringify(Gn)),endTime:now()});break;case GC.SPAN_DATA:isDefined((Qn=r(Gn)).data)&&c$7({t:GC.SPAN_CALLBACK_DATA,websocketData:Qn}),Qn.isRelated||w$5(Qn,v$5.spanEvent);break;case GC.RESOURCE_DATA:Gn.type===1&&U(Gn),Gn.type===2&&M(Gn),Gn.type===3&&isDefined(Qn=V(Gn))&&(isDefined(D$5.sett.osType)&&D$5.sett.osType!==GC.BROWSER?(qn=window.top&&window.self&&window.top===window.self?1:0,Qn.imd=qn,Vn={ent:Qn.ent,v:Qn.data,evi:Qn.evi},isDefined(Qn.revts)&&0<Qn.revts.length&&(Qn.revts.map(function(Jn){return Jn.v.ru.split("?")[0]==window.location.href.split("?")[0]&&(Jn.v.imd=qn),Qn.evi&&(Jn.evi=Qn.evi),Jn}),Vn.revts=Qn.revts),checkMoudle("network")||delete Vn.revts,v$5.webviewPerformanceTimingEvent(stringify(Vn),D$5.sett,qn)):pushQueueData(Qn)),Gn.type===4&&isDefined(Qn=V(Gn))&&(isDefined(D$5.secondSett.osType)&&D$5.secondSett.osType!==GC.BROWSER?(qn=window.top&&window.self&&window.top===window.self?1:0,Qn.imd=qn,Qn.data.url=d$1({p:{info:{url:Qn.data.url}}},D$5.secondSett).p.info.url,Vn={ent:Qn.ent,v:Qn.data,evi:Qn.evi},isDefined(Qn.revts)&&0<Qn.revts.length&&(Qn.revts.map(function(Jn){return Jn.v.ru.split("?")[0]==window.location.href.split("?")[0]&&(Jn.v.imd=qn),Qn.evi&&(Jn.evi=Qn.evi),Jn}),Vn.revts=Qn.revts),checkMoudle("network")||delete Vn.revts,v$5.webviewPerformanceTimingEvent(stringify(Vn),D$5.secondSett,qn)):pushQueueData(Qn));break;case GC.TRACE_ACTION_DATA:Qn=t$1(Gn),isDefined(Gn.type)&&Gn.type===1&&(isDefined(D$5.sett.osType)&&D$5.sett.osType!==GC.BROWSER?(Vn={ent:Qn.ent,v:Qn.data,evi:jn},isDefined(Qn.revts)&&0<Qn.revts.length&&(Vn.revts=Qn.revts),v$5.webviewActionEvent(stringify(Vn),D$5.sett)):pushQueueData(Qn)),isDefined(Gn.type)&&Gn.type===2&&(isDefined(D$5.secondSett.osType)&&D$5.secondSett.osType!==GC.BROWSER?(Qn.data.vn=d$1({p:{info:{url:Qn.data.vn}}},D$5.secondSett).p.info.url,Vn={ent:Qn.ent,v:Qn.data,evi:jn},isDefined(Qn.revts)&&0<Qn.revts.length&&(Vn.revts=Qn.revts),v$5.webviewActionEvent(stringify(Vn),D$5.secondSett)):pushQueueData(Qn));break;default:log("C118")}})}function h(){I([GC.ROUTE_DATA,GC.ERROR_DATA,GC.CONSOLE_DATA,GC.REQUEST_DATA,GC.TRACE_ACTION_DATA,GC.SPAN_DATA,GC.RESOURCE_DATA,GC.PAGE_DATA])}function s(){var ee;window.bonreeRUM?log("C104"):((ee=getDeviceInfo())&&ee.ctn&&ee.ctn.toLowerCase()!=="ie"&&(window.BonreeRecord={BonreeRecord:BonreeRecord}),_(),h(),Z$1())}function w(ee){if(m$6(),document&&T$5(),isDefined(window.getIsRunWithNetClient)&&(ee.dataFusionInfo="net"),isDefined(window.bonreeRUM)){if(isDefined(window.bonreeRUM.config))return void window.bonreeRUM.config(ee,!1,1)}else D$5.setConfig(ee),p$6(ee.appId);D$5.sett.initTime=now(),s()}L$3()&&s();var l={BonreeRecord:BonreeRecord,BonreeStart:w,Sm4:u,getDeviceInfo:getDeviceInfo,setExtraInfo:C$7,setUserID:T$4};window.BonreeAgent=l;export{BonreeRecord,w as BonreeStart,u as Sm4,getDeviceInfo,C$7 as setExtraInfo,T$4 as setUserID};
