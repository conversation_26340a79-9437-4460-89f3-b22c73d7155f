import fs from 'fs'
import path from 'path'
import process from 'node:process'
import FormData from 'form-data'
import fetch from 'node-fetch'

// Bonree配置 - 支持从环境变量读取
const BONREE_CONFIG = {
  secretId: process.env.VITE_BONREE_SECRET_ID || '8e88439aSnhBpXbA',
  secretKey: process.env.VITE_BONREE_SECRET_KEY || '3bc2b3c0ca60daf7aa36e444333a87cd',
  appMd5: process.env.VITE_BONREE_APP_MD5 || '1e8618ab881b420aba1c48370586eb1b',
  // 强制使用指定IP地址，避免DNS解析差异
  tokenUrl: 'https://**************/openapi/iam/v1/access/token',
  uploadUrl: 'https://**************/openapi/rum/v1/sourceMap/upload',
  // 原始域名，用于Host头
  originalHost: 'one.bonree.com'
}

/**
 * 获取Bonree访问token
 */
async function getBonreeToken() {
  try {
    console.log('✅ BONREE_CONFIG', BONREE_CONFIG)

    const response = await fetch(BONREE_CONFIG.tokenUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Host: BONREE_CONFIG.originalHost // 添加Host头指定原始域名
      },
      body: JSON.stringify({
        secretId: BONREE_CONFIG.secretId,
        secretKey: BONREE_CONFIG.secretKey
      })
    })

    const result = await response.json()

    if (result.code === '200' && result.data) {
      console.log('✅ Bonree token获取成功')
      return result.data.accessToken
    } else {
      throw new Error(`获取token失败: ${result.msg}`)
    }
  } catch (error) {
    console.error('❌ 获取Bonree token失败:', error.message)
    throw error
  }
}

/**
 * 批量上传sourcemap文件到Bonree
 */
async function uploadSourceMapsBatch(accessToken, mapFiles) {
  try {
    console.log(`📦 批量上传 ${mapFiles.length} 个sourcemap文件`)
    console.log(`🔧 使用应用MD5: ${BONREE_CONFIG.appMd5}`)
    console.log(`🔑 Token: ${accessToken.substring(0, 20)}...`)

    const formData = new FormData()

    // 添加应用MD5（固定值，不是文件MD5）
    formData.append('md5', BONREE_CONFIG.appMd5)

    // 添加所有SourceMap文件
    for (const filePath of mapFiles) {
      const fileName = path.basename(filePath)
      const fileSize = fs.statSync(filePath).size
      console.log(`📄 准备文件: ${fileName} (${(fileSize / 1024).toFixed(2)}KB)`)

      // 添加文件到FormData
      formData.append('uploadFile', fs.createReadStream(filePath), {
        filename: fileName,
        contentType: 'application/json'
      })
    }

    console.log(`🚀 开始批量上传到: ${BONREE_CONFIG.uploadUrl}`)
    console.log(`🏠 Host头: ${BONREE_CONFIG.originalHost}`)

    const startTime = Date.now()

    const response = await fetch(BONREE_CONFIG.uploadUrl, {
      method: 'POST',
      headers: {
        Authorization: accessToken,
        Host: BONREE_CONFIG.originalHost, // 添加Host头指定原始域名
        ...formData.getHeaders()
      },
      body: formData
    })

    const endTime = Date.now()
    const duration = endTime - startTime

    console.log(`⏱️ 上传耗时: ${duration}ms`)
    console.log(`📊 响应状态: ${response.status} ${response.statusText}`)

    // 打印响应头
    console.log(`📊 响应头:`)
    for (const [key, value] of response.headers) {
      console.log(`   ${key}: ${value}`)
    }

    const result = await response.json()
    console.log(`📊 响应数据:`, result)

    if (result.code === '200') {
      console.log(`✅ 批量上传成功！`)
      return { success: true, count: mapFiles.length }
    } else if (result.code === '1400093') {
      // 1400093 表示文件已存在，视为成功
      console.log(`✅ 文件已存在，跳过上传 (错误码: ${result.code})`)
      console.log(`📝 详细信息: ${result.msg}`)
      return { success: true, count: mapFiles.length, skipped: true }
    } else {
      const errorMsg = `批量上传失败: ${result.msg || result.message || '未知错误'} (代码:${result.code})`
      console.error(`❌ ${errorMsg}`)
      return { success: false, error: errorMsg, count: mapFiles.length }
    }
  } catch (error) {
    console.error(`❌ 批量上传异常:`, error.message)

    // 如果是网络错误，打印更多信息
    if (error.code) {
      console.error(`🔧 错误代码: ${error.code}`)
    }
    if (error.type) {
      console.error(`🔧 错误类型: ${error.type}`)
    }

    return { success: false, error: error.message, count: mapFiles.length }
    }
}

/**
 * 批量上传sourcemap文件
 */
async function uploadSourceMaps(distDir) {
  try {
    console.log('🚀 开始上传sourcemap文件到Bonree...')

    // 获取访问token
    const accessToken = await getBonreeToken()

    // 查找所有.map文件
    const mapFiles = []

    function findMapFiles(dir) {
      const files = fs.readdirSync(dir)

      for (const file of files) {
        const fullPath = path.join(dir, file)
        const stat = fs.statSync(fullPath)

        if (stat.isDirectory()) {
          findMapFiles(fullPath)
        } else if (file.endsWith('.js.map')) {
          mapFiles.push(fullPath)
        }
      }
    }

    findMapFiles(distDir)

    if (mapFiles.length === 0) {
      console.log('⚠️ 未找到sourcemap文件')
      return
    }

    console.log(`📁 找到 ${mapFiles.length} 个sourcemap文件:`)
    mapFiles.forEach(file => {
      console.log(`   - ${path.relative(distDir, file)}`)
    })

    // 批量上传所有文件
    const result = await uploadSourceMapsBatch(accessToken, mapFiles)

    // 统计结果
      console.log('\n📊 上传结果统计:')
    if (result.success) {
      if (result.skipped) {
        console.log(`✅ 跳过: ${result.count} 个文件 (文件已存在)`)
        console.log(`❌ 失败: 0 个文件`)
      } else {
        console.log(`✅ 成功: ${result.count} 个文件`)
      console.log(`❌ 失败: 0 个文件`)
      }
    } else {
      console.log(`✅ 成功: 0 个文件`)
      console.log(`❌ 失败: ${result.count} 个文件`)
      console.error(`🚫 失败原因: ${result.error}`)
    }

    console.log('🎉 Sourcemap上传完成!')
  } catch (error) {
    console.error('❌ 上传sourcemap失败:', error.message)
    throw error
  }
}

export { uploadSourceMaps }
