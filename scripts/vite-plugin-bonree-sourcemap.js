import { uploadSourceMaps } from './upload-sourcemap.js'

/**
 * Vite插件：构建完成后自动上传sourcemap到Bonree
 */
export function viteBonreeSourceMapPlugin(options = {}) {
  return {
    name: 'vite-bonree-sourcemap',
    apply: 'build', // 只在构建时应用

    // 在构建完成后执行
    async closeBundle() {
      try {
        // 默认dist目录，可以通过options.distDir自定义
        const distDir = options.distDir || 'dist'

        console.log('\n🎯 构建完成，开始上传sourcemap到Bonree...')

        // 上传sourcemap文件
        await uploadSourceMaps(distDir)
      } catch (error) {
        console.error('❌ 上传sourcemap到Bonree失败:', error.message)

        // 根据配置决定是否让构建失败
        if (options.failOnError !== false) {
          throw error
        } else {
          console.warn('⚠️ sourcemap上传失败，但构建将继续...')
        }
      }
    }
  }
}
