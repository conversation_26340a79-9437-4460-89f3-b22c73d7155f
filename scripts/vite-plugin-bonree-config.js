/**
 * Vite 插件：动态生成 BonreeSDK 配置
 * 根据环境变量动态生成不同环境的 BonreeSDK data 参数
 */
import process from 'node:process'

export function viteBonreeConfigPlugin() {
  let viteConfig = null

  return {
    name: 'bonree-config-plugin',
    configResolved(config) {
      // 存储配置以便在 transformIndexHtml 中使用
      viteConfig = config
    },
    transformIndexHtml: {
      order: 'pre', // 在其他插件之前执行
      handler(html, context) {
        // 获取环境变量 - 优先使用 Vite 配置中的环境变量
        const env = viteConfig?.env || context.server?.config?.env || process.env

        // 构建 BonreeSDK 配置对象
        const bonreeConfig = {
          reqHeaderTraceKey: ['tracestate', 'traceparent'],
          uploadAddrHttps:
            env.VITE_BONREE_UPLOAD_ADDR_HTTPS || 'https://oneupload.bonree.com/RUM/upload',
          probability: parseInt(env.VITE_BONREE_PROBABILITY || '1000'),
          mc: [{ n: 'network', cs: { fc: 0 } }],
          appId: env.VITE_BONREE_APP_ID || '7f4c70a2aa5a45008964366079400c65',
          uploadAddrHttp:
            env.VITE_BONREE_UPLOAD_ADDR_HTTP || 'http://oneupload.bonree.com/RUM/upload',
          hcs: 2,
          respHeaderTraceKey: ['traceresponse', 'x-br-response'],
          brss: false
        }

        // 将配置对象转换为 URL 编码的字符串
        const bonreeDataString = encodeURIComponent(JSON.stringify(bonreeConfig))

        // 替换 HTML 中的 BonreeSDK script 标签
        const bonreeScriptRegex =
          /<script[^>]*src="[^"]*BonreeSDK[^"]*"[^>]*data="[^"]*"[^>]*><\/script>/

        const newBonreeScript = `<script
      type="text/javascript"
      src="/libs/BonreeSDK/BonreeSDK_JS.min.js"
      data="${bonreeDataString}"
      id="BonreeAgent"
    ></script>`

        // 如果找到现有的 BonreeSDK script 标签，则替换它
        if (bonreeScriptRegex.test(html)) {
          return html.replace(bonreeScriptRegex, newBonreeScript)
        }

        // 如果没有找到，则在 head 标签中添加
        return html.replace('</head>', `  ${newBonreeScript}\n  </head>`)
      }
    }
  }
}
