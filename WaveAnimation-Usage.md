# WaveAnimation Vue 组件使用指南

## 📝 概述

`WaveAnimation` 是一个轻量级的 Vue 3 组件，用纯 CSS 实现波动动画效果，替代了原有的 Lottie 动画。

## 🚀 特性

- ✅ **零依赖** - 纯 CSS + Vue 实现
- ✅ **高性能** - GPU 加速动画
- ✅ **响应式** - 完全适配容器尺寸
- ✅ **可定制** - 支持颜色、速度、尺寸配置
- ✅ **轻量级** - 比 Lottie 文件小很多

## 📦 安装

组件已放置在 `src/components/Common/WaveAnimation.vue`，可直接导入使用。

## 🔧 基本用法

### 最简单的使用方式

```vue
<template>
  <div style="width: 24px; height: 24px;">
    <WaveAnimation />
  </div>
</template>

<script setup>
import WaveAnimation from '@/components/Common/WaveAnimation.vue'
</script>
```

### 自定义颜色

```vue
<template>
  <div style="width: 32px; height: 32px;">
    <WaveAnimation color="#e74c3c" />
  </div>
</template>
```

### 指定尺寸

```vue
<template>
  <!-- 方式1：通过父容器控制尺寸（推荐） -->
  <div style="width: 40px; height: 40px;">
    <WaveAnimation color="#27ae60" />
  </div>

  <!-- 方式2：通过 props 直接指定尺寸 -->
  <WaveAnimation :width="40" :height="40" color="#3498db" />
</template>
```

### 控制动画速度

```vue
<template>
  <div style="width: 30px; height: 30px;">
    <!-- 慢速动画 (2秒) -->
    <WaveAnimation speed="slow" color="#9b59b6" />
  </div>

  <div style="width: 30px; height: 30px;">
    <!-- 正常速度 (1.2秒，默认) -->
    <WaveAnimation speed="normal" color="#f39c12" />
  </div>

  <div style="width: 30px; height: 30px;">
    <!-- 快速动画 (0.8秒) -->
    <WaveAnimation speed="fast" color="#e67e22" />
  </div>
</template>
```

### 暂停/恢复动画

```vue
<template>
  <div style="width: 30px; height: 30px;">
    <WaveAnimation :paused="isPaused" />
  </div>
  <button @click="isPaused = !isPaused">{{ isPaused ? '恢复' : '暂停' }} 动画</button>
</template>

<script setup>
import { ref } from 'vue'
const isPaused = ref(false)
</script>
```

## 📋 API 参考

### Props

| 属性名   | 类型               | 默认值      | 说明                                                          |
| -------- | ------------------ | ----------- | ------------------------------------------------------------- |
| `width`  | `String \| Number` | `'100%'`    | 动画容器宽度，数字为px，字符串保持原样                        |
| `height` | `String \| Number` | `'100%'`    | 动画容器高度，数字为px，字符串保持原样                        |
| `color`  | `String`           | `'#ffaa0a'` | 动画颜色，支持任何有效的CSS颜色值                             |
| `speed`  | `String`           | `'normal'`  | 动画速度：`'slow'` (2s) / `'normal'` (1.2s) / `'fast'` (0.8s) |
| `paused` | `Boolean`          | `false`     | 是否暂停动画                                                  |

### 使用示例对比

#### 替换前（Lottie）

```vue
<template>
  <div class="enter-icon">
    <lottie-player
      mode="normal"
      autoplay
      loop
      :src="getAssetPath('/lottiefiles/today/bodong.json')"
    />
  </div>
</template>

<script setup>
import '@lottiefiles/lottie-player'
import { getAssetPath } from '@/utils/assetPath'
</script>

<style>
.enter-icon {
  width: 12px;
  height: 12px;
}
</style>
```

#### 替换后（CSS动画）

```vue
<template>
  <div class="enter-icon">
    <WaveAnimation color="#ff9f0a" />
  </div>
</template>

<script setup>
import WaveAnimation from '@/components/Common/WaveAnimation.vue'
</script>

<style>
.enter-icon {
  width: 12px;
  height: 12px;
}
</style>
```

## 🎯 实际应用场景

### 1. 按钮中的加载动画

```vue
<template>
  <button class="live-button" @click="enterClassroom">
    <div class="icon-container">
      <WaveAnimation color="#ff9f0a" />
    </div>
    <span>进入直播</span>
  </button>
</template>

<style>
.live-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.icon-container {
  width: 12px;
  height: 12px;
}
</style>
```

### 2. 状态指示器

```vue
<template>
  <div class="status-indicator">
    <div class="animation-wrapper">
      <WaveAnimation :color="statusColor" :speed="statusSpeed" />
    </div>
    <span>{{ statusText }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps(['status'])

const statusColor = computed(() => {
  const colors = {
    live: '#27ae60',
    loading: '#f39c12',
    error: '#e74c3c'
  }
  return colors[props.status] || '#ffaa0a'
})

const statusSpeed = computed(() => {
  return props.status === 'live' ? 'fast' : 'normal'
})
</script>

<style>
.animation-wrapper {
  width: 16px;
  height: 16px;
}
</style>
```

### 3. 响应式使用

```vue
<template>
  <div class="responsive-container">
    <WaveAnimation color="#3498db" />
  </div>
</template>

<style>
.responsive-container {
  width: 100%;
  max-width: 60px;
  height: 40px;

  /* 在小屏幕上调整尺寸 */
  @media (max-width: 768px) {
    max-width: 40px;
    height: 30px;
  }
}
</style>
```

## 🔧 进阶自定义

如果需要更精细的控制，可以通过 CSS 变量进行自定义：

```vue
<template>
  <div
    class="custom-wave"
    style="
      --wave-color: #e91e63;
      --bar-width: 12%;
      --bar1-height-min: 20%;
      --bar1-height-max: 80%;
    "
  >
    <WaveAnimation />
  </div>
</template>

<style>
.custom-wave {
  width: 50px;
  height: 50px;
}
</style>
```

## 📊 性能对比

| 方案          | 文件大小      | 加载时间 | 运行性能 | 自定义性 |
| ------------- | ------------- | -------- | -------- | -------- |
| Lottie        | ~150KB + JSON | 较慢     | 中等     | 低       |
| WaveAnimation | ~2KB          | 很快     | 很好     | 高       |

## 🐛 常见问题

### Q: 动画不显示？

A: 确保父容器有明确的宽高设置。

### Q: 如何让动画在鼠标悬停时暂停？

A: 使用 `:paused` prop 结合鼠标事件：

```vue
<template>
  <div @mouseenter="paused = true" @mouseleave="paused = false">
    <WaveAnimation :paused="paused" />
  </div>
</template>
```

### Q: 可以自定义动画的其他属性吗？

A: 目前支持颜色、速度、暂停控制。如需更多自定义，可以通过 CSS 变量或修改组件源码。

## 🔄 迁移指南

从 Lottie 迁移到 WaveAnimation：

1. **移除 Lottie 相关导入**
2. **导入 WaveAnimation 组件**
3. **替换模板中的 lottie-player**
4. **根据需要调整颜色和尺寸**

完成！🎉
