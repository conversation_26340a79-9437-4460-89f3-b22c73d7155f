# BonreeSDK 环境配置说明

## 概述

本项目已实现 BonreeSDK 的环境化配置，可以根据不同的构建环境（development、beta、preprod、production）自动使用不同的 BonreeSDK 配置参数。

## 配置方式

### 1. 环境变量配置

在对应的环境变量文件中配置 BonreeSDK 相关参数：

- `.env.development` - 开发环境
- `.env.beta` - 测试环境
- `.env.preprod` - 预生产环境
- `.env.production` - 生产环境

### 2. 可配置的环境变量

```bash
# BonreeSDK 应用 ID（不同环境可使用不同的 appId）
VITE_BONREE_APP_ID=your_app_id_here

# HTTPS 上传地址
VITE_BONREE_UPLOAD_ADDR_HTTPS=https://oneupload.bonree.com/RUM/upload

# HTTP 上传地址
VITE_BONREE_UPLOAD_ADDR_HTTP=http://oneupload.bonree.com/RUM/upload

# 采样率（1000 = 100%，100 = 10%，1 = 0.1%）
VITE_BONREE_PROBABILITY=1000
```

### 3. 当前环境配置

#### Development 环境

- **App ID**: `7f4c70a2aa5a45008964366079400c65`
- **采样率**: 10%（VITE_BONREE_PROBABILITY=100）
- **用途**: 开发调试，减少数据上报量

#### Beta 环境

- **App ID**: `7f4c70a2aa5a45008964366079400c65`
- **采样率**: 100%（VITE_BONREE_PROBABILITY=1000）
- **用途**: 测试环境完整监控

#### Preprod 环境

- **App ID**: `c597ce06796942939d63363f1ee006e4`
- **采样率**: 100%（VITE_BONREE_PROBABILITY=1000）
- **用途**: 预生产环境完整监控

#### Production 环境

- **App ID**: `df7820a23b354af6821f1b1be6a5ae43`
- **采样率**: 100%（VITE_BONREE_PROBABILITY=1000）
- **用途**: 生产环境完整监控

## 技术实现

### 1. Vite 插件

使用自定义 Vite 插件 `vite-plugin-bonree-config.js` 实现：

- 在构建时读取环境变量
- 动态生成 BonreeSDK 配置对象
- 自动替换 HTML 中的 script 标签

### 2. 配置生成流程

1. 插件读取当前环境的环境变量
2. 构建 BonreeSDK 配置对象
3. 将配置对象序列化并 URL 编码
4. 在 HTML 中动态插入或替换 BonreeSDK script 标签

## 使用方法

### 1. 修改环境配置

直接编辑对应环境的 `.env.*` 文件：

```bash
# 例如：为 beta 环境设置不同的 appId
# .env.beta
VITE_BONREE_APP_ID=beta_specific_app_id
VITE_BONREE_PROBABILITY=500  # 50% 采样率
```

### 2. 构建项目

使用现有的构建命令，插件会自动应用对应环境的配置：

```bash
# 开发环境
npm run dev

# Beta 环境构建
npm run build:beta

# 预生产环境构建
npm run build:pre

# 生产环境构建
npm run build
```

### 3. 验证配置

构建完成后，检查生成的 `dist/index.html` 文件中的 BonreeSDK script 标签，确认 `data` 属性包含正确的配置。

## 注意事项

1. **环境变量优先级**：插件会优先使用环境变量中的配置，如果环境变量不存在则使用默认值

2. **配置验证**：建议在不同环境部署前验证生成的配置是否正确

3. **敏感信息**：如果 BonreeSDK 配置包含敏感信息，请确保环境变量文件的安全性

4. **缓存清理**：修改环境变量后，建议清理构建缓存重新构建

## 扩展配置

如需添加更多 BonreeSDK 配置项，可以：

1. 在环境变量文件中添加新的 `VITE_BONREE_*` 变量
2. 在 `vite-plugin-bonree-config.js` 中添加对应的配置读取逻辑
3. 更新 `bonreeConfig` 对象结构

例如：

```javascript
// 在插件中添加新配置项
const bonreeConfig = {
  // ... 现有配置
  customField: env.VITE_BONREE_CUSTOM_FIELD || 'default_value'
}
```
