import { fileURLToPath, URL } from 'node:url'
import process from 'node:process'
import https from 'node:https'
import { <PERSON><PERSON><PERSON> } from 'node:buffer'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import svgLoader from 'vite-svg-loader'
import { viteS3 } from 'vite-plugin-s3'
import { viteBonreeSourceMapPlugin } from './scripts/vite-plugin-bonree-sourcemap.js'
import { viteBonreeConfigPlugin } from './scripts/vite-plugin-bonree-config.js'

// 综合CDN处理插件：处理静态资源路径、注入运行时脚本、添加资源ID注释
function cdnProcessPlugin({ cdnUrl, uniqueResourceId }) {
  return {
    name: 'cdn-process-plugin',
    // 在 transformIndexHtml 钩子中处理 HTML
    transformIndexHtml: {
      order: 'post',
      handler(html) {
        // 1. 为打包后的 JS 和 CSS 文件添加 CDN 前缀
        let processedHtml = html
          .replace(/(<script[^>]*src=")\.\/assets\/([^"]*\.js)(")/g, `$1${cdnUrl}/assets/$2$3`)
          .replace(/(<link[^>]*href=")\.\/assets\/([^"]*\.css)(")/g, `$1${cdnUrl}/assets/$2$3`)
          // 处理其他静态资源
          .replace(/(<link[^>]*href=")\.\/font\/([^"]*\.css)(")/g, `$1${cdnUrl}/font/$2$3`)
          .replace(/(<script[^>]*src=")\.\/libs\/([^"]*\.js)(")/g, `$1${cdnUrl}/libs/$2$3`)
          .replace(/(<script[^>]*src=")\/libs\/([^"]*\.js)(")/g, `$1${cdnUrl}/libs/$2$3`)

          .replace(/(<link[^>]*href=")\.\/favicon\.ico(")/g, `$1${cdnUrl}/favicon.ico$2`)

        // 2. 注入运行时 CDN 替换脚本
        const replaceScript = `
        <script>
          window.customCDN = '${cdnUrl}';
        </script>`

        // 3. 添加包含唯一资源ID的注释
        const resourceComment = `\n  <!--//e8e3b41dd86c006864//${uniqueResourceId}//-->`

        // 4. 将脚本和注释一起插入到 <head> 标签后
        return processedHtml.replace('<head>', `<head>${resourceComment}${replaceScript}`)
      }
    }
  }
}

// API调用函数
async function callApiAfterUpload({ apiUrl, projectData }) {
  try {
    console.log('🚀 S3上传完成，开始调用API...')

    const data = JSON.stringify(projectData)
    const url = new URL(apiUrl)

    const options = {
      hostname: url.hostname,
      port: url.port || 443,
      path: url.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(data)
      }
    }

    return new Promise((resolve, reject) => {
      const req = https.request(options, res => {
        let responseBody = ''

        res.on('data', chunk => {
          responseBody += chunk
        })

        res.on('end', () => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            console.log('✅ API调用成功:', responseBody)
            resolve(responseBody)
          } else {
            console.error('❌ API调用失败:', res.statusCode, responseBody)
            reject(new Error(`API call failed: ${res.statusCode}`))
          }
        })
      })

      req.on('error', error => {
        console.error('❌ API调用出错:', error.message)
        reject(error)
      })

      req.write(data)
      req.end()

      console.log('🚀 正在调用API:', apiUrl)
      console.log('📦 发送数据:', projectData)
    })
  } catch (error) {
    console.error('❌ API调用出错:', error.message)
    throw error
  }
}

const cdnDomain = '//download-pa-s3.thethinkacademy.com'
// https://vitejs.dev/config/
const name = process.env.npm_package_name

// 生成唯一的资源ID
const generateUniqueResourceId = () => {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 8) // 6位随机字符
  return `${timestamp}-${random}`
}

// https://vite.dev/config/
export default defineConfig(({ mode, command }) => {
  // 判断是否为打包环境
  const isBuild = command === 'build'
  console.log(`🔧 当前模式: ${mode}, 命令: ${command}, 是否打包: ${isBuild}`)
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  const uniqueResourceId = generateUniqueResourceId()
  console.log(`🆔 生成唯一资源ID: ${uniqueResourceId}`)

  // 获取环境变量中的域名
  const overseaDomain = env.VITE_APP_URL_OVERSEA
  if (!overseaDomain) {
    throw new Error('❌ 环境变量 VITE_APP_URL_OVERSEA 未设置')
  }

  // 构建完整的API URL
  const apiUrl = `${overseaDomain}/api/smartline/v1/createStaticResourceDistributeTask`
  console.log(`🌐 使用API地址: ${apiUrl}`)

  // 构建完整的CDN路径，包含唯一资源ID
  const cdnPath = `/static-pa/project/${name}/${mode}/${uniqueResourceId}`
  const fullCdnPath = `${cdnDomain}${cdnPath}`
  console.log(`📁 CDN完整路径: ${fullCdnPath}`)

  return {
    base: './',
    server: { host: '0.0.0.0', allowedHosts: true },
    build: {
      sourcemap: true,
      rollupOptions: {
        output: {
          sourcemapExcludeSources: false // 确保源代码包含在sourcemap中
        }
      }
    },
    define: {
      global: 'globalThis',
      'process.env': {}
    },
    plugins: [
      vue(),
      vueDevTools(),
      // BonreeSDK 配置插件 - 在所有环境都启用
      viteBonreeConfigPlugin(),
      svgLoader({
        svgoConfig: {
          plugins: [
            {
              name: 'preset-default',
              params: {
                overrides: {
                  removeViewBox: false
                }
              }
            }
          ]
        }
      }),
      // 在所有打包环境启用CDN和S3相关插件，本地开发环境不启用
      ...(isBuild
        ? [
            cdnProcessPlugin({
              cdnUrl: fullCdnPath,
              uniqueResourceId: uniqueResourceId
            }),
            // 使用有回调功能的S3插件
            viteS3({
              exclude: /.*\.html$|.*\.map$/, // Exclude HTML and sourcemap files
              uploadEnabled: true,
              basePath: cdnPath,
              uploadDir: 'dist',
              s3Options: {
                accessKeyId: '********************',
                secretAccessKey: 'Eq8XWHCEf/ESUs/WGDBtuapkIKPLLMPn0feR2rVN',
                region: 'us-west-2',
                // 增加超时配置
                httpOptions: {
                  timeout: 300000 // 5分钟超时
                },
                maxRetries: 3
              },
              s3UploadOptions: {
                Bucket: 'pa-s3-prod',
                ACL: 'private'
              },
              // 关键：上传完成后的回调
              onFinished: async (s3Client, config, manifestHash) => {
                console.log('✅ S3上传完成，收到回调通知')
                console.log('📊 上传配置:', config)
                console.log('🔗 manifest hash:', manifestHash)

                // 在S3上传完成后调用API
                await callApiAfterUpload({
                  apiUrl: apiUrl, // 使用环境变量构建的API URL
                  projectData: {
                    project: name, // 使用项目名称
                    resourceId: uniqueResourceId, // 使用唯一ID
                    bucketId: 'pa-s3-prod', // S3桶名称
                    path: cdnPath, // S3中的路径，包含唯一资源ID
                    index: 'index.html' // 入口文件
                  }
                })
              }
            }),
            // 添加Bonree sourcemap上传插件
            viteBonreeSourceMapPlugin({
              distDir: 'dist',
              failOnError: false // 上传失败不中断构建
            })
          ]
        : [])
    ],
    resolve: { alias: { '@': fileURLToPath(new URL('./src', import.meta.url)) } }
  }
})
